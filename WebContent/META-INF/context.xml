<?xml version='1.0' encoding='utf-8'?>  
  
<Context>  
  <!--  开发环境数据库连接池   
    <Resource name="jdbc_hgzdb"    
       auth="Container"     
       type="javax.sql.DataSource"
       driverClassName="com.mysql.jdbc.Driver"     
       url="***********************************************************************"
       username="dfmhgzuser"     
       password="dfmhgz88!"
        
        initialSize="10"
        minIdle="10"
        maxIdle="20"
		maxTotal="50"
		maxWaitMillis="10000"
		
		logAbandoned="true"
		removeAbandonedOnMaintenance="true"  
        removeAbandonedTimeout="180"  
        
        validationQuery="select 1 " 
		testWhileIdle = "true"      
		testOnBorrow = "false"   
		timeBetweenEvictionRunsMillis = "300000"  
		minEvictableIdleTimeMillis = "1800000"  
		numTestsPerEvictionRun="10"  
  />
  -->
  <!--  本地数据库连接池   -->
    <Resource name="jdbc_hgzdb"     
       auth="Container"     
       type="javax.sql.DataSource"
       driverClassName="com.mysql.jdbc.Driver"     
       url="*******************************************************************"
       username="dfmhgzuser"     
       password="dfmhgz88!"
        
        initialSize="10"
        minIdle="10"
        maxIdle="20"
		maxTotal="50"
		maxWaitMillis="10000"
		
		logAbandoned="true"
		removeAbandonedOnMaintenance="true"  
        removeAbandonedTimeout="180"  
        
        validationQuery="select 1 " 
		testWhileIdle = "true"      
		testOnBorrow = "false"   
		timeBetweenEvictionRunsMillis = "300000"  
		minEvictableIdleTimeMillis = "1800000"  
		numTestsPerEvictionRun="10"  
  />


  <!--  综测环境数据库连接池  
     <Resource name="jdbc_hgzdb"     
       auth="Container"     
       type="javax.sql.DataSource"
       driverClassName="com.mysql.jdbc.Driver"     
       url="jdbc:mysql://*************:3306/dfmhgz?useUnicode=true&amp;useSSL=false"
       username="dfmhgzuser"     
       password="Dfmhgz88!@"
        
        initialSize="10"
        minIdle="10"
        maxIdle="20"
		maxTotal="50"
		maxWaitMillis="10000"
		
		logAbandoned="true"
		removeAbandonedOnMaintenance="true"  
        removeAbandonedTimeout="180"  
        
        validationQuery="select 1 " 
		testWhileIdle = "true"      
		testOnBorrow = "false"   
		timeBetweenEvictionRunsMillis = "300000"  
		minEvictableIdleTimeMillis = "1800000"  
		numTestsPerEvictionRun="10"  
  />
--> 

  <!--  业务测试环境数据库连接池  
     <Resource name="jdbc_hgzdb"     
       auth="Container"     
       type="javax.sql.DataSource"
       driverClassName="com.mysql.jdbc.Driver"     
       url="*********************************************************************************************************************"
       username="dfmhgzuser"     
       password="Dfmhgz88!@"
        
        initialSize="10"
        minIdle="10"
        maxIdle="20"
		maxTotal="50"
		maxWaitMillis="10000"
		
		logAbandoned="true"
		removeAbandonedOnMaintenance="true"  
        removeAbandonedTimeout="180"  
        
        validationQuery="select 1 " 
		testWhileIdle = "true"      
		testOnBorrow = "false"   
		timeBetweenEvictionRunsMillis = "300000"  
		minEvictableIdleTimeMillis = "1800000"  
		numTestsPerEvictionRun="50"  
  />
--> 
 
</Context>