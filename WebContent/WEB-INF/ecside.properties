messages=org.ecside.resource.TableResourceBundle
table.interceptor.default=org.ecside.table.interceptor.DefaultInterceptor
row.interceptor.default=org.ecside.table.interceptor.DefaultInterceptor
column.interceptor.default=org.ecside.table.interceptor.DefaultInterceptor
export.interceptor.default=org.ecside.table.interceptor.DefaultInterceptor

table.state.default=org.ecside.table.state.DefaultState
table.state.notifyToDefault=org.ecside.table.state.NotifyToDefaultState
table.state.persist=org.ecside.table.state.PersistState
table.state.notifyToPersist=org.ecside.table.state.NotifyToPersistState
table.stateAttr=notifyState

table.useSessionFilterSortParam=useSessionFilterSort
table.view.html=org.ecside.view.DefaultHtmlView
column.headerCell.radiobox=org.ecside.table.cell.RadioBoxHeaderCell
column.headerCell.checkbox=org.ecside.table.cell.CheckBoxHeaderCell
column.headerCell.header=org.ecside.table.cell.HeaderCell
column.cell.display=org.ecside.table.cell.DisplayCell
column.cell.radiobox=org.ecside.table.cell.RadioBoxCell
column.cell.checkbox=org.ecside.table.cell.CheckBoxCell
column.cell.calcTotal=org.ecside.table.cell.RowCalcTotalCell
column.cell.calcAvg=org.ecside.table.cell.RowCalcAvgCell
column.cell.shadowRow=org.ecside.table.cell.ShadowRowCell

column.calc.total=org.ecside.table.calc.TotalCalc
column.calc.average=org.ecside.table.calc.AverageCalc
column.cell.currency=org.ecside.table.cell.NumberCell
column.cell.number=org.ecside.table.cell.NumberCell
column.cell.date=org.ecside.table.cell.DateCell
column.cell.rowCount=org.ecside.table.cell.RowCountCell
column.cell.tree=org.extremecomponents.tree.TreeCell
column.filterCell.droplist=org.ecside.table.cell.FilterDroplistCell
column.filterCell.filter=org.ecside.table.cell.FilterCell
column.headerCell.selectAll=org.ecside.table.cell.SelectAllHeaderCell

export.view.xls=org.ecside.view.XlsView
export.viewResolver.xls=org.ecside.view.XlsViewResolver
export.view.csv=org.ecside.view.CsvView
export.viewResolver.csv=org.ecside.view.CsvViewResolver
export.view.pdf=org.ecside.view.PdfView
export.viewResolver.pdf=org.ecside.view.PdfViewResolver
export.view.print=org.ecside.view.PrintView
export.viewResolver.print=org.ecside.view.PrintViewResolver
export.encoding=UTF
export.maxRowsExported=1000000


tool.navigation=org.ecside.table.tool.PageNavigationTool
tool.pagejump=org.ecside.table.tool.PageJumpTool
tool.pagesize=org.ecside.table.tool.PageSizeTool
tool.export=org.ecside.table.tool.ExportTool
tool.status=org.ecside.table.tool.StatusTool
tool.extend=org.ecside.table.tool.ExtendTool
tool.blank=org.ecside.table.tool.BlankTool
tool.refresh=org.ecside.table.tool.RefreshTool
tool.save=org.ecside.table.tool.SaveTool
tool.add=org.ecside.table.tool.AddTool
tool.del=org.ecside.table.tool.DelTool
tool.|=org.ecside.table.tool.SeparatorTool
tool.,=org.ecside.table.tool.NewLineTool

table.showExports=true
table.showPagination=true
table.showStatusBar=true
table.showTitle=false
table.showTooltips=true

#table.filterRowsCallback.default=org.ecside.table.callback.ProcessRowsCallback
table.filterRowsCallback.process=org.ecside.table.callback.ProcessRowsCallback
table.filterRowsCallback.limit=org.ecside.table.callback.LimitCallback
table.filterRowsCallback.common=org.ecside.table.callback.CommonLimitCallback
table.filterRowsCallback.default=org.ecside.table.callback.CommonLimitCallback


#table.sortRowsCallback.default=org.ecside.table.callback.ProcessRowsCallback
table.sortRowsCallback.process=org.ecside.table.callback.ProcessRowsCallback
table.sortRowsCallback.limit=org.ecside.table.callback.LimitCallback
table.sortRowsCallback.common=org.ecside.table.callback.CommonLimitCallback
table.sortRowsCallback.default=org.ecside.table.callback.CommonLimitCallback

table.retrieveRowsCallback.default=org.ecside.table.callback.ProcessRowsCallback
table.retrieveRowsCallback.process=org.ecside.table.callback.ProcessRowsCallback
table.retrieveRowsCallback.limit=org.ecside.table.callback.LimitCallback
table.retrieveRowsCallback.common=org.ecside.table.callback.CommonLimitCallback
table.retrieveRowsCallback.default=org.ecside.table.callback.CommonLimitCallback
exportPdf.font=SimSun,SimHei
exportPdf.userconfigLocation=fop_pdf_config.xml
exportPdf.fontLocation=/WEB-INF/lib

table.autoIncludeParameters=true
table.border=0
table.cellpadding=0
table.cellspacing=0
table.width=100%
table.method=post
table.theme=ecSide
table.styleClass=tableRegion
table.headerClass=tableHeader
table.headerSortClass=tableHeaderSort
table.locale=zh_CN
table.resizeColWidth=true
table.classic=true
table.filterable=true
table.height=100%
table.generateScript=true
table.useAjax=true
table.doPreload=false
table.sortable=false
table.editable=false
table.exportable=true
table.addTemplate=add_template
table.toolbarLocation=bottom
table.toolbarContent=status|refresh|export|extend|navigation|pagejump
table.minColWidth=80
table.pageSizeList=max:999999999,18,50,100,1000
table.nearPageNum=3
table.rowsDisplayed=18
table.alwaysShowExtend=bottom
table.bufferView=false
#å¯¼åºæä»¶æ ¼å¼
table.pdfExports=false
table.xlsExports=false
table.csvExports=true
table.retrieveRowsCallback=process
table.tableId=records
table.showPrint=false


row.highlightRow=true
row.highlightClass=highlight
row.selectlightRow=true
row.selectlightClass=selectlight


column.format.number=0.##
column.format.date=yyyy-MM-dd
column.editEvent=ondblclick
column.editTemplate=ecs_t_input


column.escapeAutoFormat=true
defaultCalcLayout=multiRowCalcResults







