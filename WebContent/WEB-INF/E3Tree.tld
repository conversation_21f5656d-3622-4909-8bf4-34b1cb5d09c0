<?xml version="1.0" encoding="UTF-8" ?>
<taglib xmlns="http://java.sun.com/xml/ns/j2ee" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://java.sun.com/xml/ns/j2ee web-jsptaglibrary_2_0.xsd" version="2.0">
<tlib-version>1.0</tlib-version>
<jsp-version>2.0</jsp-version>
<short-name>e3tree</short-name>
<uri>http://tree.dawnpro.com.cn</uri>
<info></info>
	<tag>
		<name>tree</name>
		<tagclass>net.jcreate.e3.tree.taglib.TreeTag</tagclass>
		<bodycontent>JSP</bodycontent>
		<info></info>
        <dynamic-attributes>true</dynamic-attributes>
		<attribute>
			<name>var</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>

		<attribute>
			<name>defaultSort</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>

		<attribute>
			<name>items</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>scope</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>comparator</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>visitor</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>sortProperty</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>reverse</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>

		<attribute>
			<name>builder</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
	</tag>

	<tag>
		<name>node</name>
		<tagclass>net.jcreate.e3.tree.taglib.NodeTag</tagclass>
		<bodycontent>JSP</bodycontent>
		<info></info>
        <dynamic-attributes>true</dynamic-attributes>
		<attribute>
			<name>id</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>parentId</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>name</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>

		<attribute>
			<name>subTreeURL</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>

		<attribute>
			<name>cls</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>

		<attribute>
			<name>target</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>icon</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>openIcon</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>action</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>

		<attribute>
			<name>tip</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>value</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>selected</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>dropable</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>dragable</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>disabled</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>


	</tag>

	<tag>
		<name>userAttribute</name>
		<tagclass>net.jcreate.e3.tree.taglib.UserAttributeTag</tagclass>
		<bodycontent>EMPTY</bodycontent>
		<info></info>
		<attribute>
			<name>name</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>value</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
	</tag>


</taglib>
