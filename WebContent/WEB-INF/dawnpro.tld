<?xml version="1.0" encoding="UTF-8"?>
<taglib xmlns="http://java.sun.com/xml/ns/j2ee"
	    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	    xsi:schemaLocation="http://java.sun.com/xml/ns/j2ee/web-jsptaglibrary_2_0.xsd"
	    version="2.0">		

	<tlib-version>1.0</tlib-version>
	<jsp-version>2.0</jsp-version>
	<short-name>DPTG</short-name>
	<uri>http://www.dawnpro.com.cn</uri>
	<display-name>DawnproTag</display-name>
	<description>
		<![CDATA[DawnproTag 是为简化界面开发所开发的标签库]]>
	</description>
	<tag>
		<name>Head</name>
		<tag-class>com.dawnpro.commons.tag.StyleTag</tag-class>
		<body-content>JSP</body-content>
		<display-name>TableTag</display-name>
		<description>
			<![CDATA[文件头加载页面.]]>
		</description>
		<attribute>
			<name>title</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[文件名.]]>
			</description>
		</attribute>
		<attribute>
			<name>treeselect</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[EasyUI下拉树.]]>
			</description>
		</attribute>
		<attribute>
			<name>css</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[需要单独加载的css文件.]]>
			</description>
		</attribute>
		<attribute>
			<name>js</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[需要单独加载的js文件.]]>
			</description>
		</attribute>
		<attribute>
			<name>dpui</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[是否需要的dpui文件.Y为需要,默认为Y]]>
			</description>
		</attribute>
		<attribute>
			<name>ecside</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[是否需要的ECSIDE文件.Y为需要]]>
			</description>
		</attribute>
		<attribute>
			<name>iscache</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[是否需要的缓存页面.Y为需要]]>
			</description>
		</attribute>
		<attribute>
			<name>ajax</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[是否需要的ajax页面.Y为需要]]>
			</description>
		</attribute>
		<attribute>
			<name>firewindow</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[是否需要的弹出窗口.Y为需要]]>
			</description>
		</attribute>
		<attribute>
			<name>validator</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[是否需要的校验.Y为需要]]>
			</description>
		</attribute>
		<attribute>
			<name>calendar</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[是否需要的日历控件.Y为需要]]>
			</description>
		</attribute>
		<attribute>
			<name>advanquery</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[高级查询]]>
			</description>
		</attribute>
		<attribute>
			<name>alert</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[是否需要的提醒js.Y为需要]]>
			</description>
		</attribute>
		<attribute>
			<name>workflow</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[是否需要的工作流JS文件.Y为需要]]>
			</description>
		</attribute>
	</tag>
	<tag>
		<name>QueryFieldset</name>
		<tag-class>com.dawnpro.commons.tag.QueryTag</tag-class>
		<body-content>JSP</body-content>
		<display-name>TableTag</display-name>
		<description>查询组件</description>
		<attribute>
			<name>tableName</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[.组件编码，在查询组件管理里面的组件id]]>
			</description>
		</attribute>
		<attribute>
			<name>cssstyle</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[样式，默认未c3；还有c1和c2（后面的数据表示每行显示的个数）]]>
			</description>
		</attribute>
		<attribute>
			<name>isLoad</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[是否第一次调用服务，实现查询]]>
			</description>

		</attribute>
		<attribute>
			<name>jspPage</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[需要指定跳转的jsp页面，默认为空，即为当前页面]]>
			</description>

		</attribute>
	</tag>
	<tag>
		<name>QueryFiled</name>
		<tag-class>com.dawnpro.commons.tag.QueryFiledTag</tag-class>
		<body-content>empty</body-content>
		<description>
			<![CDATA[查询标签扩展内容]]>
		</description>
		<attribute>
			<name>name</name>
			<required>true</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[字段名]]>
			</description>

		</attribute>
		<attribute>
			<name>value</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[查询的值]]>
			</description>
		</attribute>
		<attribute>
			<name>inputtype</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[文本框类型，1为隐藏域，其它为文本]]>
			</description>
		</attribute>
		<attribute>
			<name>label</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[文字说明]]>
			</description>
		</attribute>
		<attribute>
			<name>condition</name>
			<required>true</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[条件：1	等于			eq		2	大于			gt		3	大于等于		ge
		4	小于			lt
		5	小于等于		le
		6	不等于		ne
		7	模糊查询		lk
		8	多值查询		in/ni
		9	为空/不为空	iu/nu
		10	范围查询		bt]]>
			</description>
		</attribute>
		<attribute>
			<name>type</name>
			<required>true</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[类型：log  数字串参数
	str  字符串参数
	dat  日期参数]]>
			</description>
		</attribute>
		<attribute>
			<name>description</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[注释，方便程序阅读]]>
			</description>
		</attribute>
	</tag>
	<tag>
		<name>CallServer</name>
		<tag-class>com.dawnpro.commons.tag.CallTag</tag-class>
		<body-content>JSP</body-content>
		<display-name>TableTag</display-name>
		<description>服务请求组件</description>
		<attribute>
			<name>serverName</name>
			<required>true</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[服务名(spring的applictioncontext注册的服务名).]]>
			</description>

		</attribute>
		<attribute>
			<name>methodName</name>
			<required>true</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[方法名(method_Name).]]>
			</description>

		</attribute>
		<attribute>
			<name>paramType</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[参数类型(同type_Name:获取参数个数类型和名称格式:Str**;Str表示字符串类型,**表示字段名,多个参数用下划线分割,类型有:los(数字数组),sts(字符串数组),str(字符串),lon(整型),dat(日期),sql：为查询条件，：list为对象list).]]>
			</description>

		</attribute>
		<attribute>
			<name>domainName</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[对象名(domain对象或者entity对象)(domain_Name).]]>
			</description>

		</attribute>
		<attribute>
			<name>sqlName</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[查询的条件(sql_Name).如：sqlName="Strlktablename_Strlkcode" 前三位代表参数类型，第4、5位代表查询类型（lk=like），后面的代表参数名，多个条件以"_"分割。]]>
			</description>

		</attribute>
		<attribute>
			<name>isPage</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[是否分页(is_Page，是否分页显示，默认为不分页).]]>
			</description>

		</attribute>
		<attribute>
			<name>pageSize</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[每页行数(page_Size：设置每页显示记录行数，默认为20行).]]>
			</description>

		</attribute>
		<attribute>
			<name>returnObject</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[返回参数类型可选值（String，Records）(默认为String，forward_Type).]]>
			</description>

		</attribute>
		<attribute>
			<name>forwardURL</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[返回路径(默认为当前路径，forward_URL).]]>
			</description>

		</attribute>
	</tag>
	<tag>
		<name>CallSql</name>
		<tag-class>com.dawnpro.commons.tag.CallSqlTag</tag-class>
		<body-content>empty</body-content>
		<description>
			<![CDATA[服务调用标签扩展内容sql条件]]>
		</description>
		<attribute>
			<name>name</name>
			<required>true</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[字段名]]>
			</description>
		</attribute>
		<attribute>
			<name>value</name>
			<required>true</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[参数值]]>
			</description>
		</attribute>
		<attribute>
			<name>value2</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[参数值2，用于范围查询]]>
			</description>
		</attribute>
		<attribute>
			<name>condition</name>
			<required>true</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[条件1	等于			eq		2	大于			gt		3	大于等于		ge
		4	小于			lt
		5	小于等于		le
		6	不等于		ne
		7	模糊查询		lk
		8	多值查询		in/ni
		9	为空/不为空	iu/nu
		10	范围查询		bt]]>
			</description>
		</attribute>
		<attribute>
			<name>type</name>
			<required>true</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[类型：log  数字串参数
	str  字符串参数
	dat  日期参数]]>
			</description>
		</attribute>
		<attribute>
			<name>description</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[注释，方便程序阅读]]>
			</description>
		</attribute>
	</tag>
	<tag>
		<name>CallParam</name>
		<tag-class>com.dawnpro.commons.tag.CallParamTag</tag-class>
		<body-content>empty</body-content>
		<description>
			<![CDATA[服务调用标签扩展内容sql条件]]>
		</description>
		<attribute>
			<name>name</name>
			<required>true</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[字段名]]>
			</description>
		</attribute>
		<attribute>
			<name>value</name>
			<required>true</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[参数值]]>
			</description>
		</attribute>
		<attribute>
			<name>type</name>
			<required>true</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[类型：log  数字串参数
	str  字符串参数
	dat  日期参数]]>
			</description>
		</attribute>
		<attribute>
			<name>description</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[注释，方便程序阅读]]>
			</description>
		</attribute>
	</tag>
	<tag>
		<name>select</name>
		<tag-class>com.dawnpro.commons.tag.SelectTag</tag-class>
		<body-content>JSP</body-content>
		<description>
			<![CDATA[下拉选择标签]]>
		</description>
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[select标签的id属性]]>
			</description>

		</attribute>
		<attribute>
			<name>name</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[select的标签name属性]]>
			</description>

		</attribute>
		<attribute>

			<name>cssclass</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[select 标签的class属性]]>
			</description>

		</attribute>
		<attribute>
			<name>dataType</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[select 标签的校验类型属性]]>
			</description>
		</attribute>
		<attribute>
			<name>msg</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[select 标签的msg属性]]>
			</description>

		</attribute>
		<attribute>
			<name>type</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[显示类型，默认为select，radio，【checkbox】不推荐]]>
			</description>

		</attribute>
		<attribute>
			<name>tableName</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[查询表名,如果要查询单表生成下拉框，则可以使用该属性，使用时必须同时指定keyColums,valueColums<p>优先级: 如果typename为空，则计算datasource的值,然后如果datasource为空，使用tableName;</p><p>优先级  typename > datasource > tablename</p>]]>
			</description>
		</attribute>
		<attribute>
			<name>typename</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[数据字典类型<p>优先级: 如果typename为空，则计算datasource的值,然后如果datasource为空，使用tableName;</p><p>优先级  typename > datasource > tablename</p>]]>
			</description>

		</attribute>
		<attribute>
			<name>datasource</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[数据源，可以指定数据源，支持ognl表达式,表达式计算值必须是map<p>优先级: 如果typename为空，则计算datasource的值,然后如果datasource为空，使用tableName;</p><p>优先级  typename > datasource > tablename</p>]]>
			</description>

		</attribute>
		<attribute>
			<name>valueColums</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[显示字段]]>
			</description>

		</attribute>
		<attribute>
			<name>keyColums</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[主键字段]]>
			</description>

		</attribute>
		<attribute>
			<name>value</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[输入值，如果为空则显示默认值]]>
			</description>
		</attribute>
		<attribute>
			<name>defaultValue</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[默认值]]>
			</description>
		</attribute>
		<attribute>
			<name>description</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[注释，方便程序阅读]]>
			</description>

		</attribute>
		<attribute>
			<name>disabled</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[是否禁用,支持OGNL表达式]]>
			</description>

		</attribute>
		<attribute>
			<name>eventName</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[JS事件名称,不支持OGNL表达式]]>
			</description>
		</attribute>
		<attribute>
			<name>eventProperties</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[JS事件,不支持OGNL表达式]]>
			</description>
		</attribute>
		<attribute>
			<name>extend</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[加在select标签上的属性,支持OGNL表达式]]>
			</description>
		</attribute>

		<attribute>
			<name>where</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[查询数据库的where 条件 直接添加在where 后面]]>
			</description>
		</attribute>
		
		<attribute>
			<name>orderType</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[排序]]>
			</description>
		</attribute>
	</tag>
	<tag>
		<name>option</name>
		<tag-class>com.dawnpro.commons.tag.OptionTag</tag-class>
		<body-content>empty</body-content>
		<description>
			<![CDATA[下拉选择子选项]]>
		</description>
		<attribute>
			<name>value</name>
			<required>true</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[对应option的value]]>
			</description>

		</attribute>
		<attribute>
			<name>content</name>
			<required>true</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[对应option的内容]]>
			</description>

		</attribute>
		<attribute>
			<name>defaultSelect</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[是否选中,Y为选中]]>
			</description>

		</attribute>
		<attribute>
			<name>description</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[注释，方便程序阅读]]>
			</description>

		</attribute>
	</tag>
	<tag>
		<name>out</name>
		<tag-class>com.dawnpro.commons.tag.OutTag</tag-class>
		<body-content>empty</body-content>
		<description>
			<![CDATA[常用表达式输出]]>
		</description>
		<attribute>
			<name>value</name>
			<required>true</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[<p>需要输出的表达式,其语法遵循OGNL表达式</p><ul>
	<li>#attr,#param,#params,#session分别取request.attribute,request.parameter,request.parameter,session.attrbute的值
		<ul>
			<li>&nbsp;#attr.myMap.a&nbsp;等于 ((Map)request.getAttribute("myMap")).get("a")</li>
			<li>&nbsp;#params.username[0]&nbsp;等于request.getParameterValues("username")[0];</li>
			<li>&nbsp;#param.username&nbsp;等于request.getParameter("username");</li>
			<li>&nbsp;#session.sessionMap.role&nbsp;等于((Map)session.getAttribute("sessionMap")).get("role")</li>
			<li>&nbsp;#page.record.role&nbsp;等于((Map)pageContext.getAttribute("record")).get("role")</li>
		</ul>
	</li>
	<li>&nbsp;&eplanstatus['10']&nbsp;等于((Map)CommonDictionary.getDictionary("eplanstatus")).get("10")</li>
	<li>&nbsp;*keyword&nbsp;等于SysParam.get('keyword')</li>
	<li>&nbsp;@common.ok&nbsp;等于I18nProvider.getProperties().get(session("lang").get("common.ok"))</li>
</ul>]]>
			</description>
		</attribute>
		<attribute>
			<name>other</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
			</description>
		</attribute>
		<attribute>
			<name>format</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[日期格式,默认是yyyy-MM-dd(只支持日期类型)]]>
			</description>
		</attribute>
		<attribute>
			<name>descr</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
		</attribute>
	</tag>
	<tag>
		<name>flash</name>
		<tag-class>com.dawnpro.commons.tag.FlashTag</tag-class>
		<body-content>empty</body-content>
		<description>
			<![CDATA[将错误/警告输出到页面上 ]]>
		</description>
		<attribute>
			<name>result</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[获得ServiceResult的ognl表达式,默认是#attr.result]]>
			</description>
		</attribute>
		<attribute>
			<name>level</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[错误/警告的区分等级警告/错误区分等级 >=level 视为警告< level视为错误]]>
			</description>
		</attribute>
	</tag>
	<tag>
		<name>if</name>
		<tag-class>com.dawnpro.commons.tag.IfTag</tag-class>
		<description>
			<![CDATA[条件判断]]>
		</description>
		<attribute>
			<name>test</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[需要测试的OGNL表达式]]>
			</description>
		</attribute>
	</tag>
<tag>
		<name>table</name>
		<tag-class>org.ecside.tag.TableTag</tag-class>
		<body-content>JSP</body-content>
		<display-name>TableTag</display-name>
		<description>
			<![CDATA[The container which holds all the main table information. Will also hold global information if needed. The table tag is copied into the Table and encapsulated in the Model.]]>
		</description>
		<attribute>
			<name>generateScript</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[Specify the location of pagination bar . Acceptable values are up or down.]]>
			</description>
		</attribute>

		<attribute>
			<name>useAjax</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[Specify the location of pagination bar . Acceptable values are up or down.]]>
			</description>
		</attribute>
		<attribute>
			<name>doPreload</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[Specify the location of pagination bar . Acceptable values are up or down.]]>
			</description>
		</attribute>
		<attribute>
			<name>classic</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[Specify the location of pagination bar . Acceptable values are up or down.]]>
			</description>
		</attribute>
		<attribute>
			<name>oddRowBgColor</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[Specify the location of pagination bar . Acceptable values are up or down.]]>
			</description>
		</attribute>
		<attribute>
			<name>evenRowBgColor</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[Specify the location of pagination bar . Acceptable values are up or down.]]>
			</description>
		</attribute>
		<attribute>
			<name>scrollList</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[Specify the location of pagination bar . Acceptable values are up or down.]]>
			</description>
		</attribute>
		<attribute>
			<name>listWidth</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[Specify the location of pagination bar . Acceptable values are up or down.]]>
			</description>
		</attribute>
		<attribute>
			<name>listHeight</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[Specify the location of pagination bar . Acceptable values are up or down.]]>
			</description>
		</attribute>
		<attribute>
			<name>height</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[Specify the location of pagination bar . Acceptable values are up or down.]]>
			</description>
		</attribute>
		<attribute>
			<name>includeParameters</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[Specify the location of pagination bar . Acceptable values are up or down.]]>
			</description>
		</attribute>
		<attribute>
			<name>excludeParameters</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[Specify the location of pagination bar . Acceptable values are up or down.]]>
			</description>
		</attribute>
		<attribute>
			<name>paginationLocation</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[Specify the location of pagination bar . Acceptable values are up or down.]]>
			</description>
		</attribute>
		<attribute>
			<name>toolbarContent</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[Specify whether or not to show the rows displayed bar . Acceptable values are "true" or "false".]]>
			</description>
		</attribute>
		<attribute>
			<name>excludeTool</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[Specify whether or not to show the rows displayed bar . Acceptable values are "true" or "false".]]>
			</description>
		</attribute>

		<attribute>
			<name>alwaysShowExtend</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[Specify whether or not to show the rows displayed bar . Acceptable values are "true" or "false".]]>
			</description>
		</attribute>
		<attribute>
			<name>toolbarLocation</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[Specify whether or not to show the rows displayed bar . Acceptable values are "true" or "false".]]>
			</description>
		</attribute>
		<attribute>
			<name>pageSizeList</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[Specify whether or not to show the rows displayed bar . Acceptable values are "true" or "false".]]>
			</description>
		</attribute>
		<attribute>
			<name>nearPageNum</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[Specify whether or not to show the rows displayed bar . Acceptable values are "true" or "false".]]>
			</description>
		</attribute>
		<attribute>
			<name>maxRowsExported</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[Specify whether or not to show the rows displayed bar . Acceptable values are "true" or "false".]]>
			</description>
		</attribute>
		<attribute>
			<name>resizeColWidth</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[Specify whether or not to show the rows displayed bar . Acceptable values are "true" or "false".]]>
			</description>
		</attribute>
		<attribute>
			<name>minColWidth</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[Specify whether or not to show the rows displayed bar . Acceptable values are "true" or "false".]]>
			</description>
		</attribute>
		<attribute>
			<name>minHeight</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[Specify whether or not to show the rows displayed bar . Acceptable values are "true" or "false".]]>
			</description>
		</attribute>
		<attribute>
			<name>tagAttributes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>xlsFileName</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>showPrint</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>showHeader</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>pdfFileName</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>csvFileName</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>action</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The URI that will be called when the filter, sort and pagination is used.]]>
			</description>
		</attribute>
		<attribute>
			<name>insertAction</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The URI that will be called when the filter, sort and pagination is used.]]>
			</description>
		</attribute>
		<attribute>
			<name>updateAction</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The URI that will be called when the filter, sort and pagination is used.]]>
			</description>
		</attribute>
		<attribute>
			<name>deleteAction</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The URI that will be called when the filter, sort and pagination is used.]]>
			</description>
		</attribute>
		<attribute>
			<name>shadowRowAction</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The URI that will be called when the filter, sort and pagination is used.]]>
			</description>
		</attribute>
		<attribute>
			<name>autoIncludeParameters</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[Specify whether or not to automatically include the parameters, as hidden inputs, passed into the JSP.]]>
			</description>
		</attribute>
		<attribute>
			<name>border</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The table border attribute. The default is 0.]]>
			</description>
		</attribute>
		<attribute>
			<name>bufferView</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[Whether of not to buffer the view. Boolean value with the default being false.]]>
			</description>
		</attribute>
		<attribute>
			<name>cellpadding</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The table cellpadding attribute. The default is 0.]]>
			</description>
		</attribute>
		<attribute>
			<name>cellspacing</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The table cellspacing attribute. The default is 0.]]>
			</description>
		</attribute>
		<attribute>
			<name>filterable</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[Specify whether or not the table is filterable. Boolean value with the default being true.]]>
			</description>
		</attribute>
		<attribute>
			<name>filterRowsCallback</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[A fully qualified class name to a custom FilterRowsCallback implementation. Could also be a named type in the preferences. Used to filter the Collection of Beans or Collection of Maps.]]>
			</description>
		</attribute>
		<attribute>
			<name>editable</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[Specify whether or not the table is filterable. Boolean value with the default being true.]]>
			</description>
		</attribute>
		<attribute>
			<name>form</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The reference to a surrounding form element.]]>
			</description>
		</attribute>
		<attribute>
			<name>interceptor</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[A fully qualified class name to a custom InterceptTable implementation. Could also be a named type in the preferences. Used to add table attributes.]]>
			</description>
		</attribute>
		<attribute>
			<name>items</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[Reference the collection that will be retrieved.]]>
			</description>
		</attribute>
		<attribute>
			<name>locale</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The locale for this table. For example fr_FR is used for the French translation.]]>
			</description>
		</attribute>
		<attribute>
			<name>method</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[Used to invoke the table action using a POST or GET.]]>
			</description>
		</attribute>
		<attribute>
			<name>onInvokeAction</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The javascript that will be invoked when a table action enabled.]]>
			</description>
		</attribute>
		<attribute>
			<name>retrieveRowsCallback</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[A fully qualified class name to a custom RetrieveRowsCallback implementation. Could also be a named type in the preferences. Used to retrieve the Collection of Beans or Collection of Maps.]]>
			</description>
		</attribute>
		<attribute>
			<name>rowsDisplayed</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The number of rows to display in the table.]]>
			</description>
		</attribute>
		<attribute>
			<name>scope</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The scope (page, request, session, or application) to find the Collection of beans or Collection of Maps defined by the collection attribute.]]>
			</description>
		</attribute>
		<attribute>
			<name>showPagination</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[Specify whether or not the table should use pagination. Boolean value with the default being true.]]>
			</description>
		</attribute>
		<attribute>
			<name>showExports</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[Specify whether or not the table should use the exports. Boolean value with the default being true.]]>
			</description>
		</attribute>
		<attribute>
			<name>showTitle</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[Specify whether or not to show the title. Boolean value with the default being true.]]>
			</description>
		</attribute>
		<attribute>
			<name>showTooltips</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[Specify whether or not to show the tooltips. Boolean value with the default being true.]]>
			</description>
		</attribute>
		<attribute>
			<name>sortRowsCallback</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[A fully qualified class name to a custom SortRowsCallback implementation. Could also be a named type in the preferences. Used to sort the Collection of Beans or Collection of Maps.]]>
			</description>
		</attribute>
		<attribute>
			<name>sortable</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[Specify whether or not the table is sortable. Boolean value with the default being true.]]>
			</description>
		</attribute>
		<attribute>
			<name>state</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The table state to use when returning to a table. Acceptable values are default, notifyToDefault, persist, notifyToPersist.]]>
			</description>
		</attribute>
		<attribute>
			<name>stateAttr</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The table attribute used to invoke the state change of the table.]]>
			</description>
		</attribute>
		<attribute>
			<name>style</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The css inline style sheet.]]>
			</description>
		</attribute>
		<attribute>
			<name>styleClass</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The css class style sheet.]]>
			</description>
		</attribute>
		<attribute>
			<name>tableId</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The unique identifier for the table.]]>
			</description>
		</attribute>
		<attribute>
			<name>theme</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The theme to style the table. The default is eXtremeTable.]]>
			</description>
		</attribute>
		<attribute>
			<name>title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The title of the table. The title will display above the table.]]>
			</description>
		</attribute>
		<attribute>
			<name>var</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The name of the variable to hold the current row bean.]]>
			</description>
		</attribute>
		<attribute>
			<name>view</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[Generates the output. The default is the HtmlView to generate the HTML. Also used by the exports to generate XLS-FO, POI, and CSV.]]>
			</description>
		</attribute>
		<attribute>
			<name>width</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[Width of the table.]]>
			</description>
		</attribute>
	</tag>
	<tag>
		<name>row</name>
		<tag-class>org.ecside.tag.RowTag</tag-class>
		<body-content>JSP</body-content>
		<display-name>RowTag</display-name>
		<description>
			<![CDATA[The container which holds all the row specific information.]]>
		</description>
		<attribute>
			<name>rowId</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The css class style sheet when highlighting rows.]]>
			</description>
		</attribute>
		<attribute>
			<name>recordKey</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The css class style sheet when highlighting rows.]]>
			</description>
		</attribute>
		<attribute>
			<name>highlightClass</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The css class style sheet when highlighting rows.]]>
			</description>
		</attribute>
		<attribute>
			<name>highlightRow</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[Used to turn the highlight feature on and off. Acceptable values are true or false. The default is false.]]>
			</description>
		</attribute>
		<attribute>
			<name>selectlightClass</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The css class style sheet when highlighting rows.]]>
			</description>
		</attribute>
		<attribute>
			<name>selectlightRow</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[Used to turn the highlight feature on and off. Acceptable values are true or false. The default is false.]]>
			</description>
		</attribute>
		<attribute>
			<name>interceptor</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[A fully qualified class name to a custom InterceptRow implementation. Could also be a named type in the preferences. Used to add or modify row attributes.]]>
			</description>
		</attribute>
		<attribute>
			<name>tagAttributes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The javascript ondblclick action]]>
			</description>
		</attribute>
		<attribute>
			<name>ondblclick</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The javascript ondblclick action]]>
			</description>
		</attribute>
		<attribute>
			<name>onclick</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The javascript onclick action]]>
			</description>
		</attribute>
		<attribute>
			<name>onmouseout</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The javascript onmouseout action]]>
			</description>
		</attribute>
		<attribute>
			<name>onmouseover</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The javascript onmouseover action]]>
			</description>
		</attribute>
		<attribute>
			<name>style</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The css inline style sheet.]]>
			</description>
		</attribute>
		<attribute>
			<name>styleClass</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The css class style sheet.]]>
			</description>
		</attribute>
	</tag>
	<tag>
		<name>extend</name>
		<tag-class>org.ecside.tag.ExtendTag</tag-class>
		<body-content>JSP</body-content>
		<display-name>ExtendTag</display-name>
		<description>
			<![CDATA[....]]>
		</description>
		<attribute>
			<name>location</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[...]]>
			</description>
		</attribute>
	</tag>
	<tag>
		<name>extendrow</name>
		<tag-class>org.ecside.tag.ExtendRowTag</tag-class>
		<body-content>JSP</body-content>
		<display-name>ExtendRowTag</display-name>
		<description>
			<![CDATA[....]]>
		</description>
		<attribute>
			<name>location</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[...]]>
			</description>
		</attribute>
	</tag>
	<tag>
		<name>column</name>
		<tag-class>org.ecside.tag.ColumnTag</tag-class>
		<body-content>JSP</body-content>
		<display-name>ColumnTag</display-name>
		<description>
			<![CDATA[The container which holds all the column specific information. A copy of each column will be fed to the Model.]]>
		</description>
		<attribute>
			<name>columnId</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The css class style sheet when highlighting rows.]]>
			</description>
		</attribute>
		<attribute>
			<name>tipTitle</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The css class style sheet when highlighting rows.]]>
			</description>
		</attribute>
		<attribute>
			<name>headerSpan</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[Custom header td span.]]>
			</description>
		</attribute>
		<attribute>
			<name>headerSpan</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[Custom header td span.]]>
			</description>
		</attribute>
		<attribute>
			<name>group</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[Custom header td span.]]>
			</description>
		</attribute>

		<attribute>
			<name>nowrap</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[Custom header td span.]]>
			</description>
		</attribute>
		<attribute>
			<name>resizeColWidth</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[Custom header td span.]]>
			</description>
		</attribute>
		<attribute>
			<name>minWidth</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[Custom header td span.]]>
			</description>
		</attribute>
		<attribute>
			<name>cellValue</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[Custom header td span.]]>
			</description>
		</attribute>
		<attribute>
			<name>editTemplate</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[Custom header td span.]]>
			</description>
		</attribute>
		<attribute>
			<name>editEvent</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[Custom header td span.]]>
			</description>
		</attribute>
		<attribute>
			<name>editable</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[Custom header td span.]]>
			</description>
		</attribute>
		<attribute>
			<name>cellName</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[Custom header td span.]]>
			</description>
		</attribute>
		<attribute>
			<name>mappingItem</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[Custom header td span.]]>
			</description>
		</attribute>
		<attribute>
			<name>mappingDefaultKey</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[Custom header td span.]]>
			</description>
		</attribute>
		<attribute>
			<name>mappingDefaultValue</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[Custom header td span.]]>
			</description>
		</attribute>
		<attribute>
			<name>ellipsis</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[Custom header td span.]]>
			</description>
		</attribute>
		<attribute>
			<name>calcSpan</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[Custom header td span.]]>
			</description>
		</attribute>
		<attribute>
			<name>calcspan</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[Custom header td span.]]>
			</description>
		</attribute>
		<attribute>
			<name>onclick</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The javascript onclick action]]>
			</description>
		</attribute>
		<attribute>
			<name>ondblclick</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The javascript ondblclick action]]>
			</description>
		</attribute>
		<attribute>
			<name>onmouseout</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The javascript onmouseout action]]>
			</description>
		</attribute>
		<attribute>
			<name>onmouseover</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The javascript onmouseover action]]>
			</description>
		</attribute>
		<attribute>
			<name>alias</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[Used to uniquely identify the column when the same property is used for more than one column.]]>
			</description>
		</attribute>
		<attribute>
			<name>calc</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[A fully qualified class name to a custom Calc implementation. Could also be a named type in the preferences. Used to do math on a column.]]>
			</description>
		</attribute>
		<attribute>
			<name>calcTitle</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The title of the calc.]]>
			</description>
		</attribute>
		<attribute>
			<name>cell</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[Display for the column. The valid values are display, currency, rowCount, and date. The default value is display. The cell can also be a fully qualified class name to a custom Cell. Be sure to implement the Cell interface or extend AbstractCell if making a custom cell.]]>
			</description>
		</attribute>
		<attribute>
			<name>escapeAutoFormat</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[Specify whether auto format of value will be skipped. False by default, and is only effective if autoformatting is implement in the view.]]>
			</description>
		</attribute>
		<attribute>
			<name>filterable</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[Specify whether or not the column should be filterable. Acceptable values are true or false. The default is to use the value for the table filterable attribute.]]>
			</description>
		</attribute>
		<attribute>
			<name>filterCell</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[Displays the filter column. The valid values are filter and droplist. The default is filter. The cell can also be a fully qualified class name to a custom cell.]]>
			</description>
		</attribute>
		<attribute>
			<name>filterClass</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The css class style sheet used to define what the table filter column looks like.]]>
			</description>
		</attribute>
		<attribute>
			<name>filterOptions</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The object that contains the collection of elements that implement the Option interface.]]>
			</description>
		</attribute>
		<attribute>
			<name>filterStyle</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The css class style sheet to use for the filter column.]]>
			</description>
		</attribute>
		<attribute>
			<name>format</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The format to use for the cell. For instance if used with a date cell then the format can be MM/dd/yyyy.]]>
			</description>
		</attribute>
		<attribute>
			<name>headerCell</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[Display for the header column. The default is header. The cell can also be a fully qualified class name to a custom cell.]]>
			</description>
		</attribute>
		<attribute>
			<name>headerClass</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The css class style sheet used to define what the table header column looks like.]]>
			</description>
		</attribute>
		<attribute>
			<name>headerStyle</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The css class style sheet to use for the header column.]]>
			</description>
		</attribute>
		<attribute>
			<name>headerStyleClass</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The css class style sheet to use for the header column.]]>
			</description>
		</attribute>

		<attribute>
			<name>interceptor</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[A fully qualified class name to a custom InterceptColumn implementation. Could also be a named type in the preferences. Used to add or modify column attributes.]]>
			</description>
		</attribute>
		<attribute>
			<name>parse</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[Used if the format needs to be interpreted. For instance, a date needs to be parsed in the specific format, such as MM-dd-yyyy.]]>
			</description>
		</attribute>
		<attribute>
			<name>property</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The bean attribute to use for the column.]]>
			</description>
		</attribute>
		<attribute>
			<name>sortable</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[Specify whether or not the column should be sortable. The acceptable values are true or false. The default is to use the value for the table sortable attribute.]]>
			</description>
		</attribute>
		<attribute>
			<name>style</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The css inline style sheet.]]>
			</description>
		</attribute>
		<attribute>
			<name>styleClass</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The css class style sheet.]]>
			</description>
		</attribute>
		<attribute>
			<name>title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The display for the table column header. If the title is not specified then it will default to the name of the property, changing the camelcase syntax to separate words.]]>
			</description>
		</attribute>
		<attribute>
			<name>value</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The value for the column. If the value attribute is not specifed then it will be retrieved automatically using the property attribute. The value can also be defined within the column body.]]>
			</description>
		</attribute>
		<attribute>
			<name>viewsAllowed</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The comma separated list of views that this column will be used in.]]>
			</description>
		</attribute>
		<attribute>
			<name>viewsDenied</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The comma separated list of views that this column will not be used in.]]>
			</description>
		</attribute>
		<attribute>
			<name>width</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[Specify the column width.]]>
			</description>
		</attribute>
		<attribute>
			<name>tagAttributes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
	</tag>
	<tag>
		<name>exportXls</name>
		<tag-class>org.ecside.tag.ExportXlsTag</tag-class>
		<body-content>JSP</body-content>
		<display-name>ExportXlsTag</display-name>
		<description>
			<![CDATA[Export data for a xls view.]]>
		</description>
		<attribute>
			<name>encoding</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The encoding that set is support UTF-8.]]>
			</description>
		</attribute>
		<attribute>
			<name>fileName</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The name of the export file.]]>
			</description>
		</attribute>
		<attribute>
			<name>imageName</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The image name.]]>
			</description>
		</attribute>
		<attribute>
			<name>interceptor</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[A fully qualified class name to a custom InterceptExport implementation. Could also be a named type in the preferences. Used to add or modify export attributes.]]>
			</description>
		</attribute>
		<attribute>
			<name>view</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[A fully qualified class name to a custom View implementation. Could also be a named type in the preferences. Default types are pdf, xls, or csv.]]>
			</description>
		</attribute>
		<attribute>
			<name>viewResolver</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[A fully qualified class name to a custom ViewResolver implementation. Could also be a named type in the preferences. Default types are pdf, xls, or csv.]]>
			</description>
		</attribute>
		<attribute>
			<name>text</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The text for the export view.]]>
			</description>
		</attribute>
		<attribute>
			<name>tooltip</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The tooltip that shows up when you mouseover the export image.]]>
			</description>
		</attribute>
	</tag>
	<tag>
		<name>exportCsv</name>
		<tag-class>org.ecside.tag.ExportCsvTag</tag-class>
		<body-content>JSP</body-content>
		<display-name>ExportCsvTag</display-name>
		<description>
			<![CDATA[Export data for a csv view.]]>
		</description>
		<attribute>
			<name>delimiter</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[What to use as the file delimiter. The default is a comma.]]>
			</description>
		</attribute>
		<attribute>
			<name>encoding</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The encoding that set is support UTF-8.]]>
			</description>
		</attribute>
		<attribute>
			<name>fileName</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The name of the export file.]]>
			</description>
		</attribute>
		<attribute>
			<name>imageName</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The image name.]]>
			</description>
		</attribute>
		<attribute>
			<name>interceptor</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[A fully qualified class name to a custom InterceptExport implementation. Could also be a named type in the preferences. Used to add or modify export attributes.]]>
			</description>
		</attribute>
		<attribute>
			<name>view</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[A fully qualified class name to a custom View implementation. Could also be a named type in the preferences. Default types are pdf, xls, or csv.]]>
			</description>
		</attribute>
		<attribute>
			<name>viewResolver</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[A fully qualified class name to a custom ViewResolver implementation. Could also be a named type in the preferences. Default types are pdf, xls, or csv.]]>
			</description>
		</attribute>
		<attribute>
			<name>text</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The text for the export view.]]>
			</description>
		</attribute>
		<attribute>
			<name>tooltip</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The tooltip that shows up when you mouseover the export image.]]>
			</description>
		</attribute>
	</tag>
	<tag>
		<name>exportPdf</name>
		<tag-class>org.ecside.tag.ExportPdfTag</tag-class>
		<body-content>JSP</body-content>
		<display-name>ExportPdfTag</display-name>
		<description>
			<![CDATA[Export data for a pdf view.]]>
		</description>
		<attribute>
			<name>headerBackgroundColor</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The background color on the header column.]]>
			</description>
		</attribute>
		<attribute>
			<name>headerColor</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The font color for the header column.]]>
			</description>
		</attribute>
		<attribute>
			<name>headerTitle</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The title displayed at the top of the page.]]>
			</description>
		</attribute>
		<attribute>
			<name>encoding</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The encoding that set is support UTF-8.]]>
			</description>
		</attribute>
		<attribute>
			<name>fileName</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The name of the export file.]]>
			</description>
		</attribute>
		<attribute>
			<name>imageName</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The image name.]]>
			</description>
		</attribute>
		<attribute>
			<name>interceptor</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[A fully qualified class name to a custom InterceptExport implementation. Could also be a named type in the preferences. Used to add or modify export attributes.]]>
			</description>
		</attribute>
		<attribute>
			<name>view</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[A fully qualified class name to a custom View implementation. Could also be a named type in the preferences. Default types are pdf, xls, or csv.]]>
			</description>
		</attribute>
		<attribute>
			<name>viewResolver</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[A fully qualified class name to a custom ViewResolver implementation. Could also be a named type in the preferences. Default types are pdf, xls, or csv.]]>
			</description>
		</attribute>
		<attribute>
			<name>text</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The text for the export view.]]>
			</description>
		</attribute>
		<attribute>
			<name>tooltip</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The tooltip that shows up when you mouseover the export image.]]>
			</description>
		</attribute>
	</tag>
	<tag>
		<name>columns</name>
		<tag-class>org.ecside.tag.ColumnsTag</tag-class>
		<body-content>JSP</body-content>
		<display-name>ColumnsTag</display-name>
		<description>
			<![CDATA[Auto generate the columns.]]>
		</description>
		<attribute>
			<name>autoGenerateColumns</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[A fully qualified class name to a custom AutoGenerateColumns implementation. Could also be a named type in the preferences. Used to generate columns on the fly.]]>
			</description>
		</attribute>
		<attribute>
			<name>titles</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[A fully qualified class name to a custom AutoGenerateColumns implementation. Could also be a named type in the preferences. Used to generate columns on the fly.]]>
			</description>
		</attribute>
		<attribute>
			<name>widths</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[A fully qualified class name to a custom AutoGenerateColumns implementation. Could also be a named type in the preferences. Used to generate columns on the fly.]]>
			</description>
		</attribute>
		<attribute>
			<name>cellValues</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[A fully qualified class name to a custom AutoGenerateColumns implementation. Could also be a named type in the preferences. Used to generate columns on the fly.]]>
			</description>
		</attribute>
		<attribute>
			<name>cellNames</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[A fully qualified class name to a custom AutoGenerateColumns implementation. Could also be a named type in the preferences. Used to generate columns on the fly.]]>
			</description>
		</attribute>
		<attribute>
			<name>editEvents</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[A fully qualified class name to a custom AutoGenerateColumns implementation. Could also be a named type in the preferences. Used to generate columns on the fly.]]>
			</description>
		</attribute>
		<attribute>
			<name>editTemplates</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[A fully qualified class name to a custom AutoGenerateColumns implementation. Could also be a named type in the preferences. Used to generate columns on the fly.]]>
			</description>
		</attribute>
		<attribute>
			<name>editables</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[A fully qualified class name to a custom AutoGenerateColumns implementation. Could also be a named type in the preferences. Used to generate columns on the fly.]]>
			</description>
		</attribute>

	</tag>
	<tag>
		<name>export</name>
		<tag-class>org.ecside.tag.ExportTag</tag-class>
		<body-content>JSP</body-content>
		<display-name>ExportTag</display-name>
		<description>
			<![CDATA[Export data to a given view. For example pdf or xls.]]>
		</description>
		<attribute>
			<name>encoding</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The encoding that set is support UTF-8.]]>
			</description>
		</attribute>
		<attribute>
			<name>fileName</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The name of the export file.]]>
			</description>
		</attribute>
		<attribute>
			<name>imageName</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The image name.]]>
			</description>
		</attribute>
		<attribute>
			<name>interceptor</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[A fully qualified class name to a custom InterceptExport implementation. Could also be a named type in the preferences. Used to add or modify export attributes.]]>
			</description>
		</attribute>
		<attribute>
			<name>view</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[A fully qualified class name to a custom View implementation. Could also be a named type in the preferences. Default types are pdf, xls, or csv.]]>
			</description>
		</attribute>
		<attribute>
			<name>viewResolver</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[A fully qualified class name to a custom ViewResolver implementation. Could also be a named type in the preferences. Default types are pdf, xls, or csv.]]>
			</description>
		</attribute>
		<attribute>
			<name>text</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The text for the export view.]]>
			</description>
		</attribute>
		<attribute>
			<name>tooltip</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[The tooltip that shows up when you mouseover the export image.]]>
			</description>
		</attribute>
	</tag>
	<!-- form tag -->
	<tag>
		<name>ecform</name>
		<tag-class>org.ecside.tag.form.ECSideFormTag</tag-class>
		<body-content>JSP</body-content>
		<description>form tag</description>
		<attribute>
			<name>beans</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>scopes</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
	</tag>
	<tag>
		<name>set</name>
		<tag-class>com.dawnpro.commons.tag.SetTag</tag-class>
		<body-content>JSP</body-content>
		<description><![CDATA[把变量放到SESSION或REQUEST.]]></description>
		<attribute>
			<name>var</name>
			<required>true</required>
			<rtexprvalue>false</rtexprvalue>
			<description><![CDATA[变量名]]></description>
		</attribute>
		<attribute>
			<name>value</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<description><![CDATA[值，支持OGNL]]></description>
		</attribute>
		<attribute>
			<name>scope</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
			<description><![CDATA[作用域session或request，默认request]]></description>
		</attribute>
	</tag>
	<tag>
		<name>columns</name>
		<tag-class>org.ecside.tag.ColumnsTag</tag-class>
		<body-content>JSP</body-content>
		<display-name>ColumnsTag</display-name>
		<description>
			<![CDATA[Auto generate the columns.]]>
		</description>
		<attribute>
			<name>autoGenerateColumns</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[A fully qualified class name to a custom AutoGenerateColumns implementation. Could also be a named type in the preferences. Used to generate columns on the fly.]]>
			</description>
		</attribute>
		<attribute>
			<name>titles</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[A fully qualified class name to a custom AutoGenerateColumns implementation. Could also be a named type in the preferences. Used to generate columns on the fly.]]>
			</description>
		</attribute>
		<attribute>
			<name>widths</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[A fully qualified class name to a custom AutoGenerateColumns implementation. Could also be a named type in the preferences. Used to generate columns on the fly.]]>
			</description>
		</attribute>
		<attribute>
			<name>cellValues</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[A fully qualified class name to a custom AutoGenerateColumns implementation. Could also be a named type in the preferences. Used to generate columns on the fly.]]>
			</description>
		</attribute>
		<attribute>
			<name>cellNames</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[A fully qualified class name to a custom AutoGenerateColumns implementation. Could also be a named type in the preferences. Used to generate columns on the fly.]]>
			</description>
		</attribute>
		<attribute>
			<name>editEvents</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[A fully qualified class name to a custom AutoGenerateColumns implementation. Could also be a named type in the preferences. Used to generate columns on the fly.]]>
			</description>
		</attribute>
		<attribute>
			<name>editTemplates</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[A fully qualified class name to a custom AutoGenerateColumns implementation. Could also be a named type in the preferences. Used to generate columns on the fly.]]>
			</description>
		</attribute>
		<attribute>
			<name>editables</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[A fully qualified class name to a custom AutoGenerateColumns implementation. Could also be a named type in the preferences. Used to generate columns on the fly.]]>
			</description>
		</attribute>

	</tag>
	<tag>
		<name>dpform</name>
		<tag-class>com.dawnpro.commons.tag.NewFormTag</tag-class>
		<body-content>JSP</body-content>
		<display-name>FormTag</display-name>
		<description>
			<![CDATA[生成form表单,包括一些常用的隐藏信息]]>
		</description>
		<attribute>
			<name>name</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[Form名称.]]>
			</description>
		</attribute>
		<attribute>
			<name>action</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[action.]]>
			</description>
		</attribute>
		<attribute>
			<name>method</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[method.]]>
			</description>
		</attribute>
		<attribute>
			<name>onsumbit</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[onsumbit.]]>
			</description>
		</attribute>
		<attribute>
			<name>reqparam</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[从request中获取param参数生成隐藏域.]]>
			</description>
		</attribute>
		<attribute>
			<name>sesparam</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[从session中获取atrribute参数生成隐藏域.]]>
			</description>
		</attribute>
	</tag>
	<!--新自定义 -->
	<tag>
      <name>filedset</name>
      <tag-class>com.dawnpro.commons.tag.FiledSetTag</tag-class>
      <body-content>jsp</body-content>
      <display-name>FiledsetTag</display-name>
      	<attribute>
      	 <name>id</name>
      	 <required>false</required>
      	 <rtexprvalue>true</rtexprvalue>
      	 <description>
				<![CDATA[filedset的唯一属性]]>
			</description>
      </attribute>
      <attribute>
      	 <name>title</name>
       	<required>false</required>
       	 <rtexprvalue>true</rtexprvalue>
       	<description>
				<![CDATA[位于filedset前面的描述]]>
			</description>
      </attribute>
      <attribute>
       	<name>multilang_title</name>
       	<required>false</required>
       	<rtexprvalue>true</rtexprvalue>
       	<description>
				<![CDATA[多语言的描述]]>
			</description>
      </attribute>
      <attribute>
       	<name>style</name>
       	<required>false</required>
       	<rtexprvalue>true</rtexprvalue>
       	<description>
				<![CDATA[CSS样式]]>
			</description>
      </attribute>
 </tag>
 <tag>
      <name>form</name>
      <tag-class>com.dawnpro.commons.tag.FormTag</tag-class>
      <body-content>jsp</body-content>
      <display-name>FormTag</display-name>
      	<attribute>
      	 <name>id</name>
      	 <required>false</required>
      	 <rtexprvalue>true</rtexprvalue>
      	 <description>
				<![CDATA[form的唯一属性]]>
			</description>
      </attribute>
      <attribute>
      	 <name>name</name>
       	<required>false</required>
       	 <rtexprvalue>true</rtexprvalue>
       	<description>
				<![CDATA[form的name属性]]>
			</description>
      </attribute>
      <attribute>
       	<name>method</name>
       	<required>true</required>
       	<rtexprvalue>false</rtexprvalue>
       	<description>
				<![CDATA[提交表单的方式post/get]]>
			</description>
      </attribute>
      <attribute>
       	<name>action</name>
       	<required>true</required>
       	<rtexprvalue>false</rtexprvalue>
       	<description>
				<![CDATA[提交表单的url]]>
			</description>
      </attribute>
      <attribute>
       	<name>desc</name>
       	<required>false</required>
       	<rtexprvalue>false</rtexprvalue>
       	<description>
				<![CDATA[form的描述]]>
			</description>
      </attribute>
      <attribute>
       	<name>onSubmit</name>
       	<required>false</required>
       	<rtexprvalue>false</rtexprvalue>
       	<description>
				<![CDATA[提交表单时验证的方式]]>
			</description>
      </attribute>
      <attribute>
       	<name>enctype</name>
       	<required>false</required>
       	<rtexprvalue>false</rtexprvalue>
       	<description>
				<![CDATA[表单数据在发送到服务器之前的编码方式]]>
			</description>
      </attribute>
      <attribute>
       	<name>autoValid</name>
       	<required>false</required>
       	<rtexprvalue>false</rtexprvalue>
       	<description>
				<![CDATA[是否自动验证]]>
			</description>
      </attribute>
      <attribute>
       	<name>validType</name>
       	<required>false</required>
       	<rtexprvalue>false</rtexprvalue>
       	<description>
				<![CDATA[验证方式可选1|2|3]]>
			</description>
      </attribute>
 </tag>
 <tag>
      <name>input</name>
      <tag-class>com.dawnpro.commons.tag.InputTag</tag-class>
      <body-content>jsp</body-content>
      <display-name>InputTag</display-name>
      	<attribute>
      	 <name>id</name>
      	 <required>false</required>
      	 <rtexprvalue>true</rtexprvalue>
      	 <description>
				<![CDATA[input元素的唯一属性]]>
			</description>
      </attribute>
      <attribute>
      	 <name>name</name>
       	<required>false</required>
       	 <rtexprvalue>true</rtexprvalue>
       	<description>
				<![CDATA[input元素的name属性]]>
			</description>
      </attribute>
      <attribute>
       	<name>type</name>
       	<required>true</required>
       	<rtexprvalue>false</rtexprvalue>
       	<description>
				<![CDATA[input元素的类型text|password|button|radio|file|checkbox|submit|reset|hidden|textarea|]]>
			</description>
      </attribute>
      <attribute>
       	<name>title</name>
       	<required>false</required>
       	<rtexprvalue>true</rtexprvalue>
       	<description>
				<![CDATA[input元素前面的描述]]>
			</description>
      </attribute>
      <attribute>
       	<name>multilang_title</name>
       	<required>false</required>
       	<rtexprvalue>true</rtexprvalue>
       	<description>
				<![CDATA[多语言的描述]]>
			</description>
      </attribute>
      <attribute>
       	<name>icon</name>
       	<required>false</required>
       	<rtexprvalue>false</rtexprvalue>
       	<description>
				<![CDATA[图标类型]]>
			</description>
      </attribute>
      <attribute>
       	<name>value</name>
       	<required>false</required>
       	<rtexprvalue>false</rtexprvalue>
       	<description>
				<![CDATA[input元素的值]]>
			</description>
      </attribute>
       <attribute>
       	<name>align</name>
       	<required>false</required>
       	<rtexprvalue>false</rtexprvalue>
       	<description>
				<![CDATA[对齐方式]]>
			</description>
      </attribute>
      <attribute>
       	<name>maxlength</name>
       	<required>false</required>
       	<rtexprvalue>false</rtexprvalue>
       	<description>
				<![CDATA[input元素的长度]]>
			</description>
      </attribute>
      <attribute>
       	<name>styleClass</name>
       	<required>false</required>
       	<rtexprvalue>false</rtexprvalue>
       	<description>
				<![CDATA[input元素的CSS样式]]>
			</description>
      </attribute>
      <attribute>
       	<name>checked</name>
       	<required>false</required>
       	<rtexprvalue>false</rtexprvalue>
       	<description>
				<![CDATA[选择型输入域是否被默认选中]]>
			</description>
      </attribute>
      <attribute>
       	<name>readonly</name>
       	<required>false</required>
       	<rtexprvalue>false</rtexprvalue>
       	<description>
				<![CDATA[是否允许修改input元素的值]]>
			</description>
      </attribute>
      <attribute>
       	<name>dataType</name>
       	<required>false</required>
       	<rtexprvalue>false</rtexprvalue>
       	<description>
				<![CDATA[input元素的的数据类型]]>
			</description>
      </attribute>
      <attribute>
       	<name>format</name>
       	<required>false</required>
       	<rtexprvalue>false</rtexprvalue>
       	<description>
				<![CDATA[对日期进行格式化可选ymd|dmy]]>
			</description>
      </attribute>
      <attribute>
       	<name>require</name>
       	<required>false</required>
       	<rtexprvalue>false</rtexprvalue>
       	<description>
				<![CDATA[input元素是否是必须的]]>
			</description>
      </attribute>
      <attribute>
       	<name>msg</name>
       	<required>false</required>
       	<rtexprvalue>false</rtexprvalue>
       	<description>
				<![CDATA[input元素输入格式不正确时的提示信息]]>
			</description>
      </attribute>
      <attribute>
       	<name>blankmsg</name>
       	<required>false</required>
       	<rtexprvalue>false</rtexprvalue>
       	<description>
				<![CDATA[input元素输入为空时的提示信息]]>
			</description>
      </attribute>
      <attribute>
       	<name>regexp</name>
       	<required>false</required>
       	<rtexprvalue>false</rtexprvalue>
       	<description>
				<![CDATA[自定义的正则表达式]]>
			</description>
      </attribute>
      <attribute>
       	<name>operator</name>
       	<required>false</required>
       	<rtexprvalue>false</rtexprvalue>
       	<description>
				<![CDATA[在dataType为Compare时可选，可选项为：NotEqual|GreaterThan|GreaterThanEqual|LessThan|LessThanEqual|Equal|]]>
			</description>
      </attribute>
      <attribute>
       	<name>min</name>
       	<required>false</required>
       	<rtexprvalue>false</rtexprvalue>
       	<description>
				<![CDATA[当dataType为Number，Integer，Double时最小值]]>
			</description>
      </attribute>
      <attribute>
       	<name>max</name>
       	<required>false</required>
       	<rtexprvalue>false</rtexprvalue>
       	<description>
				<![CDATA[最大值]]>
			</description>
      </attribute>
      <attribute>
       	<name>to</name>
       	<required>false</required>
       	<rtexprvalue>false</rtexprvalue>
       	<description>
				<![CDATA[当dataType为Repeate，to的值为某表单项的name属性值，用于设定当前表单项的值是否与目标表单项的值一致；
				当为Compare时to的选项值类型为实数，用于判断当前表单项的输入与to的值是否符合operator属性值所指定的关系]]>
			</description>
      </attribute>
      <attribute>
       	<name>if</name>
       	<required>false</required>
       	<rtexprvalue>false</rtexprvalue>
       	<description>
				<![CDATA[将本表单的值与某个值做判断，这某个值可以是一个具体的值，也可以是其它表单的值，对本表单做一个限定]]>
			</description>
      </attribute>
      <attribute>
       	<name>rows</name>
       	<required>false</required>
       	<rtexprvalue>false</rtexprvalue>
       	<description>
				<![CDATA[只能是type为textarea时可选，规定文本域内可见的行数]]>
			</description>
      </attribute>
      <attribute>
       	<name>cols</name>
       	<required>false</required>
       	<rtexprvalue>false</rtexprvalue>
       	<description>
				<![CDATA[规定文本区域可见的列数]]>
			</description>
      </attribute>
      <attribute>
       	<name>eventtype</name>
       	<required>false</required>
       	<rtexprvalue>false</rtexprvalue>
       	<description>
				<![CDATA[处理本表单的事件]]>
			</description>
      </attribute>
      <attribute>
       	<name>eventcontent</name>
       	<required>false</required>
       	<rtexprvalue>false</rtexprvalue>
       	<description>
				<![CDATA[处理本表单事件对应的内容]]>
			</description>
      </attribute>
 </tag>
 <tag>
      <name>popWin</name>
      <tag-class>com.dawnpro.commons.tag.PopWinTag</tag-class>
      <body-content>empty</body-content>
      <display-name>popWin</display-name>
      	<attribute>
      	 <name>url</name>
      	 <required>true</required>
      	 <rtexprvalue>true</rtexprvalue>
      	 <description>
				<![CDATA[弹出窗口的子窗口的url]]>
			</description>
      </attribute>
      <attribute>
      	 <name>height</name>
      	 <required>false</required>
      	 <rtexprvalue>true</rtexprvalue>
      	 <description>
				<![CDATA[弹出子窗口的高度]]>
			</description>
      </attribute>
      <attribute>
      	 <name>width</name>
      	 <required>false</required>
      	 <rtexprvalue>true</rtexprvalue>
      	 <description>
				<![CDATA[弹出子窗口的宽度]]>
			</description>
      </attribute>
 </tag>
 <tag>
      <name>fileUpload</name>
      <tag-class>com.dawnpro.commons.tag.FileUpLoadTag</tag-class>
      <body-content>empty</body-content>
      <display-name>fileUpload</display-name>
      	<attribute>
      	 <name>path</name>
      	 <required>true</required>
      	 <rtexprvalue>true</rtexprvalue>
      	 <description>
				<![CDATA[上传路径]]>
			</description>
      </attribute>
      <attribute>
      	 <name>height</name>
      	 <required>false</required>
      	 <rtexprvalue>true</rtexprvalue>
      	 <description>
				<![CDATA[iframe的高度]]>
			</description>
      </attribute>
      <attribute>
      	 <name>width</name>
      	 <required>false</required>
      	 <rtexprvalue>true</rtexprvalue>
      	 <description>
				<![CDATA[iframe的宽度]]>
			</description>
      </attribute>
      <attribute>
      	<name>microcode</name>
      	<required>true</required>
      	<rtexprvalue>true</rtexprvalue>
      	<description>
      		<![CDATA[微码用来识别哪个domain]]>
      	</description>
      </attribute>
      <attribute>
      	<name>id</name>
      	<required>true</required>
      	<rtexprvalue>true</rtexprvalue>
		<description>
			<![CDATA[与之关联的实体id]]>
		</description>
      </attribute>
 </tag>
  <tag>
      <name>fileDownload</name>
      <tag-class>com.dawnpro.commons.tag.FileDownloadTag</tag-class>
      <body-content>empty</body-content>
      <display-name>fileDownload</display-name>
      <attribute>
      	<name>realname</name>
      	<required>true</required>
      	<rtexprvalue>true</rtexprvalue>
		<description>
			<![CDATA[附件原本文件名]]>
		</description>
      </attribute>
      <attribute>
      	<name>path</name>
      	<required>true</required>
      	<rtexprvalue>true</rtexprvalue>
      	<description>
      		<![CDATA[附件绝对路径]]>
      	</description>
      </attribute>
      <attribute>
      	<name>downType</name>
      	<required>false</required>
      	<rtexprvalue>true</rtexprvalue>
      	<description>
      	<![CDATA[下载方式：1、文件流；2、直接下载]]>
      	</description>
      </attribute>
 </tag>
   <tag>
      <name>chart</name>
      <tag-class>com.dawnpro.service.demo.another.GraphTag</tag-class>
      <body-content>empty</body-content>
      <display-name>chart</display-name>
      <attribute>
      	<name>template</name>
      	<required>true</required>
      	<rtexprvalue>true</rtexprvalue>
		<description>
			<![CDATA[chart模板]]>
		</description>
      </attribute>
      <attribute>
      	<name>width</name>
      	<required>true</required>
      	<rtexprvalue>true</rtexprvalue>
      	<description>
      		<![CDATA[chart宽度]]>
      	</description>
      </attribute>
      <attribute>
      	<name>height</name>
      	<required>false</required>
      	<rtexprvalue>true</rtexprvalue>
      	<description>
      	<![CDATA[chart高]]>
      	</description>
      </attribute>
      <attribute>
      	<name>dataSource</name>
      	<required>false</required>
      	<rtexprvalue>true</rtexprvalue>
      	<description>
      	<![CDATA[数据源]]>
      	</description>
      </attribute>
 </tag>
 <tag>
		<name>tree</name>
		<tag-class>net.jcreate.e3.tree.taglib.TreeTag</tag-class>
		 <body-content>jsp</body-content>
		<attribute>
			<name>var</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
      			<![CDATA[用于保存items元素]]>
      		</description>
		</attribute>
		<attribute>
			<name>defaultSort</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>items</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
      			<![CDATA[是业务数据列表对象的key]]>
      		</description>
		</attribute>
		<attribute>
			<name>scope</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>comparator</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>visitor</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>sortProperty</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
      			<![CDATA[排序属性名称，默认是按节点的名称来排序的，如果要使用别的属性排序，则需要设置该值.：如果你的业务对象有排序属性时，则需要指定，如sortProperty=”orgOrder”. 注意：如果设置了comparator属性，那么该值无效.]]>
      		</description>
		</attribute>
		<attribute>
			<name>reverse</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
      			<![CDATA[是否反向排序，默认false]]>
      		</description>
		</attribute>
		<attribute>
			<name>builder</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
      			<![CDATA[用于构造树的builder对象（builder是什么下面会有介绍）,可以选值有
[XTree, XLoadTree, RadioXTree, RadioXLoadTree, CheckXTree, CheckXLoadTree, CompositeXTree, CompositeXLoadTree, ExtTree, ExtLoadTree]
如果这些builder不能满足您的需求，你可以指定一个class,只要指定class实现了WebTreeBuilder接口即可.
]]>
      		</description>
		</attribute>
	</tag>
	<tag>
		<name>node</name>
		<tag-class>net.jcreate.e3.tree.taglib.NodeTag</tag-class>
		<body-content>JSP</body-content>
		<dynamic-attributes>true</dynamic-attributes>
		<attribute>
			<name>id</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>parentId</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>name</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>

		<attribute>
			<name>subTreeURL</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>

		<attribute>
			<name>cls</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>

		<attribute>
			<name>target</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>icon</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>openIcon</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>action</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>

		<attribute>
			<name>tip</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>value</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>selected</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>dropable</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>dragable</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>disabled</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>


	</tag>

	<tag>
		<name>userAttribute</name>
		<tag-class>net.jcreate.e3.tree.taglib.UserAttributeTag</tag-class>
		<body-content>EMPTY</body-content>
		<attribute>
			<name>name</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>value</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
	</tag>
<tag>
		<description>
			Creates a FCKeditor instance with the given parameters.
		</description>
		<display-name>editor</display-name>
		<name>editor</name>
		<tag-class>net.fckeditor.tags.EditorTag</tag-class>
		<body-content>JSP</body-content>
		<attribute>
			<description>
				The unique instance name under which the editor can be
				retrieved through the API.
			</description>
			<name>instanceName</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<description>
				Width of the FCKeditor instance in the browser window.
			</description>
			<name>width</name>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<description>
				Height of the FCKeditor instance in the browser window.
			</description>
			<name>height</name>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<description>
				The toolbar set which shall be displayed to the user.
			</description>
			<name>toolbarSet</name>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<description>
				The path/folder in which the editor is deployed under
				the given context. The context path will be attached
				automatically. (e.g. '/fckeditor')
			</description>
			<name>basePath</name>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<description>
				Passes any content to the FCKeditor document. Use the
				jsp:attribute tag for large inline content. \r, \n, and
				\t will be truncated.
			</description>
			<name>value</name>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<example><![CDATA[
<FCK:editor instanceName="editorDefault" height="500px" />]]>
		</example>
	</tag>
	<tag>
		<description>
			Sets a config property of the editor to the supplied value.
			You may provide any attribute you want for the editor. Set
			at least one attribute per tag or several attributes with
			one tag. This tag can only be nested within an editor tag.
			For all configuration options click
			<![CDATA[<a href="http://docs.fckeditor.net/FCKeditor_2.x/Developers_Guide/Configuration/Configuration_Options">here</a>]]>.
		</description>
		<display-name>config</display-name>
		<name>config</name>
		<tag-class>net.fckeditor.tags.ConfigTag</tag-class>
		<body-content>empty</body-content>
		<dynamic-attributes>true</dynamic-attributes>
		<example>
			<![CDATA[
<FCK:config SkinPath="/skins/silver/" AutoDetectLanguage="true" />]]>
		</example>
	</tag>
	<tag>
		<description>
			Displays session-dependent and compatibility-related
			information. This tag is intended for developers only.
			Response messages cannot be localized, they are English
			only.
		</description>
		<display-name>check</display-name>
		<name>check</name>
		<tag-class>net.fckeditor.tags.CheckTag</tag-class>
		<body-content>empty</body-content>
		<attribute>
			<description>
				Provide the feature name you want to check. Valid
				features are [FileUpload, FileBrowsing,
				CompatibleBrowser]
			</description>
			<name>command</name>
			<required>true</required>
			<type>java.lang.String</type>
		</attribute>
		<example><![CDATA[
		<FCK:check command="AttachUpload" />
		<FCK:check command="CompatibleBrowser" />]]>
		</example>
	</tag>
	<tag>
    <name>catch</name>
    <tag-class>org.apache.taglibs.standard.tag.common.core.CatchTag</tag-class>
    <body-content>JSP</body-content>
    <description>
        Catches any Throwable that occurs in its body and optionally
        exposes it.
    </description>
    <attribute>
        <name>var</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
  </tag>
  <tag>
    <name>choose</name>
    <tag-class>org.apache.taglibs.standard.tag.common.core.ChooseTag</tag-class>
    <body-content>JSP</body-content>
    <description>
        Simple conditional tag that establishes a context for
        mutually exclusive conditional operations, marked by
        &lt;when&gt; and &lt;otherwise&gt;
    </description>
  </tag>
  <tag>
    <name>out</name>
    <tag-class>org.apache.taglibs.standard.tag.el.core.OutTag</tag-class>
    <body-content>JSP</body-content>
    <description>
	Like &lt;%= ... &gt;, but for expressions.
    </description>
    <attribute>
        <name>value</name>
        <required>true</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
        <name>default</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
        <name>escapeXml</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
  </tag>
  <tag>
    <name>if</name>
    <tag-class>org.apache.taglibs.standard.tag.el.core.IfTag</tag-class>
    <body-content>JSP</body-content>
    <description>
        Simple conditional tag, which evalutes its body if the
        supplied condition is true and optionally exposes a Boolean
        scripting variable representing the evaluation of this condition
    </description>
    <attribute>
        <name>test</name>
        <required>true</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
        <name>var</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
        <name>scope</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
  </tag>
  <tag>
    <name>import</name>
    <tag-class>org.apache.taglibs.standard.tag.el.core.ImportTag</tag-class>
    <tei-class>org.apache.taglibs.standard.tei.ImportTEI</tei-class>
    <body-content>JSP</body-content>
    <description>
	Retrieves an absolute or relative URL and exposes its contents
	to either the page, a String in 'var', or a Reader in 'varReader'.
    </description>
    <attribute>
        <name>url</name>
        <required>true</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
        <name>var</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
        <name>scope</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
        <name>varReader</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
        <name>context</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
        <name>charEncoding</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
  </tag>
  <tag>
    <name>forEach</name>
    <tag-class>org.apache.taglibs.standard.tag.el.core.ForEachTag</tag-class>
    <tei-class>org.apache.taglibs.standard.tei.ForEachTEI</tei-class>
    <body-content>JSP</body-content>
    <description>
	The basic iteration tag, accepting many different
        collection types and supporting subsetting and other
        functionality
    </description>
    <attribute>
	<name>items</name>
	<required>false</required>
	<rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
	<name>begin</name>
	<required>false</required>
	<rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
	<name>end</name>
	<required>false</required>
	<rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
	<name>step</name>
	<required>false</required>
	<rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
	<name>var</name>
	<required>false</required>
	<rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
	<name>varStatus</name>
	<required>false</required>
	<rtexprvalue>false</rtexprvalue>
    </attribute>
  </tag>
  <tag>
    <name>forTokens</name>
    <tag-class>org.apache.taglibs.standard.tag.el.core.ForTokensTag</tag-class>
    <body-content>JSP</body-content>
    <description>
	Iterates over tokens, separated by the supplied delimeters
    </description>
    <attribute>
	<name>items</name>
	<required>true</required>
	<rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
	<name>delims</name>
	<required>true</required>
	<rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
	<name>begin</name>
	<required>false</required>
	<rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
	<name>end</name>
	<required>false</required>
	<rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
	<name>step</name>
	<required>false</required>
	<rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
	<name>var</name>
	<required>false</required>
	<rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
	<name>varStatus</name>
	<required>false</required>
	<rtexprvalue>false</rtexprvalue>
    </attribute>
  </tag>
  <tag>
    <name>otherwise</name>
    <tag-class>org.apache.taglibs.standard.tag.common.core.OtherwiseTag</tag-class>
    <body-content>JSP</body-content>
    <description>
	Subtag of &lt;choose&gt; that follows &lt;when&gt; tags
	and runs only if all of the prior conditions evaluated to
	'false'
    </description>
  </tag>
  <tag>
    <name>param</name>
    <tag-class>org.apache.taglibs.standard.tag.el.core.ParamTag</tag-class>
    <body-content>JSP</body-content>
    <description>
	Adds a parameter to a containing 'import' tag's URL.
    </description>
    <attribute>
        <name>name</name>
        <required>true</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
        <name>value</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
  </tag>
  <tag>
    <name>redirect</name>
    <tag-class>org.apache.taglibs.standard.tag.el.core.RedirectTag</tag-class>
    <body-content>JSP</body-content>
    <description>
	Redirects to a new URL.
    </description>
    <attribute>
        <name>var</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
        <name>scope</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
        <name>url</name>
        <required>true</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
        <name>context</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
  </tag>
  <tag>
    <name>remove</name>
    <tag-class>org.apache.taglibs.standard.tag.common.core.RemoveTag</tag-class>
    <body-content>empty</body-content>
    <description>
	Removes a scoped variable (from a particular scope, if specified).
    </description>
    <attribute>
        <name>var</name>
        <required>true</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
        <name>scope</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
  </tag>
  <tag>
    <name>set</name>
    <tag-class>org.apache.taglibs.standard.tag.el.core.SetTag</tag-class>
    <body-content>JSP</body-content>
    <description>
	Sets the result of an expression evaluation in a 'scope'
    </description>
    <attribute>
        <name>var</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
        <name>value</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
        <name>target</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
        <name>property</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
        <name>scope</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
  </tag>
  <tag>
    <name>url</name>
    <tag-class>org.apache.taglibs.standard.tag.el.core.UrlTag</tag-class>
    <body-content>JSP</body-content>
    <description>
	Prints or exposes a URL with optional query parameters
        (via the c:param tag).
    </description>
    <attribute>
        <name>var</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
        <name>scope</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
        <name>value</name>
        <required>true</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
        <name>context</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
  </tag>
  <tag>
    <name>when</name>
    <tag-class>org.apache.taglibs.standard.tag.el.core.WhenTag</tag-class>
    <body-content>JSP</body-content>
    <description>
        Subtag of &lt;choose&gt; that includes its body if its
        condition evalutes to 'true'
    </description>
    <attribute>
        <name>test</name>
        <required>true</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
  </tag>
</taglib>