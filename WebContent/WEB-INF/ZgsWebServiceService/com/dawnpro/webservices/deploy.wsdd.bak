<!-- Use this file to deploy some handlers/chains and services      -->
<!-- Two ways to do this:                                           -->
<!--   java org.apache.axis.client.AdminClient deploy.wsdd          -->
<!--      after the axis server is running                          -->
<!-- or                                                             -->
<!--   java org.apache.axis.utils.Admin client|server deploy.wsdd   -->
<!--      from the same directory that the Axis engine runs         -->

<deployment
    xmlns="http://xml.apache.org/axis/wsdd/"
    xmlns:java="http://xml.apache.org/axis/wsdd/providers/java">

  <!-- Services from ZgsWebServiceService WSDL service -->

  <service name="ZgsWebService" provider="java:RPC" style="wrapped" use="literal">
      <parameter name="wsdlTargetNamespace" value="http://webservices.dawnpro.com"/>
      <parameter name="wsdlServiceElement" value="ZgsWebServiceService"/>
      <parameter name="schemaQualified" value="http://webservices.dawnpro.com"/>
      <parameter name="wsdlServicePort" value="ZgsWebService"/>
      <parameter name="className" value="com.dawnpro.webservices.ZgsWebServiceSoapBindingImpl"/>
      <parameter name="wsdlPortType" value="ZgsWebService"/>
      <parameter name="typeMappingVersion" value="1.2"/>
      <operation name="uploadBaseHgzcs" qname="operNS:uploadBaseHgzcs" xmlns:operNS="http://webservices.dawnpro.com" returnQName="retNS:uploadBaseHgzcsReturn" xmlns:retNS="http://webservices.dawnpro.com" returnType="rtns:string" xmlns:rtns="http://www.w3.org/2001/XMLSchema" soapAction="" >
        <parameter qname="pns:jsonstr" xmlns:pns="http://webservices.dawnpro.com" type="tns:string" xmlns:tns="http://www.w3.org/2001/XMLSchema"/>
      </operation>
      <operation name="helloWorld" qname="operNS:helloWorld" xmlns:operNS="http://webservices.dawnpro.com" returnQName="retNS:helloWorldReturn" xmlns:retNS="http://webservices.dawnpro.com" returnType="rtns:string" xmlns:rtns="http://www.w3.org/2001/XMLSchema" soapAction="" >
      </operation>
      <operation name="uploadXcxxjc" qname="operNS:uploadXcxxjc" xmlns:operNS="http://webservices.dawnpro.com" returnQName="retNS:uploadXcxxjcReturn" xmlns:retNS="http://webservices.dawnpro.com" returnType="rtns:string" xmlns:rtns="http://www.w3.org/2001/XMLSchema" soapAction="" >
        <parameter qname="pns:jsonstr" xmlns:pns="http://webservices.dawnpro.com" type="tns:string" xmlns:tns="http://www.w3.org/2001/XMLSchema"/>
      </operation>
      <operation name="getHgzinfo" qname="operNS:getHgzinfo" xmlns:operNS="http://webservices.dawnpro.com" returnQName="retNS:getHgzinfoReturn" xmlns:retNS="http://webservices.dawnpro.com" returnType="rtns:string" xmlns:rtns="http://www.w3.org/2001/XMLSchema" soapAction="" >
        <parameter qname="pns:jsonstr" xmlns:pns="http://webservices.dawnpro.com" type="tns:string" xmlns:tns="http://www.w3.org/2001/XMLSchema"/>
      </operation>
      <operation name="uploadProductData" qname="operNS:uploadProductData" xmlns:operNS="http://webservices.dawnpro.com" returnQName="retNS:uploadProductDataReturn" xmlns:retNS="http://webservices.dawnpro.com" returnType="rtns:string" xmlns:rtns="http://www.w3.org/2001/XMLSchema" soapAction="" >
        <parameter qname="pns:jsonstr" xmlns:pns="http://webservices.dawnpro.com" type="tns:string" xmlns:tns="http://www.w3.org/2001/XMLSchema"/>
      </operation>
      <parameter name="allowedMethods" value="uploadBaseHgzcs getHgzinfo uploadXcxxjc uploadProductData helloWorld"/>

  </service>
</deployment>
