<?xml version="1.0" encoding="UTF-8"?><!-- Use this file to deploy some handlers/chains and services      --><!-- Two ways to do this:                                           --><!--   java org.apache.axis.client.AdminClient deploy.wsdd          --><!--      after the axis server is running                          --><!-- or                                                             --><!--   java org.apache.axis.utils.Admin client|server deploy.wsdd   --><!--      from the same directory that the Axis engine runs         --><deployment xmlns="http://xml.apache.org/axis/wsdd/" xmlns:java="http://xml.apache.org/axis/wsdd/providers/java">

  <!-- Services from ZgsWebServiceService WSDL service -->

  <service name="ZgsWebService" provider="java:RPC" style="wrapped" use="literal">
      <parameter name="wsdlTargetNamespace" value="http://webservices.dawnpro.com"/>
      <parameter name="wsdlServiceElement" value="ZgsWebServiceService"/>
      <parameter name="schemaQualified" value="http://webservices.dawnpro.com"/>
      <parameter name="wsdlServicePort" value="ZgsWebService"/>
      <parameter name="className" value="com.dawnpro.webservices.ZgsWebService"/>
      <parameter name="wsdlPortType" value="ZgsWebService"/>
      <parameter name="typeMappingVersion" value="1.2"/>
      <operation xmlns:operNS="http://webservices.dawnpro.com" xmlns:retNS="http://webservices.dawnpro.com" xmlns:rtns="http://www.w3.org/2001/XMLSchema" name="uploadBaseHgzcs" qname="operNS:uploadBaseHgzcs" returnQName="retNS:uploadBaseHgzcsReturn" returnType="rtns:string" soapAction="">
        <parameter xmlns:pns="http://webservices.dawnpro.com" xmlns:tns="http://www.w3.org/2001/XMLSchema" qname="pns:jsonstr" type="tns:string"/>
      </operation>
      <operation xmlns:operNS="http://webservices.dawnpro.com" xmlns:retNS="http://webservices.dawnpro.com" xmlns:rtns="http://www.w3.org/2001/XMLSchema" name="helloWorld" qname="operNS:helloWorld" returnQName="retNS:helloWorldReturn" returnType="rtns:string" soapAction="">
      </operation>
      <operation xmlns:operNS="http://webservices.dawnpro.com" xmlns:retNS="http://webservices.dawnpro.com" xmlns:rtns="http://www.w3.org/2001/XMLSchema" name="uploadXcxxjc" qname="operNS:uploadXcxxjc" returnQName="retNS:uploadXcxxjcReturn" returnType="rtns:string" soapAction="">
        <parameter xmlns:pns="http://webservices.dawnpro.com" xmlns:tns="http://www.w3.org/2001/XMLSchema" qname="pns:jsonstr" type="tns:string"/>
      </operation>
      <operation xmlns:operNS="http://webservices.dawnpro.com" xmlns:retNS="http://webservices.dawnpro.com" xmlns:rtns="http://www.w3.org/2001/XMLSchema" name="getHgzinfo" qname="operNS:getHgzinfo" returnQName="retNS:getHgzinfoReturn" returnType="rtns:string" soapAction="">
        <parameter xmlns:pns="http://webservices.dawnpro.com" xmlns:tns="http://www.w3.org/2001/XMLSchema" qname="pns:jsonstr" type="tns:string"/>
      </operation>
      <operation xmlns:operNS="http://webservices.dawnpro.com" xmlns:retNS="http://webservices.dawnpro.com" xmlns:rtns="http://www.w3.org/2001/XMLSchema" name="uploadProductData" qname="operNS:uploadProductData" returnQName="retNS:uploadProductDataReturn" returnType="rtns:string" soapAction="">
        <parameter xmlns:pns="http://webservices.dawnpro.com" xmlns:tns="http://www.w3.org/2001/XMLSchema" qname="pns:jsonstr" type="tns:string"/>
      </operation>
      <parameter name="allowedMethods" value="uploadBaseHgzcs getHgzinfo uploadXcxxjc uploadProductData helloWorld"/>

  </service>
</deployment>