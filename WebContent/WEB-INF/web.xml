<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://java.sun.com/xml/ns/j2ee" xmlns:web="http://xmlns.jcp.org/xml/ns/javaee" xsi:schemaLocation="http://xmlns.jcp.org/xml/ns/javaee http://java.sun.com/xml/ns/javaee/web-app_2_5.xsd http://java.sun.com/xml/ns/j2ee http://java.sun.com/xml/ns/j2ee/web-app_2_4.xsd" version="2.4">
  <display-name>schgz_dfm</display-name>
  <context-param>
    <param-name>webAppRootKey</param-name>
    <param-value>schgz_dfm.root</param-value>
  </context-param>
  <context-param>
    <param-name>log4jConfigLocation</param-name>
    <param-value>WEB-INF/classes/log4j.properties</param-value>
  </context-param>
  <context-param>
    <param-name>log4jRefreshInterval</param-name>
    <param-value>60000</param-value>
  </context-param>
  <listener>
    <listener-class>org.springframework.web.util.Log4jConfigListener</listener-class>
  </listener>
  <servlet>
    <servlet-name>scheduler</servlet-name>
    <servlet-class>com.dawnpro.service.scheduler.Scheduler_BasicConnector</servlet-class>
  </servlet>
  <servlet-mapping>
    <servlet-name>scheduler</servlet-name>
    <url-pattern>/scheduler</url-pattern>
  </servlet-mapping>
  <servlet>
    <servlet-name>ModeQueueServlet</servlet-name>
    <servlet-class>com.dawnpro.core.servlet.ModeQueueServlet</servlet-class>
  </servlet>
  <servlet-mapping>
    <servlet-name>ModeQueueServlet</servlet-name>
    <url-pattern>/ModeQueueServlet</url-pattern>
  </servlet-mapping>
  <servlet>
    <servlet-name>TreeSelectServlet</servlet-name>
    <servlet-class>com.dawnpro.core.servlet.TreeSelectServlet</servlet-class>
  </servlet>
  <servlet-mapping>
    <servlet-name>TreeSelectServlet</servlet-name>
    <url-pattern>/TreeSelectServlet</url-pattern>
  </servlet-mapping>
  <context-param>
    <param-name>ecsidePreferencesLocation</param-name>
    <param-value>/WEB-INF/ecside.properties</param-value>
  </context-param>
  <context-param>
    <param-name>encoding</param-name>
    <param-value>UTF-8</param-value>
  </context-param>
  <filter>
    <filter-name>ecsideExport</filter-name>
    <filter-class>org.ecside.filter.ECSideFilter</filter-class>
    <init-param>
      <param-name>useEasyDataAccess</param-name>
      <param-value>true</param-value>
    </init-param>
    <init-param>
      <param-name>useEncoding</param-name>
      <param-value>true</param-value>
    </init-param>
    <init-param>
      <param-name>encoding</param-name>
      <param-value>UTF-8</param-value>
    </init-param>
  </filter>
  <filter>
    <filter-name>FrontController</filter-name>
    <filter-class>
			com.dawnpro.core.servlet.filter.FrontControllerFilter
		</filter-class>
  </filter>
  <filter-mapping>
    <filter-name>FrontController</filter-name>
    <url-pattern>*.jsp</url-pattern>
  </filter-mapping>
  <filter-mapping>
    <filter-name>ecsideExport</filter-name>
    <url-pattern>/*</url-pattern>
  </filter-mapping>
  <listener>
    <listener-class>
			com.dawnpro.core.servlet.SessionListener
		</listener-class>
  </listener>
  <servlet>
    <servlet-name>csvexport</servlet-name>
    <servlet-class>com.dawnpro.service.dpcvs.zhcx.Go2Csv</servlet-class>
  </servlet>
  <servlet-mapping>
    <servlet-name>csvexport</servlet-name>
    <url-pattern>/csvexport</url-pattern>
  </servlet-mapping>
  <servlet>
    <servlet-name>FileDownSvt</servlet-name>
    <servlet-class>
          com.dawnpro.core.servlet.FileDownSvt
      </servlet-class>
    <load-on-startup>1</load-on-startup>
  </servlet>
  <servlet-mapping>
    <servlet-name>FileDownSvt</servlet-name>
    <url-pattern>
        *.do
      </url-pattern>
  </servlet-mapping>
  <servlet>
    <servlet-name>ReadXML</servlet-name>
    <servlet-class>
			com.dawnpro.core.servlet.ServletConfigXml
		</servlet-class>
    <load-on-startup>0</load-on-startup>
  </servlet>
  <servlet>
    <display-name>CocParamServlet</display-name>
    <servlet-name>CocParamServlet</servlet-name>
    <servlet-class>com.dawnpro.service.dpcvs.coczsgl.CocParamServlet</servlet-class>
  </servlet>
  <servlet-mapping>
    <servlet-name>CocParamServlet</servlet-name>
    <url-pattern>/CocParamServlet</url-pattern>
  </servlet-mapping>
  <servlet>
    <display-name>CocServlet</display-name>
    <servlet-name>CocServlet</servlet-name>
    <servlet-class>com.dawnpro.service.dpcvs.coczsgl.CocServlet</servlet-class>
  </servlet>
  <servlet-mapping>
    <servlet-name>CocServlet</servlet-name>
    <url-pattern>/CocServlet</url-pattern>
  </servlet-mapping>
  <servlet>
    <display-name>PhotoServlet</display-name>
    <servlet-name>PhotoServlet</servlet-name>
    <servlet-class>com.dawnpro.service.dpcvs.photo.PhotoPrintServlet</servlet-class>
  </servlet>
  <servlet-mapping>
    <servlet-name>PhotoServlet</servlet-name>
    <url-pattern>/PhotoServlet</url-pattern>
  </servlet-mapping>
  <session-config>
    <session-timeout>30</session-timeout>
  </session-config>
  <mime-mapping>
    <extension>doc</extension>
    <mime-type>application/msword</mime-type>
  </mime-mapping>
  <mime-mapping>
    <extension>xls</extension>
    <mime-type>application/msexcel</mime-type>
  </mime-mapping>
  <mime-mapping>
    <extension>csv</extension>
    <mime-type>application/msexcel</mime-type>
  </mime-mapping>
  <mime-mapping>
    <extension>pdf</extension>
    <mime-type>application/pdf</mime-type>
  </mime-mapping>
  <mime-mapping>
    <extension>zip</extension>
    <mime-type>application/zip</mime-type>
  </mime-mapping>
  <mime-mapping>
    <extension>rar</extension>
    <mime-type>application/rar</mime-type>
  </mime-mapping>
  <mime-mapping>
    <extension>txt</extension>
    <mime-type>application/txt</mime-type>
  </mime-mapping>
  <mime-mapping>
    <extension>chm</extension>
    <mime-type>application/mshelp</mime-type>
  </mime-mapping>
  <mime-mapping>
    <extension>mp3</extension>
    <mime-type>audio/x-mpeg</mime-type>
  </mime-mapping>
  <welcome-file-list>
    <welcome-file>/login.jsp</welcome-file>
  </welcome-file-list>
  <servlet>
    <display-name>Apache-Axis Servlet</display-name>
    <servlet-name>AxisServlet</servlet-name>
    <servlet-class>org.apache.axis.transport.http.AxisServlet</servlet-class>
  </servlet>
  <servlet-mapping>
    <servlet-name>AxisServlet</servlet-name>
    <url-pattern>/servlet/AxisServlet</url-pattern>
  </servlet-mapping>
  <servlet-mapping>
    <servlet-name>AxisServlet</servlet-name>
    <url-pattern>*.jws</url-pattern>
  </servlet-mapping>
  <servlet-mapping>
    <servlet-name>AxisServlet</servlet-name>
    <url-pattern>/services/*</url-pattern>
  </servlet-mapping>
  <servlet>
    <display-name>Axis Admin Servlet</display-name>
    <servlet-name>AdminServlet</servlet-name>
    <servlet-class>org.apache.axis.transport.http.AdminServlet</servlet-class>
    <load-on-startup>100</load-on-startup>
  </servlet>
  <servlet-mapping>
    <servlet-name>AdminServlet</servlet-name>
    <url-pattern>/servlet/AdminServlet</url-pattern>
  </servlet-mapping>
  
  	<servlet>
		<servlet-name>jersey-serlvet</servlet-name>
		<servlet-class>com.sun.jersey.spi.container.servlet.ServletContainer</servlet-class>
		<init-param>
			<param-name>com.sun.jersey.config.property.packages</param-name>
			<param-value>com.dawnpro.restful</param-value>
		</init-param>
	</servlet>

	<servlet-mapping>
		<servlet-name>jersey-serlvet</servlet-name>
		<url-pattern>/restful/*</url-pattern>
	</servlet-mapping>
	
</web-app>