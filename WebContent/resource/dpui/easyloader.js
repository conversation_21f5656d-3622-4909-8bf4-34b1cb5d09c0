/**
 * jQuery dpui 1.1
 * 
 * Licensed under the GPL:
 *   http://www.gnu.org/licenses/gpl.txt
 *
 * Copyright 2010 stworthy [ <EMAIL> ] 
 * 
 */
(function(){
var _1={draggable:{js:"jquery.draggable.js"},droppable:{js:"jquery.droppable.js"},resizable:{js:"jquery.resizable.js"},linkbutton:{js:"jquery.linkbutton.js",css:"linkbutton.css"},pagination:{js:"jquery.pagination.js",css:"pagination.css",dependencies:["linkbutton"]},datagrid:{js:"jquery.datagrid.js",css:"datagrid.css",dependencies:["resizable","linkbutton","pagination"]},panel:{js:"jquery.panel.js",css:"panel.css"},window:{js:"jquery.window.js",css:"window.css",dependencies:["resizable","draggable","panel"]},dialog:{js:"jquery.dialog.js",css:"dialog.css",dependencies:["window"]},messager:{js:"jquery.messager.js",css:"messager.css",dependencies:["linkbutton","window"]},layout:{js:"jquery.layout.js",css:"layout.css",dependencies:["resizable","panel"]},form:{js:"jquery.form.js"},menu:{js:"jquery.menu.js",css:"menu.css"},tabs:{js:"jquery.tabs.js",css:"tabs.css"},splitbutton:{js:"jquery.splitbutton.js",css:"splitbutton.css",dependencies:["linkbutton","menu"]},menubutton:{js:"jquery.menubutton.js",css:"menubutton.css",dependencies:["linkbutton","menu"]},accordion:{js:"jquery.accordion.js",css:"accordion.css",dependencies:["panel"]},calendar:{js:"jquery.calendar.js",css:"calendar.css"},combobox:{js:"jquery.combobox.js",css:"combobox.css",dependencies:["validatebox"]},combotree:{js:"jquery.combotree.js",css:"combotree.css",dependencies:["tree","validatebox"]},validatebox:{js:"jquery.validatebox.js",css:"validatebox.css"},numberbox:{js:"jquery.numberbox.js",dependencies:["validatebox"]},tree:{js:"jquery.tree.js",css:"tree.css"},datebox:{js:"jquery.datebox.js",css:"datebox.css",dependencies:["calendar"]},parser:{js:"jquery.parser.js"}};
var _2={"af":"dpui-lang-af.js","bg":"dpui-lang-bg.js","ca":"dpui-lang-ca.js","cs":"dpui-lang-cs.js","da":"dpui-lang-da.js","de":"dpui-lang-de.js","en":"dpui-lang-en.js","fr":"dpui-lang-fr.js","nl":"dpui-lang-nl.js","zh_CN":"dpui-lang-zh_CN.js","zh_TW":"dpui-lang-zh_TW.js"};
var _3={};
function _4(_5,_6){
var _7=false;
var _8=document.createElement("script");
_8.type="text/javascript";
_8.language="javascript";
_8.src=_5;
_8.onload=_8.onreadystatechange=function(){
if(!_7&&(!_8.readyState||_8.readyState=="loaded"||_8.readyState=="complete")){
_7=true;
_8.onload=_8.onreadystatechange=null;
if(_6){
_6.call(_8);
}
}
};
document.getElementsByTagName("head")[0].appendChild(_8);
};
function _9(_a,_b){
_4(_a,function(){
document.getElementsByTagName("head")[0].removeChild(this);
if(_b){
_b();
}
});
};
function _c(_d,_e){
var _f=document.createElement("link");
_f.rel="stylesheet";
_f.type="text/css";
_f.media="screen";
_f.href=_d;
document.getElementsByTagName("head")[0].appendChild(_f);
if(_e){
_e.call(_f);
}
};
function _10(_11){
_3[_11]="loading";
var _12=_1[_11];
var _13="loading";
var _14=(easyloader.css&&_12["css"])?"loading":"loaded";
if(easyloader.css&&_12["css"]){
if(/^http/i.test(_12["css"])){
var url=_12["css"];
}else{
var url=easyloader.base+"themes/"+easyloader.theme+"/"+_12["css"];
}
_c(url,function(){
_14="loaded";
if(_13=="loaded"&&_14=="loaded"){
_15();
}
});
}
if(/^http/i.test(_12["js"])){
var url=_12["js"];
}else{
var url=easyloader.base+"plugins/"+_12["js"];
}
_4(url,function(){
_13="loaded";
if(_13=="loaded"&&_14=="loaded"){
_15();
}
});
function _15(){
_3[_11]="loaded";
easyloader.onProgress(_11);
};
};
function _16(_17,_18){
var p=[];
var _19=false;
if(typeof _17=="string"){
add(_17);
}else{
for(var i=0;i<_17.length;i++){
add(_17[i]);
}
}
function add(_1a){
if(!_1[_1a]){
return;
}
var d=_1[_1a]["dependencies"];
if(d){
for(var i=0;i<d.length;i++){
add(d[i]);
}
}
p.push(_1a);
if(!_3[_1a]){
_10(_1a);
_19=true;
}
};
function _1b(){
if(_18){
_18();
}
easyloader.onLoad(_17);
};
var _1c=0;
(function(){
var b=true;
for(var i=0;i<p.length;i++){
if(_3[p[i]]=="loading"){
b=false;
break;
}
}
if(b==true){
if(easyloader.locale&&_19==true&&_2[easyloader.locale]){
var url=easyloader.base+"locale/"+_2[easyloader.locale];
_9(url,function(){
_1b();
});
}else{
_1b();
}
}else{
if(_1c<easyloader.timeout){
_1c+=10;
setTimeout(arguments.callee,10);
}
}
})();
};
easyloader={modules:_1,locales:_2,base:".",theme:"gray",css:true,locale:null,timeout:2000,load:function(_1d,_1e){
if(/\.css$/i.test(_1d)){
if(/^http/i.test(_1d)){
_c(_1d,_1e);
}else{
_c(easyloader.base+_1d,_1e);
}
}else{
if(/\.js$/i.test(_1d)){
if(/^http/i.test(_1d)){
_4(_1d,_1e);
}else{
_4(easyloader.base+_1d,_1e);
}
}else{
_16(_1d,_1e);
}
}
},onProgress:function(_1f){
},onLoad:function(_20){
}};
var _21=document.getElementsByTagName("script");
for(var i=0;i<_21.length;i++){
var src=_21[i].src;
if(!src){
continue;
}
var m=src.match(/easyloader\.js(\W|$)/i);
if(m){
easyloader.base=src.substring(0,m.index);
}
}
window.using=easyloader.load;
if(window.jQuery){
jQuery(function(){
easyloader.load("parser");
});
}
})();

