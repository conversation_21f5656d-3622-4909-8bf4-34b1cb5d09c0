@CHARSET "utf-8";
body{font-size:12px; padding:0px; margin:5px;overflow-x:hidden;overflow-y:auto; font-family:tahoma, arial, verdana, sans-serif;
background-color:#fbfbfb;
scrollbar-face-color:#cbd2c5;
scrollbar-shadow-color:#86947e;
scrollbar-highlight-color:#fff;
scrollbar-3dlight-color:#d5d6d4;
scrollbar-darkshadow-color:#5b6358;
scrollbar-track-color:#edeeec;
scrollbar-arrow-color:#315d0c;
}
/* LAYOUT */
.outertreecontainer{ /*border-right:solid 1px #737573;*/ width:160px;}
.ui-layout-toggler-west-open{ background:transparent url(images/s-toggle-h.gif) left -55px  no-repeat;}
.outertreecontainer {background:#fff}
.ui-layout-toggler-west-closed{ background: transparent url(images/s-toggle-h.gif) left -7px  no-repeat;}
.ui-layout-resizer-west,.modulescontainer{background-color:#e7e3de;}
/* class to make the 'draggable iframe mask' visible */
div.ui-draggable-iframeFix {
	opacity: 0.2 !important;
	filter:	 alpha(opacity=20) !important;
	background-color: #666 !important;
}
.currentuser{color:#1e2718;padding-left:8px;font-size:12px;font-weight:normal;}
.modulescontainer{padding-top:6px; text-align: right;}
.modulescontainer .modulelabel{color:#000; font-weight: normal;}
.modulescontainer .txtModuleID{border:solid 1px #000;border-right:none;width:90px; height:20px;}
.modulescontainer .btnOpenModule{cursor:pointer;background: transparent url(images/go.gif); width:20px;height: 20px;border-width:0px;}
.FF .modulescontainer .btnOpenModule{ position:relative;top:2px; }
.SAFARI .modulescontainer .btnOpenModule{ position:relative;top:-2px; }

.cUser{color:#1f4c86;padding-left:8px;font-size:12px;}
.accontainer .acheader { display: block; text-decoration: none; color:#000; padding:5px 0px 5px 15px; background: #b5d5ff url(images/s-ac.gif) left -87px repeat-x; font-size:13px;color:#204d9f; border-top:solid 1px  #888; border-right:solid 1px  #888;}
.accontainer .acheader:hover {background: #b5d5ff url(images/s-ac.gif) left -10px repeat-x;}
.accontainer a.selected { font-weight: bold; background: #b5d5ff url(images/s-ac.gif) left -10px repeat-x;color: #000;}
.accontainer .acpane {  overflow:auto; border-right:1px solid #737573;border-top:1px solid #737573;}
.accontainer {border-bottom:1px solid #737573;}

.topmenucontainer{height:70px;}
.topBanner{padding:4px;position:relative;background-image:url(images/topback_blue.gif);background-repeat:repeat x;}
.logocontainer .bLinks{position:absolute;right:0px; top:40px;font-size:12px;}
.logocontainer .imgsbar{position:absolute;right:0px; top:14px;padding-right:2px;}
.logocontainer .imgsbar img{cursor:pointer;}
.bLink{background-color:transparent;width:auto; height:16px; border-width:0px;cursor:pointer; color:#28391b;}
.bLinkhover{background-color:transparent;width:auto; height:16px; border-width:0px;cursor:pointer; color:#fff;}
.navbar .navcontainer,.navbar .logocontainer{ display: inline;}
.navbar .navcontainer{float:right;margin-right:10px;}
.navbar .logocontainer{float:left;width:100%;height:62px;background-image:url(images/topbanner.jpg);background-repeat: no-repeat; background-position: left bottom;}
.navbar .navcontainer a {text-decoration: none;color: #fff; display:block; float:left; text-align: center; margin:2px 0px 0px 15px;  padding:2px 0px 2px 0px; width:56px;}
.navbar .navcontainer a:hover{ background: #87bcf8 url(images/module_hoverbg.gif) no-repeat; color:#0b1c76;}
.navbar .navcontainer a  b{display: block; padding:29px 0px 0px 0px; font-weight: normal; background: transparent 14px -192px url(images/s-top-menu.gif) no-repeat;}
.navbar .navcontainer .bt_xx  b{ background-position: 14px -84px;}
.navbar .navcontainer .bt_db  b{background-position: 14px -246px;}
.navbar .navcontainer .bt_lb  b{ background-position: 14px -300px;}
.navbar .navcontainer .bt_close  b{background-position: 14px -138px;}
.navbar .navcontainer .bt_help  b{ background-position: 14px -30px;}


/* CONTEXT MENU */
.tree .context {display:none;position:absolute;list-style-type:none;margin:0;padding:0;}
.tree .context .separator {display:none;}
.tree .context a {display:block;margin:0;padding:0;}

/* TREE LAYOUT  ENDS*/

/*  CONTEXT MENU */
#contextmenu {position: absolute; z-index: 2222222;display: none; border:solid 1px #c6c7c6; background: #fff none 0px 0px repeat-y;}
#contextmenu a { display: block; text-decoration: none; color:#000;  border:solid 1px #f0f0f0; padding:3px; margin:2px;}
#contextmenu a  span{line-height: 20px; padding:4px 1px;}
#contextmenu a:hover {  border:solid 1px #c6c7c6; background: #dedfd6 none top left repeat-y;}
#contextmenu .open span{padding-left: 24px;background: transparent url(images/s-buttons.gif)  0 -1414px no-repeat;}
#contextmenu .addfavorite span{padding-left: 24px; background: transparent url(images/s-buttons.gif) -166px -1180px no-repeat;}
#contextmenu .removefavorite span{padding-left: 24px; background: transparent url(images/s-buttons.gif)  0 -1388px no-repeat;}
#contextmenu .closeMenu span{padding-left: 24px; background: transparent url(images/s-buttons.gif)  -166px -1050px no-repeat;}
#contextmenu .add span{padding-left: 24px; background: transparent url(images/s-buttons.gif)   0 -1570px no-repeat;}
#contextmenu .edit span{padding-left: 24px; background: transparent url(images/s-buttons.gif)  -166px -738px  no-repeat;}

/* IFRAME CONTAINER & TABS */
.ui_tabs {background:#e7e3de;position: relative;top:0px;left:0px;height:24px; padding-top:6px;}
.ui_tabs ul {padding:1px 10px 0px 20px;}
.ui_tabs li {float:left;background:url(images/tableft.gif) no-repeat left top; margin-right:3px;}
.ui_tabs li a {display:block;background: url(images/tabright.gif) no-repeat right top;margin:0 0 0 4px;padding:5px 40px 5px 12px;text-decoration:none;color:#000000;position:relative;top:0px;left:0px;}
.ui_tabs li a .ui_tab_close{position:absolute; right:8px; top:6px; width:11px; background: transparent url(images/s-tab-close.gif) top left no-repeat; cursor: pointer; display: block; font-size: 10px; padding-bottom:1px; }
.SAFARI .ui_tabs li a .ui_tab_close{height:10px;}
.FF .ui_tabs li a .ui_tab_close{height:10px;}
.ui_tabs li.ui_tab_loading .ui_tab_close{ background: transparent url(images/loading11_11.gif) top left no-repeat;}
#current_tab {background-position:0% -42px;}
#current_tab a {background-position:100% -42px;}
.ui_tabs .ui_toggle_north{ position: absolute; right:10px;  top :6px; width:15px; height:15px; background: transparent url(images/s-toggle.gif) left -15px no-repeat;}
.ui_tabs .ui_toggled{  background: transparent url(images/s-toggle.gif) left top no-repeat;}
#iframecontainer iframe{width:0px; height:0px;}

/* MASK */
.mask{ position: absolute; width:100%; height:100%; top:0px; left:0px;display: none;background-color: #000;Filter: Alpha(Opacity=50); opacity:0.5;-moz-opacity:.5;}

/* POP DIV FOR POPIframe */
.popedDiv{position: absolute; z-index: 101;left:50%; top:40%; border:solid 1px #45629a; background-color: #fff;}
.popedDiv  .popedTitleBar{ padding:5px; padding-left:25px; background:#6f8fa8 url(images/titlebarbg.gif);color: #fff;text-align: left; position: relative;font-weight: bold;cursor: move;}
.popedDiv  .popedTitleBar .pClose{float:right; position: absolute;right: 2px; top:3px; width:18px; height:18px; background:#6f8fa8 url(images/titleclose.gif)}

/* COMMON DIALOG */
.w_all {FILTER: alpha(opacity=80); opacity: 0.8; -moz-opacity:.8; background: #dde9f6 url(images/s-dialog.gif) left -338px no-repeat;}
.rightcorner{ background: #dde9f6 url(images/s-dialog.gif) -73px -379px no-repeat;}
.w_all img{display: none;}
.shadow {FILTER: alpha(opacity=30);opacity: 0.3; -moz-opacity:0.3; LEFT: 0px; WIDTH: 100%; TOP: 0px; HEIGHT: 100%; BACKGROUND-COLOR: #000;}
.shadow {POSITION: absolute;}
.dialog {POSITION: absolute;}
.lrb_corner {background: transparent url(images/s-dialog.gif) left -543px no-repeat; WIDTH: 7px; HEIGHT: 7px}
.moveposition {BACKGROUND-POSITION: -73px -543px}
.b_bg {BACKGROUND: #fff url(images/s-dialog.gif) left -2160px; repeat-x; HEIGHT: 7px}
.t_bg {BACKGROUND: #fff url(images/s-dialog.gif) left 0px; repeat-x;  CURSOR: move; HEIGHT: 31px}
.w_content { overflow: auto; padding: 10px; background-color: white}
.FF .w_content{padding:0px;}
.closediv {RIGHT: 18px; WIDTH: 44px; POSITION: absolute; HEIGHT: 31px;BACKGROUND: transparent url(images/s-dialog.gif) left -420px no-repeat;HEIGHT: 19px;}
.closeimg_over,.closediv:hover {BACKGROUND: transparent url(images/s-dialog.gif) left -439px no-repeat; CURSOR: pointer;}
.r_bg {BACKGROUND: #fff url(images/s-dialog.gif) -73px -1437px no-repeat; WIDTH: 7px}
.w_title {PADDING-LEFT: 30px; FONT-WEIGHT: bold; FONT-SIZE: 12px;BACKGROUND: transparent url(images/s-dialog.gif) 6px -2128px no-repeat; FLOAT: left; LINE-HEIGHT: 31px; HEIGHT: 31px}
.l_bg {BACKGROUND: #fff url(images/s-dialog.gif) 0px -740px no-repeat;WIDTH: 7px}
.w_imgtd {PADDING-LEFT: 100px; FONT-SIZE: 12px; BACKGROUND-REPEAT: no-repeat; HEIGHT: 100px}
.btn {BACKGROUND: #fff url(images/s-dialog.gif) 0px -479px no-repeat; MARGIN: 0px 5px; WIDTH: 67px; BORDER-TOP-STYLE: none; LINE-HEIGHT: 27px; BORDER-RIGHT-STYLE: none; BORDER-LEFT-STYLE: none; HEIGHT: 27px; BORDER-BOTTOM-STYLE: none; color:#000;text-decoration: none;}
.FF .btn {padding:5px 22px;}
.btn_over,.btn:hover {BACKGROUND: #fff url(images/s-dialog.gif) 0px -506px no-repeat;}
.w_alert {BACKGROUND: #fff url(images/s-dialog.gif) 0px -236px no-repeat;}
.w_confirm {BACKGROUND: #fff url(images/s-dialog.gif) 0px -151px no-repeat;}
.w_error {BACKGROUND: #fff url(images/s-dialog.gif) 0px -54px no-repeat;}
.w_ok {BACKGROUND: #fff url(images/s-dialog.gif) 0px -636px no-repeat;}
.w_info {BACKGROUND: #fff url(images/s-dialog.gif) 0px -552px no-repeat;}

.ui-window{border:2px outset #efe7de;position: absolute; background-color: #fff;}
.ui-window .ui-window-title{ padding:4px; font-weight: bold; background: #AFD5FF url(images/windowtitle.gif) top left repeat-y; color:#fff;}
.ui-window .ui-window-title .ui-window-close{float:right; position: relative; right:0px; top:-3px; cursor:pointer; border:2px outset #404040; border-color:#fff #404040 #404040 #fff; background-color: #D4D0C8; color:#000; padding:0px 4px; font-size:13px; font-family: arial;}

/* COMMON OPERATION BUTTON */
.obtn{height:16px; padding-left: 20px; background: transparent url(images/s-buttons.gif) left top no-repeat; text-decoration: none; color:#000; line-height:20px;}
.btnback { background-position: 0 -10px; }
.btnfolder1 { background-position: 0 -36px; }
.btnlink { background-position: 0 -62px; }
.btnclaim { background-position: 0 -88px; }
.btninputinvoice { background-position: 0 -114px; }
.btncut { background-position: 0 -140px; }
.btnreg { background-position: 0 -166px; }
.btnrefresh1 { background-position: 0 -192px; }
.btnabort { background-position: 0 -218px; }
.btnreceivetel { background-position: 0 -244px; }
.btnresume { background-position: 0 -270px; }
.btnselectcustomer { background-position: 0 -296px; }
.btnfind { background-position: 0 -322px; }
.btnhtmldoc { background-position: 0 -348px; }
.btnreply { background-position: 0 -374px; }
.btncheck1 { background-position: 0 -400px; }
.btnstation { background-position: 0 -426px; }
.btnview { background-position: 0 -452px; }
.btnadd { background-position: 0 -478px; }
.btnreplace_bat { background-position: 0 -504px; }
.btnposition { background-position: 0 -530px; }
.btnunactive { background-position: 0 -556px; }
.btnadd_user { background-position: 0 -582px; }
.btnexchange { background-position: 0 -608px; }
.btncolumns { background-position: 0 -634px; }
.btndelete { background-position: 0 -660px; }
.btnlastcheck { background-position: 0 -686px; }
.btndel { background-position: 0 -712px; }
.btnpublish { background-position: 0 -738px; }
.btncheck { background-position: 0 -764px; }
.btncancel { background-position: 0 -790px; }
.btnfax { background-position: 0 -816px; }
.btnsum { background-position: 0 -842px; }
.btnoutfit { background-position: 0 -868px; }
.btnclearinvoice { background-position: 0 -894px; }
.btncopy1 { background-position: 0 -920px; }
.btnhistory { background-position: 0 -946px; }
.btnaddtel { background-position: 0 -972px; }
.btnstart { background-position: 0 -998px; }
.btnpartnum { background-position: 0 -1024px; }
.btndistrace { background-position: 0 -1050px; }
.btnpartdetail { background-position: 0 -1076px; }
.btnpws_msg { background-position: 0 -1102px; }
.btndept { background-position: 0 -1128px; }
.btnnewmsg { background-position: 0 -1154px; }
.btnoptions { background-position: 0 -1180px; }
.btnpermissions { background-position: 0 -1206px; }
.btnsave { background-position: 0 -1232px; }
.btnfirstcheck { background-position: 0 -1258px; }
.btnbt_qj { background-position: 0 -1284px; }
.btndown { background-position: 0 -1310px; }
.btnsend { background-position: 0 -1336px; }
.btnrefresh { background-position: 0 -1362px; }
.btnremove { background-position: 0 -1388px; }
.btnretract { background-position: 0 -1414px; }
.btntrace { background-position: 0 -1440px; }
.btnsendclaimparts { background-position: 0 -1466px; }
.btnenrol { background-position: 0 -1492px; }
.btnprint { background-position: 0 -1518px; }
.btnattache { background-position: 0 -1544px; }
.btncreate { background-position: 0 -1570px; }
.btndownload { background-position: 0 -1596px; }
.btncustomer { background-position: 0 -1622px; }
.btnnew { background-position: 0 -1648px; }
.btntel { background-position: 0 -1674px; }
.btntop { background-position: 0 -1700px; }
.btnaddtocase { background-position: 0 -1726px; }
.btnpws_task { background-position: 0 -1752px; }
.btnpage { background-position: 0 -1778px; }
.btnw_icon { background-position: 0 -1804px; }
.btnoutdepotdetail { background-position: 0 -1830px; }
.btnf { background-position: 0 -1856px; }
.btncancelsend { background-position: 0 -1882px; }
.btnconfirmreceivepayment { background-position: 0 -1908px; }
.btnoldmsg { background-position: 0 -1934px; }
.btncancelconfirm { background-position: 0 -1960px; }
.btnaddtask { background-position: 0 -1986px; }
.btnbalance { background-position: -166px -10px; }
.btnmove { background-position: -166px -36px; }
.btnchart { background-position: -166px -62px; }
.btnpaste { background-position: -166px -88px; }
.btnmobile { background-position: -166px -114px; }
.btnfile { background-position: -166px -140px; }
.btnup { background-position: -166px -166px; }
.btnprice { background-position: -166px -192px; }
.btnmovetocompany { background-position: -166px -218px; }
.btnaddcustomer { background-position: -166px -244px; }
.btnselect { background-position: -166px -270px; }
.btnmingxi { background-position: -166px -296px; }
.btnhuifu { background-position: -166px -322px; }
.btnshouquan { background-position: -166px -348px; }
.btnnotepad { background-position: -166px -374px; }
.btnstop { background-position: -166px -400px; }
.btnundo { background-position: -166px -426px; }
.btnsearch { background-position: -166px -452px; }
.btnfolder { background-position: -166px -478px; }
.btnpicture { background-position: -166px -504px; }
.btnmodify { background-position: -166px -530px; }
.btncancellastcheck { background-position: -166px -556px; }
.btntoproot { background-position: -166px -582px; }
.btnbottom { background-position: -166px -608px; }
.btnhavereplymsg { background-position: -166px -634px; }
.btnprovince { background-position: -166px -660px; }
.btnforward { background-position: -166px -686px; }
.btnmerge { background-position: -166px -712px; }
.btnedit { background-position: -166px -738px; }
.btnfolder2 { background-position: -166px -764px; }
.btnperson { background-position: -166px -790px; }
.btnthrobber { background-position: -166px -816px; }
.btndelete1 { background-position: -166px -842px; }
.btnalertjudge { background-position: -166px -868px; }
.btnbt_ht { background-position: -166px -894px; }
.btnunreg { background-position: -166px -920px; }
.btnbt_sx { background-position: -166px -946px; }
.btnmoveperson { background-position: -166px -972px; }
.btncopy { background-position: -166px -998px; }
.btnindepotdetail { background-position: -166px -1024px; }
.btndeletealertjudge { background-position: -166px -1050px; }
.btncompanycustomer { background-position: -166px -1076px; }
.btnfolderopen { background-position: -166px -1102px; }
.btndetail { background-position: -166px -1128px; }
.btndelete2 { background-position: -166px -1154px; }
.btnsaveadd { background-position: -166px -1180px; }
.btnstatechange { background-position: -166px -1206px; }
.btnsendparts { background-position: -166px -1232px; }
.btnjiesuan { background-position: -166px -1258px; }
.btnreplace { background-position: -166px -1284px; }
.btnguihuan { background-position: -166px -1310px; }
.btnclosecase { background-position: -166px -1336px; }
.btnwarning { background-position: -166px -1362px; }
.btnalert { background-position: -166px -1388px; }
.btngrade { background-position: -166px -1414px; }
.btnconfirm { background-position: -166px -1440px; }

/* REGION CONTAINER LAYOUT */
/* rc :Region container default 2 columns*/
.rc,.rc2,rc3{clear: both;}
.rc .region,.rc2  .region,.rc3 .region{float:left; width:49.9%; border-width:0px; padding:1px; }
.FF .rc .region,.FF .rc2 .region,.FF .rc3 .region{width:49.7%; }
.rc3 .region{width:33.3%;}
iframe{clear: both;}

/* FORM GRID LAYOUT */
.formgrid {width:100%; margin:2px 0px;}
.formgrid div{ width:33%;float:left;}
.formgrid div input{ width:160px;}
.formgrid div select{width:166px;}
.formgrid .ui_q_time input{ width:71px;}
.formgrid .ui_q_right{float:right; text-align:right;}
.formgrid div label{ width:80px; display:block; float:left; text-align:right;line-height:22px;}

/* FIELDSET */
fieldset{ border:1px solid #c7cdc2;clear:both; background-color: #fbfbfb;}
legend{ padding:3px; font-weight: bold; /* border:1px solid #d9d9d9;display:block; padding:5px; margin-bottom:2px; margin-left:-8px;margin-right:-8px; position: relative; z-index:102;*/ }
fieldset ul {width:100%; margin:2px 0px;}
/*fieldset select { margin:1px 0px;}
.FF fieldset select { margin:0px;}*/

/* css3 hack */
body:nth-of-type(1) fieldset select {
	margin:1px 0px;
}
* html fieldset select {margin-top:1px;}/*IE 6 hack*/
/*DEFAULT AS 3 COLUMNS*/
fieldset ul li { width:33.3%;float:left; margin-bottom:2px;height:27px;}/*DEFAULT 3 COLUMNS ,ORVERRIDE TO CHANGE COLUMN NUMBERS*/
fieldset ul .cs2{width:66.6%;height:27px;}/*col span 2 */
fieldset ul li label {width:100px;height:27px;}
fieldset .c3 li {width:33.3%;height:27px;}
fieldset .c3 li label {width:100px;height:27px;}
fieldset .c3 .csr{width:99.5%;}/* col span whole ROW*/
fieldset .c3 .cs2{width:66.6%;height:27px;}/*col span 2 */
/* 2 columns*/
fieldset .c2 li {width:49.5%;height:27px;}
fieldset .c2 li label {width:120px;height:27px;}
fieldset .c2 .csr{width:99.5%;}/* col span whole ROW*/
/* 4 columns*/
fieldset .c4 li {width:24.5%;height:27px;}
fieldset .c4 .cs2{width:49.5%;height:27px;}/*col span 2 */
fieldset .c4 .cs3{width:74.5%;height:27px;}/*col span 3 */
/* 1 column only*/
fieldset .c1 li {width:100%;}
fieldset ul .csr{width:99.5%;}/* col span whole ROW*/
fieldset ul .csr textarea{width:70%; font-family: tahoma, arial, verdana, sans-serif;}
fieldset ul li input([@type=text){ width:71px;}
fieldset ul li select{width:160px;}
fieldset .ui_q_time input{ width:74px;}
fieldset .ui_q_right{float:right; text-align:right;padding-right:4px;}
fieldset .ui_q_left{float:left; text-align:left;padding-left:4px;}
fieldset ul li label{ width:80px; display:inline-block;text-align:right;line-height:22px; background: transparent url(images/maohao.gif) -72px right no-repeat; padding-right:8px;}
fieldset ul li span{line-height:22px; padding: 4px 8px 4px 0px ;}
fieldset ul .required{background: transparent url(images/maohao.gif) 98% -102px no-repeat;}
fieldset .l120 .required{background: transparent url(images/maohao.gif) right -102px no-repeat;}

fieldset .l100 li label,fieldset .l100 li  input,fieldset .l100 li select{ width:100px;}
fieldset .l120 li label{ width:120px;}
fieldset .l120 li  input,fieldset .l120 li select{ width:100px;}

fieldset ul .ui_q_radio input { width:auto;display:block; float:left; }
fieldset ul .ui_q_radio label {width:auto; background-image: none;  }
fieldset ul .ui_q_radio label:first-child {width:80px;  background: transparent url(images/maohao.gif) -72px right no-repeat;  }
* html fieldset ul .ui_q_radio label  {width:expression(this.previousSibling==null?'80px':'auto');  }/*IE 6 hack*/

.readonly{background-color: #ddd9cd;}

/* MULTI ROW SUBMIT*/
.mrtemplate{display: none;}

/* TABLE FORM ELEMENT LAYOUT (DEPRECATED)  */
.ui-table1{ border:solid 1px #abb6a0; width:100%; margin-bottom:2px;}
.ui-table1 td ,.ui-table1 th{border:#abb6a0 solid;border-width:0 1 1 0; padding:5px;}
.ui-table1 th{background-image:url(images/headerOverBg.gif);background-repeat:repeat x;color:#000; font-weight:normal;}
.ui-table1 caption{ padding:6px; text-align: left; font-weight:bold; border:solid 1px #abb6a0;color:#666; }
.ui-table1  tfoot td{text-align: right;}

.ui-table2{ border:solid 1px #abb6a0; width:100%; margin-bottom:2px;}
.ui-table2 td ,.ui-table2 th{border:#abb6a0 solid;border-width:0 1 1 0; padding:5px;}
.ui-table2 th{background-color: #efe7de; color:#000; text-align: right;font-weight:normal;}
.ui-table2 caption{ padding:6px; background-color:#efe7de;text-align: left; font-weight:bold; border:solid 1px #abb6a0;color:#666; }


/* BUTTON
.sysBtn{ border:solid 1px #000;cursor:pointer; font-size:12px; background-color: #dedbce; margin-right:4px;width:auto!important; height:20px; }
*/
.sysBtn {height:25;
BORDER-RIGHT: #abb6a0 1px solid; PADDING-RIGHT: 2px; BORDER-TOP:
#7b9ebd 1px solid; PADDING-LEFT: 2px; FONT-SIZE: 12px; FILTER:
progid:DXImageTransform.Microsoft.Gradient(GradientType=0,
StartColorStr=#ffffff, EndColorStr=#cecfde); BORDER-LEFT: #abb6a0
1px solid; CURSOR: hand; COLOR: black; PADDING-TOP: 2px;
BORDER-BOTTOM: #abb6a0 1px solid}
.IE .sysBtn{width:auto;}
fieldset .sysBtn{width:auto;}

/* AJAX LOADING BAR */
#mask,.mask{ position: absolute; width:100%; height:100%; top:0px; left:0px;z-index: 100;display: none;Filter: Alpha(Opacity=0);  opacity:0;}
#loadingbar,#loadingbarcontainer{width:160px; height:40px;}
#loadingbarcontainer{margin:-35px 0 0 -80px;background-color: #aaa; z-index: 101;position: absolute;top:40%; left: 50%;display: none;}
#loadingbar{
	border:solid 1px #888;
	position: relative;
	top: -2px;
	left: -2px;
	padding: 0px 0px 0px 50px;
	background-image: url(images/loading32_32.gif);
	background-repeat:no-repeat;
	background-position:  4px center;
	z-index: 101;
	background-color:#fff;
	line-height: 40px;
	text-align: left;
	font-size: 14px;
}
/* BAR */
.titlebar{text-align: right; background-color: #fff; }
.titlebar2{padding:4px; background-color: #fff;font-size:14px;  font-weight:bold;}
.titlebar b{font-size:14px; padding-top:4px;}
.toolbar{text-align: right; padding:1px; margin:1px 0px;border:1px solid #abb6a0; background-image:url(images/footerBg.gif);background-repeat:repeat x; color:#000;}
.binfo,.bwarn,.bhelp,.berror{ background:  #fffecc url(images/binfo.gif) 4px 4px no-repeat; padding:4px; margin:2px 0px; padding-left:24px;border:1px solid #b2b04b; color:#894808; line-height:16px;}
.bwarn{  background:   #ffffa2 url(images/bwarn.gif) 4px 4px no-repeat; border:solid 1px #800000;}
.bhelp{ background:   #f7f7ff url(images/bhelp.gif) 4px 4px no-repeat;}
.berror{ background:   yellow url(images/berror.gif) 4px 4px no-repeat; border:solid 1px #ff3800;}
.binfo li,.bwarn li,.bhelp li,.berror li{margin-bottom:4px;}


/* TOOL TIP */
#tooltip {position: absolute;z-index: 3000;border: 1px solid #111;background-color: #eee;padding: 5px;opacity: 0.85;width:200px;line-height: 20px;}
#tooltip h3, #tooltip div { margin: 0; }
/* AUTOCOMPLETE */
.ac_results {padding: 0px;border: 1px solid WindowFrame;background-color: Window;overflow: hidden; z-index: 100000}
.ac_results ul {width: 100%;list-style-position: outside;list-style: none;padding: 0;margin: 0;}
.ac_results iframe {
	display:none;/*sorry for IE5*/
	display/**/:block;/*sorry for IE5*/
	position:absolute;top:0;left:0;z-index:-1;filter:mask();width:3000px;height:3000px;}

.ac_results li {margin: 0px;padding: 2px 5px;cursor: pointer;display: block;width: 100%;font: menu;font-size: 12px;overflow: hidden;}
.ac_loading {}
.ac_over {background-color: Highlight;color: HighlightText;}


/* TIP WINDOW  */
.tip{position:absolute; left:10px; top:50px; width:200px; text-align:center;z-index: 120; font-size: 11px;}
.tip .t_header{position:absolute; left:40%; top:0px;margin-left:-9px;  width:17px; height:12px; line-height:12px; background:transparent url(images/t_arrow.gif) fixed bottom center no-repeat ; z-index: 121; }
.tip .t_content{border:1px solid #ff0000; margin-top:9px; background-color: #ffff00;position: relative;}
.tip .t_title{text-align: left;}
.tip textarea ,.tip input{font-size: 11px; }
.tip textarea{border:1px solid #000; width:198px;height: 60px;}

/*button*/
.z-btn{display:inline;font:normal 11px tahoma,verdana,helvetica;cursor:pointer;white-space:nowrap;width: auto;}
.z-btn button{border:0 none;background:transparent;FONT-SIZE: 12px; font-family: Arial,Courier New;padding-left:0px;padding-right:0px;margin:0;overflow:visible;width:auto;-moz-outline:0 none;outline:0 none;}
.z-btn-left,.z-btn-right{font-size:1px;line-height:1px;}
.z-btn-left{width:3px;height:25px;background:url(images/btnout_bg_left.gif) no-repeat 0 0;}
.z-btn-right{width:3px;height:25px;background:url(images/btnout_bg_right.gif) no-repeat -7px 0px;}
.z-btn-left i,.z-btn-right i{display:block;width:3px;overflow:hidden;font-size:1px;line-height:1px;}

.z-btn .z-btn-center{background:url(images/btnout_bg_center.gif) no-repeat 0px 0px;vertical-align:middle;text-align:center;padding:0 6px 0 3px;white-space:nowrap;}
.z-btn em{font-style:normal;font-weight:normal;}
.z-btn .z-btn-center .z-btn-text{background-position:0 1px;background-repeat:no-repeat;padding-left:18px;padding-top:2px;padding-bottom:0px;padding-right:0;+width:0;-width:0;}

.z-btn-over .z-btn-left{background:url(images/btnover_bg_left.gif) no-repeat 0 0;}
.z-btn-over .z-btn-right{background:url(images/btnover_bg_right.gif) no-repeat -9px 0;}
.z-btn-over .z-btn-center{background:url(images/btnover_bg_center.gif) no-repeat 0px 0px;}

.z-btn-dsb { opacity:.40;-moz-opacity:.40;filter:alpha(opacity=40);}
.z-btn-dsb .z-btn-left{background:url(images/btnDsb_bg_left.gif) no-repeat 0 0;opacity:.35;-moz-opacity:.35;filter:alpha(opacity=35);}
.z-btn-dsb .z-btn-right{background:url(images/btnDsb_bg_right.gif) no-repeat -9px 0;}
.z-btn-dsb .z-btn-center{background:url(images/btnDsb_bg_center.gif) no-repeat 0px 0px;}


.fixedHeaderTr{
	Z-INDEX: 10; BACKGROUND: #d4d0c8; POSITION: relative; ; TOP: expression(this.offsetParent.scrollTop)
}
.relativeTag{
	BACKGROUND: #ffffff; POSITION: relative;
}
.fixcontent{
	BORDER-RIGHT: #000000 1px solid; PADDING-RIGHT: 0px; BORDER-TOP: #000000 1px solid; PADDING-LEFT: 0px; FONT-SIZE: 12px; BACKGROUND: #d4d0c8; PADDING-BOTTOM: 0px; BORDER-LEFT: #ffffff 1px solid; PADDING-TOP: 0px; BORDER-BOTTOM: #ffffff 1px solid
}
.scrollcontent{
	BORDER-RIGHT: #000000 1px solid; PADDING-RIGHT: 3px; BORDER-TOP: #000000 1px solid; PADDING-LEFT: 3px; BACKGROUND: #ffffff; PADDING-BOTTOM: 2px; BORDER-LEFT: #ffffff 1px solid; PADDING-TOP: 2px; BORDER-BOTTOM: #ffffff 1px solid
}
.fixedHeaderCol{
	BORDER-RIGHT: #000000 1px solid; PADDING-RIGHT: 3px; BORDER-TOP: #000000 1px solid; PADDING-LEFT: 3px; FONT-SIZE: 12px; BACKGROUND: #d4d0c8; ; LEFT: expression(this.parentElement.offsetParent.scrollLeft); PADDING-BOTTOM: 2px; BORDER-LEFT: #ffffff 1px solid; PADDING-TOP: 2px; BORDER-BOTTOM: #ffffff 1px solid; POSITION: relative
}

#top,#main,#bottom{
margin:0px auto;
border:0px solid #CC6666;
}
#top{
margin-left:5px;
margin-top:5px;
margin-bottom:5px;
}
#main{
padding:5px 0px 5px 0px;
}
#bottom{
height:50px;
margin-top:5px;
}
#main .column1{
margin-left:4px;
}
#main .column2{
margin-left:5px;
margin-right:5px;
}
#main .widget{
border:1px solid #99BBE8;
margin-bottom:5px;
}
#main .widget-header{
line-height:25px;
border:1px solid #99BBE8;
}
#main .widget-header a{
margin-left:8px;
}
.widget_win{
divne-height:30px;
background-color:#E0ECFF;
}
.widget-placeholder {
border:1px dashed #FF99CC;
}
#userinfo_edit {
	padding: 0px 7px 10px 5px;
	color:#343433;
	font-weight:bold;
}

#userinfo_edit legend {
	color:#08428d;
}

.pageTblName {
	background:#e4f0dc;
	text-align:right;
}

.sysBtn2 {height:25;
BORDER-RIGHT: #7b9ebd 1px solid; PADDING-RIGHT: 2px; BORDER-TOP:
#7b9ebd 1px solid; PADDING-LEFT: 2px; FONT-SIZE: 12px; FILTER:
progid:DXImageTransform.Microsoft.Gradient(GradientType=0,
StartColorStr=#ffffff, EndColorStr=#cecfde); BORDER-LEFT: #7b9ebd
1px solid; CURSOR: hand; COLOR: black; PADDING-TOP: 2px;
BORDER-BOTTOM: #7b9ebd 1px solid}
.IE .sysBtn2{width:auto;}
fieldset .sysBtn2{width:auto;}