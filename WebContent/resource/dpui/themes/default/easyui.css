.accordion{
	background:#fff;
	overflow:hidden;
}
.accordion .accordion-header{
	background:#E0ECFF;
	border-top-width:0;
	cursor:pointer;
}
.accordion .accordion-header .panel-title{
	font-weight:normal;
}
.accordion .accordion-header-selected .panel-title{
	font-weight:bold;
}
.accordion-noborder .accordion-header{
	border-width:0 0 1px;
}
.accordion-noborder .accordion-body{
	border-width:0px;
}.calendar{
	background:#fff;
	border:1px solid #A4BED4;
	padding:1px;
	overflow:hidden;
}
.calendar-noborder{
	border:0px;
}
.calendar-header{
	position:relative;
	background:#E0ECFF;
	font-size:12px;
	height:22px;
}
.calendar-title{
	text-align:center;
	height:22px;
}
.calendar-title span{
	position:relative;
	top:4px;
	display:incline-block;
	padding:3px;
	cursor:pointer;
	-moz-border-radius:4px;
	-webkit-border-radius:4px;
}
.calendar-prevmonth,.calendar-nextmonth,.calendar-prevyear,.calendar-nextyear{
	position:absolute;
	top:4px;
	width:14px;
	height:14px;
	line-height:12px;
	cursor:pointer;
	font-size:1px;
	-moz-border-radius:4px;
	-webkit-border-radius:4px;
}
.calendar-prevmonth{
	left:20px;
	background:url('images/calendar_prevmonth.gif') no-repeat 3px 2px;
}
.calendar-nextmonth{
	right:20px;
	background:url('images/calendar_nextmonth.gif') no-repeat 3px 2px;
}
.calendar-prevyear{
	left:3px;
	background:url('images/calendar_prevyear.gif') no-repeat 1px 2px;
}
.calendar-nextyear{
	right:3px;
	background:url('images/calendar_nextyear.gif') no-repeat 1px 2px;
}
.calendar-body{
	font-size:12px;
	position:relative;
}
.calendar-body table{
	width:100%;
	height:100%;
	border:1px solid #eee;
	font-size:12px;
	padding1:5px;
}
.calendar-body th,.calendar-body td{
	text-align:center;
}
.calendar-body th{
	background:#fafafa;
	color:#888;
	border-bottom1:1px solid #ccc;
}
.calendar-day{
	color:#222;
	cursor:pointer;
	border:1px solid #fff;
	-moz-border-radius:4px;
	-webkit-border-radius:4px;
}
.calendar-sunday{
	color:#CC2222;
}
.calendar-saturday{
	color:#00ee00;
}
.calendar-today{
	color:#0000ff;
}
.calendar-other-month{
	opacity:0.3;
	filter:alpha(opacity=30);
}
.calendar-hover{
	border:1px solid red;
}
.calendar-selected{
	background:#FBEC88;
	border:1px solid red;
}
.calendar-nav-hover{
	background-color:#FBEC88;
}


.calendar-menu{
	position:absolute;
	top:0px;
	left:0px;
	width:180px;
	height:150px;
	padding:5px;
	font-size:12px;
	background:#fafafa;
	opacity:0.8;
	filter:alpha(opacity=80);
	display:none;
}
.calendar-menu-year-inner{
	text-align:center;
	padding-bottom:5px;
}
.calendar-menu-year{
	width:40px;
	text-align:center;
	border:1px solid #ccc;
	padding:2px;
	font-weight:bold;
}
.calendar-menu-prev,.calendar-menu-next{
	display:inline-block;
	width:21px;
	height:21px;
	vertical-align:top;
	cursor:pointer;
}
.calendar-menu-prev{
	margin-right:10px;
	background:url('images/calendar_prevyear.gif') no-repeat 5px 6px;
}	
.calendar-menu-next{
	margin-left:10px;
	background:url('images/calendar_nextyear.gif') no-repeat 5px 6px;
}
.calendar-menu-hover{
	background-color:#FBEC88;
}
.calendar-menu-month-inner table{
	width:100%;
	height:100%;
}
.calendar-menu-month{
	text-align:center;
	cursor:pointer;
	border:1px solid #fafafa;
	font-weight:bold;
	color:#666;
	-moz-border-radius:4px;
	-webkit-border-radius:4px;
}
.combo{
	display:inline-block;
	white-space:nowrap;
	font-size:12px;
	margin:0;
	padding:0;
	border:1px solid #A4BED4;
}
.combo-text{
	font-size:12px;
	border:0px;
	line-height:20px;
	height:20px;
	padding:0px;
	*height:18px;
	*line-height:18px;
	_height:18px;
	_line-height:18px;
}
.combo-arrow{
	background:#E0ECF9 url('images/combo_arrow.gif') no-repeat 3px 4px;
	width:18px;
	height:20px;
	overflow:hidden;
	display:inline-block;
	vertical-align:top;
	cursor:pointer;
	opacity:0.6;
	filter:alpha(opacity=60);
}
.combo-arrow-hover{
	opacity:1.0;
	filter:alpha(opacity=100);
}
.combo-panel{
	background:#fff;
	overflow:auto;
}
.combobox-item{
	padding:2px;
	font-size:12px;
	padding:3px;
	padding-right:0px;
}
.combobox-item-hover{
	background:#fafafa;
}
.combobox-item-selected{
	background:#FBEC88;
}.datagrid .panel-body{
	overflow:hidden;
}
.datagrid-wrap{
	position:relative;
}
.datagrid-view{
	position:relative;
	overflow:hidden;
}
.datagrid-view1{
	position:absolute;
	overflow:hidden;
	left:0px;
	top:0px;
	border-right1:1px solid #ccc;
}
.datagrid-view2{
	position:absolute;
	overflow:hidden;
	left:210px;
	top:0px;
}
.datagrid-mask{
	position:absolute;
	left:0;
	top:0;
	background:#ccc;
	opacity:0.3;
	filter:alpha(opacity=30);
	display:none;
}
.datagrid-mask-msg{
	position:absolute;
	cursor1:wait;
	left:100px;
	top:50px;
	width:auto;
	height:16px;
	padding:12px 5px 10px 30px;
	background:#fff url('images/pagination_loading.gif') no-repeat scroll 5px 10px;
	border:2px solid #6593CF;
	color:#222;
	display:none;
}
.datagrid-title{
	background:url('images/datagrid_title_bg.png') repeat-x;
	border-bottom:1px solid #8DB2E3;
	border-top:1px solid #fff;
	position:relative;
	padding:5px 0px;
}
.datagrid-title-text{
	color:#15428b;
	font-weight:bold;
	padding-left:5px;
}
.datagrid-title-with-icon{
	padding-left:22px;
}
.datagrid-title-icon{
	position:absolute;
	width:16px;
	height:16px;
	left:3px;
	top:4px!important;
	top:6px;
}
.datagrid-sort-desc .datagrid-sort-icon{
	padding:2px 13px 3px 0px;
	background:url('images/datagrid_sort_desc.gif') no-repeat center center;
}
.datagrid-sort-asc .datagrid-sort-icon{
	padding:2px 13px 3px 0px;
	background:url('images/datagrid_sort_asc.gif') no-repeat center center;
}
.datagrid-toolbar{
	height:28px;
	background:#efefef;
	padding:1px 2px;
	border-bottom:1px solid #ccc;
}
.datagrid-btn-separator{
	float:left;
	height:24px;
	border-left:1px solid #ccc;
	border-right:1px solid #fff;
	margin:2px 1px;
}
.datagrid-pager{
	background:#efefef;
	border-top:1px solid #ccc;
	position:relative;
}

.datagrid-header{
	overflow:hidden;
	background:#fafafa url('images/datagrid_header_bg.gif') repeat-x left bottom;
	border-bottom:1px solid #ccc;
	margin-bottom:1px;
}
.datagrid-header-inner{
	float:left;
	padding-right:20px;
	margin-bottom:-1px;
}
.datagrid-header td{
	border-right:1px dotted #ccc;
	font-size:12px;
	font-weight:normal;
	background:#fafafa url('images/datagrid_header_bg.gif') repeat-x left bottom;
	border-bottom:1px dotted #ccc;
	border-top:1px dotted #fff;
}
.datagrid-header td.datagrid-header-over{
	background:#EBF3FD;
}
.datagrid-header .datagrid-cell{
	margin:0;
	padding:3px 4px;
	white-space:nowrap;
	word-wrap:normal;
	overflow:hidden;
	text-align:center;
}
.datagrid-header .datagrid-cell-group{
	margin:0;
	padding:4px 2px 4px 4px;
	white-space:nowrap;
	word-wrap:normal;
	overflow:hidden;
	text-align:center;
}
.datagrid-header-rownumber{
	width:25px;
	text-align:center;
	margin:0px;
	padding:3px 0px;
}
.datagrid-td-rownumber{
	background:#fafafa url('images/datagrid_header_bg.gif') repeat-x left bottom;
}
.datagrid-cell-rownumber{
	width:25px;
	text-align:center;
	margin:0px;
	padding:3px 0px;
}
.datagrid-body{
	margin:0;
	padding:0;
	overflow:auto;
	zoom:1;
}
.datagrid-view1 .datagrid-body-inner{
	padding-bottom:20px;
}
.datagrid-view1 .datagrid-body{
	overflow:hidden;
}
.datagrid-body td{
	font-size:12px;
	border-right:1px dotted #ccc;
	border-bottom:1px dotted #ccc;
	overflow:hidden;
	padding:0;
	margin:0;
}
.datagrid-body .datagrid-cell{
	overflow:hidden;
	margin:0;
	padding:3px 4px;
	white-space:nowrap;
	word-wrap:normal;
}
.datagrid-header-check{
	padding:3px 6px;
}
.datagrid-cell-check{
	padding:3px 6px;
}
.datagrid-header-check input{
	margin:0;
	padding:0;
	width:15px;
	height:15px;
}
.datagrid-cell-check input{
	margin:0;
	padding:0;
	width:15px;
	height:15px;
}
.datagrid-row-alt{
	background:#EEEEFF;
}
.datagrid-row-over{
	background:#D0E5F5;
	background1:#FBEC88;
	cursor:default;
}
.datagrid-row-selected{
	background:#FBEC88;
}
.datagrid-resize-proxy{
	position:absolute;
	width:1px;
	top:0;
	height:10000px;
	background:red;
	cursor:e-resize;
	display:none;
}
.datagrid-body .datagrid-editable{
	padding:0;
}
.datagrid-body .datagrid-editable table{
	width:100%;
	height:100%;
}
.datagrid-body .datagrid-editable td{
	border:0;
	padding:0;
}
.datagrid-body .datagrid-editable .datagrid-editable-input{
	width:100%;
	font-size:12px;
	border:1px solid #A4BED4;
	padding:3px 2px;
}
.datebox-calendar{
	position:absolute;
	border:1px solid #A4BED4;
	width:180px;
}
.datebox-calendar-inner{
	height:180px;
}
.datebox-button{
	height:18px;
	padding:2px 5px;
	font-size:12px;
	background-color:#fafafa;
}
.datebox-current,.datebox-close{
	float:left;
	color:#888;
	text-decoration:none;
	font-weight:bold;
}
.datebox-close{
	float:right;
}
.datebox-button-hover{
	color:#A4BED4;
}
.dialog-content{
	overflow:auto;
}
.dialog-toolbar{
	background:#fafafa;
	padding:2px 5px;
	border-bottom:1px solid #eee;
}
.dialog-tool-separator{
	float:left;
	height:24px;
	border-left:1px solid #ccc;
	border-right:1px solid #fff;
	margin:2px 1px;
}
.layout{
	position:relative;
	overflow:hidden;
	margin:0;
	padding:0;
}
.layout-panel{
	position:absolute;
	overflow:hidden;
}
.layout-panel-east,.layout-panel-west{
	z-index:2;
	background1:#fff;
}
.layout-panel-north,.layout-panel-south{
	z-index:3;
	background1:#fff;
}
.layout-button-up{
	background:url('images/layout_button_up.gif') no-repeat;
}
.layout-button-down{
	background:url('images/layout_button_down.gif') no-repeat;
}
.layout-button-left{
	background:url('images/layout_button_left.gif') no-repeat;
}
.layout-button-right{
	background:url('images/layout_button_right.gif') no-repeat;
}
.layout-expand{
	position:absolute;
	padding:0px 5px;
	padding:0px;
	background:#D2E0F2;
	font-size:1px;
	cursor:pointer;
	z-index:1;
}
.layout-expand .panel-header{
	background:transparent;
	border-bottom-width:0px;
}
.layout-expand .panel-header .panel-tool{
	top: 5px;
}
.layout-expand .panel-body{
	overflow:hidden;
}
.layout-expand-over{
	background:#E1F0F2;
}
.layout-body{
	overflow:auto;
	background:#fff;
}
.layout-split-proxy-h{
	position:absolute;
	width:5px;
	background:#ccc;
	font-size:1px;
	cursor:e-resize;
	display:none;
	z-index:5;
}
.layout-split-proxy-v{
	position:absolute;
	height:5px;
	background:#ccc;
	font-size:1px;
	cursor:n-resize;
	display:none;
	z-index:5;
}
.layout-split-north{
	border-bottom:5px solid #D2E0F2;
}
.layout-split-south{
	border-top:5px solid #D2E0F2;
}
.layout-split-east{
	border-left:5px solid #D2E0F2;
}
.layout-split-west{
	border-right:5px solid #D2E0F2;
}
.layout-mask{
	position:absolute;
	background:#fafafa;
	filter:alpha(opacity=10);
	opacity:0.10;
	z-index:4;
}






.menu{
	position:absolute;
	background:#f0f0f0 url('images/menu.gif') repeat-y;
	margin:0;
	padding:2px;
	border:1px solid #ccc;
	overflow:hidden;
}
.menu-item{
	position:relative;
	margin:0;
	padding:0;
	height:22px;
	line-height:20px;
	overflow:hidden;
	font-size:12px;
	cursor:pointer;
	border:1px solid transparent;
	_border:1px solid #f0f0f0;
}
.menu-text{
	position:absolute;
	left:28px;
	top:0px;
}
.menu-icon{
	position:absolute;
	width:16px;
	height:16px;
	top:3px;
	left:2px;
}
.menu-rightarrow{
	position: absolute;
	width:4px;
	height:7px;
	top:7px;
	right:5px;
	background:url('images/menu_rightarrow.png') no-repeat;
}
.menu-sep{
	margin:3px 0px 3px 24px;
	line-height:2px;
	font-size:2px;
	background:url('images/menu_sep.png') repeat-x;
}
.menu-active{
	border:1px solid #7eabcd;
	background:#fafafa;
	-moz-border-radius:3px;
	-webkit-border-radius: 3px;
}
.menu-shadow{
	position:absolute;
	background:#ddd;
	-moz-border-radius:5px;
	-webkit-border-radius: 5px;
	-moz-box-shadow: 2px 2px 3px rgba(0, 0, 0, 0.2);
	-webkit-box-shadow: 2px 2px 3px rgba(0, 0, 0, 0.2);
	filter: progid:DXImageTransform.Microsoft.Blur(pixelRadius=2,MakeShadow=false,ShadowOpacity=0.2);
}

.m-btn-downarrow{
	display:inline-block;
	width:12px;
	background:url('images/menu_downarrow.png') no-repeat 4px 6px;
}


.messager-body{
	padding:5px 10px;
}
.messager-button{
	text-align:center;
	padding-top:10px;
}
.messager-icon{
	float:left;
	width:47px;
	height:35px;
}
.messager-error{
	background:url('images/messager_error.gif') no-repeat scroll left top;
}
.messager-info{
	background:url('images/messager_info.gif') no-repeat scroll left top;
}
.messager-question{
	background:url('images/messager_question.gif') no-repeat scroll left top;
}
.messager-warning{
	background:url('images/messager_warning.gif') no-repeat scroll left top;
}
.messager-input{
	width: 262px;
	border:1px solid #ccc;
}.pagination{
	zoom:1;
}
.pagination table{
	float:left;
}
.pagination-btn-separator{
	float:left;
	height:24px;
	border-left:1px solid #ccc;
	border-right:1px solid #fff;
	margin:3px 1px;
}
.pagination-num{
	border:1px solid #ccc;
	margin:0 2px;
}
.pagination-page-list{
	margin:0px 6px;
}
.pagination-info{
	float:right;
	padding-right:6px;
	padding-top:8px;
	font-size:12px;
}
.pagination span{
	font-size:12px;
}
.pagination-first{
	background:url('images/pagination_first.gif') no-repeat;
}
.pagination-prev{
	background:url('images/pagination_prev.gif') no-repeat;
}
.pagination-next{
	background:url('images/pagination_next.gif') no-repeat;
}
.pagination-last{
	background:url('images/pagination_last.gif') no-repeat;
}
.pagination-load{
	background:url('images/pagination_load.png') no-repeat;
}
.pagination-loading{
	background:url('images/pagination_loading.gif') no-repeat;
}
.panel{
	overflow:hidden;
	font-size:12px;
}
.panel-header{
	padding:5px;
	line-height:15px;
	color:#15428b;
	font-weight:bold;
	font-size:12px;
	background:url('images/panel_title.png') repeat-x;
	position:relative;
	border:1px solid #99BBE8;
}
.panel-header-noborder{
	border-width:0px;
	border-bottom:1px solid #99BBE8;
}
.panel-body{
	overflow:auto;
	border:1px solid #99BBE8;
	border-top-width:0px;
}
.panel-body-noheader{
	border-top-width:1px;
}
.panel-body-noborder{
	border-width:0px;
}
.panel-with-icon{
	padding-left:18px;
}
.panel-icon{
	position:absolute;
	left:5px;
	top:4px;
	width:16px;
	height:16px;
}

.panel-tool{
	position:absolute;
	right:5px;
	top:4px;
}
.panel-tool div{
	display:block;
	float:right;
	width:16px;
	height:16px;
	margin-left:2px;
	cursor:pointer;
	opacity:0.6;
	filter:alpha(opacity=60);
}
.panel-tool div.panel-tool-over{
	opacity:1;
	filter:alpha(opacity=100);
}
.panel-tool-close{
	background:url('images/panel_tools.gif') no-repeat -16px 0px;
}
.panel-tool-min{
	background:url('images/panel_tools.gif') no-repeat 0px 0px;
}
.panel-tool-max{
	background:url('images/panel_tools.gif') no-repeat 0px -16px;
}
.panel-tool-restore{
	background:url('images/panel_tools.gif') no-repeat -16px -16px;
}
.panel-tool-collapse{
	background:url('images/panel_tool_collapse.gif') no-repeat;
}
.panel-tool-expand{
	background:url('images/panel_tool_expand.gif') no-repeat;
}
.panel-loading{
	padding:11px 0px 10px 30px;
	background:url('images/panel_loading.gif') no-repeat 10px 10px;
}

.spinner{
	display:inline-block;
	white-space:nowrap;
	font-size:12px;
	margin:0;
	padding:0;
	border:1px solid #A4BED4;
}
.spinner-text{
	font-size:12px;
	border:0px;
	line-height:20px;
	height:20px;
	padding:0px;
	*height:18px;
	*line-height:18px;
	_height:18px;
	_line-height:18px;
}
.spinner-arrow{
	display:inline-block;
	vertical-align:top;
	margin:0;
	padding:0;
}
.spinner-arrow-up,.spinner-arrow-down{
	display:block;
	background:#E0ECF9 url('images/spinner_arrow_up.gif') no-repeat 5px 2px;
	font-size:1px;
	width:18px;
	height:10px;
}
.spinner-arrow-down{
	background:#E0ECF9 url('images/spinner_arrow_down.gif') no-repeat 5px 3px;
}
.spinner-arrow-hover{
	background-color:#ECF9F9;
}.s-btn-downarrow{
	display:inline-block;
	width:16px;
	background:url('images/menu_downarrow.png') no-repeat 9px center;
}

.tabs-container{
	overflow:hidden;
	background:#fff;
}
.tabs-header{
	border:1px solid #8DB2E3;
	background:#E0ECFF;
	border-bottom:0px;
	position:relative;
	overflow:hidden;
	padding:0px;
	padding-top:2px;
	overflow:hidden;
}
.tabs-header-noborder{
	border:0px;
}
.tabs-header-plain{
	border:0px;
	background:transparent;
}
.tabs-scroller-left{
	position:absolute;
	left:0px;
	top:-1px;
	width:18px;
	height:28px!important;
	height:30px;
	border:1px solid #8DB2E3;
	font-size:1px;
	display:none;
	cursor:pointer;
	background:#E0ECFF url('images/tabs_leftarrow.png') no-repeat 1px 5px;
}
.tabs-scroller-right{
	position:absolute;
	right:0;
	top:-1px;
	width:18px;
	height:28px!important;
	height:30px;
	border:1px solid #8DB2E3;
	font-size:1px;
	display:none;
	cursor:pointer;
	background:#E0ECFF url('images/tabs_rightarrow.png') no-repeat 2px 5px;
}
.tabs-header-plain .tabs-scroller-left{
	top:2px;
	height:25px!important;
	height:27px;
}
.tabs-header-plain .tabs-scroller-right{
	top:2px;
	height:25px!important;
	height:27px;
}
.tabs-scroller-over{
	background-color:#ECF9F9;
}
.tabs-wrap{
	position:relative;
	left:0px;
	overflow:hidden;
	width:100%;
	margin:0px;
	padding:0px;
}
.tabs-scrolling{
	margin-left:18px;
	margin-right:18px;
}
.tabs{
	list-style-type:none;
	height:26px;
	margin:0px;
	padding:0px;
	padding-left:4px;
	font-size:12px;
	width:5000px;
	border-bottom:1px solid #8DB2E3;
}
.tabs li{
	float:left;
	display:inline-block;
	margin1:0px 1px;
	margin-right:4px;
	margin-bottom:-1px;
	padding:0;
	position:relative;
	border:1px solid #8DB2E3;
	-moz-border-radius-topleft:5px;
	-moz-border-radius-topright:5px;
	-webkit-border-top-left-radius:5px;
	-webkit-border-top-right-radius:5px;
}
.tabs li a.tabs-inner{
	display:inline-block;
	text-decoration:none;
	color:#416AA3;
	background:url('images/tabs_enabled.png') repeat-x left top;
	margin:0px;
	padding:0px 10px;
	height:25px;
	line-height:25px;
	text-align:center;
	white-space:nowrap;
	-moz-border-radius-topleft:5px;
	-moz-border-radius-topright:5px;
	-webkit-border-top-left-radius:5px;
	-webkit-border-top-right-radius:5px;
}
.tabs li a.tabs-inner:hover{
	background:url('images/tabs_active.png') repeat-x left bottom;
}
.tabs li.tabs-selected{
	border:1px solid #8DB2E3;
	border-bottom:1px solid #fff;
	border-top1:2px solid #8DB2E3;
}
.tabs li.tabs-selected a{
	color:#416AA3;
	font-weight:bold;
	background:#fff;
	background:#7eabcd url('images/tabs_active.png') repeat-x left bottom;
	outline: none;
}
.tabs li.tabs-selected a:hover{
	cursor:default;
	pointer:default;
}
.tabs-with-icon{
	padding-left:18px;
}
.tabs-icon{
	position:absolute;
	width:16px;
	height:16px;
	left:10px;
	top:5px;
}
.tabs-closable{
	padding-right:8px;
}
.tabs li a.tabs-close{
	position:absolute;
	font-size:1px;
	display:block;
	padding:0px;
	width:11px;
	height:11px;
	top:7px;
	right:5px;
	opacity:0.6;
	filter:alpha(opacity=60);
	background:url('images/tabs_close.gif') no-repeat 2px 2px;
}
.tabs li a:hover.tabs-close{
	opacity:1;
	filter:alpha(opacity=100);
	cursor:hand;
	cursor:pointer;
	background-color:#8DB2E3;
}


.tabs-panels{
	margin:0px;
	padding:0px;
	border:1px solid #8DB2E3;
	border-top:0px;
	overflow:auto;
}
.tabs-panels-noborder{
	border:0px;
}
.tree{
	font-size:12px;
	margin:0;
	padding:0;
	list-style-type:none;
}
.tree li{
	white-space:nowrap;
}
.tree li ul{
	list-style-type:none;
	margin:0;
	padding:0;
}
.tree-node{
	height:18px;
	white-space:nowrap;
	cursor:pointer;
}
.tree-indent{
	display:inline-block;
	width:16px;
	height:18px;
	vertical-align:middle;
}
.tree-hit{
	cursor:pointer;
}
.tree-expanded{
	display:inline-block;
	width:16px;
	height:18px;
	vertical-align:middle;
	background:url('images/tree_arrows.gif') no-repeat -18px 0px;
}
.tree-expanded-hover{
	background:url('images/tree_arrows.gif') no-repeat -50px 0px;
}
.tree-collapsed{
	display:inline-block;
	width:16px;
	height:18px;
	vertical-align:middle;
	background:url('images/tree_arrows.gif') no-repeat 0px 0px;
}
.tree-collapsed-hover{
	background:url('images/tree_arrows.gif') no-repeat -32px 0px;
}
.tree-folder{
	display:inline-block;
	background:url('images/tree_folder.gif') no-repeat;
	width:16px;
	height:18px;
	vertical-align:middle;
}
.tree-folder-open{
	background:url('images/tree_folder_open.gif') no-repeat;
}
.tree-file{
	display:inline-block;
	background:url('images/tree_file.gif') no-repeat;
	width:16px;
	height:18px;
	vertical-align:middle;
}
.tree-loading{
	background:url('images/tree_loading.gif') no-repeat;
}
.tree-title{
	display:inline-block;
	line-height1:18px;
	text-decoration:none;
	vertical-align:middle;
	padding:1px 2px 1px 2px;
	white-space:nowrap;
}

.tree-node-hover{
	background:#fafafa;
}
.tree-node-selected{
	background:#FBEC88;
}
.tree-checkbox{
	display:inline-block;
	width:16px;
	height:18px;
	vertical-align:middle;
}
.tree-checkbox0{
	background:url('images/tree_checkbox_0.gif') no-repeat;
}
.tree-checkbox1{
	background:url('images/tree_checkbox_1.gif') no-repeat;
}
.tree-checkbox2{
	background:url('images/tree_checkbox_2.gif') no-repeat;
}
.validatebox-invalid{
	background:#FFFFEE url('images/validatebox_warning.png') no-repeat right 1px;
}
.validatebox-tip{
	position:absolute;
	width:200px;
	height:auto;
	display:none;
	z-index:9900000;
}
.validatebox-tip-content{
	display:inline-block;
	position:absolute;
	top:0px;
	left:10px;
	padding:3px 5px;
	border:1px solid #CC9933;
	background:#FFFFCC;
	z-index:9900001;
	font-size:12px;
}
.validatebox-tip-pointer{
	background:url('images/validatebox_pointer.gif') no-repeat left top;
	display:inline-block;
	width:10px;
	height:19px;
	position:absolute;
	left:1px;
	top:0px;
	z-index:9900002;
}.window {
	font-size:12px;
	position:absolute;
	overflow:hidden;
	background:transparent url('images/panel_title.png');
	background1:#878787;
	padding:5px;
	border:1px solid #99BBE8;
	-moz-border-radius:5px;
	-webkit-border-radius: 5px;
}
.window-shadow{
	position:absolute;
	background:#ddd;
	-moz-border-radius:5px;
	-webkit-border-radius: 5px;
	-moz-box-shadow: 2px 2px 3px rgba(0, 0, 0, 0.2);
	-webkit-box-shadow: 2px 2px 3px rgba(0, 0, 0, 0.2);
	filter: progid:DXImageTransform.Microsoft.Blur(pixelRadius=2,MakeShadow=false,ShadowOpacity=0.2);
}
.window .window-header{
	background:transparent;
	padding:2px 0px 4px 0px;
}
.window .window-body{
	background:#fff;
	border:1px solid #99BBE8;
	border-top-width:0px;
}
.window .window-header .panel-icon{
	left:1px;
	top:1px;
}
.window .window-header .panel-with-icon{
	padding-left:18px;
}
.window .window-header .panel-tool{
	top:0px;
	right:1px;
}
.window-proxy{
	position:absolute;
	overflow:hidden;
	border:1px dashed #15428b;
}
.window-mask{
	position:absolute;
	left:0;
	top:0;
	width:100%;
	height:100%;
	filter:alpha(opacity=40);
	opacity:0.40;
	background:#ccc;
	display1:none;
	font-size:1px;
	*zoom:1;
	overflow:hidden;
}
