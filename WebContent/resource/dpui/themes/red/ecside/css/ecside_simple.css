.tableRegion td {
	font-size:12px;
	
	border-left:1px #d6d6d6 solid;
	border-bottom:1px #d6d6d6 solid;

	border-top:0px;
	border-right:0px;
	padding:2px;
	height:23px;
}



.tableHeader {
background-image:url(../images/gird/headerBg.gif);
background-color:#dddddd;
white-space:nowrap;word-break:keep-all;word-wrap:normal;
color:#3A3A3A;
font-weight:bolder;
height:23px;
text-align:center;

}

.tableRegion {
border:0px;
padding:0px;
margin:0px;
/* border-collapse: collapse; */

}





.even td{
  	background-color: #f3f4f3;

}











.pageNav {
	background-color:transparent;
	background-repeat: no-repeat;
	background-position: center;
	border: 0px  #dddddd none;
	width:24px;
	height:25px;
	cursor:pointer;
	vertical-align: -2px;
}




 .toolbar select {
	width:50px;
	font-size:11px;
}





.toolbarTable{
 font-size:12px;
border:0px red solid;
	width:100%;
}
.toolbarTable td {
	border:0px none red;
}


.statusTool {
	border:0px solid red;
	padding-top:2px;
	text-align: right;
	white-space:nowrap;word-break:keep-all;word-wrap:normal;
}

 .pageNavigationTool {
	white-space:nowrap;word-break:keep-all;word-wrap:normal;
	width:50px;
}



 .pageJumpTool {
width:95px;
}

 .jumpPageInput {
	height:19px;
	width:25px;
	margin-top:0px;
	/* border:1px solid #aaaaaa; */
}

 .pageSizeTool {
padding-top:2px;
width:90px;
}



  .exportTool {
text-align:center;
width:95px;
white-space:nowrap;word-break:keep-all;word-wrap:normal;
}


 .exportButton {
	background-color:transparent;
	background-repeat: no-repeat;
	background-position: center;
	border: 0px  #dddddd none;
	width:20px;
	height:18px;
	cursor:pointer;
	display:inline;
}




 .jumpPage {
background-image:url(../images/gird/gotoPage.gif);

}
 .firstPage {
background-image:url(../images/gird/firstPage.gif);
}

 .prevPage {
background-image:url(../images/gird/prevPage.gif);
}

 .nextPage {
background-image:url(../images/gird/nextPage.gif);
}

 .lastPage {
background-image:url(../images/gird/lastPage.gif);
}

 .firstPageD {
background-image:url(../images/gird/firstPageDisabled.gif);
}

 .prevPageD {
background-image:url(../images/gird/prevPageDisabled.gif);
}

 .nextPageD {
background-image:url(../images/gird/nextPageDisabled.gif);
}

 .lastPageD {
background-image:url(../images/gird/lastPageDisabled.gif);
}




 .exportPrint {

background-image:url(../images/gird/print.gif);

}


  .separatorTool {
	font-size:1px;	
}

  .blankTool {
	width:10%;	
}

@media print {

img { visibilty:hidden }
textarea  {display:none}
input  {display:none}
select  {display:none} 
.noprint {display:none} 
.toolbar {display:none} 
.tableRegion td {
	font-size:12px;
		white-space:nowrap;
	word-break:keep-all;
	word-wrap:normal;
	border-left:1px #d6d6d6 solid;
	border-bottom:1px #d6d6d6 solid;

	border-top:0px;
	border-right:0px;
	padding:2px;
	height:23px;
}

 }
