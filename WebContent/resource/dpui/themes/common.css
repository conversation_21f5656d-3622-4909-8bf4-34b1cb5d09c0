@CHARSET "UTF-8";
/*
	这是一个通用的css样式,负责css reset
	Ver 1.0 by yhf 2008-12-13
	DO NOT MODIFY THIS FILE!
	css reset 旨在屏蔽各种浏览器对html标签的默认呈现之间的差异
	For more information about css reset,
	please visit  http://meyerweb.com/eric/tools/css/reset/
*/
/*css reset start*/
html, body, div, span, applet, object, iframe,
h1, h2, h3, h4, h5, h6, p, blockquote, pre,
a, abbr, acronym, address, big, cite, code,
del, dfn, em, font, img, ins, kbd, q, s, samp,
small, strike, strong, sub, sup, tt, var,
b, u, i, center,
dl, dt, dd, ol, ul, li,
fieldset, form, label, legend,
table, caption, tbody, tfoot, thead, tr, th, td {
	margin: 0;
	padding: 0;
	border: 0;
	outline: 0;
	font-size: 100%;
	vertical-align: baseline;
	background: transparent;
}
body {
	line-height: 1;
}
ol, ul {
	list-style: none;
}
blockquote, q {
	quotes: none;
}
blockquote:before, blockquote:after,
q:before, q:after {
	content: '';
	content: none;
}

/* remember to define focus styles! */
:focus {
	outline: 0;
}

/* remember to highlight inserts somehow! */
ins {
	text-decoration: none;
}
del {
	text-decoration: line-through;
}

/* tables still need 'cellspacing="0"' in the markup */
table {
	border-collapse: collapse;
	border-spacing: 0;
}
/*css reset end*/

/* COMMON CLASS*/
.pointer {CURSOR: pointer}
.cen,.center {TEXT-ALIGN: center}
.left {TEXT-ALIGN: left}
.right {TEXT-ALIGN: right}
.bold {FONT-WEIGHT: bold}
.red {COLOR: red}
.hide {DISPLAY: none}
.block {DISPLAY: block}
.clear {CLEAR: both}
input, textarea {
     border: 1px solid #888;
}
.formletdiv{
border:1px dotted #D4D0C8;
margin-left:1px;
height:22px;
font-size:13;
font-family: serif;
}
.formletdiv2{
border:1px dotted #D4D0C8;
margin-left:1px;
height:22px;
}
.focus {
     border: 1px solid #A4730D;
     background: #f8f8d6;
}
