.icon-list{
	background:url('icons/columns.gif') no-repeat;
}
.icon-query{
	background:url('icons/search.gif') no-repeat;
	}
.icon-add{
	background:url('icons/edit_add.png') no-repeat;
}
.icon-date{
	background:url('icons/date.png') no-repeat;
}
.icon-edit{
	background:url('icons/pencil.png') no-repeat;
}
.icon-remove{
	background:url('icons/edit_remove.png') no-repeat;
}
.icon-return{
	background:url('icons/return.png') no-repeat;
}
.icon-save{
	background:url('icons/filesave.png') no-repeat;
}
.icon-cut{
	background:url('icons/cut.png') no-repeat;
}
.icon-ok{
	background:url('icons/ok.png') no-repeat;
}
.icon-no{
	background:url('icons/no.png') no-repeat;
}
.icon-cancel{
	background:url('icons/cancel.png') no-repeat;
}
.icon-reload{
	background:url('icons/reload.png') no-repeat;
}
.icon-search{
	background:url('icons/search.png') no-repeat;
}
.icon-print{
	background:url('icons/print.png') no-repeat;
}
.icon-help{
	background:url('icons/help.png') no-repeat;
}
.icon-undo{
	background:url('icons/undo.png') no-repeat;
}
.icon-redo{
	background:url('icons/redo.png') no-repeat;
}
.icon-back{
	background:url('icons/back.png') no-repeat;
}
.icon-cls {
    background: url(icons/class.gif) no-repeat;
}
.icon-event {
    background: url(icons/event.gif) no-repeat;
}
.icon-config {
    background: url(icons/config.gif) no-repeat;
}
.icon-prop {
    background: url(icons/prop.gif) no-repeat;
}
.icon-method {
    background: url(icons/method.gif) no-repeat;
}
.icon-cmp {
    background: url(icons/cmp.gif) no-repeat;
}
.icon-pkg {
    background: url(icons/pkg.gif) no-repeat;
}
.icon-fav {
    background: url(icons/fav.gif) no-repeat;
}
.icon-sys {
	 background: url(icons/sys.png) no-repeat;
}
.icon-fac {
	 background: url(icons/fac.gif) no-repeat;
}
.icon-static {
    background: url(icons/static.gif) no-repeat;
}
.icon-docs {
    background: url(icons/docs.gif) no-repeat;
}
.icon-expand-all {
    background: url(icons/expand-all.gif) no-repeat;
}
.icon-collapse-all {
    background: url(icons/collapse-all.gif) no-repeat;
}
.icon-expand-members {
    background: url(icons/expand-members.gif) no-repeat;
}
.icon-hide-inherited {
    background: url(icons/hide-inherited.gif) no-repeat;
}
.icon-org {
    background: url(icons/org.gif) no-repeat;
}
.icon-save {
    background: url(icons/save.gif) no-repeat;
}
.icon-save-back {
    background: url(icons/save-back.png) no-repeat;
}
.icon-add {
    background: url(icons/add.gif) no-repeat;
}
.icon-delete {
    background: url(icons/delete.gif) no-repeat;
}
.icon-add-row {
    background: url(icons/add-row.gif) no-repeat;
}
.icon-delete-row {
    background: url(icons/delete-row.gif) no-repeat;
}
.icon-back {
    background: url(icons/back.png) no-repeat;
}
.icon-search {
    background: url(icons/search.png) no-repeat;
}
.icon-search-row {
    background: url(icons/search-row.png) no-repeat;
}
.icon-reload {
    background: url(icons/reload.png) no-repeat;
}
.icon-reset {
    background: url(icons/reload.png) no-repeat;
}
.icon-print {
    background: url(icons/print.gif) no-repeat;
}
.icon-export {
    background: url(icons/print.gif) no-repeat;
}
.icon-help {
    background: url(icons/help.png) no-repeat;
}
.icon-check {
    background: url(icons/check.png) no-repeat;
}
.icon-copy {
    background: url(icons/copy.png) no-repeat;
}
.icon-example {
    background: url(icons/example.gif) no-repeat;
}
.icon-retry {
    background: url(icons/retry.gif) no-repeat;
}
.icon-send-now {
    background: url(icons/send-now.gif) no-repeat;
}
.icon-receive-now {
    background: url(icons/send-receive.gif) no-repeat;
}
.icon-edit {
    background: url(icons/edit.png) no-repeat;
}
.icon-checked {
    background: url(icons/checked.gif) no-repeat;
}
.icon-unchecked {
    background: url(icons/unchecked.gif) no-repeat;
}
.icon-pick-button {
    background: url(icons/pick-button.gif) no-repeat;
}
.icon-cancel {
    background: url(icons/cancel.png) no-repeat;
}
.icon-apply {
    background: url(icons/apply.png) no-repeat;
}
.icon-offset {
    background: url(icons/offset.png) no-repeat;
}
.icon-success {
    background: url(icons/success.gif) no-repeat;
}
.icon-forum {
    background: url(icons/forum.gif) no-repeat;
}
.icon-pdf {
    background: url(icons/pdf.gif) no-repeat;
}
.icon-excel {
    background: url(icons/excel.gif) no-repeat;
}
.icon-word {
    background: url(icons/word.gif) no-repeat;
}
.icon-preprint {
    background: url(icons/pre_print.gif) no-repeat;
}
.icon-btncom {
	 background: url(icons/prop.gif) no-repeat;
}
.icon-folder_open {
	 background: url(icons/folder_open.gif) no-repeat;
}
.icon-addPeople {
	 background: url(icons/addPeople.png) no-repeat;
}
.icon-addrole {
	 background: url(icons/addrole.png) no-repeat;
}
.icon-fanhui {
	 background: url(icons/fanhui.png) no-repeat;
}
.icon-choosepeople {
	 background: url(icons/choosepeople.png) no-repeat;
}
.icon-post {
	 background: url(icons/post.png) no-repeat;
}
.icon-ok1 {
	 background: url(icons/ok1.png) no-repeat;
}
.icon-pencil {
	 background: url(icons/pencil.png) no-repeat;
}
.icon-close {
	 background: url(icons/close.gif) no-repeat;
}
.icon-login {
	 background: url(icons/login.png) no-repeat;
}
.icon-drop-yes {
	 background: url(icons/drop-yes.gif) no-repeat;
}