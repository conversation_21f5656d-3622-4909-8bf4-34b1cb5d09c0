/**
 * dialog.js:通用窗口，对话框
 * @version 1.0
 * @requires common.js
 */
/**
 * 通用窗口类
 * @class通用窗口类
 * @constructor
 */
function CommonWindow(){}
CommonWindow.prototype={
	/**
	 * @private
	 * @type Number
	 */
	divX:0,
	/**
	 * @private
	 * @type Number
	 */
	divY:0,
	/**
	 * @private
	 * @type Number
	 */
	mouseX:0,
	/**
	 * @private
	 * @type Number
	 */
	mouseY:0,
	/**
	 * zIndex
	 * @private
	 * @type Number
	 */
	zindex:10000,
	/**
	 * @type Boolean
	 */
	isMove:false,
	/**
	 * 是否遮罩body
	 * @type Boolean
	 */
	isBlock:true,
	/**
	 * @private
	 * @type  DOM
	 */
	w_window:null,
	/**
	 * 是否显示关闭按钮
	 * @type Boolean
	 */
	canClose:true,
	/**
	 * 窗口对象
	 * @type Window
	 */
	targetWindow:window.top,
	/**
	 * 弹出窗口标题
	 * @type String
	 */
	w_title:'系统窗口',
	/**
	 * 内容css样式
	 * @type String
	 */
	contentDivCss:'w_content',
	/**
	 * 初始化
	 */
	init:function(){
		if(this.targetWindow.maxZIndex){
			this.zindex=this.targetWindow.maxZIndex;
		}
		//var w_window=$C('div',{'class':'dialog'});
		var w_window=this.targetWindow.document.createElement("div");
		w_window.className="dialog";
		this.w_window=w_window;
		this.w_window.style.zIndex=(this.zindex+1);
		if(true==this.canClose){

			//var oClose=$C('a',{'class':'closediv','href':'javascript:void(0)'});
			var oClose=this.targetWindow.document.createElement("a");
			oClose.className="closediv";
			oClose.href="javascript:void(0)";
			oClose.style.zIndex=(this.zindex+2);
			w_window.appendChild(oClose);
			util.addEvent(oClose,'click',this.closeWindow,this);
		}
		var t=this.targetWindow.document.createElement("div");
		var h="<table cellPadding='0' cellSpacing='0' ><TBODY><TR><TD width='7' class=\"w_all\"></TD><TD class=\"w_all t_bg move\"><DIV class=\"w_title\">";
		h+=this.w_title;
		h+="</DIV></TD><TD  width='7' class=\"w_all rightcorner\"></TD></TR><TR><TD class=\"w_all l_bg\"></TD>";
		h+="<TD class=\"w_center\"><DIV class=\"";
		h+=this.contentDivCss;
		h+="\" style=\"WIDTH: ";
		h+=(this.w_width-14);
		h+="px\">";
		h+=this.innerDiv.outerHTML;
		h+="</DIV></TD><TD class=\"w_all r_bg\"></TD></TR><TR><TD class=\"w_all lrb_corner\"></TD>";
		h+="<TD class=\"w_all b_bg\"></TD><TD class=\"w_all lrb_corner moveposition\"></TD></TR></TBODY></table>";
		t.innerHTML=h;
		this.contentDiv=t;
		this.w_window.appendChild(t);
		this.contentDiv.style.width=this.w_width+'px';
		this.w_window.style.top=document.documentElement.scrollTop+100+'px';
		this.w_window.style.left='-1000px';
		if(true==this.isBlock){
			this.block=this.targetWindow.document.createElement("div");
			this.block.className="shadow";
			//this.block=$C('div',{'class':'shadow'});
			this.block.style.height=this.targetWindow.document.body.offsetHeight+'px';
			this.block.style.zIndex=this.zindex;
			this.targetWindow.document.body.appendChild(this.block);
			//this.targetWindow.document.body.innerHTML+=(this.block.outerHTML);
		}
		this.targetWindow.document.body.appendChild(this.w_window);
		//this.targetWindow.document.body.innerHTML+=(this.w_window.outerHTML);
		var w_width=this.w_width||this.w_window.clientWidth;
		this.w_window.style.left=parseInt((this.targetWindow.document.body.clientWidth-w_width)/2)+'px';
		if(true==this.canClose){//如果这个窗口可以移动,加入可移动事件
			util.addEvent(this.w_window.getElementsByTagName('td')[1],'mousedown',this.initDrag,this);
		}
		this.zindex=this.zindex+10;
		//在targetWindow上记录最大的zIndex
		this.targetWindow.maxZIndex=this.zindex;
	},
	/**
	 * @deprecated
	 * @returns {DOM}
	 */
	createHtml:function(){
		//this.contentDiv=$C('div',{'class':this.contentDivCss},this.innerDiv);
		//return $C('table',{'cellPadding':'0','cellSpacing':'0'},$C('tbody',{},$C('tr',{},$C('td',{'class':'w_all'}),$C('td',{'class':'w_all t_bg'+((this.canClose)?' move':'')},$C('div',{'class':'w_title'},this.w_title)),$C('td',{'class':'w_all rightcorner'})),$C('tr',{},$C('td',{'class':'w_all l_bg'}),$C('td',{'class':'w_center'},this.contentDiv),$C('td',{'class':'w_all r_bg'})),$C('tr',{},$C('td',{'class':'w_all lrb_corner'}),$C('td',{'class':'w_all b_bg'}),$C('td',{'class':'w_all lrb_corner moveposition'}))));

		//t.innerHTML="<TBODY><TR><TD class=\"w_all\"></TD><TD class=\"w_all t_bg move\"><DIV class=\"w_title\">"+this.w_title+"</DIV></TD><TD class=\"w_all rightcorner\"></TD></TR><TR><TD class=\"w_all l_bg\"></TD><TD class=\"w_center\"><DIV class=\""+this.contentDivCss+"\" style=\"WIDTH: "+this.w_width+"px\">"+this.innerDiv.outerHTML+"</DIV></TD><TD class=\"w_all r_bg\"></TD></TR><TR><TD class=\"w_all lrb_corner\"></TD><TD class=\"w_all b_bg\"></TD><TD class=\"w_all lrb_corner moveposition\"></TD></TR></TBODY>";
		//return t;
	},
	/**
	 * 关闭窗口
	 * @public
	 */
	closeWindow:function(){
		util.removeEvent(this.w_window.getElementsByTagName('td')[1],'mousedown',this.initDrag);
		util.removeDom(this.w_window);
		if(true==this.isBlock)	util.removeDom(this.block);
		for(var m in this)	this[m]=null;
	},
	/**
	 * @private
	 * @param {Event} e
	 */
	initDrag:function(e){
		if(true==this.isMove)	return;
		this.isMove=true;
		var divPosition=this.getPosition(this.w_window);
		this.divX=divPosition[1];
		this.divY=divPosition[0];
		this.mouseX=e.clientX;
		this.mouseY=e.clientY;
		util.addEvent(this.targetWindow.document.body,'mousemove',this.drag,this);
		util.addEvent(this.targetWindow.document.body,'mouseup',this.stopDrag,this);
	},
	/**
	 * @private
	 * @param {Event} e
	 */
	drag:function(e){
		var mx=e.clientX-this.mouseX;
		var my=e.clientY-this.mouseY;
		this.w_window.style.left=this.divX+mx+'px';
		this.w_window.style.top=this.divY+my+'px';
		this.divX+=mx;
		this.divY+=my;
		this.mouseX+=mx;
		this.mouseY+=my;
	},
	/**
	 * @private
	 */
	stopDrag:function(){
		util.removeEvent(this.targetWindow.document.body,'mousemove',this.drag,this);
		util.removeEvent(this.targetWindow.document.body,'mouseup',this.stopDrag,this);
		this.isMove=false;
	},
	/**
	 * @private
	 * @param {DOM} obj
	 * @returns {[obj.offsetTop,obj.offsetLeft]}
	 */
	getPosition:function(obj){
		return [obj.offsetTop,obj.offsetLeft];
	}
};
/**
 * @namespace 通用对话框
 */
var dialog={};
/**
 * @private
 * @param {String} str
 * @param {String} cssName
 * @param {String} titlestr
 * @param {Number} w_width
 */
dialog.warn=function(str,cssName,titlestr,w_width){
	var frame=new CommonWindow();
	frame.w_width=w_width;
	frame.w_title=titlestr;
	//<TABLE width="100%"><TBODY><TR><TD class="w_imgtd w_alert">测试功能:提示!</TD></TR><TR><TD class="cen"><A class="btn" title="关闭窗口" href="javascript:void(0)">确定</A></TD></TR></TBODY></TABLE>
	var innerContent=this.targetWindow.document.createElement("table");
	innerContent.setAttribute("width","100%");
	innerContent.innerHTML="<TBODY><TR><TD class=\"w_imgtd "+cssName+"\">"+str+"</TD></TR><TR><TD class=\"cen\"><A class=\"btn btn_close_dialog\" title=\"关闭窗口\" href=\"javascript:void(0)\">确定</A></TD></TR></TBODY>";
	//frame.innerDiv=$C('table',{'width':'100%'},$C('tbody',{},$C('tr',{},$C('td',{'class':'w_imgtd '+cssName},str)),$C('tr',{},$C('td',{'class':'cen'},this.createButton('确定',frame.closeWindow.bind(frame),emptyFunc,frame)))));
	frame.innerDiv=innerContent;
	frame.init();
	// bind the btn_close_dialog method
	var btns=frame.innerDiv.getElementsByTagName("*");
	for (var i = 0; i < btns.length; i++) {
		if(btns[i].tagName.toLowerCase()=="a" && /btn_close_dialog/gi.test(btns[i].className)){
			util.addEvent(btns[i],"click",function (){
				frame.closeWindow.bind(frame);
			});
		}
	}
}
/**
 * 警告框
 * @param {String} str 提示内容
 * @param {Number} w_width 窗口宽度
 */
dialog.alert=function(str,w_width){
	this.warn(str,'w_alert','系统提示',w_width||300);
}
/**
 * 错误提示
 * @param {String} str 提示内容
 * @param {Number} w_width 窗口宽度
 */
dialog.error=function(str,w_width){
	this.warn(str,'w_error','出错了',w_width||300);
}
/**
 * 正确提示
 * @param {String} str 提示内容
 * @param {Number} w_width 窗口宽度
 */
dialog.ok=function(str,w_width){
	this.warn(str,'w_ok','成功了',w_width||300);
}
/**
 * 信息提示
 * @param {String} str 提示内容
 * @param {Number} w_width 窗口宽度
 */
dialog.info=function(str,w_width){
	this.warn(str,'w_info','系统消息',w_width||300);
}
/**
 * 确认
 * @param {String} str 显示内容
 * @param {Object} btnObj
 * @param {Number} w_width
 * @example dialog.confirm('测试功能:任意键询问',{'键1':function(){dialog.info('你点击的是键1');},'键2':function(){dialog.info('你点击的是键2');}},500);
 */
dialog.confirm=function(str,btnObj,w_width){
	var btnTd=$C('td',{'class':'cen'});
	var frame=new CommonWindow();
	for(var btn in btnObj)
		btnTd.appendChild(this.createButton(btn,btnObj[btn],frame.closeWindow.bind(frame)));
	frame.w_width=w_width||400;

	frame.w_title='网页对话框';
	frame.innerDiv=$C('table',{'width':'100%'},$C('tbody',{},$C('tr',{},$C('td',{'class':'w_imgtd w_confirm'},str)),$C('tr',{},btnTd)));
	frame.init();
}
/**
 * @private 创建button
 */
dialog.createButton=function(val,act,closeAct){
	var btn=$C('a',{'class':'btn','href':'javascript:void(0)','title':'关闭窗口'},val);
	util.addEvent(btn,'click',act);
	util.addEvent(btn,'click',closeAct);
	return btn;
}
/**
 * 弹出html
 * @param {String} htmlId DOM节点的ID 弹出窗口的内容是该节点的innerHTML
 * @param {String} title 标题
 * @param {Number} width 宽度
 * @param {Boolean} createCloseButton 在窗口右下位置是否创建button
 * @returns {CommonWindow} 弹出窗口对象
 */
dialog.block=function (htmlId,title,width,createCloseButton){
	var frame=new CommonWindow();
	frame.w_width=width||500;
	frame.w_title=title||'提示';
	var cHTML=createCloseButton?"<div class='right'><a class='btn cen closeWindow' href='javascript:void(0)'>关闭</a></div>":"";
	frame.innerDiv=$C("div",{},($(htmlId)?$(htmlId).innerHTML:"")+cHTML);
	frame.init();
	var btns=frame.innerDiv.getElementsByTagName("a");//
	for (var i = 0; i < btns.length; i++) {
		var o=btns[i];
		if(/closeWindow/gi.test(o.className)){
			util.addEvent(o,'click',frame.closeWindow,frame);
		}
	}
	return frame;
}
/**
 * 弹出iframe(标题取iframe的html上的标题)
 * @param {String} url 弹出窗口url
 * @param {} w 宽度
 * @param {} h 高度
 * @param {} scroll "yes"/"no" 是否显示滚动条
 * @param {} callback 关闭窗口的回调函数 接收两个参数[返回值,触发弹出窗口的窗口]
 * @param {} targetParentWindow 目标窗口 默认是在顶层窗口中打开
 * @returns {CommonWindow} 弹出窗口对象
 */
dialog.iframe=function(url,w,h,scroll,callback,targetParentWindow){
	var frame=new CommonWindow();
	frame.w_width=w||500;
	frame.targetWindow=targetParentWindow?targetParentWindow:window.top;
	var html="<iframe "+(scroll=="yes"?"":" scrolling='no' ")+"  scrolling='auto' frameborder='0'  width='100%' height='"+h+"' src='"+url+"' class='popediframe'></iframe>";
	var innerDIV=frame.targetWindow.document.createElement("div");
	innerDIV.innerHTML=html;
	frame.innerDiv=innerDIV;
	frame.w_title='正在加载..'
	frame.init();
	//popediframe
	var iframes =frame.targetWindow.document.getElementsByTagName("iframe");
	for (var i = 0; i < iframes.length; i++) {
		var iframeWindow=iframes[i];
		function iframeload (){
			var w=this.contentWindow;//!!! contentWindow属性 saved me!
			//alert(this.parentNode.parentNode.parentNode.parentNode.parentNode.childNodes[0].childNodes[1].childNodes[0].innerHTML);
			this.parentNode.parentNode.parentNode.parentNode.parentNode.childNodes[0].childNodes[1].childNodes[0].innerHTML=w.document.title;
			//alert(w.document.title);
			w.close=function (){
				var returnedValue=w.returnValue?w.returnValue:"";
				frame.closeWindow();
				if(callback) callback.apply(window,[returnedValue,window]);
				//focus on the first textbox
				util.focusFirst(window);

			}
			w.openerWindow=window;
			//alert(w.location+"\n"+w.close+"\n"+w.openerWindow.location);
			//focus on the first textbox
			util.focusFirst(w);
		}
		util.addEvent(iframeWindow,"load",iframeload,iframeWindow);
	}


	return frame;
}
/**
 * 简写openIframe=dialog.iframe
 * @type dialog.iframe
 */
var openIframe=dialog.iframe;
