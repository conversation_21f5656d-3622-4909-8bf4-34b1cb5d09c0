
/* define outerHTML for firefox */
if(typeof(HTMLElement)!="undefined" && !window.opera) 
{ 
    HTMLElement.prototype.__defineGetter__("outerHTML",function() 
    { 
        var a=this.attributes, str="<"+this.tagName, i=0;for(;i<a.length;i++) 
        if(a[i].specified) 
            str+=" "+a[i].name+'="'+a[i].value+'"'; 
        if(!this.canHaveChildren) 
            return str+" />"; 
        return str+">"+this.innerHTML+"</"+this.tagName+">"; 
    }); 
    HTMLElement.prototype.__defineSetter__("outerHTML",function(s) 
    { 
        var r = this.ownerDocument.createRange(); 
        r.setStartBefore(this); 
        var df = r.createContextualFragment(s); 
        this.parentNode.replaceChild(df, this); 
        return s; 
    }); 
    HTMLElement.prototype.__defineGetter__("canHaveChildren",function() 
    { 
        return !/^(area|base|basefont|col|frame|hr|img|br|input|isindex|link|meta|param)$/.test(this.tagName.toLowerCase()); 
    }); 
} 
function CommonWindow(){}
CommonWindow.prototype={
	divX:0,
	divY:0,
	mouseX:0,
	mouseY:0,
	zindex:10000,
	isMove:false,
	isBlock:true,
	w_window:null,
	canClose:true,
	targetWindow:window.top,
	w_title:i18n.dialog.defaultTitle,
	contentDivCss:'w_content',
	init:function(){
		if(this.targetWindow.maxZIndex){
			this.zindex=this.targetWindow.maxZIndex;
		}
		var w_window=this.targetWindow.document.createElement("div");
		w_window.className="dialog";
		this.w_window=w_window;
		this.w_window.style.zIndex=(this.zindex+1);
		if(true==this.canClose){
			var oClose=this.targetWindow.document.createElement("a");
			oClose.className="closediv";
			oClose.href="javascript:void(0)";
			oClose.style.zIndex=(this.zindex+2);
			w_window.appendChild(oClose);
			util.addEvent(oClose,'click',this.closeWindow,this);
		}
		var t=this.targetWindow.document.createElement("div");
		var h="<table cellPadding='0' cellSpacing='0' ><TBODY><TR><TD   style='width:7px;' class=\"w_all\"></TD><TD class=\"w_all t_bg move\"><DIV class=\"w_title\"   unselectable=\"on\">";
		h+=this.w_title;
		h+="</DIV></TD><TD  style='width:7px;' class=\"w_all rightcorner\"></TD></TR><TR><TD class=\"w_all l_bg\"></TD>";
		h+="<TD class=\"w_center\"><DIV class=\"";
		h+=this.contentDivCss;
		h+="\" style=\"width:";
		h+=(parseInt(this.w_width,10)-14);
		h+="px\">";
		h+=this.innerDiv.outerHTML;
		h+="</DIV></TD><TD class=\"w_all r_bg\"></TD></TR><TR><TD class=\"w_all lrb_corner\"></TD>";
		h+="<TD class=\"w_all b_bg\"></TD><TD class=\"w_all lrb_corner moveposition\"></TD></TR></TBODY></table>";
		t.innerHTML=h;
		this.contentDiv=t;
		this.w_window.appendChild(t);
		this.contentDiv.style.width=this.w_width+'px';
		this.w_window.style.top=document.documentElement.scrollTop+100+'px';
		this.w_window.style.left='-1000px';
		if(true==this.isBlock){
			this.block=this.targetWindow.document.createElement("iframe");
			this.block.className="shadow";
			this.block.style.height=this.targetWindow.util.getPageSize().windowHeight+'px';
			this.block.style.zIndex=this.zindex;
			var blankPagePath="";
			var scripts=document.getElementsByTagName("script");
			for (var i = 0; i < scripts.length; i++) {
				var s=scripts[i];
				if(/dialog.js$/.test(s.src)){
					blankPagePath=s.src.replace(/dialog.js$/,"blank.html");
				}
			}
			this.block.setAttribute("src",blankPagePath);
			this.targetWindow.document.body.appendChild(this.block);
		}
		this.targetWindow.document.body.appendChild(this.w_window);
		var w_width=this.w_width||this.w_window.clientWidth;
		this.w_window.style.left=parseInt((this.targetWindow.document.body.clientWidth-w_width)/2)+'px';
		this.w_window.style.top=parseInt((this.targetWindow.util.getPageSize().windowHeight-this.w_window.clientHeight)/2)+'px';
		if(true==this.canClose){
			util.addEvent(this.w_window.getElementsByTagName('td')[1],'mousedown',this.initDrag,this);
		}
		this.zindex=this.zindex+10;
		this.targetWindow.maxZIndex=this.zindex;
		
		util.addEvent(this.w_window,"keydown",function (){
			var evt=util.getEvent();
			evt.cancelBubble =true;
			if(evt.stopPropagation) evt.stopPropagation();
			evt.returnValue=false;
			if(evt.preventDefault )evt.preventDefault ();
			return false;
		});
		
		// saved to window
		window._currentWindow=this;
	},
	createHtml:function(){
	},
	closeWindow:function(){
		if(this.w_window){
			util.removeEvent(this.w_window.getElementsByTagName('td')[1],'mousedown',this.initDrag);
			util.removeDom(this.w_window);
		}
		if(true==this.isBlock)	util.removeDom(this.block);
		for(var m in this)	this[m]=null;
	},
	initDrag:function(e){
		if(true==this.isMove)	return;
		this.isMove=true;
		var divPosition=this.getPosition(this.w_window);
		this.divX=divPosition[1];
		this.divY=divPosition[0];
		this.mouseX=e.clientX;
		this.mouseY=e.clientY;
		util.addEvent(this.targetWindow.document.body,'mousemove',this.drag,this);
		util.addEvent(this.targetWindow.document.body,'mouseup',this.stopDrag,this);
	},
	drag:function(e){
		var mx=e.clientX-this.mouseX;
		var my=e.clientY-this.mouseY;
		this.w_window.style.left=this.divX+mx+'px';
		this.w_window.style.top=this.divY+my+'px';
		this.divX+=mx;
		this.divY+=my;
		this.mouseX+=mx;
		this.mouseY+=my;
	},
	stopDrag:function(){
		util.removeEvent(this.targetWindow.document.body,'mousemove',this.drag,this);
		util.removeEvent(this.targetWindow.document.body,'mouseup',this.stopDrag,this);
		this.isMove=false;
	},
	getPosition:function(obj){
		return [obj.offsetTop,obj.offsetLeft];
	}
};
var dialog={};
function _closeWindow(){
	if(window._currentWindow){
		window._currentWindow.closeWindow.apply(window._currentWindow);
	}else{
		var nodes=window.top.document.body.childNodes;
		var removeNodes=new Array();//the node to remove
		for (var i = 0; i < nodes.length; i++) {
			if(/shadow|dialog/gi.test(nodes[i].className)){
				removeNodes.push(nodes[i]);
			}
		}
		for (var i = 0; i < removeNodes.length; i++) {
			removeNodes[i].parentNode.removeChild(removeNodes[i]);
		}
	}
}
dialog.warn=function(str,cssName,titlestr,w_width){
	var frame=new CommonWindow();
	frame.w_width=w_width;
	frame.w_title=titlestr;
	if(!this.targetWindow)this.targetWindow=window;
	var innerContent=document.createElement("div");
	innerContent.innerHTML="<TABLE style='width:100%'><TBODY><TR><TD class=\"w_imgtd "+cssName+"\">"+str+"</TD></TR><TR><TD class=\"cen\"><A class=\"btn btn_close_dialog\" title=\""+i18n.dialog.closeText+"\" href=\"javascript:_closeWindow();\">"+i18n.dialog.okText+"</A></TD></TR></TBODY></TABLE>";
	frame.innerDiv=innerContent;
	frame.init();
}
dialog.alert=function(str,w_width){
	this.warn(str,'w_alert',i18n.dialog.alertTitle,w_width||300);
}
dialog.error=function(str,w_width){
	this.warn(str,'w_error',i18n.dialog.errorTitle,w_width||300);
}
dialog.ok=function(str,w_width){
	this.warn(str,'w_ok',i18n.dialog.okTitle,w_width||300);
}
dialog.info=function(str,w_width){
	this.warn(str,'w_info',i18n.dialog.infoTitle,w_width||300);
}
dialog.block=function (htmlId,title,width,createCloseButton){
	var frame=new CommonWindow();
	frame.w_width=width||500;
	frame.w_title=title||i18n.dialog.alertTitle;
	var cHTML=createCloseButton?"<div class='right'><a class='btn cen closeWindow' href='javascript:_closeWindow();'>"+i18n.dialog.closeText+"</a></div>":"";
	frame.innerDiv=$C("div",{},($(htmlId)?$(htmlId).innerHTML:"")+cHTML);
	frame.init();
	return frame;
}
var openIframe=dialog.iframe=function(url,w,h,scroll,callback,targetParentWindow){
	if(h!='100%')h=parseInt(h,10)+30;//fix height
	var targetWindow=targetParentWindow?targetParentWindow:window.top;
	var zindex=10000;
	if(targetWindow.maxZIndex){
		zindex=parseInt(targetWindow.maxZIndex,10)+10;
		targetWindow.maxZIndex=(zindex+10);
	}else{
		targetWindow.maxZIndex=zindex;
	}
	//url
	if(url.indexOf("/")==-1){
		var l=window.location.pathname;
		l=l.substr(0,l.lastIndexOf("/")+1);
		url=l+url;
	}
	(function ($){
		var theight=targetWindow.jQuery(targetWindow).height();
		var twidth=targetWindow.jQuery(targetWindow).width();
		var isFullScreen=(w=="100%"||h=="100%");
		if(w=="100%") w=twidth;
		if(h=="100%") h=theight;
		var blankPagePath="";
		var scripts=document.getElementsByTagName("script");
		for (var i = 0; i < scripts.length; i++) {
			var s=scripts[i];
			if(/dialog.js$/.test(s.src)){
				blankPagePath=s.src.replace(/dialog.js$/,"blank.html");
			}
		}
		var $mask=targetWindow.jQuery("<iframe class='mask'  frameborder='0' src='"+blankPagePath+"'></iframe>").appendTo(targetWindow.document.body)
		.css({'zIndex':zindex})
		.width(twidth)
		.height(theight)
		.show();
		
		var $w=targetWindow.jQuery("<div class='ui-window'><div class='ui-window-title' onselectstart='return false' unselectable='on' ><span class='ui-window-close'  onselectstart='return false' unselectable='on'>X</span><span class='ui-window-doc-title'  onselectstart='return false' unselectable='true'>"+i18n.dialog.loadingTitle+"</span></div></div>")
		.appendTo(targetWindow.document.body)
		.css('zIndex',zindex+1)
		.width(w)
		.height(h)
		.show();
		
		var _w= $w.width();
		var _h= $w.height();
		var wt = ((theight-_h)/2-2);
		var wl = ((twidth-_w)/2-2);
		var wtitle= $w.css({"top":wt,'left':wl}).children(".ui-window-title");
		if(!isFullScreen){
			/*$w.draggable({
				handle:">.ui-window-doc-title",
				start:function (e,ui){
					targetWindow.jQuery("iframe",ui.helper).hide(); 
				},
				stop:function (e,ui){
					targetWindow.jQuery("iframe",ui.helper).show(); 
				}
			});*/
			var dragX,dragY;
			wtitle.mousedown(function(e){
				wl= $w.position().left;
				wt= $w.position().top;
				dragX= e.clientX - wl ;
				dragY= e.clientY- wt;
				targetWindow.jQuery("<div class='mask'></div>").css("zIndex","99999999").appendTo(targetWindow.document.body).show().bind("mousemove",function (e){
					var x= e.clientX;
					var y =e.clientY;
					$w.css({"left":(x-dragX),"top":(y-dragY)});
				}).mouseup(function(e){
					targetWindow.jQuery(this).remove();
					var x= e.clientX;
					var y =e.clientY;
					$w.css({"left":(x-dragX),"top":(y-dragY)});
				});
			});
		}
		
		h=parseInt(h,10)-25;
		w=parseInt(w,10)-4;
		targetWindow.jQuery("<iframe "+(scroll=="yes"?"":" scrolling='no' ")+"  scrolling='auto' frameborder='0'  width='"+w+"' height='"+h+"' src='"+url+"' class='popediframe'></iframe>").appendTo($w).bind("load",function (){
			var t=typeof(this.contentWindow.document.title)=='undefined'?'':this.contentWindow.document.title;
			targetWindow.jQuery(".ui-window-doc-title",$w).html(t);
			var w =this.contentWindow;
			if(!this.contentWindow._close_){
				this.contentWindow.close=function (){
					$w.remove();
					$mask.remove();
					if(callback) callback.apply(window,[w.returnValue?w.returnValue:'',window]);
					if(typeof(w.pageUnload)=='function' && !w._unloadexecuted){//弹出窗口执行pageUnload事件
						w._unloadexecuted=true;//标记已经执行，防止重复执行
						w.pageUnload();
					}
					util.focusFirst(window);
					setNullObj([$w,$mask,w,t,scripts,blankPagePath,wtitle,targetWindow,theight,twidth,isFullScreen,h,_w,_h]);
				}
			}
			targetWindow.jQuery(".ui-window-close",$w).click(function (e){
				$w.remove();
				$mask.remove();
				if(callback) callback.apply(window,['close',window]);
				if(typeof(w.pageUnload)=='function' &&  !w._unloadexecuted ){//弹出窗口执行pageUnload事件
					w._unloadexecuted=true;//标记已经执行，防止重复执行
					w.pageUnload();
				}
				util.focusFirst(window);
				setNullObj([$w,$mask,w,t,scripts,blankPagePath,wtitle,targetWindow,theight,twidth,isFullScreen,h,_w,_h]);
				e.stopPropagation();
			}).mousedown(function (e){e.stopPropagation()});
			util.focusFirst(w);
			w.openerWindow=window;
			//disable the backspace 
			util.addEvent(w.document.body,"keydown",function (){
				var evt=util.getEvent();
				var o=evt.srcElement||evt.target;
				var isText=/^input|textarea$/gi.test(o.tagName);
				var isDisabled=( o.readOnly || o.disabled );
				if(evt.keyCode==8 ){
					if(isText && !(isDisabled)){
						return true;
					}else{
						evt.cancelBubble =true;
						if(evt.stopPropagation) evt.stopPropagation();
						return false;
					}
				}
			});
		});
		
	})(window.jQuery);
};


var openWin=function(url,w,h,scroll){
	var targetWindow= window.top;
	var theight= parseInt(window.screen.availHeight)-20;//targetWindow.jQuery(targetWindow).height();
	var twidth=parseInt(window.screen.availWidth);//targetWindow.jQuery(targetWindow).width();
	if(w=="100%") w=twidth;
	if(h=="100%") h=theight;
	var wt = (Math.abs(theight-h)/2);
	var wl = (Math.abs(twidth-w)/2);
	var newWin=window.open(url,"_dawnpro"+(new Date()).getTime(),'width='+w+',height='+h+',toolbar=no,menubar=no,top='+wt+',left='+wl+',scrollbars=' + scroll+',resizable=no');
}

 function continueSkipSetNull(obj){
	if (obj==null) return true;
 	var typeo=""+typeof(obj);		
 	if (typeo == null || typeo == 'undefined'){		
 		return true;
 	}
 	return false;
 }                      
                        
function setNullObj(obj){
 	if (continueSkipSetNull(obj)) return ;

 	for ( var o = 0; o < obj.length; o++){
	 	if (continueSkipSetNull(obj[o])){
	 		continue;
	 	}   	
	 	else {
     	  setNullObj(obj[o]);
     	  try{
     		  obj[o]=null;
     	  }catch(ex){
     		  
     	  }
 		}
     }
     obj=null;
  }


/*****************************************
功能：打开普通窗口
参数：url:要打开窗口的url路径
      winname:窗口的名称
	  W:打开窗口的宽度
	  H:打开窗口的高度
	  replace:是一个可选的布尔值(true或false)，它说明是否应该用定义的url的内容取代窗口的内容，它应用于一个已经创建的窗口
*****************************************/
function openWindow(url,winname,W,H,scroll,position)
{
	var bScrolling = "no";
	if(scroll == "yes")
	{
		bScrolling = scroll;
	}
	if(W > screen.availWidth)
	{
		bScrolling = "yes";
	}
	if(H > screen.availHeight)
	{
		bScrolling = "yes";
	}
		var ix;
	var iy;
	if(position == null || position == "lefttop") {
		ix = 0;
		iy = 0;
	}else if(position == "center"){
		ix = (screen.availWidth - W)/2;
		iy= (screen.availHeight - H)/2;
	}
	var newWin=window.open(url,winname,'width='+W+',height='+H+',toolbar=no,menubar=no,top='+iy+',left='+ix+',scrollbars=' + bScrolling+',resizable=yes');

	//newWin.moveTo(ix,iy);
	return newWin;
}
/*****************************************
功能：打开模态窗口
参数：url:要打开窗口的url路径
      arg:要传入的参数
	  W:打开窗口的宽度
	  H:打开窗口的高度
*****************************************/
function openModal(url,arg,W,H,scroll)
{
	var bScrolling = "no";
	if(scroll == "yes")
	{
		bScrolling = scroll;
	}
	if(W > screen.availWidth)
	{
		bScrolling = "yes";
	}
	if(H > screen.availHeight)
	{
		bScrolling = "yes";
	}
	var newModal=window.showModalDialog(url,arg,'dialogWidth:'+W+'px;dialogHeight:'+H+'px;scroll:' + bScrolling + ';status:yes;center:yes;help:no');
	return newModal;
}