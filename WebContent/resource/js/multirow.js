/**
 * multirow.js:解决多行提交中的新增行,删除行,表单命名
 * @requires jquery.js
 */

var MultiRow={
	/**
	 * 删除当前行
	 * @param  o 如果存在，查找o所在的行，否则查找evt.source所在的行
	 */
	deleteRow:function (o){
		var evt=window.event?window.event:MultiRow.deleteRow.caller.arguments[0];
		var source=o?o:(evt.srcElement||evt.target);
		var $row=jQuery(source).parents(".dynamicrow");
		var containerId=$row[0]?$row[0].parentNode.id:"";
		if(containerId && containerId!="") {
			if( containerId.indexOf(".")!=-1){
				containerId=containerId.substr(0,containerId.lastIndexOf("."));
			}
			$row.remove();
			MultiRow.rename(containerId);
		}else{
			alert("获取容器错误");
		}
	},
	/**
	 * 在最后添加一行,返回新添加的一行jQuery对象(添加失败返回null)
	 */
	newRow:function (containerId,maxrownum,exceedCallback){
		 maxrownum=maxrownum||1000;
		if(maxrownum && parseInt(maxrownum,10)>0){
			if(jQuery("#"+containerId+"> .dynamicrow").length==parseInt(maxrownum,10)){
				if(typeof(exceedCallback)=='function')exceedCallback.apply(this,[]);
				return null;
			}
			var $result=jQuery(jQuery("#"+containerId+"_template").val())
			.clone()
			.addClass("dynamicrow")
			.appendTo("#"+containerId)
			.show()
			.children(":input:not([@type='button']):not([@type='hidden'])").val("").end();
			MultiRow.rename(containerId);
			return $result;
		}
	},
	/**
	 * 设置所有行的input标签的name属性
	 */
	rename:function (containerId){
		//如果模板里面的元素name属性有containerId(也就是有从表类名)认为是从表
		var isPrimaryEntity=jQuery(":input",jQuery("#"+containerId+"_template").val()).attr("name").indexOf(containerId)==-1;
		//多行命名规则 第一行 与单行提交无异,
		//第二行开始命名为 formname+$+index index从1开始
		jQuery("#"+containerId+" .dynamicrow").each(function (i,o){
			jQuery(":input,input:hidden",o).each(function(index,element){
				var oldname=element.getAttribute("name")?element.getAttribute("name"):"";
				var newname=oldname.indexOf("$")==-1?oldname:oldname.substr(0,oldname.lastIndexOf("$"));
				newname=newname+(i==0?"":("$"+i));
				element.setAttribute("name",newname);
			});
			jQuery(".index",o).each(function (ind,ele){
				if(jQuery(ele).is(":input")){
					jQuery(ele).val((parseInt(i,10)+1));
				}else{
					jQuery(ele).html((parseInt(i,10)+1));
				}
			});
		});
		var rownum=jQuery("#"+containerId+"> .dynamicrow").length;
		if(isPrimaryEntity){
			//主表的多行提交需要加隐藏域 "domain_Num"
			// 主表多行提交 rownum = 行数- 2 两行提交 rownum 为1 
			var $domainNum=jQuery("[@name='domain_Num']");
			rownum=parseInt(rownum,10)-1;
			if($domainNum.length!=0) {
				$domainNum.eq(0).val(rownum);
			}else{
				jQuery("<input type='hidden' name='domain_Num' value='"+rownum+"' />").appendTo(document.forms[0]);
			}
		}else{
			//从表多行提交需要添加隐藏域 "containerId_num"
			var $num=jQuery("[@name='"+containerId+"_num']");
			if($num.length!=0) {
				$num.eq(0).val(rownum);
			}else{
				jQuery("<input type='hidden' name='"+containerId.toLowerCase()+"_num' value='"+rownum+"' />").appendTo(document.forms[0]);
			}
		}
		return true;
	}
}
