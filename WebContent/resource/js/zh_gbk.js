var i18n=new Object();
i18n.common=new Object();
i18n.ajax=new Object();
i18n.dialog=new Object();
i18n.validate=new Object();
i18n.tree=new Object();
i18n.layout=new Object();

i18n.common.loadingText="���ڼ��ء�����";

i18n.ajax.loadingText=i18n.common.loadingText;

i18n.dialog.defaultTitle="������";
i18n.dialog.closeText="�ر�";
i18n.dialog.okText="ȷ��";
i18n.dialog.alertTitle="��ʾ";
i18n.dialog.errorTitle="����";
i18n.dialog.okTitle="�ɹ�";
i18n.dialog.infoTitle="��Ϣ";
i18n.dialog.confirmTitle="ȷ��";
i18n.dialog.loadingTitle=i18n.common.loadingText;

i18n.validate.errorText="����ԭ�����ύʧ�ܣ�\t\t\t\t";
i18n.validate.area=['','','','','','','','','','','','����','���','�ӱ�','ɽ��','���ɹ�','','','','','','����','����','������','','','','','','','','�Ϻ�','����','�㽭','��΢','����','����','ɽ��','','','','����','����','����','�㶫','����','����','','','','����','�Ĵ�','����','����','����','','','','','','','����','����','�ຣ','����','�½�','','','','','','̨��','','','','','','','','','','���','����','','','','','','','','','����'];

i18n.tree.newNodeText="�½ڵ�";
i18n.tree.loadingText=i18n.common.loadingText;

i18n.layout.openText="��";
i18n.layout.closeText="�ر�";
i18n.layout.resizerText="�϶��ı��С";
i18n.layout.sliderText="�������";
