//common language for en
var i18n=new Object();
i18n.common=new Object();
i18n.ajax=new Object();
i18n.dialog=new Object();
i18n.validate=new Object();
i18n.tree=new Object();
i18n.layout=new Object();
i18n.fav=new Object();

i18n.common.loadingText="loading...";
i18n.common.toomuchtabs="You can not open tab any more!";

i18n.ajax.loadingText=i18n.common.loadingText;

i18n.dialog.defaultTitle="Information";
i18n.dialog.closeText="Close";
i18n.dialog.okText="OK";
i18n.dialog.alertTitle="Warn";
i18n.dialog.errorTitle="Error";
i18n.dialog.okTitle="Success";
i18n.dialog.infoTitle="Information";
i18n.dialog.confirmTitle="Confirm";
i18n.dialog.loadingTitle=i18n.common.loadingText;

i18n.validate.errorText="Fields below caused error:\t\t\t\t";
i18n.validate.area=[];

i18n.tree.newNodeText="new Node";
i18n.tree.loadingText=i18n.common.loadingText;

i18n.layout.openText="Open";
i18n.layout.closeText="Close";
i18n.layout.resizerText="Resize";
i18n.layout.sliderText="Slider";

i18n.fav.addsuccess="Favourite added successful!";
i18n.fav.added="The item has added!";
i18n.fav.removeerror="error when removing the item";
i18n.fav.adderror="error when add the item";

