<!--
/*****************************************
功能：打开普通窗口
参数：url:要打开窗口的url路径
      winname:窗口的名称
	  W:打开窗口的宽度
	  H:打开窗口的高度
	  replace:是一个可选的布尔值(true或false)，它说明是否应该用定义的url的内容取代窗口的内容，它应用于一个已经创建的窗口
*****************************************/
function openWindow(url,winname,W,H,scroll,position)
{
	var bScrolling = "no";
	if(scroll == "yes")
	{
		bScrolling = scroll;
	}
	if(W > screen.availWidth)
	{
		bScrolling = "yes";
	}
	if(H > screen.availHeight)
	{
		bScrolling = "yes";
	}
		var ix;
	var iy;
	if(position == null || position == "lefttop") {
		ix = 0;
		iy = 0;
	}else if(position == "center"){
		ix = (screen.availWidth - W)/2;
		iy= (screen.availHeight - H)/2;
	}
	var newWin=window.open(url,winname,'width='+W+',height='+H+',toolbar=no,menubar=no,top='+iy+',left='+ix+',scrollbars=' + bScrolling+',resizable=yes');

	//newWin.moveTo(ix,iy);
	return newWin;
}
/*****************************************
功能：打开模态窗口
参数：url:要打开窗口的url路径
      arg:要传入的参数
	  W:打开窗口的宽度
	  H:打开窗口的高度
*****************************************/
function openModal(url,arg,W,H,scroll)
{
	var bScrolling = "no";
	if(scroll == "yes")
	{
		bScrolling = scroll;
	}
	if(W > screen.availWidth)
	{
		bScrolling = "yes";
	}
	if(H > screen.availHeight)
	{
		bScrolling = "yes";
	}
	var newModal=window.showModalDialog(url,arg,'dialogWidth:'+W+'px;dialogHeight:'+H+'px;scroll:' + bScrolling + ';status:yes;center:yes;help:no');
	return newModal;
}

-->