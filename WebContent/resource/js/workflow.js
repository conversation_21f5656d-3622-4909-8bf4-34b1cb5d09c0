/**
* workflow.js 工作流的相关方法的封装
*/
/**
 * 调用动作
 */
function doWfAction(process,action,actionname,entityids,needmeno,needconfirm){
	var webapp=window.location.pathname.match(/^\/[^\/]{1,}/i)[0];// /bfcec
	if(needmeno){
		openIframe(webapp+"/service/common/workflow/workflow_memo.jsp",400,200,'yes',function (v){
			if(v!='close'){
				if(needconfirm){
					if(!confirm("确认进行"+actionname+"操作?")){
						return false;
					}
				}
				util.createLoading();
				util.showLoading(true);
				jQuery.ajax({
					url:webapp+"/service/common/workflow/workflow_action.jsp",
					type: "POST",
					data:{
						"_process":process,
						"_action":action,
						"_entityids":entityids,
						"_memo":v
					},
					success: function(msg){
						util.showLoading(false);
						if(msg.indexOf("ok")!=-1){
							//调用回调函数
							try {eval(action+"_callback();");}catch (e){}
						}else{//报错
							alert(msg);
						}
					}
				});
			}
		});
	}else{
		if(needconfirm){
			if(!confirm("确认进行"+actionname+"操作?")){
				return false;
			}
		}
		jQuery.ajax({
			url:webapp+"/service/common/workflow/workflow_action.jsp",
			type: "POST",
			data:{
				"_process":process,
				"_action":action,
				"_entityids":entityids,
				"_memo":""
			},
			success: function(msg){
				if(msg.indexOf("ok")!=-1){
					//调用回调函数
					try {eval(action+"_callback();");}catch (e){}
				}else{//报错
					alert(msg);
				}
			}
		});
	}
	
}