
function $(id){
	return document.getElementById(id);
}
function $N(givenName){
	var allNodes=document.getElementsByTagName("*");
	var result=new Array();
	for(var i=0;i<allNodes.length;i++){
		if(allNodes[i].name==givenName){
			result.push(allNodes[i]);
		}
	}
	return result;
}

function $FF(formname){
	var cbResult=new Array();
	var v=typeof(arguments[1])!="undefined"?arguments[1]:"$$";
	var result="";
	var ischeckbox=false;
	$N(formname).each(function (o){
		if(o.tagName && o.tagName.toLowerCase()=="input"){
			var t=o.type.toLowerCase();
			switch (t){
				case 'checkbox' :{
					ischeckbox=true;
					if(v!="$$"){
						o.checked=false;
						if(typeof(v)=="string" ){
							if(o.value==v) o.checked=true;
						}
						if(typeof(v)=="object"){//array
							if(v.indexOf && v.indexOf(this.value)!=-1){
								o.checked=true;
							}
						}
					}
					if(o.checked) cbResult.push(o.value);
					break;
				}
				case 'radio':{
					if(v!="$$"){
						o.checked=false;
						if(o.value==v) o.checked=true;
					}
					if(o.checked) result=o.value;
					break;
				}
				default:{
					if(v!="$$"){
						o.value=v;
					}
					result=o.value
					break;
				}
			}
		}else if(o.tagName.toLowerCase()=="select"){
			for(var i=0;i<o.options.length;i++){
				if(v!="$$"){
					o.options[i].selected=false;
					if(o.options[i].value==v){
						o.options[i].selected=true;
					}
				}
				if(o.options[i].selected){
					result=o.options[i].value;
				}
			}
		}
	});
	return ischeckbox?cbResult:result;
}
var util={
	getJ$:function(){
		window._previous$=$;
		window.$=window.jQuery;
		return window.$;
	},
	reset$:function (){
		window.$=window._previous$;
		return window.$;
	},
	browser:{
		safari:/webkit/.test(navigator.userAgent.toLowerCase()),
		opera:/opera/.test(navigator.userAgent.toLowerCase()),
		ie:/msie/.test(navigator.userAgent.toLowerCase()) && !/opera/.test(navigator.userAgent.toLowerCase()),
		ie6:(navigator.userAgent.toLowerCase().indexOf("msie 6")!=-1),
		mozilla:/mozilla/.test(navigator.userAgent.toLowerCase()) && !/(compatible|webkit)/.test(navigator.userAgent.toLowerCase()),
		version: (navigator.userAgent.toString().toLowerCase().match( /.+(?:rv|it|ra|ie)[\/: ]([\d.]+)/ ) || [])[1]
	},
	removeDom:function (obj){
		var m=obj.childNodes;
		for(var i=m.length-1;i>=0;i--){
			if(m[i].nodeType!=1)	continue;
			if(m[i].childNodes.length>0)
				this.removeDom(m[i]);
			else{
				try{
					obj.removeChild(m[i]);
					m[i]=null;
				}catch(e){}
			}
		}
		try{
			obj.parentNode.removeChild(obj);
		}catch(e){debug(obj.className);}
		obj=null;
	},
	addClass:function (elem,cName){
		if(elem.className.indexOf(cName)>=0)	return;
		var s=(elem.className=='')?'':' ';
		elem.className+=s+cName;
	},
	guid:1,
	removeClass:function (elem,cName){//
		var t=elem.className.indexOf(cName);
		if(cName==elem.className)	return elem.className='';
		var s='';
		if(-1==t)	return;
		if(0==t)
			s=cName+' ';
		else
			s=' '+cName;
		elem.className=elem.className.replace(s,'');
	},
	addEvent:function (elem,type,handle){
		if(!handle.guid)	handle.guid=this.guid++;
		var obj=arguments[3]?arguments[3]:elem;
		var fn=handle;
		handle=function(){
			return fn.apply(obj||this,arguments);
		};
		handle.guid=fn.guid;
		elem[type]={};
		elem[type][fn.guid]=handle;
		if(elem.attachEvent)
			elem.attachEvent("on"+type,handle);
		else
			elem.addEventListener(type,handle,false);
		elem=null;
	},
	removeEvent:function (elem,type,handle){
		if(elem.detachEvent)
			elem.detachEvent("on"+type,elem[type][handle.guid]);
		else
			elem.removeEventListener(type,elem[type][handle.guid],false);
		delete(elem[type][handle.guid]);
		elem=null;
	},
	getEvent : function() {
	    if (window.event) {
	        return window.event;
	    } else {
	        return window.util.getEvent.caller.arguments[0];
	    }
	},
	getOffset:function (e){
		var t=e.offsetTop;
		var l=e.offsetLeft;
		while(e=e.offsetParent){
			 t+=e.offsetTop;
			 l+=e.offsetLeft;
		}
		return {left:l,top:t} ;
	},
	createDOM:function (tagName,properties){
		var childObj,argLen=arguments.length;
		var domAttr;
		var tmpObj=parent.document.createElement(tagName);
		var fix={'class':'className','colspan':'colSpan','rowspan':'rowSpan'};
		for(var pro in properties){
			domAttr=fix[pro]||pro;
			tmpObj[domAttr]=properties[pro];
		}
		if(argLen==2)return tmpObj;
		for(var i=2;i<argLen;i++){
			childObj=arguments[i];
			if('string'==typeof(arguments[i]))
			tmpObj.innerHTML+=arguments[i];
			else{
				try{
					tmpObj.appendChild(childObj);
				}catch(e){
					if('number'==typeof(arguments[i]))
						tmpObj.innerHTML=arguments[i]+'';
					else
						alert("create DOM node error!")
				}
			}
		}
		return tmpObj;

	},
	focusFirst:function (w){
		var txts=(w&&w.document)?w.document.getElementsByTagName("input"):[];
		var k=0;
		/*for(var i=0;i<txts.length;i++){
			if(txts[i].type=="text"){
				if(k==0 && (!txts[i].readOnly) && (!txts[i].disabled) ){
					txts[i].focus();
					if(this.browser.ie){
						var oInput = txts[i];
						var rtextRange =oInput.createTextRange();
						rtextRange.moveStart('character',oInput.value.length);
						rtextRange.collapse(true);
						rtextRange.select();
					}
					k=1;
				}
				txts[i].autocomplete="off";
			}
		}*/
		if(k==0){
			if(this.browser.ie){w.focus();}
		}

	},
	getPageSize:function (){
		var xScroll, yScroll;

		if (window.innerHeight && window.scrollMaxY) {
			xScroll = document.body.scrollWidth;
			yScroll = window.innerHeight + window.scrollMaxY;
		} else if (document.body.scrollHeight > document.body.offsetHeight){ // all but Explorer Mac
			xScroll = document.body.scrollWidth;
			yScroll = document.body.scrollHeight;
		} else { // Explorer Mac...would also work in Explorer 6 Strict, Mozilla and Safari
			xScroll = document.body.offsetWidth;
			yScroll = document.body.offsetHeight;
		}

		var windowWidth, windowHeight;
		if (self.innerHeight) { // all except Explorer
			windowWidth = self.innerWidth;
			windowHeight = self.innerHeight;
			} else if (document.documentElement && document.documentElement.clientHeight) { // Explorer 6 Strict Mode
			windowWidth = document.documentElement.clientWidth;
			windowHeight = document.documentElement.clientHeight;
		} else if (document.body) { // other Explorers
			windowWidth = document.body.clientWidth;
			windowHeight = document.body.clientHeight;
		}

		// for small pages with total height less then height of the viewport
		if(yScroll < windowHeight){
			pageHeight = windowHeight;
		} else {
			pageHeight = yScroll;
		}

		// for small pages with total width less then width of the viewport
		if(xScroll < windowWidth){
			pageWidth = windowWidth;
		} else {
			pageWidth = xScroll;
		}

		var oPageSize={
			'pageWidth':pageWidth,
			'pageHeight':pageHeight,
			'windowWidth':windowWidth,
			'windowHeight':windowHeight
		};
		return oPageSize;
	},
	fillSelect:function(xhr,selectid){
		var array=new Array();
		 try{array=(xhr&&xhr.responseText)?eval('('+xhr.responseText+')'):(new Array());} catch(e){}
		if( document.getElementById(selectid).options){
			document.getElementById(selectid).innerHTML="";
    		if(arguments[2]){// see array  as a list when arguments[2] exists
        		var textcolumn=arguments[2].toString().toLowerCase();;
        		var valuecolumn=arguments[3]?arguments[3].toString().toLowerCase():arguments[2].toString().toLowerCase();
	        	for (var i = 0; i < array.length; i++) {
	        		var text = array[i][textcolumn]?array[i][textcolumn]:"";
	        		var value = array[i][valuecolumn]?array[i][valuecolumn]:"";
	        		document.getElementById(selectid).options.add(new Option(text,value));
	        	}
    		}else{// see array as a map
    			for (var k  in array){
    				var text =k?k:"";
	        		var value = array[k]?array[k]:"";
	        		document.getElementById(selectid).options.add(new Option(text,value));
    			}
    		}
		}
	},
	createLoading:function (){
		var oPageSize= window.util.getPageSize();
		var mask=document.getElementById("mask");
		if(null==mask){
			mask=document.createElement("iframe");
			mask.id="mask";
			mask.frameborder="no";
			mask.border="0";
			mask.allowtransparency="yes";
			mask.style.width= oPageSize.pageWidth+"px";
			mask.style.height= oPageSize.pageHeight+"px";
			document.body.appendChild(mask);

		}
		var loadingbarcontainer=document.getElementById("loadingbarcontainer");
		if(null==loadingbarcontainer){
			loadingbarcontainer=document.createElement("div");
			loadingbarcontainer.id="loadingbarcontainer";
		}

		var loadingbar=document.getElementById("loadingbar");
		if(null==loadingbar){
			loadingbar=document.createElement("div");
			loadingbar.id="loadingbar";
			loadingbar.innerHTML=i18n.ajax.loadingText;
		}
		loadingbarcontainer.appendChild(loadingbar);
		document.body.appendChild(loadingbarcontainer);
	},
	showLoading:function (show){
		if(show){
			document.getElementById("mask").style.display="block";
			document.getElementById("loadingbarcontainer").style.display="block";
		}else{
			document.getElementById("mask").style.display="none";
			document.getElementById("loadingbarcontainer").style.display="none";

		}
	}
};

window.util.addEvent(window,"load",function (){
	if(typeof pageLoad =="function"){window.setTimeout(pageLoad,0);}
	var v=util.browser.version.toString().replace(/\./gi,"_").substr(0,3);
	var addedClass="";
	if(util.browser.ie) addedClass=("IE"+v+" IE ");
	if(util.browser.mozilla) addedClass=("FF"+v+" FF ");
	if(util.browser.opera) addedClass=("OPERA"+v+" OPERA ");
	if(util.browser.safari) addedClass=("SAFARI"+v+" SAFARI ");
	util.addClass(document.body,addedClass);
	if(util.browser.ie6){
		document.execCommand("BackgroundImageCache", false, true);
	}
	if(window.jQuery){
		jQuery("form").submit(function (){
			if(typeof(window.__validated)!="undefined" && window.__validated===true){
				util.createLoading();
				util.showLoading(true);
			}
		});
	}
});

var $C=window.util.createDOM;

Function.prototype.bind=function(obj){
	var t=this;
	return function(){
		return t.apply(obj,arguments);
	}
};
String.prototype.Trim = function() { return this.replace(/(^\s*)|(\s*$)/g, ""); }
String.prototype.LTrim = function() { return this.replace(/(^\s*)/g, ""); }
String.prototype.RTrim = function() { return this.replace(/(\s*$)/g, ""); }
Array.prototype.each=function (f){
	for(var i=0;i<this.length;i++){
		if(typeof (this[i])!="undefined"){
			f.apply(this[i],[this[i],i]);
		}
	}
}
Array.prototype.indexOf = function(p_var) {
	for (var i = 0; i < this.length; i++) {
		if (this[i] == p_var) {
			return (i);
		}
	}
	return (-1);
}
function emptyFunc(){}

Object.extend = function(destination, source) {
  for (var property in source)
    destination[property] = source[property];
  return destination;
};

var Class = {
	create: function() {
		return function() { this.initialize.apply(this, arguments); }
	}
}



//打印报表函数
function report(jasper,param){
	var webapp=window.location.pathname.match(/^\/[^\/]{1,}/i)[0];// /bfcec
	var t=(new Date()).getTime();//时间戳，保证每次都打开一个新的窗口
	//首先新开一个窗口，然后提交一个form 到这个页面中去
	var w= window.open(webapp+'/wait.html','_reportiframe'+t);
	if(w){
		var form=document.createElement("form");
		form.setAttribute("action",webapp+"/report/jsp/common.jsp");
		form.setAttribute("method","post");
		form.setAttribute("target","_reportiframe"+t);
		form.id="_reportiframe";
		document.body.appendChild(form);
		form= document.getElementById("_reportiframe");

		//jasper 传递jasper 名称
		var h= document.createElement("<input type='hidden' name='jasper' value='"+jasper+"' />");
		form.appendChild(h);

		for(var k in param){
			var h= document.createElement("<input type='hidden' name='"+k+"' value='"+param[k]+"' />");
			form.appendChild(h);
		}

		document.forms['_reportiframe'].submit();
		form.parentNode.removeChild(form);//删除form
	}else{
		alert('您的浏览器设置了阻止弹出窗口，请将此站点加入可信区域，或者阻止弹出窗口程序。');
	}
}
/**
 * 对Date的扩展，将 Date 转化为指定格式的String
 * 月(M)、日(d)、12小时(h)、24小时(H)、分(m)、秒(s)、周(E)、季度(q) 可以用 1-2 个占位符
 * 年(y)可以用 1-4 个占位符，毫秒(S)只能用 1 个占位符(是 1-3 位的数字)
 * eg:
 * (new Date()).pattern("yyyy-MM-dd hh:mm:ss.S") ==> 2006-07-02 08:09:04.423
 * (new Date()).pattern("yyyy-MM-dd E HH:mm:ss") ==> 2009-03-10 二 20:09:04
 * (new Date()).pattern("yyyy-MM-dd EE hh:mm:ss") ==> 2009-03-10 周二 08:09:04
 * (new Date()).pattern("yyyy-MM-dd EEE hh:mm:ss") ==> 2009-03-10 星期二 08:09:04
 * (new Date()).pattern("yyyy-M-d h:m:s.S") ==> 2006-7-2 8:9:4.18
 */
Date.prototype.pattern=function(fmt) {
    var o = {
    "M+" : this.getMonth()+1, //月份
    "d+" : this.getDate(), //日
    "h+" : this.getHours()%12 == 0 ? 12 : this.getHours()%12, //小时
    "H+" : this.getHours(), //小时
    "m+" : this.getMinutes(), //分
    "s+" : this.getSeconds(), //秒
    "q+" : Math.floor((this.getMonth()+3)/3), //季度
    "S" : this.getMilliseconds() //毫秒
    };
    var week = {
    "0" : "\u65e5",
    "1" : "\u4e00",
    "2" : "\u4e8c",
    "3" : "\u4e09",
    "4" : "\u56db",
    "5" : "\u4e94",
    "6" : "\u516d"
    };
    if(/(y+)/.test(fmt)){
        fmt=fmt.replace(RegExp.$1, (this.getFullYear()+"").substr(4 - RegExp.$1.length));
    }
    if(/(E+)/.test(fmt)){
        fmt=fmt.replace(RegExp.$1, ((RegExp.$1.length>1) ? (RegExp.$1.length>2 ? "\u661f\u671f" : "\u5468") : "")+week[this.getDay()+""]);
    }
    for(var k in o){
        if(new RegExp("("+ k +")").test(fmt)){
            fmt = fmt.replace(RegExp.$1, (RegExp.$1.length==1) ? (o[k]) : (("00"+ o[k]).substr((""+ o[k]).length)));
        }
    }
    return fmt;
}

// 验证input 最多保留2位小数
function checkalldouble(o) {
	var p1 = /[^\-\d\.]/g;
	var p2 = /(\.\d{2})\d*$/g;
	var p3 = /^0*$/g;
	var p7 = /^\-[\-\.]*$/g;
	var p8 = /^(\-0)\d*$/g;
	var p4 = /(\.)(\d*)\1/g;
	var p5 = /^\./g;
	var p6 = /([\d\.])(\-)/g;
	var code = event.keyCode;
	if (code != 8) {
		if (o.value.substring(0, 1) == 0 && o.value.indexOf(".") == "-1") {
			o.value = "0";
		} else {
			o.value = o.value.replace(p1, "").replace(p5, "0").replace(p7, "-").replace(p3, "0").replace(p8, "$1")
			           .replace(p2, "$1").replace(p4, "$1$2").replace(p6, "$1");
		}
	}
}

function checkdouble(o) {
	var p1 = /[^\d\.]/g;
	var p2 = /(\.\d{2})\d*$/g;
	var p3 = /^0*$/g;
	var p4 = /(\.)(\d*)\1/g;
	var p5 = /^\./g;
	var code = event.keyCode;
	if (code != 8) {
		if (o.value.substring(0, 1) == 0 && o.value.indexOf(".") == "-1") {
			o.value = "0";
		} else {
			o.value = o.value.replace(p1, "").replace(p5, "0").replace(p3, "0")
			           .replace(p2, "$1").replace(p4, "$1$2");
		}
	}
}

//验证文本框字符串长度，若超过限定长度截取
function checkTxtLen(obj,len){
	var tmp=obj.value.replace(/[^\x00-\xff]/g,"**").length;
	if(tmp>len){
	//	alert(tip);
		obj.value=obj.value.substring(0,len);
	}
}

function checknumber(o){
	var p1 = /[^\d]/g;

	var code = event.keyCode;

	if (code != 8) {

		o.value = o.value.replace(p1, "");

	}
}
/**
 * 对Date的扩展，将 Date 转化为指定格式的String
 * 月(M)、日(d)、12小时(h)、24小时(H)、分(m)、秒(s)、周(E)、季度(q) 可以用 1-2 个占位符
 * 年(y)可以用 1-4 个占位符，毫秒(S)只能用 1 个占位符(是 1-3 位的数字)
 * eg:
 * (new Date()).pattern("yyyy-MM-dd hh:mm:ss.S") ==> 2006-07-02 08:09:04.423
 * (new Date()).pattern("yyyy-MM-dd E HH:mm:ss") ==> 2009-03-10 二 20:09:04
 * (new Date()).pattern("yyyy-MM-dd EE hh:mm:ss") ==> 2009-03-10 周二 08:09:04
 * (new Date()).pattern("yyyy-MM-dd EEE hh:mm:ss") ==> 2009-03-10 星期二 08:09:04
 * (new Date()).pattern("yyyy-M-d h:m:s.S") ==> 2006-7-2 8:9:4.18
 */
Date.prototype.pattern=function(fmt) {
    var o = {
    "M+" : this.getMonth()+1, //月份
    "d+" : this.getDate(), //日
    "h+" : this.getHours()%12 == 0 ? 12 : this.getHours()%12, //小时
    "H+" : this.getHours(), //小时
    "m+" : this.getMinutes(), //分
    "s+" : this.getSeconds(), //秒
    "q+" : Math.floor((this.getMonth()+3)/3), //季度
    "S" : this.getMilliseconds() //毫秒
    };
    var week = {
    "0" : "\u65e5",
    "1" : "\u4e00",
    "2" : "\u4e8c",
    "3" : "\u4e09",
    "4" : "\u56db",
    "5" : "\u4e94",
    "6" : "\u516d"
    };
    if(/(y+)/.test(fmt)){
        fmt=fmt.replace(RegExp.$1, (this.getFullYear()+"").substr(4 - RegExp.$1.length));
    }
    if(/(E+)/.test(fmt)){
        fmt=fmt.replace(RegExp.$1, ((RegExp.$1.length>1) ? (RegExp.$1.length>2 ? "\u661f\u671f" : "\u5468") : "")+week[this.getDay()+""]);
    }
    for(var k in o){
        if(new RegExp("("+ k +")").test(fmt)){
            fmt = fmt.replace(RegExp.$1, (RegExp.$1.length==1) ? (o[k]) : (("00"+ o[k]).substr((""+ o[k]).length)));
        }
    }
    return fmt;
}
