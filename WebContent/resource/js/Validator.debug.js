/**
 * <AUTHOR>
 * @description: 基本数据类型校验
 * @version 1.0
 */
/**
 * @namespace Validator 验证方法封装
 * @example onSubmit="return Validator.Validate(this,3)"
 */
 var Validator = {
 	/**
 	 * Require :非空
 	 */
	Require : /^\s*\S+(\s|\S)*$/,//  原来 /^\S+$/ yhf 2008-12-29
	/**
	 * Email
	 */
	Email : /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/,
	/**
	 * Phone
	 */
	Phone : /^((\(\d{2,3}\))|(\d{3}\-))?(\(0\d{2,3}\)|0\d{2,3}-)?[1-9]\d{6,7}(\-\d{1,4})?$/,
	/**
	 * Mobile
	 */
	Mobile : /^((\(\d{2,3}\))|(\d{3}\-))?13\d{9}$/,
	/**
	 * Url
	 */
	Url : /^http:\/\/[A-Za-z0-9]+\.[A-Za-z0-9]+[\/=\?%\-&_~`@[\]\':+!]*([^<>\"\"])*$/,
	/**
	 * IdCard:省份证
	 */
	IdCard : "this.IsIdCard(value)",
	/**
	 * 货币
	 */
	Currency : /^\d+(\.\d+)?$/,
	/**
	 * Number 数字
	 */
	Number : /^\d+$/,
	/**
	 * 邮政编码
	 */
	Zip : /^[1-9]\d{5}$/,
	/**
	 * QQ
	 */
	QQ : /^[1-9]\d{4,8}$/,
	/**
	 * 整数
	 */
	Integer : /^[-\+]?\d+$/,
	/**
	 * 小数
	 */
	Double : /^[-\+]?\d+(\.\d+)?$/,
	/**
	 * 英文字母
	 */
	English : /^[A-Za-z]+$/,
	/**
	 * 中文
	 */
	Chinese :  /^[\u0391-\uFFE5]+$/,
	/**
	 * 用户名
	 */
	Username : /^[a-z]\w{3,}$/i,
	/**
	 * 不安全密码
	 */
	UnSafe : /^(([A-Z]*|[a-z]*|\d*|[-_\~!@#\$%\^&\*\.\(\)\[\]\{\}<>\?\\\/\'\"]*)|.{0,5})$|\s/,
	/**
	 * @function 判断是否为密码字符
	 */
	IsSafe : function(str){return !this.UnSafe.test(str);},
	/**
	 * 是否为安全字符
	 */
	SafeString : "this.IsSafe(value)",
	Filter : "this.DoFilter(value, getAttribute('accept'))",
	Limit : "this.limit(value.length,getAttribute('min'),  getAttribute('max'))",
	LimitB : "this.limit(this.LenB(value), getAttribute('min'), getAttribute('max'))",
	Date : "this.IsDate(value, getAttribute('min'), getAttribute('format'))",
	/**
	 * 重复输入
	 */
	Repeat : "value == document.getElementsByName(getAttribute('to'))[0].value",
	Range : "getAttribute('min') < (value|0) && (value|0) < getAttribute('max')",
	Compare : "this.compare(value,getAttribute('operator'),getAttribute('to'))",
	Custom : "this.Exec(value, getAttribute('regexp'))",
	Group : "this.MustChecked(getAttribute('name'), getAttribute('min'), getAttribute('max'))",
	ErrorItem : [document.forms[0]],
	ErrorMessage : ["以下原因导致提交失败：\t\t\t\t"],
	/**
	 * @function 验证方法
	 * @param {DOM} theForm  form对象
	 * @param {Number} mode 验证模式 1-为一次性校验，弹出框提示 2-跟“1”提示一样但是 将文本内容用红字标记 3-在文本框后面提示
	 */
	Validate : function(theForm, mode){
		var obj = theForm || event.srcElement;
		var count = obj.elements.length;
		this.ErrorMessage.length = 1;
		this.ErrorItem.length = 1;
		this.ErrorItem[0] = obj;
		for(var i=0;i<count;i++){
			with(obj.elements[i]){
				var _dataType = getAttribute("dataType");
				if(typeof(_dataType) == "object" || typeof(this[_dataType]) == "undefined")  continue;
				this.ClearState(obj.elements[i]);
				if(getAttribute("require") == "false" && value == "") continue;
				switch(_dataType){
					case "IdCard" :
					case "Date" :
					case "Repeat" :
					case "Range" :
					case "Compare" :
					case "Custom" :
					case "Group" : 
					case "Limit" :
					case "LimitB" :
					case "SafeString" :
					case "Filter" :
						if(!eval(this[_dataType]))	{
							this.AddError(i, getAttribute("msg"));
						}
						break;
					default :{
							if(!this[_dataType].test(value)){
								this.AddError(i, getAttribute("msg"));
							}
						}
						break;
				}
			}
		}
		if(this.ErrorMessage.length > 1){
			mode = mode || 1;
			var errCount = this.ErrorItem.length;
			switch(mode){
			case 2 :
				for(var i=1;i<errCount;i++)
					this.ErrorItem[i].style.color = "red";
			case 1 :
				alert(this.ErrorMessage.join("\n"));
				this.ErrorItem[1].focus();
				break;
			case 3 :
				for(var i=1;i<errCount;i++){
				try{
					var span = document.createElement("SPAN");
					span.id = "__ErrorMessagePanel";
					span.style.color = "red";
					this.ErrorItem[i].parentNode.appendChild(span);
					span.innerHTML = this.ErrorMessage[i].replace(/\d+:/,"*");
					}
					catch(e){alert(e.description);}
				}
				this.ErrorItem[1].focus();
				break;
			default :
				alert(this.ErrorMessage.join("\n"));
				break;
			}
			return false;
		}
		return true;
	},
	/**
	 * 长度检查
	 * @param {Number}len 待检查字符串长度
	 * @param {Number} min 最小长度
	 * @param {Number} max 最大长度
	 * @returns {Boolean} 是否在长度区间内
	 */
	limit : function(len,min, max){
		min = min || 0;
		max = max || Number.MAX_VALUE;
		return min <= len && len <= max;
	},
	/**
	 * 得到字符串的字节数
	 */
	LenB : function(str){
		return str.replace(/[^\x00-\xff]/g,"**").length;
	},
	/**
	 * 清除红色表单
	 */
	ClearState : function(elem){
		with(elem){
			if(style.color == "red")
				style.color = "";
			var lastNode = parentNode.childNodes[parentNode.childNodes.length-1];
			if(lastNode.id == "__ErrorMessagePanel")
				parentNode.removeChild(lastNode);
		}
	},
	/**
	 * 添加错误信息
	 * @param {Number} 下标
	 * @param {String} 错误信息
	 */
	AddError : function(index, str){
		this.ErrorItem[this.ErrorItem.length] = this.ErrorItem[0].elements[index];
		this.ErrorMessage[this.ErrorMessage.length] = this.ErrorMessage.length + ":" + str;
	},
	/**
	 * 测试正则表达式
	 * @param {String} op 待测的字符串
	 * @param {String} reg 正则表达式
	 */
	Exec : function(op, reg){
		return new RegExp(reg,"g").test(op);
	},
	compare : function(op1,operator,op2){
		switch (operator) {
			case "NotEqual":
				return (op1 != op2);
			case "GreaterThan":
				return (op1 > op2);
			case "GreaterThanEqual":
				return (op1 >= op2);
			case "LessThan":
				return (op1 < op2);
			case "LessThanEqual":
				return (op1 <= op2);
			default:
				return (op1 == op2);            
		}
	},
	MustChecked : function(name, min, max){
		var groups = document.getElementsByName(name);
		var hasChecked = 0;
		min = min || 1;
		max = max || groups.length;
		for(var i=groups.length-1;i>=0;i--)
			if(groups[i].checked) hasChecked++;
		return min <= hasChecked && hasChecked <= max;
	},
	DoFilter : function(input, filter){
return new RegExp("^.+\.(?=EXT)(EXT)$".replace(/EXT/g, filter.split(/\s*,\s*/).join("|")), "gi").test(input);
	},
	/**
	 * 是否是省份证号
	 * @param {String} number
	 * @return {Boolean}
	 */
	IsIdCard : function(number){
		var date, Ai;
		var verify = "10x98765432";
		var Wi = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
		var area = ['','','','','','','','','','','','北京','天津','河北','山西','内蒙古','','','','','','辽宁','吉林','黑龙江','','','','','','','','上海','江苏','浙江','安微','福建','江西','山东','','','','河南','湖北','湖南','广东','广西','海南','','','','重庆','四川','贵州','云南','西藏','','','','','','','陕西','甘肃','青海','宁夏','新疆','','','','','','台湾','','','','','','','','','','香港','澳门','','','','','','','','','国外'];
		var re = number.match(/^(\d{2})\d{4}(((\d{2})(\d{2})(\d{2})(\d{3}))|((\d{4})(\d{2})(\d{2})(\d{3}[x\d])))$/i);
		if(re == null) return false;
		if(re[1] >= area.length || area[re[1]] == "") return false;
		if(re[2].length == 12){
			Ai = number.substr(0, 17);
			date = [re[9], re[10], re[11]].join("-");
		}
		else{
			Ai = number.substr(0, 6) + "19" + number.substr(6);
			date = ["19" + re[4], re[5], re[6]].join("-");
		}
		if(!this.IsDate(date, "ymd")) return false;
		var sum = 0;
		for(var i = 0;i<=16;i++){
			sum += Ai.charAt(i) * Wi[i];
		}
		Ai +=  verify.charAt(sum%11);
		return (number.length ==15 || number.length == 18 && number == Ai);
	},
	/**
	 * 判断字符串是否为日期字符串
	 * @param {String} op 待检测的字符串
	 * @param {String} formatString 日期格式 "ymd"/"dmy"
	 * @return {Boolean} 
	 */
	IsDate : function(op, formatString){
		formatString = formatString || "ymd";
		var m, year, month, day;
		switch(formatString){
			case "ymd" :
				m = op.match(new RegExp("^((\\d{4})|(\\d{2}))([-./])(\\d{1,2})\\4(\\d{1,2})$"));
				if(m == null ) return false;
				day = m[6];
				month = m[5]*1;
				year =  (m[2].length == 4) ? m[2] : GetFullYear(parseInt(m[3], 10));
				break;
			case "dmy" :
				m = op.match(new RegExp("^(\\d{1,2})([-./])(\\d{1,2})\\2((\\d{4})|(\\d{2}))$"));
				if(m == null ) return false;
				day = m[1];
				month = m[3]*1;
				year = (m[5].length == 4) ? m[5] : GetFullYear(parseInt(m[6], 10));
				break;
			default :
				break;
		}
		if(!parseInt(month)) return false;
		month = month==0 ?12:month;
		var date = new Date(year, month-1, day);
        return (typeof(date) == "object" && year == date.getFullYear() && month == (date.getMonth()+1) && day == date.getDate());
		function GetFullYear(y){return ((y<30 ? "20" : "19") + y)|0;}
	}
 }
/***************************************************************

使用：

语法：dataType="Require | Chinese | English | Number | Integer | Double | Email | Url | Phone | Mobile | Currency | Zip | IdCard | QQ | Date | SafeString | Repeat | Compare | Range | Limit | LimitB | Group | Custom"

类型：字符串。必选。 
说明：用于设定表单项的输入数据验证类型。 
选值说明： 
可选值 验证功能 
Require 必填项 
Chinese 中文 
English   英文

Number 
数字 
Integer 
整数 
Double 
实数 
Email
Email地址格式 
Url 
基于HTTP协议的网址格式 
Phone 
电话号码格式 
Mobile 
手机号码格式 
Currency 
货币格式 
Zip
邮政编码 
IdCard
身份证号码 
QQ
QQ号码 
Date 
日期 
SafeString 
安全密码 
Repeat 
重复输入 
Compare
关系比较 
Range
输入范围 
Limit
限制输入长度 
LimitB
限制输入的字节长度 
Group   验证单/多选按钮组 
Custom   自定义正则表达式验证 


语法：max="int" 
类型：字符串。在dataType属性值为Range时必选，为Group且待验证项是多选按钮组时可选(此时默认值为1)，为为Limit/LimitB时可选(此时默认值为1.7976931348623157e+308，即Number.MAX_VALUE的值)。 
说明：当daType属性值为Range时，用于判断输入是否在min与max的属性值间；当dataType属性值为Group，且待验证项是多选按钮组时，用于设定多选按钮组的选中个数，判断选中个数是否在[min, max]区间；当daType属性值为Limit时，用于验证输入的字符数是否在[min, max]区间；当daType属性值为LimitB时，用于验证输入字符的字节数是否在[min, max]区间。



语法：min="int" 
类型：字符串。在dataType属性值为Range时必选，为Group且待验证项是多选按钮组时可选(此时默认值为1)，为为Limit/LimitB时可选(此时默认值为0)。 
说明：当daType属性值为Range时，用于判断输入是否在min与max的属性值间；当dataType属性值为Group，且待验证项是多选按钮组时，用于设定多选按钮组的选中个数，判断选中个数是否在[min, max]区间；当daType属性值为Limit时，用于验证输入的字符数是否在[min, max]区间；当daType属性值为LimitB时，用于验证输入字符的字节数是否在[min, max]区间。


语法： msg="string" 
类型：字符串。必选。 
说明：在验证失败时要提示的出错信息。


语法：operator="NotEqual | GreaterThan | GreaterThanEqual | LessThan | LessThanEqual | Equal" 
类型：字符串。在dataType属性值为Compare时可选(默认值为Equal)。 
说明：参考to属性。
各选值所对应的关系操作符：
可选值 意义说明 
NotEqual
不等于 != 
GreaterThan 
大于 > 
GreaterThanEqual
大于等于 >= 
LessThan 
小于 < 
LessThanEqual 
小于等于 <= 
Equal   等于 =


语法：require="true | false" 
类型：字符串。可选。 
说明：用于设定表单项的验证方式。当值为false时表单项不是必填项，但当有填写时，仍然要执行dataType属性所设定的验证方法，值为true或任何非false字符时可省略此属性。


语法：to="sting | int" 
类型：字符串。当dataType值为Repeat或Compare时必选。 
说明：当dataType值为Repeat时，to的值为某表单项的name属性值，用于设定当前表单项的值是否与目标表单项的值一致；当dataType的值为Compare时，to的选值类型为实数，用于判断当前表单项的输入与to的值是否符合operator属性值所指定的关系。


语法：format="ymd | dmy" 
类型：字符串。在dataType属性值为Date时可选(默认值为ymd)。 
说明：用于验证输入是否为符合format属性值所指定格式的日期。
符合规则的输入示例 : 2004-11-23，2004/11/23，04.11.23，23-11-2004等
注意：当输入的年份为2位时，如果数值小于30，将使年份看作处于21世纪，否则为20世纪。


语法：regexp="object" 
类型：字符串。在dataType属性值为Custom时必选。 
说明：用于验证输入是否符合regexp属性所指定的正则表达式。


示例：

<scrīpt src="./Js/validator.js"></scrīpt>
<form name="middle_price" action="index.php" method="post" id="middle_price" ōnSubmit="return Validator.Validate(this,3)">
<table width="595" height="244" border="1" align="left" cellpadding="0" cellspacing="1" bgcolor="#C9C9C9">
   <tr>
     <th height="15" colspan="2" align="center" bgcolor="#837B6D" class="style1"> </th>
   </tr><!--单张印刷报价-->
   <tr>
     <td width="153" height="23" align="right">数量（张）</td>
     <td width="448">
   <select name="paper_num" dataType="Require" msg="未选定数量">
       <option value="" selected>请选择</option>
   <option value="200">200</option>
   <option value="500">500</option>
   <option value="1000">1000</option>
   <option value="2000">2000</option>
   <option value="3000">3000</option>
   <option value="5000">5000</option>
   <option value="10000">10000</option>
   <option value="zdy">自定义</option>   
   </select>
   </td>
   </tr>
   <tr>
     <td height="23" align="right">纸张厚度（克）</td>
     <td>
   <select name="paper_thinckness" dataType="Require" msg="未选定纸张厚度">
       <option value="" selected>请选择</option>
   <option value="80">80</option>
   <option value="90">90</option>
   <option value="95">95</option>
   <option value="100">100</option>
   <option value="105">105</option>
   <option value="120">120</option>
   <option value="128">128</option>
   <option value="150">150</option>
   <option value="157">157</option>
   <option value="180">180</option>
   <option value="200">200</option>
   <option value="zdy">自定义</option>
   </select>
</td>
   </tr>
   <tr>
     <td height="18" align="right">纸张类型</td>
     <td>
   <select name="paper_type" dataType="Require" msg="未选定纸张类型">
       <option value="" selected>请选择</option>
       <option value="特种纸">特种纸</option>
   <option value="单面铜版纸">单面铜版纸</option>
   <option value="双面铜版纸">双面铜版纸</option>
   <option value="哑粉纸">哑粉纸</option>
   <option value="双胶纸（胶版纸）">双胶纸（胶版纸）</option>
   <option value="轻涂纸">轻涂纸</option>
   <option value="新闻纸">新闻纸</option>
   <option value="信封纸 (牛皮纸)">信封纸 (牛皮纸)</option>
   <option value="不干胶">不干胶</option>
       </select>
</td>
   </tr>
   <tr>
     <td height="18" align="right">纸张尺寸（长X宽/厘米）</td>
     <td>
   长<input name="paper_width" type="text" size="4" width="25" dataType="Double" msg="纸张尺寸-长度,请用数字">
       X宽<input name="paper_height" type="text" size="4" width="25" dataType="Double" msg="纸张尺寸-宽度,请用数字">
       厘米
</td>
   </tr>
   <tr>
     <td height="16" align="right">印刷颜色</td>
     <td>
   <input type="radio" name="print_color" value="单面（4+0）">单面（4+0） 
       <input type="radio" name="print_color" value="双面（4+4）" dataType="Group" msg="必须选择一个印刷颜色">双面（4+4）
</td>
   </tr>
   <tr>
     <td height="18" align="right">总价(元)</td>
     <td><input type="text" name="price_total" size="10" dataType="Currency" msg="总价请用数字">
       <input type="checkbox" name="show_price" value="1">显示<!--1显示,0 不显示--></td>
   </tr>
   <tr>
     <td height="18" align="right">备注</td>
     <td><textarea name="notes" cols="30" rows="4"></textarea></td>
   </tr>
   <tr>
     <td height="15" colspan="2" align="center">
     <input type="submit" name="Submit" value="提交报价">
       </td>
     </tr>
</table>
</form>


示例的一点点说明：

在form 里加 ōnSubmit="return Validator.Validate(this,3)"   其中可以定义三种错误模式，可选1,2,3(次处是3)，亲自试先好点！
* **********************************************/
