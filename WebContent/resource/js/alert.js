///下面这个iframe,用于防止div被select覆盖（原理:iframe可以在select上，而div又可以在iframe上）
var divFrm = '<iframe style="position:absolute;z-index:98;width:expression(this.nextSibling.offsetWidth);height:expression(this.nextSibling.offsetHeight);top:expression(this.nextSibling.offsetTop);left:expression(this.nextSibling.offsetLeft);" frameborder="0"></iframe>';
divFrm += '<div id="showMyMessage" style="position: absolute; width: 120; display: none; z-index: 99;font-size: 13px; background: #ffff00"></div>';
document.write(divFrm);

/****************************************************************************/
/* 显示信息                                                                 */
/* 参数说明：                                                               */
/* 参数1(必需)： 要显示的信息内容，可以包含HTML内容，如<br>                */
/* 参数2(必需):   要显示信息的对象，用来获得相对位置                        */
/* 参数3(可选)： 数字，要显示的宽度，如果没有设置，宽度＝信息长度*15px     */
/* 参数4(可选)： 数字，要显示的信息与对象的横向位移，不设置时为0           */
/* 参数5(可选)： 数字，要显示的信息与对象的纵向位移，不设置时为20          */
/* 参数6(可选)： 要显示的信息的背景色，不设置时为黄色#ffff00               */
/* 调用示例:                                                                */
/* <input onmouseover="divShow('信息',this);" onmouseout="divHidden();">    */
/* <input onmouseover="divShow('信息',this,120,20,30,'#ff0000');" onmouseout="divHidden();">    */
/* 调用示例:                                                                */
/****************************************************************************/
function divShow()
{
   //参数少于2个的时候，不进行处理
   if(arguments.length < 2)
     return;
     
   var msg,e,msgWidth,leftPosition,topPosition,backColor;
   msg = arguments[0];
   e   = arguments[1];
   
   //获取信息显示宽度，如果没有设置，宽度＝信息长度*15px
   if(arguments.length >= 3)
     msgWidth = arguments[2];
   else
     msgWidth = msg.length * 15;

   //获取信息显示与对象横向位移，不设置时为0
   if(arguments.length >= 4)
     leftPosition = arguments[3];
   else
     leftPosition = 0;

   //获取信息显示与对象纵向位移，不设置时为20
   if(arguments.length >= 5)
     topPosition = arguments[4];
   else
     topPosition = 20;

   //获取信息显示的颜色，不设置时为黄色#ffff00
   if(arguments.length >= 6)
     backColor = arguments[5];
   else
     backColor = "#ffff00";

   var divObj = document.getElementById("showMyMessage");
   divObj.style.background = backColor;
   divObj.innerHTML = msg;
   divObj.style.width = msgWidth;
   divObj.style.display = ""; //层显示
	var o=event.srcElement; 
	MouseX=event.x; 
	MouseY=event.y;
  	divObj.style.left=MouseX+12; 
	divObj.style.top=MouseY+12; 
}

function divHidden()
{//隐藏悬赏信息
   document.getElementById("showMyMessage").style.display = 'none';
}
function show(title,msg)
{
	var mask=document.getElementById("mask");
		if(null==mask){
			mask=document.createElement("iframe");
			mask.id="mask";
			mask.className="mask";
			mask.frameborder="no";
			mask.border="0";
			mask.scrolling="No"
			mask.style.width=document.body.clientWidth;
			mask.style.height=document.body.clientHeight;
			mask.src="about:blank";
			
			mask.onreadystatechange=function (){
				if(mask.readyState=='complete'){
				}
			}
			
				document.body.appendChild(mask);
		}
		mask.style.display="block";
		mask.style.zIndex=100;
	
   // var d_mask=document.getElementById('maskdiv');
    var d_dialog = document.getElementById('dialog');
     var d_dialog = document.getElementById('dialog');
     //d_mask.style.zIndex=101;
    //d_mask.style.width = document.body.clientWidth ;
    //d_mask.style.height=document.body.clientHeight;
    d_dialog.style.top = document.body.clientHeight / 2 -150;
    d_dialog.style.left =document.body.clientWidth / 2 -200;
   // d_mask.style.visibility='visible';
   // d_mask.style.display='';
    d_dialog.style.visibility='visible';
    d_dialog.style.display='';
    d_dialog.style.zIndex=102;
   
}
function DialogClose()
{
	var mask=document.getElementById("mask");
		if(null!=mask){
			mask.style.zIndex=-100;
			document.body.removeChild(mask);
		}
    //var d_mask=document.getElementById('maskdiv');
    var d_dialog = document.getElementById('dialog');
   // d_mask.style.visibility='hidden';
    d_dialog.style.visibility='hidden';
}
function divBlock_event_mousedown()
{ 
var e, obj, temp;
obj=document.getElementById('dialog');  
e=window.event?window.event:e;
obj.startX=e.clientX-obj.offsetLeft;  
obj.startY=e.clientY-obj.offsetTop;  
document.onmousemove=document_event_mousemove;  
temp=document.attachEvent?document.attachEvent('onmouseup',document_event_mouseup):document.addEventListener('mouseup',document_event_mouseup,'');
event.srcElement.setCapture();

}
  
  
function document_event_mousemove(e)
{
var e, obj;  
obj=document.getElementById('dialog'); 
e=window.event?window.event:e;   
with(obj.style){  
    position='absolute';  
    left=e.clientX-obj.startX+'px';  
    top=e.clientY-obj.startY+'px'; 
    }
}

function document_event_mouseup(e)
{ 
var temp; 
document.onmousemove='';
temp=document.detachEvent?document.detachEvent('onmouseup',document_event_mouseup):document.removeEventListener('mouseup',document_event_mouseup,'');
event.srcElement.releaseCapture();

}


window.onresize = function()
{
    var d_mask=document.getElementById('maskdiv');
    var d_dialog = document.getElementById('dialog');
    
   if(d_mask!=null && d_dialog!=null){
	    d_mask.style.width = document.body.clientWidth ;
	    d_mask.style.height=document.body.clientHeight;
	    if(d_mask.style.visibility==""){
	    	d_mask.style.visibility="hidden";
	    }
   }
}

//add calc
function accAdd(arg1,arg2){ 
	var r1,r2,m; 
	try{r1=arg1.toString().split(".")[1].length;}catch(e){r1=0} 
	try{r2=arg2.toString().split(".")[1].length;}catch(e){r2=0} 
	m=Math.pow(10,Math.max(r1,r2)); 
	return (arg1*m+arg2*m)/m;
} 

//minus calc
function accMinus(arg1,arg2){
	var r1,r2,m,n;
	try{r1=arg1.toString().split(".")[1].length;}catch(e){r1=0}
	try{r2=arg2.toString().split(".")[1].length;}catch(e){r2=0}
	m=Math.pow(10,Math.max(r1,r2));
	//last modify by deeka
	n=(r1>=r2)?r1:r2;
	var left = ((arg1*m-arg2*m)/m).toFixed(n);
	
	var arr = (new String(left)).split(".");
    var xs = arr[1];
    if((xs+"").length>3){
      if((xs+"").charAt(2)=='9'){
         left = arr[0]+"."+(parseInt((xs+"").substring(0,2))+1);
      }
     if((xs+"").charAt(2)=='0'){
         left = arr[0]+"."+(xs+"").substring(0,2);
      }
    }
    if (left == 0) {
    	left = 0;
    }		    
    return left;
}  

//multiply calc
function accMul(arg1,arg2) 
{ 
	var m=0,s1=arg1.toString(),s2=arg2.toString(); 
	try{m+=s1.split(".")[1].length;}catch(e){} 
	try{m+=s2.split(".")[1].length;}catch(e){} 
	return Number(s1.replace(".",""))*Number(s2.replace(".",""))/Math.pow(10,m);
}

//divide calc
function accDiv(arg1,arg2){ 
	var t1=0,t2=0,r1,r2; 
	try{t1=arg1.toString().split(".")[1].length;}catch(e){} 
	try{t2=arg2.toString().split(".")[1].length;}catch(e){} 
	with(Math){ 
		r1=Number(arg1.toString().replace(".","")); 
		r2=Number(arg2.toString().replace(".","")); 
		return (r1/r2)*pow(10,t2-t1); 
	} 
} 

//round
function mround(v,e) 
{ 
	var t=1; 
	for(;e>0;t*=10,e--); 
	for(;e<0;t/=10,e++); 
	return Math.round(v*t)/t; 
} 
