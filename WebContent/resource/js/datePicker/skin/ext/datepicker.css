/* 
 * My97 DatePicker 4.7
 * Ƥ������:ext
 * Ƥ������:CssRain
 * ����blog:http://www.CssRain.cn
 * ��������:<EMAIL>
 */

/* ����ѡ������ DIV */
.WdateDiv{
	width:180px;
	background-color:#fff;
	border:1px solid #718BB7;
}
/* ˫�������Ŀ�� */
.WdateDiv2{
	width:360px;
}
.WdateDiv *{font-size:9pt;}

/****************************
 * ����ͼ�� ȫ����A��ǩ
 ***************************/
.WdateDiv .NavImg a{
	margin-top:3px;
	cursor:pointer;
	display:block;
	width:15px;
	height:15px;
}
.WdateDiv .NavImgll a{
	background:url(left-btn2.gif) no-repeat center center;
	float:left; 
	margin-left:2px;
}
.WdateDiv .NavImgl a{
	background:url(left-btn.gif) no-repeat center center;
	float:left; 
	margin-left:2px;
}
.WdateDiv .NavImgr a{
	background:url(right-btn.gif) no-repeat center center;
	float:right;
	margin-right:2px;
}
.WdateDiv .NavImgrr a{
	background:url(right-btn2.gif) no-repeat center center;
	float:right;
	margin-right:2px;
}

/****************************
 * ����·����
 ***************************/
/* ����·��� DIV */
.WdateDiv #dpTitle{
	height:22px;
	background:transparent url(hd-sprite.gif) repeat-x scroll 0 -83px;
	color:#FFFFFF;
	font-family:"sans serif",tahoma,verdana,helvetica;
	font-size:12px;
	font-size-adjust:none;
	font-stretch:normal;
	font-style:normal;
	font-variant:normal;
	font-weight:bold;
	padding-top:2px;
}
/* ����·������ INPUT */
.WdateDiv .yminput{
	margin-top:2px;
	text-align:center;
	border:0px;
	height:20px;
	width:50px;
	color:#FFF;
	background-color:transparent;
	cursor:pointer;
}
/* ����·�������ý���ʱ����ʽ INPUT */
.WdateDiv .yminputfocus{
	margin-top:2px;
	text-align:center;
	border:#939393 1px solid;
	font-weight:bold;
	color:#034c50;	
	height:16px;
	width:50px;
}
/* �˵�ѡ��� DIV */
.WdateDiv .menuSel{
	z-index:1;
	position:absolute;
	background-color:#FFFFFF;
	border:1px solid #718BB7;
	display:none;
}
/* �˵�����ʽ TD */
.WdateDiv .menu{
	cursor:pointer;
	background-color:#fff;
	color:#11777C;
}
/* �˵���mouseover��ʽ TD */
.WdateDiv .menuOn{
	cursor:pointer;
	background-color: #B3CEEF;
}
/* �˵���Чʱ����ʽ TD */
.WdateDiv .invalidMenu{
	color:#aaa;
}
/* ��ѡ����ƫ�� DIV */
.WdateDiv .YMenu{
	margin-top:16px;
}
/* ��ѡ����ƫ�� DIV */
.WdateDiv .MMenu{
	margin-top:16px;
	*width:62px;
}
/* ʱѡ����λ�� DIV */
.WdateDiv .hhMenu{
	margin-top:-90px; 
	margin-left:26px;
}
/* ��ѡ����λ�� DIV */
.WdateDiv .mmMenu{
	margin-top:-46px; 
	margin-left:26px;
}
/* ��ѡ����λ�� DIV */
.WdateDiv .ssMenu{
	margin-top:-24px; 
	margin-left:26px;
}

/****************************
 * �����
 ***************************/
 .WdateDiv .Wweek {
 	text-align:center;
	background:#DAF3F5;
	border-right:#BDEBEE 1px solid;
 }
/****************************
 * ����,�������
 ***************************/
 /* ������ TR */
.WdateDiv .MTitle{
	color:#233D6D;
	background:#DFECFB url(glass-bg.gif) repeat-x scroll left top;
	color:#233D6D;
	cursor:default;
	font-size:10px; 
	padding-top:2px;
}
.WdateDiv .MTitle td{
	border-bottom:1px solid #A3BAD9;
}
.WdateDiv .WdayTable2{
	border-collapse:collapse;
	border:black 1px solid;
}
.WdateDiv .WdayTable2 table{
	border:0;
}
/* ��������� TABLE */
.WdateDiv .WdayTable{
	line-height:20px;	
	color:black; 
}
.WdateDiv .WdayTable td{
	text-align:center;
}
/* ���ڸ����ʽ TD */
.WdateDiv .Wday{
	cursor:pointer;
}
/* ���ڸ��mouseover��ʽ TD */
.WdateDiv .WdayOn{
	cursor:pointer;
	background-color:#B3CEEF;
}
/* ��ĩ���ڸ����ʽ TD */
.WdateDiv .Wwday{
	cursor:pointer;
	color:#ab1e1e;
}
/* ��ĩ���ڸ��mouseover��ʽ TD */
.WdateDiv .WwdayOn{
	cursor:pointer;
	background-color:#B3CEEF;
}
.WdateDiv .Wtoday{
	cursor:pointer;
	color:red;
}
.WdateDiv .Wselday{
	background-color:#B3CEEF;
}
.WdateDiv .WspecialDay{
	background-color:#66F4DF;
}
/* �����·ݵ����� */
.WdateDiv .WotherDay{ 
	cursor:pointer;
	color:#AAAAAA;
}
/* �����·ݵ�����mouseover��ʽ */
.WdateDiv .WotherDayOn{ 
	cursor:pointer;
	background-color:#B3CEEF;
}
/* ��Ч���ڵ���ʽ,�������ڷ�Χ�������ڸ����ʽ,����ѡ������� */
.WdateDiv .WinvalidDay{
	color:#aaa;
}

/****************************
 * ʱ�����
 ***************************/
/* ʱ���� DIV */
.WdateDiv #dpTime{
	width:120px;
	text-align:left;
	margin-left:32px;
	height:20px;
	line-height:20px;
	padding-top:1px;
}
/* ʱ������ SPAN */
.WdateDiv #dpTime #dpTimeStr{
	margin-left:1px;
	color:#233D6D;
}
/* ʱ������� INPUT */
.WdateDiv #dpTime input{
	height:16px;
	width:18px;
	text-align:center;
	color:#333;
	border:#A3BAD9 1px solid;	
}
/* ʱ�� ʱ INPUT */
.WdateDiv #dpTime .tB{
	border-right:0px;
}
/* ʱ�� �ֺͼ���� ':' INPUT */
.WdateDiv #dpTime .tE{
	border-left:0;
	border-right:0;
}
/* ʱ�� �� INPUT */
.WdateDiv #dpTime .tm{
	width:7px;
	border-left:0;
	border-right:0;
}
/* ʱ���ұߵ����ϰ�ť BUTTON */
.WdateDiv #dpTime #dpTimeUp{
	height:8px;
	width:13px;
	border:0px;
	background:url(img.gif) no-repeat -32px -16px;
	cursor:pointer;
	margin-bottom:0;
	padding-bottom:0;
}
/* ʱ���ұߵ����°�ť BUTTON */
.WdateDiv #dpTime #dpTimeDown{
	height:8px;
	width:13px;
	border:0px;
	background:url(img.gif) no-repeat -48px -16px;
	cursor:pointer;
	margin-top:0;
	padding-top:0;
}
/****************************
 * ����
 ***************************/
 .WdateDiv #dpQS {
 	float:left;
	margin-left:3px;
	margin-top:9px;
	background:url(dateselect.gif) no-repeat;
	width:20px;
	height:20px;
	cursor:pointer;
 }
.WdateDiv #dpControl {
	text-align:right;
	margin-top:3px;
	background:#DFECFB url(glass-bg.gif) repeat-x scroll left top;
	border-top:1px solid #A3BAD9;
	padding:4px;
}
.WdateDiv .dpButton{ 
	width:44px;
	height:22px;
	background:#083772 none repeat scroll 0 0;
	border-color:#3366CC #000055 #000055 #3366CC;
	border-style:solid;
	border-width:1px;
	color:white;
	cursor:pointer;

}