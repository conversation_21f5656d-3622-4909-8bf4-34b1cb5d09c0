/* 
 * My97 DatePicker 4.7
 * Ƥ������:simple
 */

/* ����ѡ������ DIV */
.WdateDiv{
	width:150px;
	background-color:#fff;
	border:#666 1px solid;
	padding:1px;
}
/* ˫�������Ŀ�� */
.WdateDiv2{
	width:300px;
}
.WdateDiv *{font-size:9pt;}

/****************************
 * ����ͼ�� ȫ����A��ǩ
 ***************************/
.WdateDiv .NavImg a{
	cursor:pointer;
	display:block;
	width:16px;
	height:16px;
}

.WdateDiv .NavImgll a{
	float:left;
	background-image:url(navLeft.gif);
}
.WdateDiv .NavImgl a{
	float:left;
	background-image:url(left.gif);
}
.WdateDiv .NavImgr a{
	float:right;
	background-image:url(right.gif);
}
.WdateDiv .NavImgrr a{
	float:right;
	background-image:url(navRight.gif);
}

/* ����·��� DIV */
.WdateDiv #dpTitle{
	height:22px;
	border:#aaa 1px solid;
	color:#009;
	background:url(bg.gif);
	margin-bottom:2px;
}
/* ����·������ INPUT */
.WdateDiv .yminput{
	margin-top:1px;
	text-align:center;
	border:0px;
	line-height:15px;
	height:15px;
	color:#224;
	background:transparent;
	cursor:pointer;
	width:36px;
}
/* ����·�������ý���ʱ����ʽ INPUT */
.WdateDiv .yminputfocus{
	text-align:center;
	font-weight:bold;
	color:#034c50;
	border:#939393 1px solid;
	height:16px;
	width:36px;
}
/* �˵�ѡ��� DIV */
.WdateDiv .menuSel{
	z-index:1;
	position:absolute;
	background-color:#FFFFFF;
	border:#bbc 1px solid;
	display:none;
}
/* �˵�����ʽ TD */
.WdateDiv .menu{
	cursor:pointer;
	background-color:#fff;
	color:#446;
}
/* �˵���mouseover��ʽ TD */
.WdateDiv .menuOn{
	cursor:pointer;
	background:background;
	color:#fff;
}
/* �˵���Чʱ����ʽ TD */
.WdateDiv .invalidMenu{
	color:#aaa;
}
/* ��ѡ����ƫ�� DIV */
.WdateDiv .YMenu{
	margin-top:16px;
}
/* ��ѡ����ƫ�� DIV */
.WdateDiv .MMenu{
	margin-top:16px;
	width:62px;
}
/* ʱѡ����λ�� DIV */
.WdateDiv .hhMenu{
	margin-top:-90px; 
	margin-left:26px;
}
/* ��ѡ����λ�� DIV */
.WdateDiv .mmMenu{
	margin-top:-46px; 
	margin-left:26px;
}
/* ��ѡ����λ�� DIV */
.WdateDiv .ssMenu{
	margin-top:-24px; 
	margin-left:26px;
}

/****************************
 * �����
 ***************************/
 .WdateDiv .Wweek {
 	text-align:center;
	background:#DAF3F5;
 }
/****************************
 * ����,�������
 ***************************/
 /* ������ TR */
.WdateDiv .MTitle{
	color:#fff;
	background-color:#777;
}
.WdateDiv .WdayTable2{
	border-collapse:collapse;
	border:#808080 1px solid;
}
.WdateDiv .WdayTable2 table{
	border:0;
}
/* ��������� TABLE */
.WdateDiv .WdayTable{
	line-height:18px;	
	color:#001;
	background-color:#f7f7f7;
	border:#808080 1px solid;
	margin-bottom:1px;
}
.WdateDiv .WdayTable td{
	text-align:center;
}
/* ���ڸ����ʽ TD */
.WdateDiv .Wday{
	cursor:pointer;
	background: url(bg_calendar.gif) no-repeat right bottom;
}
/* ���ڸ��mouseover��ʽ TD */
.WdateDiv .WdayOn{
	cursor:pointer;
	background:#333 url(bg_calendar.gif) no-repeat left top;
	color:#00d;
}
/* ��ĩ���ڸ����ʽ TD */
.WdateDiv .Wwday{
	cursor:pointer;
	background: url(bg_calendar.gif) no-repeat right bottom;
	color:#F40;
}
/* ��ĩ���ڸ��mouseover��ʽ TD */
.WdateDiv .WwdayOn{
	cursor:pointer;
	background:#333 url(bg_calendar.gif) no-repeat left top;
	color:#00c;
}
.WdateDiv .Wtoday{
	cursor:pointer;
	color:blue;
}
.WdateDiv .Wselday{
	background-color:#777;
	color:#fff;
}
.WdateDiv .WspecialDay{
	background-color:#66F4DF;
}
/* �����·ݵ����� */
.WdateDiv .WotherDay{ 
	cursor:pointer;
	background: url(bg_calendar.gif) no-repeat right bottom;
	color:#555;	
}
/* �����·ݵ�����mouseover��ʽ */
.WdateDiv .WotherDayOn{ 
	cursor:pointer;
	background:#e8e8e8;	
}
/* ��Ч���ڵ���ʽ,�������ڷ�Χ�������ڸ����ʽ,����ѡ������� */
.WdateDiv .WinvalidDay{
	color:#ccc;
}

/****************************
 * ʱ�����
 ***************************/
/* ʱ���� DIV */
.WdateDiv #dpTime{
	
}
/* ʱ������ SPAN */
.WdateDiv #dpTime #dpTimeStr{
	margin-left:1px;
	color:#223;
}
/* ʱ������� INPUT */
.WdateDiv #dpTime input{
	height:16px;
	width:18px;
	text-align:center;
	color:#334;
	border:#aaa 1px solid;	
}
/* ʱ�� ʱ INPUT */
.WdateDiv #dpTime .tB{
	border-right:0px;
}
/* ʱ�� �ֺͼ���� ':' INPUT */
.WdateDiv #dpTime .tE{
	border-left:0;
	border-right:0;
}
/* ʱ�� �� INPUT */
.WdateDiv #dpTime .tm{
	width:7px;
	border-left:0;
	border-right:0;
}
/* ʱ���ұߵ����ϰ�ť BUTTON */
.WdateDiv #dpTime #dpTimeUp{
	height:10px;
	width:13px;
	border:0px;
	background-image:url(up.jpg);
}
/* ʱ���ұߵ����°�ť BUTTON */
.WdateDiv #dpTime #dpTimeDown{
	height:10px;
	width:13px;
	border:0px;
	background-image:url(down.jpg);
}
/****************************
 * ����
 ***************************/
 .WdateDiv #dpQS {
 	float:left;
	margin-right:3px;
	margin-top:3px;
	background:url(qs.jpg) no-repeat 0 50%;
	width:20px;
	height:20px;
	cursor:pointer;
 }
.WdateDiv #dpControl {
	text-align:right;
	margin-top:3px;
}
.WdateDiv .dpButton{ 
	height:20px;
	width:36px;
	margin-top:2px;
	border:#afafaf 1px solid;
	background-color:#e9e9e9;
	color:#001;
}