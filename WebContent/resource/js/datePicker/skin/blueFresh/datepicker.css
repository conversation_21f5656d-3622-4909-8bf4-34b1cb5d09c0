/* 
 * My97 DatePicker 4.7
 * Ƥ������:blueFresh
 */

/* ����ѡ������ DIV */
.WdateDiv{
	width:180px;
	background-color:#EAF3FB;
	border:#bbb 1px solid;
	padding:1px;
	font-family:Tahoma,Verdana;
}
/* ˫�������Ŀ�� */
.WdateDiv2{
	width:360px;
}
.WdateDiv *{font-size:12px;}

/****************************
 * ����ͼ�� ȫ����A��ǩ
 ***************************/
.WdateDiv .NavImg a{
	display:block;
	cursor:pointer;
	height:16px;
	width:16px;
}

.WdateDiv .NavImgll a{
	float:left;
	background:transparent url(img.gif) no-repeat scroll 0 0;
}
.WdateDiv .NavImgl a{
	float:left;
	background:transparent url(img.gif) no-repeat scroll -16px 0;
}
.WdateDiv .NavImgr a{
	float:right;
	background:transparent url(img.gif) no-repeat scroll -32px 0;
}
.WdateDiv .NavImgrr a{
	float:right;
	background:transparent url(img.gif) no-repeat scroll -48px 0;
}

/****************************
 * ����·����
 ***************************/
/* ����·��� DIV */
.WdateDiv #dpTitle{
	height:24px;
	margin-bottom:2px;
	padding:1px;
}
/* ����·������ INPUT */
.WdateDiv .yminput{
	margin-top:2px;
	text-align:center;
	border:0px;
	height:20px;
	width:48px;
	cursor:pointer;		
	background-color:#EAF3FB;
}
/* ����·�������ý���ʱ����ʽ INPUT */
.WdateDiv .yminputfocus{
	margin-top:2px;
	text-align:center;
	font-weight:bold;
	color:blue;
	border:#ccc 1px solid;
	height:20px;
	width:50px;
}
/* �˵�ѡ��� DIV */
.WdateDiv .menuSel{
	z-index:1;
	position:absolute;
	background-color:#FFFFFF;	
	border:#666 1px solid;
	display:none;
}

.WdateDiv .menuSel td{
	font-size:12px;
}

/* �˵�����ʽ TD */
.WdateDiv .menu{
	cursor:pointer;
	background-color:#fff;
	font-size:12px;
	color:#427CA8;
}
/* �˵���mouseover��ʽ TD */
.WdateDiv .menuOn{
	cursor:pointer;
	font-size:12px;
	background-color:#D32C0E;
	color:#fff;
}
/* �˵���Чʱ����ʽ TD */
.WdateDiv .invalidMenu{
	color:#aaa;
}

/* ��ѡ����ƫ�� DIV */
.WdateDiv .YMenu{
	margin-top:20px;
}

/* ��ѡ����ƫ�� DIV */
.WdateDiv .MMenu{
	margin-top:20px;
	*width:62px;
}
/* ʱѡ����λ�� DIV */
.WdateDiv .hhMenu{
	margin-top:-87px; 
	margin-left:26px;
}
/* ��ѡ����λ�� DIV */
.WdateDiv .mmMenu{
	margin-top:-45px; 
	margin-left:26px;
}
/* ��ѡ����λ�� DIV */
.WdateDiv .ssMenu{
	margin-top:-23px; 
	margin-left:26px;
}

/****************************
 * �����
 ***************************/
 .WdateDiv .Wweek {
 	text-align:center;
	background:#DAF3F5;
	border-right:#BDEBEE 1px solid;
 }
/****************************
 * ����,�������
 ***************************/
/* ������ TR */
.WdateDiv .MTitle{
	background-color:#333333;
	color:#fff;
}
.WdateDiv .WdayTable2{
	border-collapse:collapse;
	border:#c5d9e8 1px solid;
}
.WdateDiv .WdayTable2 table{
	border:0;
}
/* ��������� TABLE */
.WdateDiv .WdayTable{
	line-height:20px;
	border:#c5d9e8 1px solid;
	background:#fff;
	border-collapse:collapse;
}
.WdateDiv .WdayTable td{
	text-align:center;
}
/* ���ڸ����ʽ TD */
.WdateDiv .Wday{
	cursor:pointer;
	background:#B6E0FA;
	border:1px solid #fcfcfc;
}
/* ���ڸ��mouseover��ʽ TD */
.WdateDiv .WdayOn{
	cursor:pointer;
	background-color:#fff;
	color:#FF6600;
}
/* ��ĩ���ڸ����ʽ TD */
.WdateDiv .Wwday{
	cursor:pointer;
	color:#D32C0E;
}
/* ��ĩ���ڸ��mouseover��ʽ TD */
.WdateDiv .WwdayOn{
	cursor:pointer;
	color:#FF6600;
	background-color:#fff;
}
.WdateDiv .Wtoday{
	cursor:pointer;
	color:#fff;
	background:#FF6600; 
}
.WdateDiv .Wselday{
	background-color:#FF6666;
	color:#fff;
}
.WdateDiv .WspecialDay{
	background-color:#66F4DF;
}
/* �����·ݵ����� */
.WdateDiv .WotherDay{ 
	cursor:pointer;
	color:#555555;
	background:#E3F1FB; 
}
/* �����·ݵ�����mouseover��ʽ */
.WdateDiv .WotherDayOn{ 
	cursor:pointer;
	background-color:#fff;	
}
/* ��Ч���ڵ���ʽ,�������ڷ�Χ�������ڸ����ʽ,����ѡ������� */
.WdateDiv .WinvalidDay{
	background:#ECF1F4; 
	color:#888;
}

/****************************
 * ʱ�����
 ***************************/
/* ʱ���� DIV */
.WdateDiv #dpTime{
	float:left;
	margin-top:3px;
	margin-right:30px;
}
.WdateDiv #dpTime table{
border-collapse:collapse;
}
.WdateDiv #dpTime td{
text-align:center;}

/* ʱ������ SPAN */
.WdateDiv #dpTime #dpTimeStr{
	margin-left:1px;
}
/* ʱ������� INPUT */
.WdateDiv #dpTime input{
	height:18px;
	width:18px;
	text-align:center;
	border:#ccc 1px solid;	
}
/* ʱ�� ʱ INPUT */
.WdateDiv #dpTime .tB{
	border-right:0px;
}
/* ʱ�� �ֺͼ���� ':' INPUT */
.WdateDiv #dpTime .tE{
	border-left:0;
	border-right:0;
}
/* ʱ�� �� INPUT */
.WdateDiv #dpTime .tm{
	width:7px;
	border-left:0;
	border-right:0;
}
/* ʱ���ұߵ����ϰ�ť BUTTON */
.WdateDiv #dpTime #dpTimeUp{
	height:10px;
	width:13px;
	border:0px;
	background:url(img.gif) no-repeat -32px -16px;
}
/* ʱ���ұߵ����°�ť BUTTON */
.WdateDiv #dpTime #dpTimeDown{
	height:10px;
	width:13px;
	border:0px;
  background:url(img.gif) no-repeat -48px -16px;
}
/****************************
 * ����
 ***************************/
 .WdateDiv #dpQS {
 	float:left;
	margin-right:3px;
	margin-top:3px;
	background:url(img.gif) no-repeat 0px -16px;
	width:20px;
	height:20px;
	cursor:pointer;
 }
.WdateDiv #dpControl {
	text-align:right;
	margin-top:3px;
	padding-bottom:1px; 
}
.WdateDiv .dpButton{ 
	height:20px;
	width:45px;
	border:#ccc 1px solid;
	margin-top:2px;
	margin-right:1px;
}