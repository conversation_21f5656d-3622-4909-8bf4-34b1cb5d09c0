.webfx-tree-container {
	margin: 0px;
	padding: 0px;
	font: icon;
	color:black;
	white-space: nowrap;
}

.webfx-tree-item {
	padding: 0px;
	margin: 0px;
	font: icon;
	color: black;
	white-space: nowrap;
}

.webfx-tree-item a, .webfx-tree-item a:active, .webfx-tree-item a:hover {
	margin-left: 3px;
	padding: 1px 2px 1px 2px;
}

.webfx-tree-item a,.webfx-tree-item a:visited  {
	color: black;
	text-decoration: none;
}

.webfx-tree-item a:hover {
	color: blue;
	text-decoration: none;
}

.webfx-tree-item a:active {
	background: highlight;
	color: white;
	text-decoration: none;
}

.webfx-tree-item img {
	vertical-align: middle;
	border: 0px;
}

.webfx-tree-icon {
	width: 16px;
	height: 16px;
}
.webfx-tree-item a.selected {
	color: white;
	background: highlight;
}

.webfx-tree-item a.selected-inactive {
	color: white;
	background: #dddddd;
}

.tree-check-box {
 		width:		auto;
 		margin:		0;
 		padding:	0;
 		height:		14px;		
 		vertical-align:	middle;
 }

 .tree-radio {
 		width:		auto;
 		margin:		0;
 		padding:	0;
 		height:		14px;		
 		vertical-align:	middle;
 }