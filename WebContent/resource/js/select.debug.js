/**
 * select.js enable the select tag as a div select
 * @requires common.js
 */
 
var SelectPlus=Class.create();
SelectPlus.prototype={
	_cachedData:new Object(),//cache all origin the select options
	initialize:function (option){
		option=Object.extend({
			zIndex:10000,
			maxHeight:400
		},option||{});
		this.option=option;
		var objs=document.getElementsByTagName("select");
		var arrayObjs=new Array();// the objects to remove
		for (var i = 0; i < objs.length; i++) {
			var o=objs[i];
			//sign up id attribute if not exist
			if(!o.id){o.id=("o"+(new Date()).getTime());}
			var oid=o.id;
			var originOptions=o.options;
			this._cachedData[oid]=originOptions;//cache the data
			var originText="";
			var originValue="";
			optionHtml="";
			for (var j = 0; j < originOptions.length; j++) {
				if(originOptions[j].selected){
					originText=originOptions[j].text;
					originValue=originOptions[j].value;
				}
				optionHtml+="<a "+(originOptions[j].selected?" class='ui_ddl_selcted' id='"+o.id+"_selected' ":"")+" href=\"javascript:void(0)\" value=\""+originOptions[j].value+"\">"+(""==originOptions[j].text?"&nbsp;":originOptions[j].text)+"</a>";
			}
			
			//calculate the pop options div height  
			//the width of pop options is the select's width
			var popH=originOptions.length*24>option.maxHeight?option.maxHeight:originOptions.length*24;
			//which is the corner the select?
			var w=o.offsetWidth;
			w=w-6;
			var h=o.offsetHeight;
			var offset=util.getOffset(o);
			var pageSize=util.getPageSize();
			var cornerClass="ui_ddl_a";
			// top or bottom?
			if(offset.top+h+popH>pageSize.pageHeight){
				cornerClass+="bottom";
			}else{
				cornerClass+="top";
			}
			//left or right?
			if(offset.left+w>pageSize.pageWidth){
				cornerClass+="right";
			}else{
				cornerClass+="left";
			}
			//nextSibling
			var nextSibling=o.nextSibling;
			var oReplaced=document.createElement("span");
			oReplaced.style.width=w+"px";
			oReplaced.className="ui_ddl "+cornerClass;
			oReplaced.innerHTML="<input id='"+o.id+"_txt' class='ui_ddl_txt' type='text' name='"+o.name+"_text' style='width:"+(w-23)+"px' value='"+originText+"' /><input id='"+o.id+"_value' type='hidden' name='"+o.name+"' value='"+originValue+"' /><input id='"+o.id+"_arrow'  class='ui_ddl_arrow' type='button'  value='��' /><div id='"+o.id+"_options' class='ui_ddl_options' style='width:"+w+"px;height:"+popH+"px'>"+optionHtml+"</div>";
			
			var c=o.parentNode;// the container
			
			c.insertBefore(oReplaced,o);
			arrayObjs.push(objs[i]);
			o.style.display="none";// hide the origin select 
			o.name="_"+o.name;// rename the name attribute
			
			// toggle option panel when arrow clicked
			var _t=this.toggle;
			var _cd=this._cachedData;
			var _a=this.activate;
			var _this=this;
			var _focusOption=this.focusOption;
			util.addEvent($(o.id+"_arrow"),"click",function (){
				var thisid=this.id.substr(0,this.id.length-6);
				var evt=util.getEvent();
				_a.apply(_this,[evt,thisid]);
			});
			util.addEvent($(o.id+"_txt"),"click",function (){
				var thisid=this.id.substr(0,this.id.length-4);
				var evt=util.getEvent();
				_a.apply(_this,[evt,thisid]);
			});
			util.addEvent($(o.id+"_txt"),"keydown",function (){
				var evt=util.getEvent();
				var code=evt.keyCode;
				var thisid=this.id.substr(0,this.id.length-4);
				if(code==13 || code==9){
					var opts=$(thisid+"_options").getElementsByTagName("a");
					if($(thisid+"_selected")){
						$(thisid+"_selected").id="";
					}
					for (var opti = 0; opti < opts.length; opti++) {
						var o=opts[opti];
						if(o.style.display=="block" && /ui_ddl_selcted/gi.test(o.className)){
							o.id=thisid+"_selected";
							$(thisid+"_arrow").value="��";
							$(thisid).value=o.getAttribute("value");
							$(thisid+"_txt").value=o.innerHTML.replace("&nbsp;","");
							$(thisid+"_options").style.display="none";
							opti=opts.length;
						}
					}
					evt.cancelBubble =true;
					evt.returnValue=false;
					if(evt.stopPropagation){evt.stopPropagation();}
					if(evt.preventDefault){evt.preventDefault();}
				}
			});
			util.addEvent($(o.id+"_txt"),"keyup",function (){
				var v=this.value;
				var thisid=this.id.substr(0,this.id.length-4);
				if($(thisid+"_options").style.display!="block"){return false;}// make sure the option panel is visible
				var evt=util.getEvent();
				
				
				// 40 down 38 up 13 enter 9 tab 13 enter
				var code=evt.keyCode;
				switch(code) {
					case 38: {// up
						var found=false;
						var _lastOption=null;
						var opts=$(thisid+"_options").getElementsByTagName("a");
						for (var opti = 0; opti < opts.length; opti++) {
							var o=opts[opti];
							if(o.style.display=="block"){
								if(/ui_ddl_selcted/gi.test(o.className)){
									util.removeClass(o,"ui_ddl_selcted");
									if(_lastOption)util.removeClass(_lastOption,"ui_ddl_selcted");
									if(_lastOption){
										util.addClass(_lastOption,"ui_ddl_selcted");
										found=true;
									}
									
								}
								_lastOption=o;
							}
						}
						// select the last one when no selection
						if(!found && _lastOption){
							util.addClass(_lastOption,"ui_ddl_selcted");
						}
						//scroll to the new selection
						_focusOption(thisid);
						
						evt.cancelBubble =true;
						if(evt.stopPropagation){evt.stopPropagation();}
						break;
					}
					case 40: {// down
						var isCurrentLast=true;// current selection is the last one?
						var _lastOption=null;
						var firstOption=null;
						var opts=$(thisid+"_options").getElementsByTagName("a");
						for (var opti = 0; opti < opts.length ; opti++) {
								var o=opts[opti];
								if(o.style.display=="block"){
								if(!firstOption)firstOption=o;
									if(_lastOption && /ui_ddl_selcted/gi.test(_lastOption.className)){
										util.removeClass(_lastOption,"ui_ddl_selcted");
										util.addClass(o,"ui_ddl_selcted");
										isCurrentLast=false;
										opti=opts.length;// stop the iteration
									}
									_lastOption=o;
							}
						}
						// select the first one when no selection
						if(isCurrentLast){
							if(_lastOption) util.removeClass(_lastOption,"ui_ddl_selcted");
							if(firstOption) util.addClass(firstOption,"ui_ddl_selcted");
						}
						_focusOption(thisid);
						evt.cancelBubble =true;
						if(evt.stopPropagation){evt.stopPropagation();}
						break;
					}
					default:
						var opts=$(thisid+"_options").getElementsByTagName("a");
						for (var opti = 0; opti < opts.length; opti++) {
							var o=opts[opti];
							o.className=o.className.replace(/ui_ddl_selcted/gi,"");
							var re=new RegExp(v);
							if(re.test(o.innerHTML)){
								o.style.display="block";
							}else{
								o.style.display="none";
							}
						}
						break;
				}
			});
			// set value and hide option panel where option is clicked
			var opts=$(o.id+"_options").childNodes;
			for (var oi = 0; oi < opts.length; oi++) {
				util.addEvent(opts[oi],"click",function (){
					var thisid=this.parentNode.id.substr(0,this.parentNode.id.length-8);
					if($(thisid+"_selected")){
						util.removeClass($(thisid+"_selected"),"ui_ddl_selcted");
						$(thisid+"_selected").id="";
					}
					util.addClass(this,"ui_ddl_selcted");
					this.id=(thisid+"_selected");
					this.parentNode.previousSibling.value="��";
					this.parentNode.previousSibling.previousSibling.value=this.getAttribute("value");
					this.parentNode.previousSibling.previousSibling.previousSibling.value=this.innerHTML.replace("&nbsp;","");
					this.parentNode.style.display="none";
				});
			}
			
		}
		// hide option panel when click document
		var _cd=this._cachedData;
		var _t=this.toggle;
		util.addEvent(document.body,"click",function (){
			var evt=util.getEvent();
			var src=evt.srcElement||evt.target;//get the event source
			for(k in _cd){
				if($(k+"_options").style.display=="block"){_t(k);}
			}
		});
	},
	toggle:function (id){
		var optionPanel=$(id+"_options");
		var arrow=$(id+"_arrow");
		if(optionPanel.style.display!="block"){
			optionPanel.style.display="block";
			arrow.value="��";
		}else{
			arrow.value="��";
			optionPanel.style.display="none";
		}
		return false;
	},activate:function (evt,id){
		var thisid=id;
		if($(thisid+"_options").style.display!="block"){
			this.toggle(thisid);
		}
		// hide other option panel
		for(k in this._cachedData){
			if($(k+"_options").style.display=="block" && k!=thisid){this.toggle(k);}
		}
		evt.cancelBubble =true;
		if(evt.stopPropagation){evt.stopPropagation();}
		var opts=$(thisid+"_options").getElementsByTagName("a");
		for (var opti = 0; opti < opts.length; opti++) {// show all options
			var o=opts[opti];
			o.style.display="block";
		}
		// scroll to the selection
		var t=$(id+"_selected").offsetTop;
		if($(id+"_txt").focus)$(id+"_txt").focus();
		$(id+"_options").scrollTop=t;
		return false;
	},focusOption:function (thisid){
		var opts=$(thisid+"_options").getElementsByTagName("a");
		for (var oi = 0; oi < opts.length; oi++) {
			var o=opts[oi];
			if(o.style.display=="block" &&  /ui_ddl_selcted/gi.test(o.className)){
				var t=o.offsetTop;
				if($(thisid+"_txt").focus)$(thisid+"_txt").focus();
				var h=$(thisid+"_options").offsetHeight;
				$(thisid+"_options").scrollTop=(t-(h/2));
			}
		}
	}
};
util.addEvent(window,"load",function (){
	var o=new SelectPlus();
});