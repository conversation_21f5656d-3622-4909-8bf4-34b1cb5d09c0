
ECSideUtil.extnduncheckAll=function(checkcontrolObj,checkboxname,formid){

	var form=ECSideList[formid].ECForm;
	if (!form.elements[checkboxname]){ return;}
	for(i = 0; i < form.elements[checkboxname].length; i++) {
		if (!form.elements[checkboxname][i].disabled){
			if(form.elements[checkboxname][i].checked){
				form.elements[checkboxname][i].checked=false;
			}else{
				form.elements[checkboxname][i].checked = true;
			}
		}
	}
};
ECSideUtil.getCheckedIds=function(checkcontrolObj,checkboxname,formid){
    var chdids="";
    var num=0;
	var form=document.getElementById(formid);
	var cbs=form.getElementsByTagName("input");
	for(var i = 0; i < cbs.length; i++) {
		//alert(checkboxname+"\n"+cbs[i].name);
		if (!cbs[i].disabled  && cbs[i].type.toLowerCase()=='checkbox' && cbs[i].checked && cbs[i].name==checkboxname){
			num+=1;
			if(num>1) chdids+=",";
			chdids+=cbs[i].value;
		}
	}
	return chdids+"$$"+num.toString();
};
ECSideUtil.extndcheckAll=function(checkcontrolObj,checkboxname,formid){

	var form=ECSideList[formid].ECForm;
	if (!form.elements[checkboxname]){ return;}
	for(i = 0; i < form.elements[checkboxname].length; i++) {
		if (!form.elements[checkboxname][i].disabled){
				form.elements[checkboxname][i].checked = true;
		}
	}
};
/**
	extnddelcheck(checkcontrolObj,checkboxname,formid,[needconfirm])
*/
ECSideUtil.extnddelcheck=function(checkcontrolObj,checkboxname,formid){
	//判断是否选择
	var iNum=0;
	var form=document.forms[formid];
	
	var cbs=form.getElementsByTagName("input");
	
	for(i = 0; i < cbs.length; i++) {
		if (!cbs[i].disabled && cbs[i].type=="checkbox" && cbs[i].name==checkboxname && cbs[i].checked){
			iNum=iNum+1;
		}
	}
	if(iNum==0){
		alert("请选择操作对象！");
		return false;
	}
	var needconfirm=typeof(arguments[3])=="boolean"?arguments[3]:true;
	if(needconfirm){
		return confirm("你确定执行该操作?");
	}else{
		return true;
	}
};