$(document).ready(function(){

		   $('.advanceds').hide();
	       $('#advanced').toggle(function(){
	           $('.advanceds').show();
	           },function(){
	           $('.advanceds').hide();
	           });

			$(':submit').submit(function(){
				 var sql_Name = $('#sql_Name').val().split('_');
				 var selects = document.getElementsByTagName("select");
				 var tempstr = "";
				 var type= "";
				 var operator="";
				 var name="";
				 var name1="";
				 var sltvalue="";
				 for(var i=0 ; i<sql_Name.length; i++){
					 	for(var j=0;j<selects.length;j++){
				    	 type = sql_Name[i].substring(0,3);
					     operator = sql_Name[i].substring(3,5);
				    	 name = sql_Name[i].substring(5,sql_Name[i].length);
				    	 name1 = selects[j].name;
				    	 name1 = name1.substring(0,name1.length-1);
					    if(name == name1){
				    		sltvalue = selects[j].options[selects[j].selectedIndex].value;
				    		sql_Name[i] = type+sltvalue+name;
						}
					 }
				    tempstr += sql_Name[i]+"_";
				 }
				 $('#sql_Name').val(tempstr);
					return true;
				});
	});