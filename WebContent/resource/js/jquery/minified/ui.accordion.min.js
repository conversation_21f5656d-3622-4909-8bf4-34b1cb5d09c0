(function($){$.widget("ui.accordion",{init:function(){var a=this.options;if(a.navigation){var b=this.element.find("a").filter(a.navigationFilter);if(b.length){if(b.filter(a.header).length){a.active=b}else{a.active=b.parent().parent().prev();b.addClass("current")}}}a.headers=this.element.find(a.header);a.active=findActive(a.headers,a.active);if($.browser.msie){this.element.find('a').css('zoom','1')}if(!this.element.hasClass("ui-accordion")){this.element.addClass("ui-accordion");$("<span class='ui-accordion-left'/>").insertBefore(a.headers);$("<span class='ui-accordion-right'/>").appendTo(a.headers);a.headers.addClass("ui-accordion-header").attr("tabindex","0")}var c;if(a.fillSpace){c=this.element.parent().height();c=Math.min(c,parseInt(jQuery.curCSS(this.element.parent()[0],"height"),10));a.headers.each(function(){c-=$(this).outerHeight()});var d=0;var e=a.headers.next().each(function(){d=Math.max(d,$(this).innerHeight()-$(this).height())});e.height(c-d)}else if(a.autoHeight){c=0;a.headers.next().each(function(){c=Math.max(c,$(this).outerHeight())}).height(c)}a.headers.not(a.active||"").next().hide();a.active.parent().andSelf().addClass(a.selectedClass);if(a.event){this.element.bind((a.event)+".accordion",clickHandler)}},activate:function(a){clickHandler.call(this.element[0],{target:findActive(this.options.headers,a)[0]})},destroy:function(){this.options.headers.next().css("display","");if(this.options.fillSpace||this.options.autoHeight){this.options.headers.next().css("height","")}$.removeData(this.element[0],"accordion");this.element.removeClass("ui-accordion").unbind(".accordion")},resize:function(){var a=this.options;var b=0,maxPadding=0;if(a.fillSpace){b=this.element.parent().height();a.headers.each(function(){b-=$(this).outerHeight()});a.headers.next().each(function(){maxPadding=Math.max(maxPadding,$(this).innerHeight()-$(this).height())}).height(b-maxPadding)}else if(a.autoHeight){a.headers.next().each(function(){b=Math.max(b,$(this).outerHeight())}).height(b)}}});function scopeCallback(a,b){return function(){return a.apply(b,arguments)}};function completed(a){if(!$.data(this,"accordion")){return}var b=$.data(this,"accordion");var c=b.options;c.running=a?0:--c.running;if(c.running){return}if(c.clearStyle){c.toShow.add(c.toHide).css({height:"",overflow:""})}$(this).triggerHandler("accordionchange",[$.event.fix({type:'accordionchange',target:b.element[0]}),c.data],c.change)}function toggle(a,b,c,d,e){var f=$.data(this,"accordion").options;f.toShow=a;f.toHide=b;f.data=c;var g=scopeCallback(completed,this);f.running=b.size()===0?a.size():b.size();if(f.animated){if(!f.alwaysOpen&&d){$.ui.accordion.animations[f.animated]({toShow:jQuery([]),toHide:b,complete:g,down:e,autoHeight:f.autoHeight})}else{$.ui.accordion.animations[f.animated]({toShow:a,toHide:b,complete:g,down:e,autoHeight:f.autoHeight})}}else{if(!f.alwaysOpen&&d){a.toggle()}else{b.hide();a.show()}g(true)}}function clickHandler(a){var b=$.data(this,"accordion").options;if(b.disabled){return false}if(!a.target&&!b.alwaysOpen){b.active.parent().andSelf().toggleClass(b.selectedClass);var c=b.active.next(),data={options:b,newHeader:jQuery([]),oldHeader:b.active,newContent:jQuery([]),oldContent:c},f=(b.active=$([]));toggle.call(this,f,c,data);return false}var d=$(a.target);d=$(d.parents(b.header)[0]||d);var e=d[0]==b.active[0];if(b.running||(b.alwaysOpen&&e)){return false}if(!d.is(b.header)){return}b.active.parent().andSelf().toggleClass(b.selectedClass);if(!e){d.parent().andSelf().addClass(b.selectedClass)}var f=d.next(),c=b.active.next(),data={options:b,newHeader:d,oldHeader:b.active,newContent:f,oldContent:c},down=b.headers.index(b.active[0])>b.headers.index(d[0]);b.active=e?$([]):d;toggle.call(this,f,c,data,e,down);return false};function findActive(a,b){return b!=undefined?typeof b=="number"?a.filter(":eq("+b+")"):a.not(a.not(b)):b===false?$([]):a.filter(":eq(0)")}$.extend($.ui.accordion,{defaults:{selectedClass:"selected",alwaysOpen:true,animated:'slide',event:"click",header:"a",autoHeight:true,running:0,navigationFilter:function(){return this.href.toLowerCase()==location.href.toLowerCase()}},animations:{slide:function(c,d){c=$.extend({easing:"swing",duration:300},c,d);if(!c.toHide.size()){c.toShow.animate({height:"show"},c);return}var e=c.toHide.height(),showHeight=c.toShow.height(),difference=showHeight/e;c.toShow.css({height:0,overflow:'auto'}).show();c.toHide.filter(":hidden").each(c.complete).end().filter(":visible").animate({height:"hide"},{step:function(a){var b=(e-a)*difference;if($.browser.msie||$.browser.opera){b=Math.ceil(b)}c.toShow.height(b)},duration:c.duration,easing:c.easing,complete:function(){if(!c.autoHeight){c.toShow.css("height","auto")}c.complete()}})},bounceslide:function(a){this.slide(a,{easing:a.down?"bounceout":"swing",duration:a.down?1000:200})},easeslide:function(a){this.slide(a,{easing:"easeinout",duration:700})}}});$.fn.activate=function(a){return this.accordion("activate",a)}})(jQuery);