(function(C){C.ui={plugin:{add:function(E,F,H){var G=C.ui[E].prototype;for(var D in H){G.plugins[D]=G.plugins[D]||[];G.plugins[D].push([F,H[D]])}},call:function(D,F,E){var H=D.plugins[F];if(!H){return }for(var G=0;G<H.length;G++){if(D.options[H[G][0]]){H[G][1].apply(D.element,E)}}}},cssCache:{},css:function(D){if(C.ui.cssCache[D]){return C.ui.cssCache[D]}var E=C('<div class="ui-gen">').addClass(D).css({position:"absolute",top:"-5000px",left:"-5000px",display:"block"}).appendTo("body");C.ui.cssCache[D]=!!((!(/auto|default/).test(E.css("cursor"))||(/^[1-9]/).test(E.css("height"))||(/^[1-9]/).test(E.css("width"))||!(/none/).test(E.css("backgroundImage"))||!(/transparent|rgba\(0, 0, 0, 0\)/).test(E.css("backgroundColor"))));try{C("body").get(0).removeChild(E.get(0))}catch(F){}return C.ui.cssCache[D]},disableSelection:function(D){C(D).attr("unselectable","on").css("MozUserSelect","none")},enableSelection:function(D){C(D).attr("unselectable","off").css("MozUserSelect","")},hasScroll:function(G,E){var D=/top/.test(E||"top")?"scrollTop":"scrollLeft",F=false;if(G[D]>0){return true}G[D]=1;F=G[D]>0?true:false;G[D]=0;return F}};var B=C.fn.remove;C.fn.remove=function(){C("*",this).add(this).triggerHandler("remove");return B.apply(this,arguments)};function A(E,F,G){var D=C[E][F].getter||[];D=(typeof D=="string"?D.split(/,?\s+/):D);return(C.inArray(G,D)!=-1)}C.widget=function(E,D){var F=E.split(".")[0];E=E.split(".")[1];C.fn[E]=function(J){var H=(typeof J=="string"),I=Array.prototype.slice.call(arguments,1);if(H&&A(F,E,J)){var G=C.data(this[0],E);return(G?G[J].apply(G,I):undefined)}return this.each(function(){var K=C.data(this,E);if(H&&K&&C.isFunction(K[J])){K[J].apply(K,I)}else{if(!H){C.data(this,E,new C[F][E](this,J))}}})};C[F][E]=function(I,H){var G=this;this.widgetName=E;this.widgetBaseClass=F+"-"+E;this.options=C.extend({},C.widget.defaults,C[F][E].defaults,H);this.element=C(I).bind("setData."+E,function(L,J,K){return G.setData(J,K)}).bind("getData."+E,function(K,J){return G.getData(J)}).bind("remove",function(){return G.destroy()});this.init()};C[F][E].prototype=C.extend({},C.widget.prototype,D)};C.widget.prototype={init:function(){},destroy:function(){this.element.removeData(this.widgetName)},getData:function(D){return this.options[D]},setData:function(D,E){this.options[D]=E;if(D=="disabled"){this.element[E?"addClass":"removeClass"](this.widgetBaseClass+"-disabled")}},enable:function(){this.setData("disabled",false)},disable:function(){this.setData("disabled",true)}};C.widget.defaults={disabled:false};C.ui.mouse={mouseInit:function(){var D=this;this.element.bind("mousedown."+this.widgetName,function(E){return D.mouseDown(E)});if(C.browser.msie){this._mouseUnselectable=this.element.attr("unselectable");this.element.attr("unselectable","on")}this.started=false},mouseDestroy:function(){this.element.unbind("."+this.widgetName);(C.browser.msie&&this.element.attr("unselectable",this._mouseUnselectable))},mouseDown:function(F){(this._mouseStarted&&this.mouseUp(F));this._mouseDownEvent=F;var E=this,G=(F.which==1),D=(typeof this.options.cancel=="string"?C(F.target).parents().add(F.target).filter(this.options.cancel).length:false);if(!G||D||!this.mouseCapture(F)){return true}this._mouseDelayMet=!this.options.delay;if(!this._mouseDelayMet){this._mouseDelayTimer=setTimeout(function(){E._mouseDelayMet=true},this.options.delay)}if(this.mouseDistanceMet(F)&&this.mouseDelayMet(F)){this._mouseStarted=(this.mouseStart(F)!==false);if(!this._mouseStarted){F.preventDefault();return true}}this._mouseMoveDelegate=function(H){return E.mouseMove(H)};this._mouseUpDelegate=function(H){return E.mouseUp(H)};C(document).bind("mousemove."+this.widgetName,this._mouseMoveDelegate).bind("mouseup."+this.widgetName,this._mouseUpDelegate);return false},mouseMove:function(D){if(C.browser.msie&&!D.button){return this.mouseUp(D)}if(this._mouseStarted){this.mouseDrag(D);return false}if(this.mouseDistanceMet(D)&&this.mouseDelayMet(D)){this._mouseStarted=(this.mouseStart(this._mouseDownEvent,D)!==false);(this._mouseStarted?this.mouseDrag(D):this.mouseUp(D))}return !this._mouseStarted},mouseUp:function(D){C(document).unbind("mousemove."+this.widgetName,this._mouseMoveDelegate).unbind("mouseup."+this.widgetName,this._mouseUpDelegate);if(this._mouseStarted){this._mouseStarted=false;this.mouseStop(D)}return false},mouseDistanceMet:function(D){return(Math.max(Math.abs(this._mouseDownEvent.pageX-D.pageX),Math.abs(this._mouseDownEvent.pageY-D.pageY))>=this.options.distance)},mouseDelayMet:function(D){return this._mouseDelayMet},mouseStart:function(D){},mouseDrag:function(D){},mouseStop:function(D){},mouseCapture:function(D){return true}};C.ui.mouse.defaults={cancel:null,distance:1,delay:0}})(jQuery);(function(A){A.widget("ui.draggable",A.extend({},A.ui.mouse,{init:function(){var B=this.options;if(B.helper=="original"&&!(/(relative|absolute|fixed)/).test(this.element.css("position"))){this.element.css("position","relative")}this.element.addClass("ui-draggable");(B.disabled&&this.element.addClass("ui-draggable-disabled"));this.mouseInit()},mouseStart:function(F){var H=this.options;if(this.helper||H.disabled||A(F.target).is(".ui-resizable-handle")){return false}var C=!this.options.handle||!A(this.options.handle,this.element).length?true:false;A(this.options.handle,this.element).find("*").andSelf().each(function(){if(this==F.target){C=true}});if(!C){return false}if(A.ui.ddmanager){A.ui.ddmanager.current=this}this.helper=A.isFunction(H.helper)?A(H.helper.apply(this.element[0],[F])):(H.helper=="clone"?this.element.clone():this.element);if(!this.helper.parents("body").length){this.helper.appendTo((H.appendTo=="parent"?this.element[0].parentNode:H.appendTo))}if(this.helper[0]!=this.element[0]&&!(/(fixed|absolute)/).test(this.helper.css("position"))){this.helper.css("position","absolute")}this.margins={left:(parseInt(this.element.css("marginLeft"),10)||0),top:(parseInt(this.element.css("marginTop"),10)||0)};this.cssPosition=this.helper.css("position");this.offset=this.element.offset();this.offset={top:this.offset.top-this.margins.top,left:this.offset.left-this.margins.left};this.offset.click={left:F.pageX-this.offset.left,top:F.pageY-this.offset.top};this.offsetParent=this.helper.offsetParent();var B=this.offsetParent.offset();if(this.offsetParent[0]==document.body&&A.browser.mozilla){B={top:0,left:0}}this.offset.parent={top:B.top+(parseInt(this.offsetParent.css("borderTopWidth"),10)||0),left:B.left+(parseInt(this.offsetParent.css("borderLeftWidth"),10)||0)};var E=this.element.position();this.offset.relative=this.cssPosition=="relative"?{top:E.top-(parseInt(this.helper.css("top"),10)||0)+this.offsetParent[0].scrollTop,left:E.left-(parseInt(this.helper.css("left"),10)||0)+this.offsetParent[0].scrollLeft}:{top:0,left:0};this.originalPosition=this.generatePosition(F);this.helperProportions={width:this.helper.outerWidth(),height:this.helper.outerHeight()};if(H.cursorAt){if(H.cursorAt.left!=undefined){this.offset.click.left=H.cursorAt.left+this.margins.left}if(H.cursorAt.right!=undefined){this.offset.click.left=this.helperProportions.width-H.cursorAt.right+this.margins.left}if(H.cursorAt.top!=undefined){this.offset.click.top=H.cursorAt.top+this.margins.top}if(H.cursorAt.bottom!=undefined){this.offset.click.top=this.helperProportions.height-H.cursorAt.bottom+this.margins.top}}if(H.containment){if(H.containment=="parent"){H.containment=this.helper[0].parentNode}if(H.containment=="document"||H.containment=="window"){this.containment=[0-this.offset.relative.left-this.offset.parent.left,0-this.offset.relative.top-this.offset.parent.top,A(H.containment=="document"?document:window).width()-this.offset.relative.left-this.offset.parent.left-this.helperProportions.width-this.margins.left-(parseInt(this.element.css("marginRight"),10)||0),(A(H.containment=="document"?document:window).height()||document.body.parentNode.scrollHeight)-this.offset.relative.top-this.offset.parent.top-this.helperProportions.height-this.margins.top-(parseInt(this.element.css("marginBottom"),10)||0)]}if(!(/^(document|window|parent)$/).test(H.containment)){var D=A(H.containment)[0];var G=A(H.containment).offset();this.containment=[G.left+(parseInt(A(D).css("borderLeftWidth"),10)||0)-this.offset.relative.left-this.offset.parent.left,G.top+(parseInt(A(D).css("borderTopWidth"),10)||0)-this.offset.relative.top-this.offset.parent.top,G.left+Math.max(D.scrollWidth,D.offsetWidth)-(parseInt(A(D).css("borderLeftWidth"),10)||0)-this.offset.relative.left-this.offset.parent.left-this.helperProportions.width-this.margins.left-(parseInt(this.element.css("marginRight"),10)||0),G.top+Math.max(D.scrollHeight,D.offsetHeight)-(parseInt(A(D).css("borderTopWidth"),10)||0)-this.offset.relative.top-this.offset.parent.top-this.helperProportions.height-this.margins.top-(parseInt(this.element.css("marginBottom"),10)||0)]}}this.propagate("start",F);this.helperProportions={width:this.helper.outerWidth(),height:this.helper.outerHeight()};if(A.ui.ddmanager&&!H.dropBehaviour){A.ui.ddmanager.prepareOffsets(this,F)}this.helper.addClass("ui-draggable-dragging");this.mouseDrag(F);return true},convertPositionTo:function(C,D){if(!D){D=this.position}var B=C=="absolute"?1:-1;return{top:(D.top+this.offset.relative.top*B+this.offset.parent.top*B-(this.cssPosition=="fixed"||(this.cssPosition=="absolute"&&this.offsetParent[0]==document.body)?0:this.offsetParent[0].scrollTop)*B+(this.cssPosition=="fixed"?A(document).scrollTop():0)*B+this.margins.top*B),left:(D.left+this.offset.relative.left*B+this.offset.parent.left*B-(this.cssPosition=="fixed"||(this.cssPosition=="absolute"&&this.offsetParent[0]==document.body)?0:this.offsetParent[0].scrollLeft)*B+(this.cssPosition=="fixed"?A(document).scrollLeft():0)*B+this.margins.left*B)}},generatePosition:function(E){var F=this.options;var B={top:(E.pageY-this.offset.click.top-this.offset.relative.top-this.offset.parent.top+(this.cssPosition=="fixed"||(this.cssPosition=="absolute"&&this.offsetParent[0]==document.body)?0:this.offsetParent[0].scrollTop)-(this.cssPosition=="fixed"?A(document).scrollTop():0)),left:(E.pageX-this.offset.click.left-this.offset.relative.left-this.offset.parent.left+(this.cssPosition=="fixed"||(this.cssPosition=="absolute"&&this.offsetParent[0]==document.body)?0:this.offsetParent[0].scrollLeft)-(this.cssPosition=="fixed"?A(document).scrollLeft():0))};if(!this.originalPosition){return B}if(this.containment){if(B.left<this.containment[0]){B.left=this.containment[0]}if(B.top<this.containment[1]){B.top=this.containment[1]}if(B.left>this.containment[2]){B.left=this.containment[2]}if(B.top>this.containment[3]){B.top=this.containment[3]}}if(F.grid){var D=this.originalPosition.top+Math.round((B.top-this.originalPosition.top)/F.grid[1])*F.grid[1];B.top=this.containment?(!(D<this.containment[1]||D>this.containment[3])?D:(!(D<this.containment[1])?D-F.grid[1]:D+F.grid[1])):D;var C=this.originalPosition.left+Math.round((B.left-this.originalPosition.left)/F.grid[0])*F.grid[0];B.left=this.containment?(!(C<this.containment[0]||C>this.containment[2])?C:(!(C<this.containment[0])?C-F.grid[0]:C+F.grid[0])):C}return B},mouseDrag:function(B){this.position=this.generatePosition(B);this.positionAbs=this.convertPositionTo("absolute");this.position=this.propagate("drag",B)||this.position;if(!this.options.axis||this.options.axis!="y"){this.helper[0].style.left=this.position.left+"px"}if(!this.options.axis||this.options.axis!="x"){this.helper[0].style.top=this.position.top+"px"}if(A.ui.ddmanager){A.ui.ddmanager.drag(this,B)}return false},mouseStop:function(C){var D=false;if(A.ui.ddmanager&&!this.options.dropBehaviour){var D=A.ui.ddmanager.drop(this,C)}if((this.options.revert=="invalid"&&!D)||(this.options.revert=="valid"&&D)||this.options.revert===true){var B=this;A(this.helper).animate(this.originalPosition,parseInt(this.options.revert,10)||500,function(){B.propagate("stop",C);B.clear()})}else{this.propagate("stop",C);this.clear()}return false},clear:function(){this.helper.removeClass("ui-draggable-dragging");if(this.options.helper!="original"&&!this.cancelHelperRemoval){this.helper.remove()}this.helper=null;this.cancelHelperRemoval=false},plugins:{},uiHash:function(B){return{helper:this.helper,position:this.position,absolutePosition:this.positionAbs,options:this.options}},propagate:function(C,B){A.ui.plugin.call(this,C,[B,this.uiHash()]);if(C=="drag"){this.positionAbs=this.convertPositionTo("absolute")}return this.element.triggerHandler(C=="drag"?C:"drag"+C,[B,this.uiHash()],this.options[C])},destroy:function(){if(!this.element.data("draggable")){return }this.element.removeData("draggable").unbind(".draggable").removeClass("ui-draggable");this.mouseDestroy()}}));A.extend(A.ui.draggable,{defaults:{appendTo:"parent",axis:false,cancel:":input",delay:0,distance:1,helper:"original"}});A.ui.plugin.add("draggable","cursor",{start:function(D,C){var B=A("body");if(B.css("cursor")){C.options._cursor=B.css("cursor")}B.css("cursor",C.options.cursor)},stop:function(C,B){if(B.options._cursor){A("body").css("cursor",B.options._cursor)}}});A.ui.plugin.add("draggable","zIndex",{start:function(D,C){var B=A(C.helper);if(B.css("zIndex")){C.options._zIndex=B.css("zIndex")}B.css("zIndex",C.options.zIndex)},stop:function(C,B){if(B.options._zIndex){A(B.helper).css("zIndex",B.options._zIndex)}}});A.ui.plugin.add("draggable","opacity",{start:function(D,C){var B=A(C.helper);if(B.css("opacity")){C.options._opacity=B.css("opacity")}B.css("opacity",C.options.opacity)},stop:function(C,B){if(B.options._opacity){A(B.helper).css("opacity",B.options._opacity)}}});A.ui.plugin.add("draggable","iframeFix",{start:function(C,B){A(B.options.iframeFix===true?"iframe":B.options.iframeFix).each(function(){A('<div class="ui-draggable-iframeFix" style="background: #fff;"></div>').css({width:this.offsetWidth+"px",height:this.offsetHeight+"px",position:"absolute",opacity:"0.001",zIndex:1000}).css(A(this).offset()).appendTo("body")})},stop:function(C,B){A("div.DragDropIframeFix").each(function(){this.parentNode.removeChild(this)})}});A.ui.plugin.add("draggable","scroll",{start:function(D,C){var E=C.options;var B=A(this).data("draggable");E.scrollSensitivity=E.scrollSensitivity||20;E.scrollSpeed=E.scrollSpeed||20;B.overflowY=function(F){do{if(/auto|scroll/.test(F.css("overflow"))||(/auto|scroll/).test(F.css("overflow-y"))){return F}F=F.parent()}while(F[0].parentNode);return A(document)}(this);B.overflowX=function(F){do{if(/auto|scroll/.test(F.css("overflow"))||(/auto|scroll/).test(F.css("overflow-x"))){return F}F=F.parent()}while(F[0].parentNode);return A(document)}(this);if(B.overflowY[0]!=document&&B.overflowY[0].tagName!="HTML"){B.overflowYOffset=B.overflowY.offset()}if(B.overflowX[0]!=document&&B.overflowX[0].tagName!="HTML"){B.overflowXOffset=B.overflowX.offset()}},drag:function(D,C){var E=C.options;var B=A(this).data("draggable");if(B.overflowY[0]!=document&&B.overflowY[0].tagName!="HTML"){if((B.overflowYOffset.top+B.overflowY[0].offsetHeight)-D.pageY<E.scrollSensitivity){B.overflowY[0].scrollTop=B.overflowY[0].scrollTop+E.scrollSpeed}if(D.pageY-B.overflowYOffset.top<E.scrollSensitivity){B.overflowY[0].scrollTop=B.overflowY[0].scrollTop-E.scrollSpeed}}else{if(D.pageY-A(document).scrollTop()<E.scrollSensitivity){A(document).scrollTop(A(document).scrollTop()-E.scrollSpeed)}if(A(window).height()-(D.pageY-A(document).scrollTop())<E.scrollSensitivity){A(document).scrollTop(A(document).scrollTop()+E.scrollSpeed)}}if(B.overflowX[0]!=document&&B.overflowX[0].tagName!="HTML"){if((B.overflowXOffset.left+B.overflowX[0].offsetWidth)-D.pageX<E.scrollSensitivity){B.overflowX[0].scrollLeft=B.overflowX[0].scrollLeft+E.scrollSpeed}if(D.pageX-B.overflowXOffset.left<E.scrollSensitivity){B.overflowX[0].scrollLeft=B.overflowX[0].scrollLeft-E.scrollSpeed}}else{if(D.pageX-A(document).scrollLeft()<E.scrollSensitivity){A(document).scrollLeft(A(document).scrollLeft()-E.scrollSpeed)}if(A(window).width()-(D.pageX-A(document).scrollLeft())<E.scrollSensitivity){A(document).scrollLeft(A(document).scrollLeft()+E.scrollSpeed)}}}});A.ui.plugin.add("draggable","snap",{start:function(D,C){var B=A(this).data("draggable");B.snapElements=[];A(C.options.snap===true?".ui-draggable":C.options.snap).each(function(){var F=A(this);var E=F.offset();if(this!=B.element[0]){B.snapElements.push({item:this,width:F.outerWidth(),height:F.outerHeight(),top:E.top,left:E.left})}})},drag:function(J,N){var I=A(this).data("draggable");var L=N.options.snapTolerance||20;var D=N.absolutePosition.left,C=D+I.helperProportions.width,P=N.absolutePosition.top,O=P+I.helperProportions.height;for(var H=I.snapElements.length-1;H>=0;H--){var E=I.snapElements[H].left,B=E+I.snapElements[H].width,R=I.snapElements[H].top,M=R+I.snapElements[H].height;if(!((E-L<D&&D<B+L&&R-L<P&&P<M+L)||(E-L<D&&D<B+L&&R-L<O&&O<M+L)||(E-L<C&&C<B+L&&R-L<P&&P<M+L)||(E-L<C&&C<B+L&&R-L<O&&O<M+L))){continue}if(N.options.snapMode!="inner"){var K=Math.abs(R-O)<=20;var Q=Math.abs(M-P)<=20;var G=Math.abs(E-C)<=20;var F=Math.abs(B-D)<=20;if(K){N.position.top=I.convertPositionTo("relative",{top:R-I.helperProportions.height,left:0}).top}if(Q){N.position.top=I.convertPositionTo("relative",{top:M,left:0}).top}if(G){N.position.left=I.convertPositionTo("relative",{top:0,left:E-I.helperProportions.width}).left}if(F){N.position.left=I.convertPositionTo("relative",{top:0,left:B}).left}}if(N.options.snapMode!="outer"){var K=Math.abs(R-P)<=20;var Q=Math.abs(M-O)<=20;var G=Math.abs(E-D)<=20;var F=Math.abs(B-C)<=20;if(K){N.position.top=I.convertPositionTo("relative",{top:R,left:0}).top}if(Q){N.position.top=I.convertPositionTo("relative",{top:M-I.helperProportions.height,left:0}).top}if(G){N.position.left=I.convertPositionTo("relative",{top:0,left:E}).left}if(F){N.position.left=I.convertPositionTo("relative",{top:0,left:B-I.helperProportions.width}).left}}}}});A.ui.plugin.add("draggable","connectToSortable",{start:function(D,C){var B=A(this).data("draggable");B.sortables=[];A(C.options.connectToSortable).each(function(){if(A.data(this,"sortable")){var E=A.data(this,"sortable");B.sortables.push({instance:E,shouldRevert:E.options.revert});E.refreshItems();E.propagate("activate",D,B)}})},stop:function(D,C){var B=A(this).data("draggable");A.each(B.sortables,function(){if(this.instance.isOver){this.instance.isOver=0;B.cancelHelperRemoval=true;this.instance.cancelHelperRemoval=false;if(this.shouldRevert){this.instance.options.revert=true}this.instance.mouseStop(D);this.instance.element.triggerHandler("sortreceive",[D,A.extend(this.instance.ui(),{sender:B.element})],this.instance.options["receive"]);this.instance.options.helper=this.instance.options._helper}else{this.instance.propagate("deactivate",D,B)}})},drag:function(F,E){var D=A(this).data("draggable"),B=this;var C=function(K){var H=K.left,J=H+K.width,I=K.top,G=I+K.height;return(H<(this.positionAbs.left+this.offset.click.left)&&(this.positionAbs.left+this.offset.click.left)<J&&I<(this.positionAbs.top+this.offset.click.top)&&(this.positionAbs.top+this.offset.click.top)<G)};A.each(D.sortables,function(G){if(C.call(D,this.instance.containerCache)){if(!this.instance.isOver){this.instance.isOver=1;this.instance.currentItem=A(B).clone().appendTo(this.instance.element).data("sortable-item",true);this.instance.options._helper=this.instance.options.helper;this.instance.options.helper=function(){return E.helper[0]};F.target=this.instance.currentItem[0];this.instance.mouseCapture(F,true);this.instance.mouseStart(F,true,true);this.instance.offset.click.top=D.offset.click.top;this.instance.offset.click.left=D.offset.click.left;this.instance.offset.parent.left-=D.offset.parent.left-this.instance.offset.parent.left;this.instance.offset.parent.top-=D.offset.parent.top-this.instance.offset.parent.top;D.propagate("toSortable",F)}if(this.instance.currentItem){this.instance.mouseDrag(F)}}else{if(this.instance.isOver){this.instance.isOver=0;this.instance.cancelHelperRemoval=true;this.instance.options.revert=false;this.instance.mouseStop(F,true);this.instance.options.helper=this.instance.options._helper;this.instance.currentItem.remove();if(this.instance.placeholder){this.instance.placeholder.remove()}D.propagate("fromSortable",F)}}})}});A.ui.plugin.add("draggable","stack",{start:function(D,B){var C=A.makeArray(A(B.options.stack.group)).sort(function(F,E){return(parseInt(A(F).css("zIndex"),10)||B.options.stack.min)-(parseInt(A(E).css("zIndex"),10)||B.options.stack.min)});A(C).each(function(E){this.style.zIndex=B.options.stack.min+E});this[0].style.zIndex=B.options.stack.min+C.length}})})(jQuery);(function(A){A.widget("ui.droppable",{init:function(){this.element.addClass("ui-droppable");this.isover=0;this.isout=1;var C=this.options,B=C.accept;C=A.extend(C,{accept:C.accept&&C.accept.constructor==Function?C.accept:function(D){return A(D).is(B)}});this.proportions={width:this.element[0].offsetWidth,height:this.element[0].offsetHeight};A.ui.ddmanager.droppables.push(this)},plugins:{},ui:function(B){return{draggable:(B.currentItem||B.element),helper:B.helper,position:B.position,absolutePosition:B.positionAbs,options:this.options,element:this.element}},destroy:function(){var B=A.ui.ddmanager.droppables;for(var C=0;C<B.length;C++){if(B[C]==this){B.splice(C,1)}}this.element.removeClass("ui-droppable ui-droppable-disabled").removeData("droppable").unbind(".droppable")},over:function(C){var B=A.ui.ddmanager.current;if(!B||(B.currentItem||B.element)[0]==this.element[0]){return }if(this.options.accept.call(this.element,(B.currentItem||B.element))){A.ui.plugin.call(this,"over",[C,this.ui(B)]);this.element.triggerHandler("dropover",[C,this.ui(B)],this.options.over)}},out:function(C){var B=A.ui.ddmanager.current;if(!B||(B.currentItem||B.element)[0]==this.element[0]){return }if(this.options.accept.call(this.element,(B.currentItem||B.element))){A.ui.plugin.call(this,"out",[C,this.ui(B)]);this.element.triggerHandler("dropout",[C,this.ui(B)],this.options.out)}},drop:function(D,C){var B=C||A.ui.ddmanager.current;if(!B||(B.currentItem||B.element)[0]==this.element[0]){return false}var E=false;this.element.find(".ui-droppable").not(".ui-draggable-dragging").each(function(){var F=A.data(this,"droppable");if(F.options.greedy&&A.ui.intersect(B,A.extend(F,{offset:F.element.offset()}),F.options.tolerance)){E=true;return false}});if(E){return false}if(this.options.accept.call(this.element,(B.currentItem||B.element))){A.ui.plugin.call(this,"drop",[D,this.ui(B)]);this.element.triggerHandler("drop",[D,this.ui(B)],this.options.drop);return true}return false},activate:function(C){var B=A.ui.ddmanager.current;A.ui.plugin.call(this,"activate",[C,this.ui(B)]);if(B){this.element.triggerHandler("dropactivate",[C,this.ui(B)],this.options.activate)}},deactivate:function(C){var B=A.ui.ddmanager.current;A.ui.plugin.call(this,"deactivate",[C,this.ui(B)]);if(B){this.element.triggerHandler("dropdeactivate",[C,this.ui(B)],this.options.deactivate)}}});A.extend(A.ui.droppable,{defaults:{disabled:false,tolerance:"intersect"}});A.ui.intersect=function(L,F,J){if(!F.offset){return false}var D=(L.positionAbs||L.position.absolute).left,C=D+L.helperProportions.width,I=(L.positionAbs||L.position.absolute).top,H=I+L.helperProportions.height;var E=F.offset.left,B=E+F.proportions.width,K=F.offset.top,G=K+F.proportions.height;switch(J){case"fit":return(E<D&&C<B&&K<I&&H<G);break;case"intersect":return(E<D+(L.helperProportions.width/2)&&C-(L.helperProportions.width/2)<B&&K<I+(L.helperProportions.height/2)&&H-(L.helperProportions.height/2)<G);break;case"pointer":return(E<((L.positionAbs||L.position.absolute).left+(L.clickOffset||L.offset.click).left)&&((L.positionAbs||L.position.absolute).left+(L.clickOffset||L.offset.click).left)<B&&K<((L.positionAbs||L.position.absolute).top+(L.clickOffset||L.offset.click).top)&&((L.positionAbs||L.position.absolute).top+(L.clickOffset||L.offset.click).top)<G);break;case"touch":return((I>=K&&I<=G)||(H>=K&&H<=G)||(I<K&&H>G))&&((D>=E&&D<=B)||(C>=E&&C<=B)||(D<E&&C>B));break;default:return false;break}};A.ui.ddmanager={current:null,droppables:[],prepareOffsets:function(D,F){var B=A.ui.ddmanager.droppables;var E=F?F.type:null;for(var C=0;C<B.length;C++){if(B[C].options.disabled||(D&&!B[C].options.accept.call(B[C].element,(D.currentItem||D.element)))){continue}B[C].visible=B[C].element.css("display")!="none";if(!B[C].visible){continue}B[C].offset=B[C].element.offset();B[C].proportions={width:B[C].element[0].offsetWidth,height:B[C].element[0].offsetHeight};if(E=="dragstart"||E=="sortactivate"){B[C].activate.call(B[C],F)}}},drop:function(B,C){var D=false;A.each(A.ui.ddmanager.droppables,function(){if(!this.options){return }if(!this.options.disabled&&this.visible&&A.ui.intersect(B,this,this.options.tolerance)){D=this.drop.call(this,C)}if(!this.options.disabled&&this.visible&&this.options.accept.call(this.element,(B.currentItem||B.element))){this.isout=1;this.isover=0;this.deactivate.call(this,C)}});return D},drag:function(B,C){if(B.options.refreshPositions){A.ui.ddmanager.prepareOffsets(B,C)}A.each(A.ui.ddmanager.droppables,function(){if(this.options.disabled||this.greedyChild||!this.visible){return }var E=A.ui.intersect(B,this,this.options.tolerance);var G=!E&&this.isover==1?"isout":(E&&this.isover==0?"isover":null);if(!G){return }var F;if(this.options.greedy){var D=this.element.parents(".ui-droppable:eq(0)");if(D.length){F=A.data(D[0],"droppable");F.greedyChild=(G=="isover"?1:0)}}if(F&&G=="isover"){F["isover"]=0;F["isout"]=1;F.out.call(F,C)}this[G]=1;this[G=="isout"?"isover":"isout"]=0;this[G=="isover"?"over":"out"].call(this,C);if(F&&G=="isout"){F["isout"]=0;F["isover"]=1;F.over.call(F,C)}})}};A.ui.plugin.add("droppable","activeClass",{activate:function(C,B){A(this).addClass(B.options.activeClass)},deactivate:function(C,B){A(this).removeClass(B.options.activeClass)},drop:function(C,B){A(this).removeClass(B.options.activeClass)}});A.ui.plugin.add("droppable","hoverClass",{over:function(C,B){A(this).addClass(B.options.hoverClass)},out:function(C,B){A(this).removeClass(B.options.hoverClass)},drop:function(C,B){A(this).removeClass(B.options.hoverClass)}})})(jQuery);(function(A){A.widget("ui.resizable",A.extend({},A.ui.mouse,{init:function(){var M=this,N=this.options;var Q=this.element.css("position");this.originalElement=this.element;this.element.addClass("ui-resizable").css({position:/static/.test(Q)?"relative":Q});A.extend(N,{_aspectRatio:!!(N.aspectRatio),helper:N.helper||N.ghost||N.animate?N.helper||"proxy":null,knobHandles:N.knobHandles===true?"ui-resizable-knob-handle":N.knobHandles});var H="1px solid #DEDEDE";N.defaultTheme={"ui-resizable":{display:"block"},"ui-resizable-handle":{position:"absolute",background:"#F2F2F2",fontSize:"0.1px"},"ui-resizable-n":{cursor:"n-resize",height:"4px",left:"0px",right:"0px",borderTop:H},"ui-resizable-s":{cursor:"s-resize",height:"4px",left:"0px",right:"0px",borderBottom:H},"ui-resizable-e":{cursor:"e-resize",width:"4px",top:"0px",bottom:"0px",borderRight:H},"ui-resizable-w":{cursor:"w-resize",width:"4px",top:"0px",bottom:"0px",borderLeft:H},"ui-resizable-se":{cursor:"se-resize",width:"4px",height:"4px",borderRight:H,borderBottom:H},"ui-resizable-sw":{cursor:"sw-resize",width:"4px",height:"4px",borderBottom:H,borderLeft:H},"ui-resizable-ne":{cursor:"ne-resize",width:"4px",height:"4px",borderRight:H,borderTop:H},"ui-resizable-nw":{cursor:"nw-resize",width:"4px",height:"4px",borderLeft:H,borderTop:H}};N.knobTheme={"ui-resizable-handle":{background:"#F2F2F2",border:"1px solid #808080",height:"8px",width:"8px"},"ui-resizable-n":{cursor:"n-resize",top:"0px",left:"45%"},"ui-resizable-s":{cursor:"s-resize",bottom:"0px",left:"45%"},"ui-resizable-e":{cursor:"e-resize",right:"0px",top:"45%"},"ui-resizable-w":{cursor:"w-resize",left:"0px",top:"45%"},"ui-resizable-se":{cursor:"se-resize",right:"0px",bottom:"0px"},"ui-resizable-sw":{cursor:"sw-resize",left:"0px",bottom:"0px"},"ui-resizable-nw":{cursor:"nw-resize",left:"0px",top:"0px"},"ui-resizable-ne":{cursor:"ne-resize",right:"0px",top:"0px"}};N._nodeName=this.element[0].nodeName;if(N._nodeName.match(/canvas|textarea|input|select|button|img/i)){var B=this.element;if(/relative/.test(B.css("position"))&&A.browser.opera){B.css({position:"relative",top:"auto",left:"auto"})}B.wrap(A('<div class="ui-wrapper"	style="overflow: hidden;"></div>').css({position:B.css("position"),width:B.outerWidth(),height:B.outerHeight(),top:B.css("top"),left:B.css("left")}));var J=this.element;this.element=this.element.parent();this.element.data("resizable",this);this.element.css({marginLeft:J.css("marginLeft"),marginTop:J.css("marginTop"),marginRight:J.css("marginRight"),marginBottom:J.css("marginBottom")});J.css({marginLeft:0,marginTop:0,marginRight:0,marginBottom:0});if(A.browser.safari&&N.preventDefault){J.css("resize","none")}N.proportionallyResize=J.css({position:"static",zoom:1,display:"block"});this.element.css({margin:J.css("margin")});this._proportionallyResize()}if(!N.handles){N.handles=!A(".ui-resizable-handle",this.element).length?"e,s,se":{n:".ui-resizable-n",e:".ui-resizable-e",s:".ui-resizable-s",w:".ui-resizable-w",se:".ui-resizable-se",sw:".ui-resizable-sw",ne:".ui-resizable-ne",nw:".ui-resizable-nw"}}if(N.handles.constructor==String){N.zIndex=N.zIndex||1000;if(N.handles=="all"){N.handles="n,e,s,w,se,sw,ne,nw"}var O=N.handles.split(",");N.handles={};var G={handle:"position: absolute; display: none; overflow:hidden;",n:"top: 0pt; width:100%;",e:"right: 0pt; height:100%;",s:"bottom: 0pt; width:100%;",w:"left: 0pt; height:100%;",se:"bottom: 0pt; right: 0px;",sw:"bottom: 0pt; left: 0px;",ne:"top: 0pt; right: 0px;",nw:"top: 0pt; left: 0px;"};for(var R=0;R<O.length;R++){var S=A.trim(O[R]),L=N.defaultTheme,F="ui-resizable-"+S,C=!A.ui.css(F)&&!N.knobHandles,P=A.ui.css("ui-resizable-knob-handle"),T=A.extend(L[F],L["ui-resizable-handle"]),D=A.extend(N.knobTheme[F],!P?N.knobTheme["ui-resizable-handle"]:{});var K=/sw|se|ne|nw/.test(S)?{zIndex:++N.zIndex}:{};var I=(C?G[S]:""),E=A(['<div class="ui-resizable-handle ',F,'" style="',I,G.handle,'"></div>'].join("")).css(K);N.handles[S]=".ui-resizable-"+S;this.element.append(E.css(C?T:{}).css(N.knobHandles?D:{}).addClass(N.knobHandles?"ui-resizable-knob-handle":"").addClass(N.knobHandles))}if(N.knobHandles){this.element.addClass("ui-resizable-knob").css(!A.ui.css("ui-resizable-knob")?{}:{})}}this._renderAxis=function(Y){Y=Y||this.element;for(var V in N.handles){if(N.handles[V].constructor==String){N.handles[V]=A(N.handles[V],this.element).show()}if(N.transparent){N.handles[V].css({opacity:0})}if(this.element.is(".ui-wrapper")&&N._nodeName.match(/textarea|input|select|button/i)){var W=A(N.handles[V],this.element),X=0;X=/sw|ne|nw|se|n|s/.test(V)?W.outerHeight():W.outerWidth();var U=["padding",/ne|nw|n/.test(V)?"Top":/se|sw|s/.test(V)?"Bottom":/^e$/.test(V)?"Right":"Left"].join("");if(!N.transparent){Y.css(U,X)}this._proportionallyResize()}if(!A(N.handles[V]).length){continue}}};this._renderAxis(this.element);N._handles=A(".ui-resizable-handle",M.element);if(N.disableSelection){N._handles.each(function(U,V){A.ui.disableSelection(V)})}N._handles.mouseover(function(){if(!N.resizing){if(this.className){var U=this.className.match(/ui-resizable-(se|sw|ne|nw|n|e|s|w)/i)}M.axis=N.axis=U&&U[1]?U[1]:"se"}});if(N.autoHide){N._handles.hide();A(M.element).addClass("ui-resizable-autohide").hover(function(){A(this).removeClass("ui-resizable-autohide");N._handles.show()},function(){if(!N.resizing){A(this).addClass("ui-resizable-autohide");N._handles.hide()}})}this.mouseInit()},plugins:{},ui:function(){return{originalElement:this.originalElement,element:this.element,helper:this.helper,position:this.position,size:this.size,options:this.options,originalSize:this.originalSize,originalPosition:this.originalPosition}},propagate:function(C,B){A.ui.plugin.call(this,C,[B,this.ui()]);if(C!="resize"){this.element.triggerHandler(["resize",C].join(""),[B,this.ui()],this.options[C])}},destroy:function(){var D=this.element,C=D.children(".ui-resizable").get(0);this.mouseDestroy();var B=function(E){A(E).removeClass("ui-resizable ui-resizable-disabled").removeData("resizable").unbind(".resizable").find(".ui-resizable-handle").remove()};B(D);if(D.is(".ui-wrapper")&&C){D.parent().append(A(C).css({position:D.css("position"),width:D.outerWidth(),height:D.outerHeight(),top:D.css("top"),left:D.css("left")})).end().remove();B(C)}},mouseStart:function(K){if(this.options.disabled){return false}var J=false;for(var H in this.options.handles){if(A(this.options.handles[H])[0]==K.target){J=true}}if(!J){return false}var C=this.options,B=this.element.position(),D=this.element,I=function(O){return parseInt(O,10)||0},G=A.browser.msie&&A.browser.version<7;C.resizing=true;C.documentScroll={top:A(document).scrollTop(),left:A(document).scrollLeft()};if(D.is(".ui-draggable")||(/absolute/).test(D.css("position"))){var M=A.browser.msie&&!C.containment&&(/absolute/).test(D.css("position"))&&!(/relative/).test(D.parent().css("position"));var L=M?C.documentScroll.top:0,F=M?C.documentScroll.left:0;D.css({position:"absolute",top:(B.top+L),left:(B.left+F)})}if(A.browser.opera&&/relative/.test(D.css("position"))){D.css({position:"relative",top:"auto",left:"auto"})}this._renderProxy();var N=I(this.helper.css("left")),E=I(this.helper.css("top"));if(C.containment){N+=A(C.containment).scrollLeft()||0;E+=A(C.containment).scrollTop()||0}this.offset=this.helper.offset();this.position={left:N,top:E};this.size=C.helper||G?{width:D.outerWidth(),height:D.outerHeight()}:{width:D.width(),height:D.height()};this.originalSize=C.helper||G?{width:D.outerWidth(),height:D.outerHeight()}:{width:D.width(),height:D.height()};this.originalPosition={left:N,top:E};this.sizeDiff={width:D.outerWidth()-D.width(),height:D.outerHeight()-D.height()};this.originalMousePosition={left:K.pageX,top:K.pageY};C.aspectRatio=(typeof C.aspectRatio=="number")?C.aspectRatio:((this.originalSize.height/this.originalSize.width)||1);if(C.preserveCursor){A("body").css("cursor",this.axis+"-resize")}this.propagate("start",K);return true},mouseDrag:function(I){var D=this.helper,C=this.options,J={},M=this,F=this.originalMousePosition,K=this.axis;var N=(I.pageX-F.left)||0,L=(I.pageY-F.top)||0;var E=this._change[K];if(!E){return false}var H=E.apply(this,[I,N,L]),G=A.browser.msie&&A.browser.version<7,B=this.sizeDiff;if(C._aspectRatio||I.shiftKey){H=this._updateRatio(H,I)}H=this._respectSize(H,I);this.propagate("resize",I);D.css({top:this.position.top+"px",left:this.position.left+"px",width:this.size.width+"px",height:this.size.height+"px"});if(!C.helper&&C.proportionallyResize){this._proportionallyResize()}this._updateCache(H);this.element.triggerHandler("resize",[I,this.ui()],this.options["resize"]);return false},mouseStop:function(I){this.options.resizing=false;var E=this.options,H=function(M){return parseInt(M,10)||0},K=this;if(E.helper){var D=E.proportionallyResize,B=D&&(/textarea/i).test(D.get(0).nodeName),C=B&&A.ui.hasScroll(D.get(0),"left")?0:K.sizeDiff.height,G=B?0:K.sizeDiff.width;var L={width:(K.size.width-G),height:(K.size.height-C)},F=(parseInt(K.element.css("left"),10)+(K.position.left-K.originalPosition.left))||null,J=(parseInt(K.element.css("top"),10)+(K.position.top-K.originalPosition.top))||null;if(!E.animate){this.element.css(A.extend(L,{top:J,left:F}))}if(E.helper&&!E.animate){this._proportionallyResize()}}if(E.preserveCursor){A("body").css("cursor","auto")}this.propagate("stop",I);if(E.helper){this.helper.remove()}return false},_updateCache:function(B){var C=this.options;this.offset=this.helper.offset();if(B.left){this.position.left=B.left}if(B.top){this.position.top=B.top}if(B.height){this.size.height=B.height}if(B.width){this.size.width=B.width}},_updateRatio:function(D,E){var F=this.options,G=this.position,C=this.size,B=this.axis;if(D.height){D.width=(C.height/F.aspectRatio)}else{if(D.width){D.height=(C.width*F.aspectRatio)}}if(B=="sw"){D.left=G.left+(C.width-D.width);D.top=null}if(B=="nw"){D.top=G.top+(C.height-D.height);D.left=G.left+(C.width-D.width)}return D},_respectSize:function(H,I){var F=this.helper,E=this.options,N=E._aspectRatio||I.shiftKey,M=this.axis,P=H.width&&E.maxWidth&&E.maxWidth<H.width,J=H.height&&E.maxHeight&&E.maxHeight<H.height,D=H.width&&E.minWidth&&E.minWidth>H.width,O=H.height&&E.minHeight&&E.minHeight>H.height;if(D){H.width=E.minWidth}if(O){H.height=E.minHeight}if(P){H.width=E.maxWidth}if(J){H.height=E.maxHeight}var C=this.originalPosition.left+this.originalSize.width,L=this.position.top+this.size.height;var G=/sw|nw|w/.test(M),B=/nw|ne|n/.test(M);if(D&&G){H.left=C-E.minWidth}if(P&&G){H.left=C-E.maxWidth}if(O&&B){H.top=L-E.minHeight}if(J&&B){H.top=L-E.maxHeight}var K=!H.width&&!H.height;if(K&&!H.left&&H.top){H.top=null}else{if(K&&!H.top&&H.left){H.left=null}}return H},_proportionallyResize:function(){var F=this.options;if(!F.proportionallyResize){return }var D=F.proportionallyResize,C=this.helper||this.element;if(!F.borderDif){var B=[D.css("borderTopWidth"),D.css("borderRightWidth"),D.css("borderBottomWidth"),D.css("borderLeftWidth")],E=[D.css("paddingTop"),D.css("paddingRight"),D.css("paddingBottom"),D.css("paddingLeft")];F.borderDif=A.map(B,function(G,I){var H=parseInt(G,10)||0,J=parseInt(E[I],10)||0;return H+J})}D.css({height:(C.height()-F.borderDif[0]-F.borderDif[2])+"px",width:(C.width()-F.borderDif[1]-F.borderDif[3])+"px"})},_renderProxy:function(){var C=this.element,F=this.options;this.elementOffset=C.offset();if(F.helper){this.helper=this.helper||A('<div style="overflow:hidden;"></div>');var B=A.browser.msie&&A.browser.version<7,D=(B?1:0),E=(B?2:-1);this.helper.addClass(F.helper).css({width:C.outerWidth()+E,height:C.outerHeight()+E,position:"absolute",left:this.elementOffset.left-D+"px",top:this.elementOffset.top-D+"px",zIndex:++F.zIndex});this.helper.appendTo("body");if(F.disableSelection){A.ui.disableSelection(this.helper.get(0))}}else{this.helper=C}},_change:{e:function(D,C,B){return{width:this.originalSize.width+C}},w:function(F,C,B){var G=this.options,D=this.originalSize,E=this.originalPosition;return{left:E.left+C,width:D.width-C}},n:function(F,C,B){var G=this.options,D=this.originalSize,E=this.originalPosition;return{top:E.top+B,height:D.height-B}},s:function(D,C,B){return{height:this.originalSize.height+B}},se:function(D,C,B){return A.extend(this._change.s.apply(this,arguments),this._change.e.apply(this,[D,C,B]))},sw:function(D,C,B){return A.extend(this._change.s.apply(this,arguments),this._change.w.apply(this,[D,C,B]))},ne:function(D,C,B){return A.extend(this._change.n.apply(this,arguments),this._change.e.apply(this,[D,C,B]))},nw:function(D,C,B){return A.extend(this._change.n.apply(this,arguments),this._change.w.apply(this,[D,C,B]))}}}));A.extend(A.ui.resizable,{defaults:{cancel:":input",distance:1,delay:0,preventDefault:true,transparent:false,minWidth:10,minHeight:10,aspectRatio:false,disableSelection:true,preserveCursor:true,autoHide:false,knobHandles:false}});A.ui.plugin.add("resizable","containment",{start:function(I,K){var E=K.options,M=A(this).data("resizable"),G=M.element;var C=E.containment,F=(C instanceof A)?C.get(0):(/parent/.test(C))?G.parent().get(0):C;if(!F){return }M.containerElement=A(F);if(/document/.test(C)||C==document){M.containerOffset={left:0,top:0};M.containerPosition={left:0,top:0};M.parentData={element:A(document),left:0,top:0,width:A(document).width(),height:A(document).height()||document.body.parentNode.scrollHeight}}else{M.containerOffset=A(F).offset();M.containerPosition=A(F).position();M.containerSize={height:A(F).innerHeight(),width:A(F).innerWidth()};var J=M.containerOffset,B=M.containerSize.height,H=M.containerSize.width,D=(A.ui.hasScroll(F,"left")?F.scrollWidth:H),L=(A.ui.hasScroll(F)?F.scrollHeight:B);M.parentData={element:F,left:J.left,top:J.top,width:D,height:L}}},resize:function(H,K){var E=K.options,N=A(this).data("resizable"),C=N.containerSize,J=N.containerOffset,G=N.size,I=N.position,L=E._aspectRatio||H.shiftKey,B={top:0,left:0},D=N.containerElement;if(D[0]!=document&&/static/.test(D.css("position"))){B=N.containerPosition}if(I.left<(E.helper?J.left:B.left)){N.size.width=N.size.width+(E.helper?(N.position.left-J.left):(N.position.left-B.left));if(L){N.size.height=N.size.width*E.aspectRatio}N.position.left=E.helper?J.left:B.left}if(I.top<(E.helper?J.top:0)){N.size.height=N.size.height+(E.helper?(N.position.top-J.top):N.position.top);if(L){N.size.width=N.size.height/E.aspectRatio}N.position.top=E.helper?J.top:0}var F=(E.helper?N.offset.left-J.left:(N.position.left-B.left))+N.sizeDiff.width,M=(E.helper?N.offset.top-J.top:N.position.top)+N.sizeDiff.height;if(F+N.size.width>=N.parentData.width){N.size.width=N.parentData.width-F;if(L){N.size.height=N.size.width*E.aspectRatio}}if(M+N.size.height>=N.parentData.height){N.size.height=N.parentData.height-M;if(L){N.size.width=N.size.height/E.aspectRatio}}},stop:function(G,J){var C=J.options,L=A(this).data("resizable"),H=L.position,I=L.containerOffset,B=L.containerPosition,D=L.containerElement;var E=A(L.helper),M=E.offset(),K=E.innerWidth(),F=E.innerHeight();if(C.helper&&!C.animate&&/relative/.test(D.css("position"))){A(this).css({left:(M.left-I.left),top:(M.top-I.top),width:K,height:F})}if(C.helper&&!C.animate&&/static/.test(D.css("position"))){A(this).css({left:B.left+(M.left-I.left),top:B.top+(M.top-I.top),width:K,height:F})}}});A.ui.plugin.add("resizable","grid",{resize:function(H,J){var D=J.options,L=A(this).data("resizable"),G=L.size,E=L.originalSize,F=L.originalPosition,K=L.axis,I=D._aspectRatio||H.shiftKey;D.grid=typeof D.grid=="number"?[D.grid,D.grid]:D.grid;var C=Math.round((G.width-E.width)/(D.grid[0]||1))*(D.grid[0]||1),B=Math.round((G.height-E.height)/(D.grid[1]||1))*(D.grid[1]||1);if(/^(se|s|e)$/.test(K)){L.size.width=E.width+C;L.size.height=E.height+B}else{if(/^(ne)$/.test(K)){L.size.width=E.width+C;L.size.height=E.height+B;L.position.top=F.top-B}else{if(/^(sw)$/.test(K)){L.size.width=E.width+C;L.size.height=E.height+B;L.position.left=F.left-C}else{L.size.width=E.width+C;L.size.height=E.height+B;L.position.top=F.top-B;L.position.left=F.left-C}}}}});A.ui.plugin.add("resizable","animate",{stop:function(I,K){var F=K.options,L=A(this).data("resizable");var E=F.proportionallyResize,B=E&&(/textarea/i).test(E.get(0).nodeName),C=B&&A.ui.hasScroll(E.get(0),"left")?0:L.sizeDiff.height,H=B?0:L.sizeDiff.width;var D={width:(L.size.width-H),height:(L.size.height-C)},G=(parseInt(L.element.css("left"),10)+(L.position.left-L.originalPosition.left))||null,J=(parseInt(L.element.css("top"),10)+(L.position.top-L.originalPosition.top))||null;L.element.animate(A.extend(D,J&&G?{top:J,left:G}:{}),{duration:F.animateDuration||"slow",easing:F.animateEasing||"swing",step:function(){var M={width:parseInt(L.element.css("width"),10),height:parseInt(L.element.css("height"),10),top:parseInt(L.element.css("top"),10),left:parseInt(L.element.css("left"),10)};if(E){E.css({width:M.width,height:M.height})}L._updateCache(M);L.propagate("animate",I)}})}});A.ui.plugin.add("resizable","ghost",{start:function(E,D){var F=D.options,B=A(this).data("resizable"),G=F.proportionallyResize,C=B.size;if(!G){B.ghost=B.element.clone()}else{B.ghost=G.clone()}B.ghost.css({opacity:0.25,display:"block",position:"relative",height:C.height,width:C.width,margin:0,left:0,top:0}).addClass("ui-resizable-ghost").addClass(typeof F.ghost=="string"?F.ghost:"");B.ghost.appendTo(B.helper)},resize:function(D,C){var E=C.options,B=A(this).data("resizable"),F=E.proportionallyResize;if(B.ghost){B.ghost.css({position:"relative",height:B.size.height,width:B.size.width})}},stop:function(D,C){var E=C.options,B=A(this).data("resizable"),F=E.proportionallyResize;if(B.ghost&&B.helper){B.helper.get(0).removeChild(B.ghost.get(0))}}});A.ui.plugin.add("resizable","alsoResize",{start:function(E,C){var F=C.options,B=A(this).data("resizable"),D=function(G){A(G).each(function(){A(this).data("resizable-alsoresize",{width:parseInt(A(this).width(),10),height:parseInt(A(this).height(),10),left:parseInt(A(this).css("left"),10),top:parseInt(A(this).css("top"),10)})})};if(typeof (F.alsoResize)=="object"){if(F.alsoResize.length){F.alsoResize=F.alsoResize[0];D(F.alsoResize)}else{A.each(F.alsoResize,function(G,H){D(G)})}}else{D(F.alsoResize)}},resize:function(F,E){var G=E.options,C=A(this).data("resizable"),D=C.originalSize,I=C.originalPosition;var H={height:(C.size.height-D.height)||0,width:(C.size.width-D.width)||0,top:(C.position.top-I.top)||0,left:(C.position.left-I.left)||0},B=function(J,K){A(J).each(function(){var N=A(this).data("resizable-alsoresize"),M={},L=K&&K.length?K:["width","height","top","left"];A.each(L||["width","height","top","left"],function(O,Q){var P=(N[Q]||0)+(H[Q]||0);if(P&&P>=0){M[Q]=P||null}});A(this).css(M)})};if(typeof (G.alsoResize)=="object"){A.each(G.alsoResize,function(J,K){B(J,K)})}else{B(G.alsoResize)}},stop:function(C,B){A(this).removeData("resizable-alsoresize-start")}})})(jQuery);(function(A){A.widget("ui.selectable",A.extend({},A.ui.mouse,{init:function(){var B=this;this.element.addClass("ui-selectable");this.dragged=false;var C;this.refresh=function(){C=A(B.options.filter,B.element[0]);C.each(function(){var D=A(this);var E=D.offset();A.data(this,"selectable-item",{element:this,$element:D,left:E.left,top:E.top,right:E.left+D.width(),bottom:E.top+D.height(),startselected:false,selected:D.hasClass("ui-selected"),selecting:D.hasClass("ui-selecting"),unselecting:D.hasClass("ui-unselecting")})})};this.refresh();this.selectees=C.addClass("ui-selectee");this.mouseInit();this.helper=A(document.createElement("div")).css({border:"1px dotted black"})},toggle:function(){if(this.options.disabled){this.enable()}else{this.disable()}},destroy:function(){this.element.removeClass("ui-selectable ui-selectable-disabled").removeData("selectable").unbind(".selectable");this.mouseDestroy()},mouseStart:function(E){var C=this;this.opos=[E.pageX,E.pageY];if(this.options.disabled){return }var D=this.options;this.selectees=A(D.filter,this.element[0]);this.element.triggerHandler("selectablestart",[E,{"selectable":this.element[0],"options":D}],D.start);A("body").append(this.helper);this.helper.css({"z-index":100,"position":"absolute","left":E.clientX,"top":E.clientY,"width":0,"height":0});if(D.autoRefresh){this.refresh()}this.selectees.filter(".ui-selected").each(function(){var F=A.data(this,"selectable-item");F.startselected=true;if(!E.ctrlKey){F.$element.removeClass("ui-selected");F.selected=false;F.$element.addClass("ui-unselecting");F.unselecting=true;C.element.triggerHandler("selectableunselecting",[E,{selectable:C.element[0],unselecting:F.element,options:D}],D.unselecting)}});var B=false;A(E.target).parents().andSelf().each(function(){if(A.data(this,"selectable-item")){B=true}});return this.options.keyboard?!B:true},mouseDrag:function(I){var C=this;this.dragged=true;if(this.options.disabled){return }var E=this.options;var D=this.opos[0],H=this.opos[1],B=I.pageX,G=I.pageY;if(D>B){var F=B;B=D;D=F}if(H>G){var F=G;G=H;H=F}this.helper.css({left:D,top:H,width:B-D,height:G-H});this.selectees.each(function(){var J=A.data(this,"selectable-item");if(!J||J.element==C.element[0]){return }var K=false;if(E.tolerance=="touch"){K=(!(J.left>B||J.right<D||J.top>G||J.bottom<H))}else{if(E.tolerance=="fit"){K=(J.left>D&&J.right<B&&J.top>H&&J.bottom<G)}}if(K){if(J.selected){J.$element.removeClass("ui-selected");J.selected=false}if(J.unselecting){J.$element.removeClass("ui-unselecting");J.unselecting=false}if(!J.selecting){J.$element.addClass("ui-selecting");J.selecting=true;C.element.triggerHandler("selectableselecting",[I,{selectable:C.element[0],selecting:J.element,options:E}],E.selecting)}}else{if(J.selecting){if(I.ctrlKey&&J.startselected){J.$element.removeClass("ui-selecting");J.selecting=false;J.$element.addClass("ui-selected");J.selected=true}else{J.$element.removeClass("ui-selecting");J.selecting=false;if(J.startselected){J.$element.addClass("ui-unselecting");J.unselecting=true}C.element.triggerHandler("selectableunselecting",[I,{selectable:C.element[0],unselecting:J.element,options:E}],E.unselecting)}}if(J.selected){if(!I.ctrlKey&&!J.startselected){J.$element.removeClass("ui-selected");J.selected=false;J.$element.addClass("ui-unselecting");J.unselecting=true;C.element.triggerHandler("selectableunselecting",[I,{selectable:C.element[0],unselecting:J.element,options:E}],E.unselecting)}}}});return false},mouseStop:function(D){var B=this;this.dragged=false;var C=this.options;A(".ui-unselecting",this.element[0]).each(function(){var E=A.data(this,"selectable-item");E.$element.removeClass("ui-unselecting");E.unselecting=false;E.startselected=false;B.element.triggerHandler("selectableunselected",[D,{selectable:B.element[0],unselected:E.element,options:C}],C.unselected)});A(".ui-selecting",this.element[0]).each(function(){var E=A.data(this,"selectable-item");E.$element.removeClass("ui-selecting").addClass("ui-selected");E.selecting=false;E.selected=true;E.startselected=true;B.element.triggerHandler("selectableselected",[D,{selectable:B.element[0],selected:E.element,options:C}],C.selected)});this.element.triggerHandler("selectablestop",[D,{selectable:B.element[0],options:this.options}],this.options.stop);this.helper.remove();return false}}));A.extend(A.ui.selectable,{defaults:{distance:1,delay:0,cancel:":input",appendTo:"body",autoRefresh:true,filter:"*",tolerance:"touch"}})})(jQuery);(function(B){function A(E,D){var C=B.browser.safari&&B.browser.version<522;if(E.contains&&!C){return E.contains(D)}if(E.compareDocumentPosition){return !!(E.compareDocumentPosition(D)&16)}while(D=D.parentNode){if(D==E){return true}}return false}B.widget("ui.sortable",B.extend({},B.ui.mouse,{init:function(){var C=this.options;this.containerCache={};this.element.addClass("ui-sortable");this.refresh();this.floating=this.items.length?(/left|right/).test(this.items[0].item.css("float")):false;if(!(/(relative|absolute|fixed)/).test(this.element.css("position"))){this.element.css("position","relative")}this.offset=this.element.offset();this.mouseInit()},plugins:{},ui:function(C){return{helper:(C||this)["helper"],placeholder:(C||this)["placeholder"]||B([]),position:(C||this)["position"],absolutePosition:(C||this)["positionAbs"],options:this.options,element:this.element,item:(C||this)["currentItem"],sender:C?C.element:null}},propagate:function(F,E,C,D){B.ui.plugin.call(this,F,[E,this.ui(C)]);if(!D){this.element.triggerHandler(F=="sort"?F:"sort"+F,[E,this.ui(C)],this.options[F])}},serialize:function(E){var C=(B.isFunction(this.options.items)?this.options.items.call(this.element):B(this.options.items,this.element)).not(".ui-sortable-helper");var D=[];E=E||{};C.each(function(){var F=(B(this).attr(E.attribute||"id")||"").match(E.expression||(/(.+)[-=_](.+)/));if(F){D.push((E.key||F[1])+"[]="+(E.key&&E.expression?F[1]:F[2]))}});return D.join("&")},toArray:function(C){var D=(B.isFunction(this.options.items)?this.options.items.call(this.element):B(this.options.items,this.element)).not(".ui-sortable-helper");var E=[];D.each(function(){E.push(B(this).attr(C||"id"))});return E},intersectsWith:function(J){var E=this.positionAbs.left,D=E+this.helperProportions.width,I=this.positionAbs.top,H=I+this.helperProportions.height;var F=J.left,C=F+J.width,K=J.top,G=K+J.height;if(this.options.tolerance=="pointer"||this.options.forcePointerForContainers||(this.options.tolerance=="guess"&&this.helperProportions[this.floating?"width":"height"]>J[this.floating?"width":"height"])){return(I+this.offset.click.top>K&&I+this.offset.click.top<G&&E+this.offset.click.left>F&&E+this.offset.click.left<C)}else{return(F<E+(this.helperProportions.width/2)&&D-(this.helperProportions.width/2)<C&&K<I+(this.helperProportions.height/2)&&H-(this.helperProportions.height/2)<G)}},intersectsWithEdge:function(J){var E=this.positionAbs.left,D=E+this.helperProportions.width,I=this.positionAbs.top,H=I+this.helperProportions.height;var F=J.left,C=F+J.width,K=J.top,G=K+J.height;if(this.options.tolerance=="pointer"||(this.options.tolerance=="guess"&&this.helperProportions[this.floating?"width":"height"]>J[this.floating?"width":"height"])){if(!(I+this.offset.click.top>K&&I+this.offset.click.top<G&&E+this.offset.click.left>F&&E+this.offset.click.left<C)){return false}if(this.floating){if(E+this.offset.click.left>F&&E+this.offset.click.left<F+J.width/2){return 2}if(E+this.offset.click.left>F+J.width/2&&E+this.offset.click.left<C){return 1}}else{if(I+this.offset.click.top>K&&I+this.offset.click.top<K+J.height/2){return 2}if(I+this.offset.click.top>K+J.height/2&&I+this.offset.click.top<G){return 1}}}else{if(!(F<E+(this.helperProportions.width/2)&&D-(this.helperProportions.width/2)<C&&K<I+(this.helperProportions.height/2)&&H-(this.helperProportions.height/2)<G)){return false}if(this.floating){if(D>F&&E<F){return 2}if(E<C&&D>C){return 1}}else{if(H>K&&I<K){return 1}if(I<G&&H>G){return 2}}}return false},refresh:function(){this.refreshItems();this.refreshPositions()},refreshItems:function(){this.items=[];this.containers=[this];var D=this.items;var C=this;var F=[[B.isFunction(this.options.items)?this.options.items.call(this.element,null,{options:this.options,item:this.currentItem}):B(this.options.items,this.element),this]];if(this.options.connectWith){for(var G=this.options.connectWith.length-1;G>=0;G--){var I=B(this.options.connectWith[G]);for(var E=I.length-1;E>=0;E--){var H=B.data(I[E],"sortable");if(H&&!H.options.disabled){F.push([B.isFunction(H.options.items)?H.options.items.call(H.element):B(H.options.items,H.element),H]);this.containers.push(H)}}}}for(var G=F.length-1;G>=0;G--){F[G][0].each(function(){B.data(this,"sortable-item",F[G][1]);D.push({item:B(this),instance:F[G][1],width:0,height:0,left:0,top:0})})}},refreshPositions:function(D){if(this.offsetParent){var C=this.offsetParent.offset();this.offset.parent={top:C.top+this.offsetParentBorders.top,left:C.left+this.offsetParentBorders.left}}for(var F=this.items.length-1;F>=0;F--){if(this.items[F].instance!=this.currentContainer&&this.currentContainer&&this.items[F].item[0]!=this.currentItem[0]){continue}var E=this.options.toleranceElement?B(this.options.toleranceElement,this.items[F].item):this.items[F].item;if(!D){this.items[F].width=E[0].offsetWidth;this.items[F].height=E[0].offsetHeight}var G=E.offset();this.items[F].left=G.left;this.items[F].top=G.top}if(this.options.custom&&this.options.custom.refreshContainers){this.options.custom.refreshContainers.call(this)}else{for(var F=this.containers.length-1;F>=0;F--){var G=this.containers[F].element.offset();this.containers[F].containerCache.left=G.left;this.containers[F].containerCache.top=G.top;this.containers[F].containerCache.width=this.containers[F].element.outerWidth();this.containers[F].containerCache.height=this.containers[F].element.outerHeight()}}},destroy:function(){this.element.removeClass("ui-sortable ui-sortable-disabled").removeData("sortable").unbind(".sortable");this.mouseDestroy();for(var C=this.items.length-1;C>=0;C--){this.items[C].item.removeData("sortable-item")}},createPlaceholder:function(E){var C=E||this,F=C.options;if(F.placeholder.constructor==String){var D=F.placeholder;F.placeholder={element:function(){return B("<div></div>").addClass(D)[0]},update:function(G,H){H.css(G.offset()).css({width:G.outerWidth(),height:G.outerHeight()})}}}C.placeholder=B(F.placeholder.element.call(C.element,C.currentItem)).appendTo("body").css({position:"absolute"});F.placeholder.update.call(C.element,C.currentItem,C.placeholder)},contactContainers:function(F){for(var D=this.containers.length-1;D>=0;D--){if(this.intersectsWith(this.containers[D].containerCache)){if(!this.containers[D].containerCache.over){if(this.currentContainer!=this.containers[D]){var I=10000;var H=null;var E=this.positionAbs[this.containers[D].floating?"left":"top"];for(var C=this.items.length-1;C>=0;C--){if(!A(this.containers[D].element[0],this.items[C].item[0])){continue}var G=this.items[C][this.containers[D].floating?"left":"top"];if(Math.abs(G-E)<I){I=Math.abs(G-E);H=this.items[C]}}if(!H&&!this.options.dropOnEmpty){continue}if(this.placeholder){this.placeholder.remove()}if(this.containers[D].options.placeholder){this.containers[D].createPlaceholder(this)}else{this.placeholder=null}this.currentContainer=this.containers[D];H?this.rearrange(F,H,null,true):this.rearrange(F,null,this.containers[D].element,true);this.propagate("change",F);this.containers[D].propagate("change",F,this)}this.containers[D].propagate("over",F,this);this.containers[D].containerCache.over=1}}else{if(this.containers[D].containerCache.over){this.containers[D].propagate("out",F,this);this.containers[D].containerCache.over=0}}}},mouseCapture:function(G,F){if(this.options.disabled||this.options.type=="static"){return false}this.refreshItems();var E=null,D=this,C=B(G.target).parents().each(function(){if(B.data(this,"sortable-item")==D){E=B(this);return false}});if(B.data(G.target,"sortable-item")==D){E=B(G.target)}if(!E){return false}if(this.options.handle&&!F){var H=false;B(this.options.handle,E).find("*").andSelf().each(function(){if(this==G.target){H=true}});if(!H){return false}}this.currentItem=E;return true},mouseStart:function(H,F,C){var J=this.options;this.currentContainer=this;this.refreshPositions();this.helper=typeof J.helper=="function"?B(J.helper.apply(this.element[0],[H,this.currentItem])):this.currentItem.clone();if(!this.helper.parents("body").length){B(J.appendTo!="parent"?J.appendTo:this.currentItem[0].parentNode)[0].appendChild(this.helper[0])}this.helper.css({position:"absolute",clear:"both"}).addClass("ui-sortable-helper");this.margins={left:(parseInt(this.currentItem.css("marginLeft"),10)||0),top:(parseInt(this.currentItem.css("marginTop"),10)||0)};this.offset=this.currentItem.offset();this.offset={top:this.offset.top-this.margins.top,left:this.offset.left-this.margins.left};this.offset.click={left:H.pageX-this.offset.left,top:H.pageY-this.offset.top};this.offsetParent=this.helper.offsetParent();var D=this.offsetParent.offset();this.offsetParentBorders={top:(parseInt(this.offsetParent.css("borderTopWidth"),10)||0),left:(parseInt(this.offsetParent.css("borderLeftWidth"),10)||0)};this.offset.parent={top:D.top+this.offsetParentBorders.top,left:D.left+this.offsetParentBorders.left};this.originalPosition=this.generatePosition(H);this.domPosition={prev:this.currentItem.prev()[0],parent:this.currentItem.parent()[0]};this.helperProportions={width:this.helper.outerWidth(),height:this.helper.outerHeight()};if(J.placeholder){this.createPlaceholder()}this.propagate("start",H);this.helperProportions={width:this.helper.outerWidth(),height:this.helper.outerHeight()};if(J.cursorAt){if(J.cursorAt.left!=undefined){this.offset.click.left=J.cursorAt.left}if(J.cursorAt.right!=undefined){this.offset.click.left=this.helperProportions.width-J.cursorAt.right}if(J.cursorAt.top!=undefined){this.offset.click.top=J.cursorAt.top}if(J.cursorAt.bottom!=undefined){this.offset.click.top=this.helperProportions.height-J.cursorAt.bottom}}if(J.containment){if(J.containment=="parent"){J.containment=this.helper[0].parentNode}if(J.containment=="document"||J.containment=="window"){this.containment=[0-this.offset.parent.left,0-this.offset.parent.top,B(J.containment=="document"?document:window).width()-this.offset.parent.left-this.helperProportions.width-this.margins.left-(parseInt(this.element.css("marginRight"),10)||0),(B(J.containment=="document"?document:window).height()||document.body.parentNode.scrollHeight)-this.offset.parent.top-this.helperProportions.height-this.margins.top-(parseInt(this.element.css("marginBottom"),10)||0)]}if(!(/^(document|window|parent)$/).test(J.containment)){var G=B(J.containment)[0];var I=B(J.containment).offset();this.containment=[I.left+(parseInt(B(G).css("borderLeftWidth"),10)||0)-this.offset.parent.left,I.top+(parseInt(B(G).css("borderTopWidth"),10)||0)-this.offset.parent.top,I.left+Math.max(G.scrollWidth,G.offsetWidth)-(parseInt(B(G).css("borderLeftWidth"),10)||0)-this.offset.parent.left-this.helperProportions.width-this.margins.left-(parseInt(this.currentItem.css("marginRight"),10)||0),I.top+Math.max(G.scrollHeight,G.offsetHeight)-(parseInt(B(G).css("borderTopWidth"),10)||0)-this.offset.parent.top-this.helperProportions.height-this.margins.top-(parseInt(this.currentItem.css("marginBottom"),10)||0)]}}if(this.options.placeholder!="clone"){this.currentItem.css("visibility","hidden")}if(!C){for(var E=this.containers.length-1;E>=0;E--){this.containers[E].propagate("activate",H,this)}}if(B.ui.ddmanager){B.ui.ddmanager.current=this}if(B.ui.ddmanager&&!J.dropBehaviour){B.ui.ddmanager.prepareOffsets(this,H)}this.dragging=true;this.mouseDrag(H);return true},convertPositionTo:function(D,E){if(!E){E=this.position}var C=D=="absolute"?1:-1;return{top:(E.top+this.offset.parent.top*C-(this.offsetParent[0]==document.body?0:this.offsetParent[0].scrollTop)*C+this.margins.top*C),left:(E.left+this.offset.parent.left*C-(this.offsetParent[0]==document.body?0:this.offsetParent[0].scrollLeft)*C+this.margins.left*C)}},generatePosition:function(F){var G=this.options;var C={top:(F.pageY-this.offset.click.top-this.offset.parent.top+(this.offsetParent[0]==document.body?0:this.offsetParent[0].scrollTop)),left:(F.pageX-this.offset.click.left-this.offset.parent.left+(this.offsetParent[0]==document.body?0:this.offsetParent[0].scrollLeft))};if(!this.originalPosition){return C}if(this.containment){if(C.left<this.containment[0]){C.left=this.containment[0]}if(C.top<this.containment[1]){C.top=this.containment[1]}if(C.left>this.containment[2]){C.left=this.containment[2]}if(C.top>this.containment[3]){C.top=this.containment[3]}}if(G.grid){var E=this.originalPosition.top+Math.round((C.top-this.originalPosition.top)/G.grid[1])*G.grid[1];C.top=this.containment?(!(E<this.containment[1]||E>this.containment[3])?E:(!(E<this.containment[1])?E-G.grid[1]:E+G.grid[1])):E;var D=this.originalPosition.left+Math.round((C.left-this.originalPosition.left)/G.grid[0])*G.grid[0];C.left=this.containment?(!(D<this.containment[0]||D>this.containment[2])?D:(!(D<this.containment[0])?D-G.grid[0]:D+G.grid[0])):D}return C},mouseDrag:function(D){this.position=this.generatePosition(D);this.positionAbs=this.convertPositionTo("absolute");B.ui.plugin.call(this,"sort",[D,this.ui()]);this.positionAbs=this.convertPositionTo("absolute");this.helper[0].style.left=this.position.left+"px";this.helper[0].style.top=this.position.top+"px";for(var C=this.items.length-1;C>=0;C--){var E=this.intersectsWithEdge(this.items[C]);if(!E){continue}if(this.items[C].item[0]!=this.currentItem[0]&&this.currentItem[E==1?"next":"prev"]()[0]!=this.items[C].item[0]&&!A(this.currentItem[0],this.items[C].item[0])&&(this.options.type=="semi-dynamic"?!A(this.element[0],this.items[C].item[0]):true)){this.direction=E==1?"down":"up";this.rearrange(D,this.items[C]);this.propagate("change",D);break}}this.contactContainers(D);if(B.ui.ddmanager){B.ui.ddmanager.drag(this,D)}this.element.triggerHandler("sort",[D,this.ui()],this.options["sort"]);return false},rearrange:function(H,G,D,F){D?D[0].appendChild(this.currentItem[0]):G.item[0].parentNode.insertBefore(this.currentItem[0],(this.direction=="down"?G.item[0]:G.item[0].nextSibling));this.counter=this.counter?++this.counter:1;var E=this,C=this.counter;window.setTimeout(function(){if(C==E.counter){E.refreshPositions(!F)}},0);if(this.options.placeholder){this.options.placeholder.update.call(this.element,this.currentItem,this.placeholder)}},mouseStop:function(E,D){if(B.ui.ddmanager&&!this.options.dropBehaviour){B.ui.ddmanager.drop(this,E)}if(this.options.revert){var C=this;var F=C.currentItem.offset();if(C.placeholder){C.placeholder.animate({opacity:"hide"},(parseInt(this.options.revert,10)||500)-50)}B(this.helper).animate({left:F.left-this.offset.parent.left-C.margins.left+(this.offsetParent[0]==document.body?0:this.offsetParent[0].scrollLeft),top:F.top-this.offset.parent.top-C.margins.top+(this.offsetParent[0]==document.body?0:this.offsetParent[0].scrollTop)},parseInt(this.options.revert,10)||500,function(){C.clear(E)})}else{this.clear(E,D)}return false},clear:function(E,D){if(this.domPosition.prev!=this.currentItem.prev().not(".ui-sortable-helper")[0]||this.domPosition.parent!=this.currentItem.parent()[0]){this.propagate("update",E,null,D)}if(!A(this.element[0],this.currentItem[0])){this.propagate("remove",E,null,D);for(var C=this.containers.length-1;C>=0;C--){if(A(this.containers[C].element[0],this.currentItem[0])){this.containers[C].propagate("update",E,this,D);this.containers[C].propagate("receive",E,this,D)}}}for(var C=this.containers.length-1;C>=0;C--){this.containers[C].propagate("deactivate",E,this,D);if(this.containers[C].containerCache.over){this.containers[C].propagate("out",E,this);this.containers[C].containerCache.over=0}}this.dragging=false;if(this.cancelHelperRemoval){this.propagate("stop",E,null,D);return false}B(this.currentItem).css("visibility","");if(this.placeholder){this.placeholder.remove()}this.helper.remove();this.helper=null;this.propagate("stop",E,null,D);return true}}));B.extend(B.ui.sortable,{getter:"serialize toArray",defaults:{helper:"clone",tolerance:"guess",distance:1,delay:0,scroll:true,scrollSensitivity:20,scrollSpeed:20,cancel:":input",items:"> *",zIndex:1000,dropOnEmpty:true,appendTo:"parent"}});B.ui.plugin.add("sortable","cursor",{start:function(E,D){var C=B("body");if(C.css("cursor")){D.options._cursor=C.css("cursor")}C.css("cursor",D.options.cursor)},stop:function(D,C){if(C.options._cursor){B("body").css("cursor",C.options._cursor)}}});B.ui.plugin.add("sortable","zIndex",{start:function(E,D){var C=D.helper;if(C.css("zIndex")){D.options._zIndex=C.css("zIndex")}C.css("zIndex",D.options.zIndex)},stop:function(D,C){if(C.options._zIndex){B(C.helper).css("zIndex",C.options._zIndex)}}});B.ui.plugin.add("sortable","opacity",{start:function(E,D){var C=D.helper;if(C.css("opacity")){D.options._opacity=C.css("opacity")}C.css("opacity",D.options.opacity)},stop:function(D,C){if(C.options._opacity){B(C.helper).css("opacity",C.options._opacity)}}});B.ui.plugin.add("sortable","scroll",{start:function(E,D){var F=D.options;var C=B(this).data("sortable");C.overflowY=function(G){do{if(/auto|scroll/.test(G.css("overflow"))||(/auto|scroll/).test(G.css("overflow-y"))){return G}G=G.parent()}while(G[0].parentNode);return B(document)}(C.currentItem);C.overflowX=function(G){do{if(/auto|scroll/.test(G.css("overflow"))||(/auto|scroll/).test(G.css("overflow-x"))){return G}G=G.parent()}while(G[0].parentNode);return B(document)}(C.currentItem);if(C.overflowY[0]!=document&&C.overflowY[0].tagName!="HTML"){C.overflowYOffset=C.overflowY.offset()}if(C.overflowX[0]!=document&&C.overflowX[0].tagName!="HTML"){C.overflowXOffset=C.overflowX.offset()}},sort:function(E,D){var F=D.options;var C=B(this).data("sortable");if(C.overflowY[0]!=document&&C.overflowY[0].tagName!="HTML"){if((C.overflowYOffset.top+C.overflowY[0].offsetHeight)-E.pageY<F.scrollSensitivity){C.overflowY[0].scrollTop=C.overflowY[0].scrollTop+F.scrollSpeed}if(E.pageY-C.overflowYOffset.top<F.scrollSensitivity){C.overflowY[0].scrollTop=C.overflowY[0].scrollTop-F.scrollSpeed}}else{if(E.pageY-B(document).scrollTop()<F.scrollSensitivity){B(document).scrollTop(B(document).scrollTop()-F.scrollSpeed)}if(B(window).height()-(E.pageY-B(document).scrollTop())<F.scrollSensitivity){B(document).scrollTop(B(document).scrollTop()+F.scrollSpeed)}}if(C.overflowX[0]!=document&&C.overflowX[0].tagName!="HTML"){if((C.overflowXOffset.left+C.overflowX[0].offsetWidth)-E.pageX<F.scrollSensitivity){C.overflowX[0].scrollLeft=C.overflowX[0].scrollLeft+F.scrollSpeed}if(E.pageX-C.overflowXOffset.left<F.scrollSensitivity){C.overflowX[0].scrollLeft=C.overflowX[0].scrollLeft-F.scrollSpeed}}else{if(E.pageX-B(document).scrollLeft()<F.scrollSensitivity){B(document).scrollLeft(B(document).scrollLeft()-F.scrollSpeed)}if(B(window).width()-(E.pageX-B(document).scrollLeft())<F.scrollSensitivity){B(document).scrollLeft(B(document).scrollLeft()+F.scrollSpeed)}}}});B.ui.plugin.add("sortable","axis",{sort:function(E,D){var C=B(this).data("sortable");if(D.options.axis=="y"){C.position.left=C.originalPosition.left}if(D.options.axis=="x"){C.position.top=C.originalPosition.top}}})})(jQuery);(function(E){E.widget("ui.accordion",{init:function(){var G=this.options;if(G.navigation){var J=this.element.find("a").filter(G.navigationFilter);if(J.length){if(J.filter(G.header).length){G.active=J}else{G.active=J.parent().parent().prev();J.addClass("current")}}}G.headers=this.element.find(G.header);G.active=C(G.headers,G.active);if(E.browser.msie){this.element.find("a").css("zoom","1")}if(!this.element.hasClass("ui-accordion")){this.element.addClass("ui-accordion");E("<span class='ui-accordion-left'/>").insertBefore(G.headers);E("<span class='ui-accordion-right'/>").appendTo(G.headers);G.headers.addClass("ui-accordion-header").attr("tabindex","0")}var I;if(G.fillSpace){I=this.element.parent().height();G.headers.each(function(){I-=E(this).outerHeight()});var H=0;G.headers.next().each(function(){H=Math.max(H,E(this).innerHeight()-E(this).height())}).height(I-H)}else{if(G.autoHeight){I=0;G.headers.next().each(function(){I=Math.max(I,E(this).outerHeight())}).height(I)}}G.headers.not(G.active||"").next().hide();G.active.parent().andSelf().addClass(G.selectedClass);if(G.event){this.element.bind((G.event)+".accordion",F)}},activate:function(G){F.call(this.element[0],{target:C(this.options.headers,G)[0]})},destroy:function(){this.options.headers.next().css("display","");if(this.options.fillSpace||this.options.autoHeight){this.options.headers.next().css("height","")}E.removeData(this.element[0],"accordion");this.element.removeClass("ui-accordion").unbind(".accordion")}});function B(H,G){return function(){return H.apply(G,arguments)}}function D(I){if(!E.data(this,"accordion")){return }var G=E.data(this,"accordion");var H=G.options;H.running=I?0:--H.running;if(H.running){return }if(H.clearStyle){H.toShow.add(H.toHide).css({height:"",overflow:""})}E(this).triggerHandler("accordionchange",[E.event.fix({type:"accordionchange",target:G.element[0]}),H.data],H.change)}function A(G,K,L,J,M){var I=E.data(this,"accordion").options;I.toShow=G;I.toHide=K;I.data=L;var H=B(D,this);I.running=K.size()===0?G.size():K.size();if(I.animated){if(!I.alwaysOpen&&J){E.ui.accordion.animations[I.animated]({toShow:jQuery([]),toHide:K,complete:H,down:M,autoHeight:I.autoHeight})}else{E.ui.accordion.animations[I.animated]({toShow:G,toHide:K,complete:H,down:M,autoHeight:I.autoHeight})}}else{if(!I.alwaysOpen&&J){G.toggle()}else{K.hide();G.show()}H(true)}}function F(L){var J=E.data(this,"accordion").options;if(J.disabled){return false}if(!L.target&&!J.alwaysOpen){J.active.parent().andSelf().toggleClass(J.selectedClass);var I=J.active.next(),M={options:J,newHeader:jQuery([]),oldHeader:J.active,newContent:jQuery([]),oldContent:I},G=(J.active=E([]));A.call(this,G,I,M);return false}var K=E(L.target);K=E(K.parents(J.header)[0]||K);var H=K[0]==J.active[0];if(J.running||(J.alwaysOpen&&H)){return false}if(!K.is(J.header)){return }J.active.parent().andSelf().toggleClass(J.selectedClass);if(!H){K.parent().andSelf().addClass(J.selectedClass)}var G=K.next(),I=J.active.next(),M={options:J,newHeader:K,oldHeader:J.active,newContent:G,oldContent:I},N=J.headers.index(J.active[0])>J.headers.index(K[0]);J.active=H?E([]):K;A.call(this,G,I,M,H,N);return false}function C(H,G){return G!=undefined?typeof G=="number"?H.filter(":eq("+G+")"):H.not(H.not(G)):G===false?E([]):H.filter(":eq(0)")}E.extend(E.ui.accordion,{defaults:{selectedClass:"selected",alwaysOpen:true,animated:"slide",event:"click",header:"a",autoHeight:true,running:0,navigationFilter:function(){return this.href.toLowerCase()==location.href.toLowerCase()}},animations:{slide:function(G,I){G=E.extend({easing:"swing",duration:300},G,I);if(!G.toHide.size()){G.toShow.animate({height:"show"},G);return }var H=G.toHide.height(),J=G.toShow.height(),K=J/H;G.toShow.css({height:0,overflow:"hidden"}).show();G.toHide.filter(":hidden").each(G.complete).end().filter(":visible").animate({height:"hide"},{step:function(L){var M=(H-L)*K;if(E.browser.msie||E.browser.opera){M=Math.ceil(M)}G.toShow.height(M)},duration:G.duration,easing:G.easing,complete:function(){if(!G.autoHeight){G.toShow.css("height","auto")}G.complete()}})},bounceslide:function(G){this.slide(G,{easing:G.down?"bounceout":"swing",duration:G.down?1000:200})},easeslide:function(G){this.slide(G,{easing:"easeinout",duration:700})}}});E.fn.activate=function(G){return this.accordion("activate",G)}})(jQuery);(function(B){var A={dragStart:"start.draggable",drag:"drag.draggable",dragStop:"stop.draggable",maxHeight:"maxHeight.resizable",minHeight:"minHeight.resizable",maxWidth:"maxWidth.resizable",minWidth:"minWidth.resizable",resizeStart:"start.resizable",resize:"drag.resizable",resizeStop:"stop.resizable"};B.widget("ui.dialog",{init:function(){var J=this,K=this.options,D=typeof K.resizable=="string"?K.resizable:"n,e,s,w,se,sw,ne,nw",E=this.element.addClass("ui-dialog-content").wrap("<div/>").wrap("<div/>"),G=(this.uiDialogContainer=E.parent().addClass("ui-dialog-container").css({position:"relative",width:"100%",height:"100%"})),H=K.title||E.attr("title")||"",C=(this.uiDialogTitlebar=B('<div class="ui-dialog-titlebar"/>')).append('<span class="ui-dialog-title">'+H+"</span>").append('<a href="#" class="ui-dialog-titlebar-close"><span>X</span></a>').prependTo(G),I=(this.uiDialog=G.parent()).appendTo(document.body).hide().addClass("ui-dialog").addClass(K.dialogClass).addClass(E.attr("className")).removeClass("ui-dialog-content").css({position:"absolute",width:K.width,height:K.height,overflow:"hidden",zIndex:K.zIndex}).attr("tabIndex",-1).css("outline",0).keydown(function(L){if(K.closeOnEscape){var M=27;(L.keyCode&&L.keyCode==M&&J.close())}}).mousedown(function(){J.moveToTop()}),F=(this.uiDialogButtonPane=B("<div/>")).addClass("ui-dialog-buttonpane").css({position:"absolute",bottom:0}).appendTo(I);this.uiDialogTitlebarClose=B(".ui-dialog-titlebar-close",C).hover(function(){B(this).addClass("ui-dialog-titlebar-close-hover")},function(){B(this).removeClass("ui-dialog-titlebar-close-hover")}).mousedown(function(L){L.stopPropagation()}).click(function(){J.close();return false});this.uiDialogTitlebar.find("*").add(this.uiDialogTitlebar).each(function(){B.ui.disableSelection(this)});if(B.fn.draggable){I.draggable({cancel:".ui-dialog-content",helper:K.dragHelper,handle:".ui-dialog-titlebar",start:function(M,L){J.moveToTop();(K.dragStart&&K.dragStart.apply(J.element[0],arguments))},drag:function(M,L){(K.drag&&K.drag.apply(J.element[0],arguments))},stop:function(M,L){(K.dragStop&&K.dragStop.apply(J.element[0],arguments));B.ui.dialog.overlay.resize()}});(K.draggable||I.draggable("disable"))}if(B.fn.resizable){I.resizable({cancel:".ui-dialog-content",helper:K.resizeHelper,maxWidth:K.maxWidth,maxHeight:K.maxHeight,minWidth:K.minWidth,minHeight:K.minHeight,start:function(){(K.resizeStart&&K.resizeStart.apply(J.element[0],arguments))},resize:function(M,L){(K.autoResize&&J.size.apply(J));(K.resize&&K.resize.apply(J.element[0],arguments))},handles:D,stop:function(M,L){(K.autoResize&&J.size.apply(J));(K.resizeStop&&K.resizeStop.apply(J.element[0],arguments));B.ui.dialog.overlay.resize()}});(K.resizable||I.resizable("disable"))}this.createButtons(K.buttons);this.isOpen=false;(K.bgiframe&&B.fn.bgiframe&&I.bgiframe());(K.autoOpen&&this.open())},setData:function(C,D){(A[C]&&this.uiDialog.data(A[C],D));switch(C){case"buttons":this.createButtons(D);break;case"draggable":this.uiDialog.draggable(D?"enable":"disable");break;case"height":this.uiDialog.height(D);break;case"position":this.position(D);break;case"resizable":(typeof D=="string"&&this.uiDialog.data("handles.resizable",D));this.uiDialog.resizable(D?"enable":"disable");break;case"title":B(".ui-dialog-title",this.uiDialogTitlebar).text(D);break;case"width":this.uiDialog.width(D);break}B.widget.prototype.setData.apply(this,arguments)},position:function(H){var D=B(window),E=B(document),F=E.scrollTop(),C=E.scrollLeft(),G=F;if(B.inArray(H,["center","top","right","bottom","left"])>=0){H=[H=="right"||H=="left"?H:"center",H=="top"||H=="bottom"?H:"middle"]}if(H.constructor!=Array){H=["center","middle"]}if(H[0].constructor==Number){C+=H[0]}else{switch(H[0]){case"left":C+=0;break;case"right":C+=D.width()-this.uiDialog.width();break;default:case"center":C+=(D.width()-this.uiDialog.width())/2}}if(H[1].constructor==Number){F+=H[1]}else{switch(H[1]){case"top":F+=0;break;case"bottom":F+=D.height()-this.uiDialog.height();break;default:case"middle":F+=(D.height()-this.uiDialog.height())/2}}F=Math.max(F,G);this.uiDialog.css({top:F,left:C})},size:function(){var D=this.uiDialogContainer,G=this.uiDialogTitlebar,E=this.element,F=parseInt(E.css("margin-top"),10)+parseInt(E.css("margin-bottom"),10),C=parseInt(E.css("margin-left"),10)+parseInt(E.css("margin-right"),10);E.height(D.height()-G.outerHeight()-F);E.width(D.width()-C)},open:function(){if(this.isOpen){return }this.overlay=this.options.modal?new B.ui.dialog.overlay(this):null;(this.uiDialog.next().length>0)&&this.uiDialog.appendTo("body");this.position(this.options.position);this.uiDialog.show(this.options.show);this.options.autoResize&&this.size();this.moveToTop(true);var C=null;var D={options:this.options};this.uiDialogTitlebarClose.focus();this.element.triggerHandler("dialogopen",[C,D],this.options.open);this.isOpen=true},moveToTop:function(E){if((this.options.modal&&!E)||(!this.options.stack&&!this.options.modal)){return this.element.triggerHandler("dialogfocus",[null,{options:this.options}],this.options.focus)}var D=this.options.zIndex,C=this.options;B(".ui-dialog:visible").each(function(){D=Math.max(D,parseInt(B(this).css("z-index"),10)||C.zIndex)});(this.overlay&&this.overlay.$el.css("z-index",++D));this.uiDialog.css("z-index",++D);this.element.triggerHandler("dialogfocus",[null,{options:this.options}],this.options.focus)},close:function(){(this.overlay&&this.overlay.destroy());this.uiDialog.hide(this.options.hide);var D=null;var C={options:this.options};this.element.triggerHandler("dialogclose",[D,C],this.options.close);B.ui.dialog.overlay.resize();this.isOpen=false},destroy:function(){(this.overlay&&this.overlay.destroy());this.uiDialog.hide();this.element.unbind(".dialog").removeData("dialog").removeClass("ui-dialog-content").hide().appendTo("body");this.uiDialog.remove()},createButtons:function(F){var E=this,C=false,D=this.uiDialogButtonPane;D.empty().hide();B.each(F,function(){return !(C=true)});if(C){D.show();B.each(F,function(G,H){B("<button/>").text(G).click(function(){H.apply(E.element[0],arguments)}).appendTo(D)})}}});B.extend(B.ui.dialog,{defaults:{autoOpen:true,autoResize:true,bgiframe:false,buttons:{},closeOnEscape:true,draggable:true,height:200,minHeight:100,minWidth:150,modal:false,overlay:{},position:"center",resizable:true,stack:true,width:300,zIndex:1000},overlay:function(C){this.$el=B.ui.dialog.overlay.create(C)}});B.extend(B.ui.dialog.overlay,{instances:[],events:B.map("focus,mousedown,mouseup,keydown,keypress,click".split(","),function(C){return C+".dialog-overlay"}).join(" "),create:function(D){if(this.instances.length===0){setTimeout(function(){B("a, :input").bind(B.ui.dialog.overlay.events,function(){var F=false;var H=B(this).parents(".ui-dialog");if(H.length){var E=B(".ui-dialog-overlay");if(E.length){var G=parseInt(E.css("z-index"),10);E.each(function(){G=Math.max(G,parseInt(B(this).css("z-index"),10))});F=parseInt(H.css("z-index"),10)>G}else{F=true}}return F})},1);B(document).bind("keydown.dialog-overlay",function(E){var F=27;(E.keyCode&&E.keyCode==F&&D.close())});B(window).bind("resize.dialog-overlay",B.ui.dialog.overlay.resize)}var C=B("<div/>").appendTo(document.body).addClass("ui-dialog-overlay").css(B.extend({borderWidth:0,margin:0,padding:0,position:"absolute",top:0,left:0,width:this.width(),height:this.height()},D.options.overlay));(D.options.bgiframe&&B.fn.bgiframe&&C.bgiframe());this.instances.push(C);return C},destroy:function(C){this.instances.splice(B.inArray(this.instances,C),1);if(this.instances.length===0){B("a, :input").add([document,window]).unbind(".dialog-overlay")}C.remove()},height:function(){if(B.browser.msie&&B.browser.version<7){var D=Math.max(document.documentElement.scrollHeight,document.body.scrollHeight);var C=Math.max(document.documentElement.offsetHeight,document.body.offsetHeight);if(D<C){return B(window).height()+"px"}else{return D+"px"}}else{return B(document).height()+"px"}},width:function(){if(B.browser.msie&&B.browser.version<7){var C=Math.max(document.documentElement.scrollWidth,document.body.scrollWidth);var D=Math.max(document.documentElement.offsetWidth,document.body.offsetWidth);if(C<D){return B(window).width()+"px"}else{return C+"px"}}else{return B(document).width()+"px"}},resize:function(){var C=B([]);B.each(B.ui.dialog.overlay.instances,function(){C=C.add(this)});C.css({width:0,height:0}).css({width:B.ui.dialog.overlay.width(),height:B.ui.dialog.overlay.height()})}});B.extend(B.ui.dialog.overlay.prototype,{destroy:function(){B.ui.dialog.overlay.destroy(this.$el)}})})(jQuery);(function(A){A.fn.unwrap=A.fn.unwrap||function(B){return this.each(function(){A(this).parents(B).eq(0).after(this).remove()})};A.widget("ui.slider",{plugins:{},ui:function(B){return{options:this.options,handle:this.currentHandle,value:this.options.axis!="both"||!this.options.axis?Math.round(this.value(null,this.options.axis=="vertical"?"y":"x")):{x:Math.round(this.value(null,"x")),y:Math.round(this.value(null,"y"))},range:this.getRange()}},propagate:function(C,B){A.ui.plugin.call(this,C,[B,this.ui()]);this.element.triggerHandler(C=="slide"?C:"slide"+C,[B,this.ui()],this.options[C])},destroy:function(){this.element.removeClass("ui-slider ui-slider-disabled").removeData("slider").unbind(".slider");if(this.handle&&this.handle.length){this.handle.unwrap("a");this.handle.each(function(){A(this).data("mouse").mouseDestroy()})}this.generated&&this.generated.remove()},setData:function(B,C){A.widget.prototype.setData.apply(this,arguments);if(/min|max|steps/.test(B)){this.initBoundaries()}if(B=="range"){C?this.handle.length==2&&this.createRange():this.removeRange()}},init:function(){var B=this;this.element.addClass("ui-slider");this.initBoundaries();this.handle=A(this.options.handle,this.element);if(!this.handle.length){B.handle=B.generated=A(B.options.handles||[0]).map(function(){var D=A("<div/>").addClass("ui-slider-handle").appendTo(B.element);if(this.id){D.attr("id",this.id)}return D[0]})}var C=function(D){this.element=A(D);this.element.data("mouse",this);this.options=B.options;this.element.bind("mousedown",function(){if(B.currentHandle){this.blur(B.currentHandle)}B.focus(this,1)});this.mouseInit()};A.extend(C.prototype,A.ui.mouse,{mouseStart:function(D){return B.start.call(B,D,this.element[0])},mouseStop:function(D){return B.stop.call(B,D,this.element[0])},mouseDrag:function(D){return B.drag.call(B,D,this.element[0])},mouseCapture:function(){return true},trigger:function(D){this.mouseDown(D)}});A(this.handle).each(function(){new C(this)}).wrap('<a href="javascript:void(0)" style="outline:none;border:none;"></a>').parent().bind("focus",function(D){B.focus(this.firstChild)}).bind("blur",function(D){B.blur(this.firstChild)}).bind("keydown",function(D){if(!B.options.noKeyboard){B.keydown(D.keyCode,this.firstChild)}});this.element.bind("mousedown.slider",function(D){B.click.apply(B,[D]);B.currentHandle.data("mouse").trigger(D);B.firstValue=B.firstValue+1});A.each(this.options.handles||[],function(D,E){B.moveTo(E.start,D,true)});if(!isNaN(this.options.startValue)){this.moveTo(this.options.startValue,0,true)}this.previousHandle=A(this.handle[0]);if(this.handle.length==2&&this.options.range){this.createRange()}},initBoundaries:function(){var B=this.element[0],C=this.options;this.actualSize={width:this.element.outerWidth(),height:this.element.outerHeight()};A.extend(C,{axis:C.axis||(B.offsetWidth<B.offsetHeight?"vertical":"horizontal"),max:!isNaN(parseInt(C.max,10))?{x:parseInt(C.max,10),y:parseInt(C.max,10)}:({x:C.max&&C.max.x||100,y:C.max&&C.max.y||100}),min:!isNaN(parseInt(C.min,10))?{x:parseInt(C.min,10),y:parseInt(C.min,10)}:({x:C.min&&C.min.x||0,y:C.min&&C.min.y||0})});C.realMax={x:C.max.x-C.min.x,y:C.max.y-C.min.y};C.stepping={x:C.stepping&&C.stepping.x||parseInt(C.stepping,10)||(C.steps?C.realMax.x/(C.steps.x||parseInt(C.steps,10)||C.realMax.x):0),y:C.stepping&&C.stepping.y||parseInt(C.stepping,10)||(C.steps?C.realMax.y/(C.steps.y||parseInt(C.steps,10)||C.realMax.y):0)}},keydown:function(C,B){if(/(37|38|39|40)/.test(C)){this.moveTo({x:/(37|39)/.test(C)?(C==37?"-":"+")+"="+this.oneStep("x"):0,y:/(38|40)/.test(C)?(C==38?"-":"+")+"="+this.oneStep("y"):0},B)}},focus:function(B,C){this.currentHandle=A(B).addClass("ui-slider-handle-active");if(C){this.currentHandle.parent()[0].focus()}},blur:function(B){A(B).removeClass("ui-slider-handle-active");if(this.currentHandle&&this.currentHandle[0]==B){this.previousHandle=this.currentHandle;this.currentHandle=null}},click:function(C){var D=[C.pageX,C.pageY];var B=false;this.handle.each(function(){if(this==C.target){B=true}});if(B||this.options.disabled||!(this.currentHandle||this.previousHandle)){return }if(!this.currentHandle&&this.previousHandle){this.focus(this.previousHandle,true)}this.offset=this.element.offset();this.moveTo({y:this.convertValue(C.pageY-this.offset.top-this.currentHandle[0].offsetHeight/2,"y"),x:this.convertValue(C.pageX-this.offset.left-this.currentHandle[0].offsetWidth/2,"x")},null,!this.options.distance)},createRange:function(){if(this.rangeElement){return }this.rangeElement=A("<div></div>").addClass("ui-slider-range").css({position:"absolute"}).appendTo(this.element);this.updateRange()},removeRange:function(){this.rangeElement.remove();this.rangeElement=null},updateRange:function(){var C=this.options.axis=="vertical"?"top":"left";var B=this.options.axis=="vertical"?"height":"width";this.rangeElement.css(C,(parseInt(A(this.handle[0]).css(C),10)||0)+this.handleSize(0,this.options.axis=="vertical"?"y":"x")/2);this.rangeElement.css(B,(parseInt(A(this.handle[1]).css(C),10)||0)-(parseInt(A(this.handle[0]).css(C),10)||0))},getRange:function(){return this.rangeElement?this.convertValue(parseInt(this.rangeElement.css(this.options.axis=="vertical"?"height":"width"),10),this.options.axis=="vertical"?"y":"x"):null},handleIndex:function(){return this.handle.index(this.currentHandle[0])},value:function(D,B){if(this.handle.length==1){this.currentHandle=this.handle}if(!B){B=this.options.axis=="vertical"?"y":"x"}var C=A(D!=undefined&&D!==null?this.handle[D]||D:this.currentHandle);if(C.data("mouse").sliderValue){return parseInt(C.data("mouse").sliderValue[B],10)}else{return parseInt(((parseInt(C.css(B=="x"?"left":"top"),10)/(this.actualSize[B=="x"?"width":"height"]-this.handleSize(D,B)))*this.options.realMax[B])+this.options.min[B],10)}},convertValue:function(C,B){return this.options.min[B]+(C/(this.actualSize[B=="x"?"width":"height"]-this.handleSize(null,B)))*this.options.realMax[B]},translateValue:function(C,B){return((C-this.options.min[B])/this.options.realMax[B])*(this.actualSize[B=="x"?"width":"height"]-this.handleSize(null,B))},translateRange:function(D,B){if(this.rangeElement){if(this.currentHandle[0]==this.handle[0]&&D>=this.translateValue(this.value(1),B)){D=this.translateValue(this.value(1,B)-this.oneStep(B),B)}if(this.currentHandle[0]==this.handle[1]&&D<=this.translateValue(this.value(0),B)){D=this.translateValue(this.value(0,B)+this.oneStep(B),B)}}if(this.options.handles){var C=this.options.handles[this.handleIndex()];if(D<this.translateValue(C.min,B)){D=this.translateValue(C.min,B)}else{if(D>this.translateValue(C.max,B)){D=this.translateValue(C.max,B)}}}return D},translateLimits:function(C,B){if(C>=this.actualSize[B=="x"?"width":"height"]-this.handleSize(null,B)){C=this.actualSize[B=="x"?"width":"height"]-this.handleSize(null,B)}if(C<=0){C=0}return C},handleSize:function(C,B){return A(C!=undefined&&C!==null?this.handle[C]:this.currentHandle)[0]["offset"+(B=="x"?"Width":"Height")]},oneStep:function(B){return this.options.stepping[B]||1},start:function(C,B){var D=this.options;if(D.disabled){return false}this.actualSize={width:this.element.outerWidth(),height:this.element.outerHeight()};if(!this.currentHandle){this.focus(this.previousHandle,true)}this.offset=this.element.offset();this.handleOffset=this.currentHandle.offset();this.clickOffset={top:C.pageY-this.handleOffset.top,left:C.pageX-this.handleOffset.left};this.firstValue=this.value();this.propagate("start",C);this.drag(C,B);return true},stop:function(B){this.propagate("stop",B);if(this.firstValue!=this.value()){this.propagate("change",B)}this.focus(this.currentHandle,true);return false},drag:function(E,D){var F=this.options;var B={top:E.pageY-this.offset.top-this.clickOffset.top,left:E.pageX-this.offset.left-this.clickOffset.left};if(!this.currentHandle){this.focus(this.previousHandle,true)}B.left=this.translateLimits(B.left,"x");B.top=this.translateLimits(B.top,"y");if(F.stepping.x){var C=this.convertValue(B.left,"x");C=Math.round(C/F.stepping.x)*F.stepping.x;B.left=this.translateValue(C,"x")}if(F.stepping.y){var C=this.convertValue(B.top,"y");C=Math.round(C/F.stepping.y)*F.stepping.y;B.top=this.translateValue(C,"y")}B.left=this.translateRange(B.left,"x");B.top=this.translateRange(B.top,"y");if(F.axis!="vertical"){this.currentHandle.css({left:B.left})}if(F.axis!="horizontal"){this.currentHandle.css({top:B.top})}this.currentHandle.data("mouse").sliderValue={x:Math.round(this.convertValue(B.left,"x"))||0,y:Math.round(this.convertValue(B.top,"y"))||0};if(this.rangeElement){this.updateRange()}this.propagate("slide",E);return false},moveTo:function(F,E,G){var H=this.options;this.actualSize={width:this.element.outerWidth(),height:this.element.outerHeight()};if(E==undefined&&!this.currentHandle&&this.handle.length!=1){return false}if(E==undefined&&!this.currentHandle){E=0}if(E!=undefined){this.currentHandle=this.previousHandle=A(this.handle[E]||E)}if(F.x!==undefined&&F.y!==undefined){var B=F.x,I=F.y}else{var B=F,I=F}if(B!==undefined&&B.constructor!=Number){var D=/^\-\=/.test(B),C=/^\+\=/.test(B);if(D||C){B=this.value(null,"x")+parseInt(B.replace(D?"=":"+=",""),10)}else{B=isNaN(parseInt(B,10))?undefined:parseInt(B,10)}}if(I!==undefined&&I.constructor!=Number){var D=/^\-\=/.test(I),C=/^\+\=/.test(I);if(D||C){I=this.value(null,"y")+parseInt(I.replace(D?"=":"+=",""),10)}else{I=isNaN(parseInt(I,10))?undefined:parseInt(I,10)}}if(H.axis!="vertical"&&B!==undefined){if(H.stepping.x){B=Math.round(B/H.stepping.x)*H.stepping.x}B=this.translateValue(B,"x");B=this.translateLimits(B,"x");B=this.translateRange(B,"x");H.animate?this.currentHandle.stop().animate({left:B},(Math.abs(parseInt(this.currentHandle.css("left"))-B))*(!isNaN(parseInt(H.animate))?H.animate:5)):this.currentHandle.css({left:B})}if(H.axis!="horizontal"&&I!==undefined){if(H.stepping.y){I=Math.round(I/H.stepping.y)*H.stepping.y}I=this.translateValue(I,"y");I=this.translateLimits(I,"y");I=this.translateRange(I,"y");H.animate?this.currentHandle.stop().animate({top:I},(Math.abs(parseInt(this.currentHandle.css("top"))-I))*(!isNaN(parseInt(H.animate))?H.animate:5)):this.currentHandle.css({top:I})}if(this.rangeElement){this.updateRange()}this.currentHandle.data("mouse").sliderValue={x:Math.round(this.convertValue(B,"x"))||0,y:Math.round(this.convertValue(I,"y"))||0};if(!G){this.propagate("start",null);this.propagate("stop",null);this.propagate("change",null);this.propagate("slide",null)}}});A.ui.slider.getter="value";A.ui.slider.defaults={handle:".ui-slider-handle",distance:1,animate:false}})(jQuery);(function(A){A.widget("ui.tabs",{init:function(){this.options.event+=".tabs";this.tabify(true)},setData:function(B,C){if((/^selected/).test(B)){this.select(C)}else{this.options[B]=C;this.tabify()}},length:function(){return this.$tabs.length},tabId:function(B){return B.title&&B.title.replace(/\s/g,"_").replace(/[^A-Za-z0-9\-_:\.]/g,"")||this.options.idPrefix+A.data(B)},ui:function(C,B){return{options:this.options,tab:C,panel:B,index:this.$tabs.index(C)}},tabify:function(O){this.$lis=A("li:has(a[href])",this.element);this.$tabs=this.$lis.map(function(){return A("a",this)[0]});this.$panels=A([]);var P=this,D=this.options;this.$tabs.each(function(R,Q){if(Q.hash&&Q.hash.replace("#","")){P.$panels=P.$panels.add(Q.hash)}else{if(A(Q).attr("href")!="#"){A.data(Q,"href.tabs",Q.href);A.data(Q,"load.tabs",Q.href);var T=P.tabId(Q);Q.href="#"+T;var S=A("#"+T);if(!S.length){S=A(D.panelTemplate).attr("id",T).addClass(D.panelClass).insertAfter(P.$panels[R-1]||P.element);S.data("destroy.tabs",true)}P.$panels=P.$panels.add(S)}else{D.disabled.push(R+1)}}});if(O){this.element.addClass(D.navClass);this.$panels.each(function(){var Q=A(this);Q.addClass(D.panelClass)});if(D.selected===undefined){if(location.hash){this.$tabs.each(function(S,Q){if(Q.hash==location.hash){D.selected=S;if(A.browser.msie||A.browser.opera){var R=A(location.hash),T=R.attr("id");R.attr("id","");setTimeout(function(){R.attr("id",T)},500)}scrollTo(0,0);return false}})}else{if(D.cookie){var J=parseInt(A.cookie("ui-tabs"+A.data(P.element)),10);if(J&&P.$tabs[J]){D.selected=J}}else{if(P.$lis.filter("."+D.selectedClass).length){D.selected=P.$lis.index(P.$lis.filter("."+D.selectedClass)[0])}}}}D.selected=D.selected===null||D.selected!==undefined?D.selected:0;D.disabled=A.unique(D.disabled.concat(A.map(this.$lis.filter("."+D.disabledClass),function(R,Q){return P.$lis.index(R)}))).sort();if(A.inArray(D.selected,D.disabled)!=-1){D.disabled.splice(A.inArray(D.selected,D.disabled),1)}this.$panels.addClass(D.hideClass);this.$lis.removeClass(D.selectedClass);if(D.selected!==null){this.$panels.eq(D.selected).show().removeClass(D.hideClass);this.$lis.eq(D.selected).addClass(D.selectedClass);var K=function(){A(P.element).triggerHandler("tabsshow",[P.fakeEvent("tabsshow"),P.ui(P.$tabs[D.selected],P.$panels[D.selected])],D.show)};if(A.data(this.$tabs[D.selected],"load.tabs")){this.load(D.selected,K)}else{K()}}A(window).bind("unload",function(){P.$tabs.unbind(".tabs");P.$lis=P.$tabs=P.$panels=null})}for(var G=0,N;N=this.$lis[G];G++){A(N)[A.inArray(G,D.disabled)!=-1&&!A(N).hasClass(D.selectedClass)?"addClass":"removeClass"](D.disabledClass)}if(D.cache===false){this.$tabs.removeData("cache.tabs")}var C,I,B={"min-width":0,duration:1},E="normal";if(D.fx&&D.fx.constructor==Array){C=D.fx[0]||B,I=D.fx[1]||B}else{C=I=D.fx||B}var H={display:"",overflow:"",height:""};if(!A.browser.msie){H.opacity=""}function M(R,Q,S){Q.animate(C,C.duration||E,function(){Q.addClass(D.hideClass).css(H);if(A.browser.msie&&C.opacity){Q[0].style.filter=""}if(S){L(R,S,Q)}})}function L(R,S,Q){if(I===B){S.css("display","block")}S.animate(I,I.duration||E,function(){S.removeClass(D.hideClass).css(H);if(A.browser.msie&&I.opacity){S[0].style.filter=""}A(P.element).triggerHandler("tabsshow",[P.fakeEvent("tabsshow"),P.ui(R,S[0])],D.show)})}function F(R,T,Q,S){T.addClass(D.selectedClass).siblings().removeClass(D.selectedClass);M(R,Q,S)}this.$tabs.unbind(".tabs").bind(D.event,function(){var T=A(this).parents("li:eq(0)"),Q=P.$panels.filter(":visible"),S=A(this.hash);if((T.hasClass(D.selectedClass)&&!D.unselect)||T.hasClass(D.disabledClass)||A(this).hasClass(D.loadingClass)||A(P.element).triggerHandler("tabsselect",[P.fakeEvent("tabsselect"),P.ui(this,S[0])],D.select)===false){this.blur();return false}P.options.selected=P.$tabs.index(this);if(D.unselect){if(T.hasClass(D.selectedClass)){P.options.selected=null;T.removeClass(D.selectedClass);P.$panels.stop();M(this,Q);this.blur();return false}else{if(!Q.length){P.$panels.stop();var R=this;P.load(P.$tabs.index(this),function(){T.addClass(D.selectedClass).addClass(D.unselectClass);L(R,S)});this.blur();return false}}}if(D.cookie){A.cookie("ui-tabs"+A.data(P.element),P.options.selected,D.cookie)}P.$panels.stop();if(S.length){var R=this;P.load(P.$tabs.index(this),Q.length?function(){F(R,T,Q,S)}:function(){T.addClass(D.selectedClass);L(R,S)})}else{throw"jQuery UI Tabs: Mismatching fragment identifier."}if(A.browser.msie){this.blur()}return false});if(!(/^click/).test(D.event)){this.$tabs.bind("click.tabs",function(){return false})}},add:function(E,D,C){if(C==undefined){C=this.$tabs.length}var G=this.options;var I=A(G.tabTemplate.replace(/#\{href\}/g,E).replace(/#\{label\}/g,D));I.data("destroy.tabs",true);var H=E.indexOf("#")==0?E.replace("#",""):this.tabId(A("a:first-child",I)[0]);var F=A("#"+H);if(!F.length){F=A(G.panelTemplate).attr("id",H).addClass(G.hideClass).data("destroy.tabs",true)}F.addClass(G.panelClass);if(C>=this.$lis.length){I.appendTo(this.element);F.appendTo(this.element[0].parentNode)}else{I.insertBefore(this.$lis[C]);F.insertBefore(this.$panels[C])}G.disabled=A.map(G.disabled,function(K,J){return K>=C?++K:K});this.tabify();if(this.$tabs.length==1){I.addClass(G.selectedClass);F.removeClass(G.hideClass);var B=A.data(this.$tabs[0],"load.tabs");if(B){this.load(C,B)}}this.element.triggerHandler("tabsadd",[this.fakeEvent("tabsadd"),this.ui(this.$tabs[C],this.$panels[C])],G.add)},remove:function(B){var D=this.options,E=this.$lis.eq(B).remove(),C=this.$panels.eq(B).remove();if(E.hasClass(D.selectedClass)&&this.$tabs.length>1){this.select(B+(B+1<this.$tabs.length?1:-1))}D.disabled=A.map(A.grep(D.disabled,function(G,F){return G!=B}),function(G,F){return G>=B?--G:G});this.tabify();this.element.triggerHandler("tabsremove",[this.fakeEvent("tabsremove"),this.ui(E.find("a")[0],C[0])],D.remove)},enable:function(B){var C=this.options;if(A.inArray(B,C.disabled)==-1){return }var D=this.$lis.eq(B).removeClass(C.disabledClass);if(A.browser.safari){D.css("display","inline-block");setTimeout(function(){D.css("display","block")},0)}C.disabled=A.grep(C.disabled,function(F,E){return F!=B});this.element.triggerHandler("tabsenable",[this.fakeEvent("tabsenable"),this.ui(this.$tabs[B],this.$panels[B])],C.enable)},disable:function(C){var B=this,D=this.options;if(C!=D.selected){this.$lis.eq(C).addClass(D.disabledClass);D.disabled.push(C);D.disabled.sort();this.element.triggerHandler("tabsdisable",[this.fakeEvent("tabsdisable"),this.ui(this.$tabs[C],this.$panels[C])],D.disable)}},select:function(B){if(typeof B=="string"){B=this.$tabs.index(this.$tabs.filter("[href$="+B+"]")[0])}this.$tabs.eq(B).trigger(this.options.event)},load:function(G,K){var L=this,D=this.options,E=this.$tabs.eq(G),J=E[0],H=K==undefined||K===false,B=E.data("load.tabs");K=K||function(){};if(!B||!H&&A.data(J,"cache.tabs")){K();return }var M=function(N){var O=A(N),P=O.find("*:last");return P.length&&P.is(":not(img)")&&P||O};var C=function(){L.$tabs.filter("."+D.loadingClass).removeClass(D.loadingClass).each(function(){if(D.spinner){M(this).parent().html(M(this).data("label.tabs"))}});L.xhr=null};if(D.spinner){var I=M(J).html();M(J).wrapInner("<em></em>").find("em").data("label.tabs",I).html(D.spinner)}var F=A.extend({},D.ajaxOptions,{url:B,success:function(O,N){A(J.hash).html(O);C();if(D.cache){A.data(J,"cache.tabs",true)}A(L.element).triggerHandler("tabsload",[L.fakeEvent("tabsload"),L.ui(L.$tabs[G],L.$panels[G])],D.load);D.ajaxOptions.success&&D.ajaxOptions.success(O,N);K()}});if(this.xhr){this.xhr.abort();C()}E.addClass(D.loadingClass);setTimeout(function(){L.xhr=A.ajax(F)},0)},url:function(C,B){this.$tabs.eq(C).removeData("cache.tabs").data("load.tabs",B)},destroy:function(){var B=this.options;this.element.unbind(".tabs").removeClass(B.navClass).removeData("tabs");this.$tabs.each(function(){var C=A.data(this,"href.tabs");if(C){this.href=C}var D=A(this).unbind(".tabs");A.each(["href","load","cache"],function(E,F){D.removeData(F+".tabs")})});this.$lis.add(this.$panels).each(function(){if(A.data(this,"destroy.tabs")){A(this).remove()}else{A(this).removeClass([B.selectedClass,B.unselectClass,B.disabledClass,B.panelClass,B.hideClass].join(" "))}})},fakeEvent:function(B){return A.event.fix({type:B,target:this.element[0]})}});A.ui.tabs.defaults={unselect:false,event:"click",disabled:[],cookie:null,spinner:"Loading&#8230;",cache:false,idPrefix:"ui-tabs-",ajaxOptions:{},fx:null,tabTemplate:'<li><a href="#{href}"><span>#{label}</span></a></li>',panelTemplate:"<div></div>",navClass:"ui-tabs-nav",selectedClass:"ui-tabs-selected",unselectClass:"ui-tabs-unselect",disabledClass:"ui-tabs-disabled",panelClass:"ui-tabs-panel",hideClass:"ui-tabs-hide",loadingClass:"ui-tabs-loading"};A.ui.tabs.getter="length";A.extend(A.ui.tabs.prototype,{rotation:null,rotate:function(C,F){F=F||false;var B=this,E=this.options.selected;function G(){B.rotation=setInterval(function(){E=++E<B.$tabs.length?E:0;B.select(E)},C)}function D(H){if(!H||H.clientX){clearInterval(B.rotation)}}if(C){G();if(!F){this.$tabs.bind(this.options.event,D)}else{this.$tabs.bind(this.options.event,function(){D();E=B.options.selected;G()})}}else{D();this.$tabs.unbind(this.options.event,D)}}})})(jQuery);(function($){var PROP_NAME="datepicker";function Datepicker(){this.debug=false;this._curInst=null;this._disabledInputs=[];this._datepickerShowing=false;this._inDialog=false;this._mainDivId="ui-datepicker-div";this._appendClass="ui-datepicker-append";this._triggerClass="ui-datepicker-trigger";this._dialogClass="ui-datepicker-dialog";this._promptClass="ui-datepicker-prompt";this._unselectableClass="ui-datepicker-unselectable";this._currentClass="ui-datepicker-current-day";this.regional=[];this.regional[""]={clearText:"Clear",clearStatus:"Erase the current date",closeText:"Close",closeStatus:"Close without change",prevText:"&#x3c;Prev",prevStatus:"Show the previous month",nextText:"Next&#x3e;",nextStatus:"Show the next month",currentText:"Today",currentStatus:"Show the current month",monthNames:["January","February","March","April","May","June","July","August","September","October","November","December"],monthNamesShort:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],monthStatus:"Show a different month",yearStatus:"Show a different year",weekHeader:"Wk",weekStatus:"Week of the year",dayNames:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],dayNamesShort:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],dayNamesMin:["Su","Mo","Tu","We","Th","Fr","Sa"],dayStatus:"Set DD as first week day",dateStatus:"Select DD, M d",dateFormat:"mm/dd/yy",firstDay:0,initStatus:"Select a date",isRTL:false};this._defaults={showOn:"focus",showAnim:"show",showOptions:{},defaultDate:null,appendText:"",buttonText:"...",buttonImage:"",buttonImageOnly:false,closeAtTop:true,mandatory:false,hideIfNoPrevNext:false,navigationAsDateFormat:false,gotoCurrent:false,changeMonth:true,changeYear:true,yearRange:"-10:+10",changeFirstDay:true,highlightWeek:false,showOtherMonths:false,showWeeks:false,calculateWeek:this.iso8601Week,shortYearCutoff:"+10",showStatus:false,statusForDate:this.dateStatus,minDate:null,maxDate:null,duration:"normal",beforeShowDay:null,beforeShow:null,onSelect:null,onChangeMonthYear:null,onClose:null,numberOfMonths:1,stepMonths:1,rangeSelect:false,rangeSeparator:" - ",altField:"",altFormat:""};$.extend(this._defaults,this.regional[""]);this.dpDiv=$('<div id="'+this._mainDivId+'" style="display: none;"></div>')}$.extend(Datepicker.prototype,{markerClassName:"hasDatepicker",log:function(){if(this.debug){console.log.apply("",arguments)}},setDefaults:function(settings){extendRemove(this._defaults,settings||{});return this},_attachDatepicker:function(target,settings){var inlineSettings=null;for(attrName in this._defaults){var attrValue=target.getAttribute("date:"+attrName);if(attrValue){inlineSettings=inlineSettings||{};try{inlineSettings[attrName]=eval(attrValue)}catch(err){inlineSettings[attrName]=attrValue}}}var nodeName=target.nodeName.toLowerCase();var inline=(nodeName=="div"||nodeName=="span");if(!target.id){target.id="dp"+new Date().getTime()}var inst=this._newInst($(target),inline);inst.settings=$.extend({},settings||{},inlineSettings||{});if(nodeName=="input"){this._connectDatepicker(target,inst)}else{if(inline){this._inlineDatepicker(target,inst)}}},_newInst:function(target,inline){return{id:target[0].id,input:target,selectedDay:0,selectedMonth:0,selectedYear:0,drawMonth:0,drawYear:0,inline:inline,dpDiv:(!inline?this.dpDiv:$('<div class="ui-datepicker-inline"></div>'))}},_connectDatepicker:function(target,inst){var input=$(target);if(input.hasClass(this.markerClassName)){return }var appendText=this._get(inst,"appendText");var isRTL=this._get(inst,"isRTL");if(appendText){input[isRTL?"before":"after"]('<span class="'+this._appendClass+'">'+appendText+"</span>")}var showOn=this._get(inst,"showOn");if(showOn=="focus"||showOn=="both"){input.focus(this._showDatepicker)}if(showOn=="button"||showOn=="both"){var buttonText=this._get(inst,"buttonText");var buttonImage=this._get(inst,"buttonImage");var trigger=$(this._get(inst,"buttonImageOnly")?$("<img/>").addClass(this._triggerClass).attr({src:buttonImage,alt:buttonText,title:buttonText}):$('<button type="button"></button>').addClass(this._triggerClass).html(buttonImage==""?buttonText:$("<img/>").attr({src:buttonImage,alt:buttonText,title:buttonText})));input[isRTL?"before":"after"](trigger);trigger.click(function(){if($.datepicker._datepickerShowing&&$.datepicker._lastInput==target){$.datepicker._hideDatepicker()}else{$.datepicker._showDatepicker(target)}return false})}input.addClass(this.markerClassName).keydown(this._doKeyDown).keypress(this._doKeyPress).bind("setData.datepicker",function(event,key,value){inst.settings[key]=value}).bind("getData.datepicker",function(event,key){return this._get(inst,key)});$.data(target,PROP_NAME,inst)},_inlineDatepicker:function(target,inst){var input=$(target);if(input.hasClass(this.markerClassName)){return }input.addClass(this.markerClassName).append(inst.dpDiv).bind("setData.datepicker",function(event,key,value){inst.settings[key]=value}).bind("getData.datepicker",function(event,key){return this._get(inst,key)});$.data(target,PROP_NAME,inst);this._setDate(inst,this._getDefaultDate(inst));this._updateDatepicker(inst)},_dialogDatepicker:function(input,dateText,onSelect,settings,pos){var inst=this._dialogInst;if(!inst){var id="dp"+new Date().getTime();this._dialogInput=$('<input type="text" id="'+id+'" size="1" style="position: absolute; top: -100px;"/>');this._dialogInput.keydown(this._doKeyDown);$("body").append(this._dialogInput);inst=this._dialogInst=this._newInst(this._dialogInput,false);inst.settings={};$.data(this._dialogInput[0],PROP_NAME,inst)}extendRemove(inst.settings,settings||{});this._dialogInput.val(dateText);this._pos=(pos?(pos.length?pos:[pos.pageX,pos.pageY]):null);if(!this._pos){var browserWidth=window.innerWidth||document.documentElement.clientWidth||document.body.clientWidth;var browserHeight=window.innerHeight||document.documentElement.clientHeight||document.body.clientHeight;var scrollX=document.documentElement.scrollLeft||document.body.scrollLeft;var scrollY=document.documentElement.scrollTop||document.body.scrollTop;this._pos=[(browserWidth/2)-100+scrollX,(browserHeight/2)-150+scrollY]}this._dialogInput.css("left",this._pos[0]+"px").css("top",this._pos[1]+"px");inst.settings.onSelect=onSelect;this._inDialog=true;this.dpDiv.addClass(this._dialogClass);this._showDatepicker(this._dialogInput[0]);if($.blockUI){$.blockUI(this.dpDiv)}$.data(this._dialogInput[0],PROP_NAME,inst);return this},_destroyDatepicker:function(target){var nodeName=target.nodeName.toLowerCase();var $target=$(target);$.removeData(target,PROP_NAME);if(nodeName=="input"){$target.siblings("."+this._appendClass).remove().end().siblings("."+this._triggerClass).remove().end().removeClass(this.markerClassName).unbind("focus",this._showDatepicker).unbind("keydown",this._doKeyDown).unbind("keypress",this._doKeyPress)}else{if(nodeName=="div"||nodeName=="span"){$target.removeClass(this.markerClassName).empty()}}},_enableDatepicker:function(target){target.disabled=false;$(target).siblings("button."+this._triggerClass).each(function(){this.disabled=false}).end().siblings("img."+this._triggerClass).css({opacity:"1.0",cursor:""});this._disabledInputs=$.map(this._disabledInputs,function(value){return(value==target?null:value)})},_disableDatepicker:function(target){target.disabled=true;$(target).siblings("button."+this._triggerClass).each(function(){this.disabled=true}).end().siblings("img."+this._triggerClass).css({opacity:"0.5",cursor:"default"});this._disabledInputs=$.map(this._disabledInputs,function(value){return(value==target?null:value)});this._disabledInputs[this._disabledInputs.length]=target},_isDisabledDatepicker:function(target){if(!target){return false}for(var i=0;i<this._disabledInputs.length;i++){if(this._disabledInputs[i]==target){return true}}return false},_changeDatepicker:function(target,name,value){var settings=name||{};if(typeof name=="string"){settings={};settings[name]=value}if(inst=$.data(target,PROP_NAME)){extendRemove(inst.settings,settings);this._updateDatepicker(inst)}},_setDateDatepicker:function(target,date,endDate){var inst=$.data(target,PROP_NAME);if(inst){this._setDate(inst,date,endDate);this._updateDatepicker(inst)}},_getDateDatepicker:function(target){var inst=$.data(target,PROP_NAME);if(inst){this._setDateFromField(inst)}return(inst?this._getDate(inst):null)},_doKeyDown:function(e){var inst=$.data(e.target,PROP_NAME);var handled=true;if($.datepicker._datepickerShowing){switch(e.keyCode){case 9:$.datepicker._hideDatepicker(null,"");break;case 13:$.datepicker._selectDay(e.target,inst.selectedMonth,inst.selectedYear,$("td.ui-datepicker-days-cell-over",inst.dpDiv)[0]);return false;break;case 27:$.datepicker._hideDatepicker(null,$.datepicker._get(inst,"duration"));break;case 33:$.datepicker._adjustDate(e.target,(e.ctrlKey?-1:-$.datepicker._get(inst,"stepMonths")),(e.ctrlKey?"Y":"M"));break;case 34:$.datepicker._adjustDate(e.target,(e.ctrlKey?+1:+$.datepicker._get(inst,"stepMonths")),(e.ctrlKey?"Y":"M"));break;case 35:if(e.ctrlKey){$.datepicker._clearDate(e.target)}break;case 36:if(e.ctrlKey){$.datepicker._gotoToday(e.target)}break;case 37:if(e.ctrlKey){$.datepicker._adjustDate(e.target,-1,"D")}break;case 38:if(e.ctrlKey){$.datepicker._adjustDate(e.target,-7,"D")}break;case 39:if(e.ctrlKey){$.datepicker._adjustDate(e.target,+1,"D")}break;case 40:if(e.ctrlKey){$.datepicker._adjustDate(e.target,+7,"D")}break;default:handled=false}}else{if(e.keyCode==36&&e.ctrlKey){$.datepicker._showDatepicker(this)}else{handled=false}}if(handled){e.preventDefault();e.stopPropagation()}},_doKeyPress:function(e){var inst=$.data(e.target,PROP_NAME);var chars=$.datepicker._possibleChars($.datepicker._get(inst,"dateFormat"));var chr=String.fromCharCode(e.charCode==undefined?e.keyCode:e.charCode);return e.ctrlKey||(chr<" "||!chars||chars.indexOf(chr)>-1)},_showDatepicker:function(input){input=input.target||input;if(input.nodeName.toLowerCase()!="input"){input=$("input",input.parentNode)[0]}if($.datepicker._isDisabledDatepicker(input)||$.datepicker._lastInput==input){return }var inst=$.data(input,PROP_NAME);var beforeShow=$.datepicker._get(inst,"beforeShow");extendRemove(inst.settings,(beforeShow?beforeShow.apply(input,[input,inst]):{}));$.datepicker._hideDatepicker(null,"");$.datepicker._lastInput=input;$.datepicker._setDateFromField(inst);if($.datepicker._inDialog){input.value=""}if(!$.datepicker._pos){$.datepicker._pos=$.datepicker._findPos(input);$.datepicker._pos[1]+=input.offsetHeight}var isFixed=false;$(input).parents().each(function(){isFixed|=$(this).css("position")=="fixed";return !isFixed});if(isFixed&&$.browser.opera){$.datepicker._pos[0]-=document.documentElement.scrollLeft;$.datepicker._pos[1]-=document.documentElement.scrollTop}var offset={left:$.datepicker._pos[0],top:$.datepicker._pos[1]};$.datepicker._pos=null;inst.rangeStart=null;inst.dpDiv.css({position:"absolute",display:"block",top:"-1000px"});$.datepicker._updateDatepicker(inst);inst.dpDiv.width($.datepicker._getNumberOfMonths(inst)[1]*$(".ui-datepicker",inst.dpDiv[0])[0].offsetWidth);offset=$.datepicker._checkOffset(inst,offset,isFixed);inst.dpDiv.css({position:($.datepicker._inDialog&&$.blockUI?"static":(isFixed?"fixed":"absolute")),display:"none",left:offset.left+"px",top:offset.top+"px"});if(!inst.inline){var showAnim=$.datepicker._get(inst,"showAnim")||"show";var duration=$.datepicker._get(inst,"duration");var postProcess=function(){$.datepicker._datepickerShowing=true;if($.browser.msie&&parseInt($.browser.version)<7){$("iframe.ui-datepicker-cover").css({width:inst.dpDiv.width()+4,height:inst.dpDiv.height()+4})}};if($.effects&&$.effects[showAnim]){inst.dpDiv.show(showAnim,$.datepicker._get(inst,"showOptions"),duration,postProcess)}else{inst.dpDiv[showAnim](duration,postProcess)}if(duration==""){postProcess()}if(inst.input[0].type!="hidden"){inst.input[0].focus()}$.datepicker._curInst=inst}},_updateDatepicker:function(inst){var dims={width:inst.dpDiv.width()+4,height:inst.dpDiv.height()+4};inst.dpDiv.empty().append(this._generateDatepicker(inst)).find("iframe.ui-datepicker-cover").css({width:dims.width,height:dims.height});var numMonths=this._getNumberOfMonths(inst);inst.dpDiv[(numMonths[0]!=1||numMonths[1]!=1?"add":"remove")+"Class"]("ui-datepicker-multi");inst.dpDiv[(this._get(inst,"isRTL")?"add":"remove")+"Class"]("ui-datepicker-rtl");if(inst.input&&inst.input[0].type!="hidden"){$(inst.input[0]).focus()}},_checkOffset:function(inst,offset,isFixed){var pos=inst.input?this._findPos(inst.input[0]):null;var browserWidth=window.innerWidth||document.documentElement.clientWidth;var browserHeight=window.innerHeight||document.documentElement.clientHeight;var scrollX=document.documentElement.scrollLeft||document.body.scrollLeft;var scrollY=document.documentElement.scrollTop||document.body.scrollTop;if(this._get(inst,"isRTL")||(offset.left+inst.dpDiv.width()-scrollX)>browserWidth){offset.left=Math.max((isFixed?0:scrollX),pos[0]+(inst.input?inst.input.width():0)-(isFixed?scrollX:0)-inst.dpDiv.width()-(isFixed&&$.browser.opera?document.documentElement.scrollLeft:0))}else{offset.left-=(isFixed?scrollX:0)}if((offset.top+inst.dpDiv.height()-scrollY)>browserHeight){offset.top=Math.max((isFixed?0:scrollY),pos[1]-(isFixed?scrollY:0)-(this._inDialog?0:inst.dpDiv.height())-(isFixed&&$.browser.opera?document.documentElement.scrollTop:0))}else{offset.top-=(isFixed?scrollY:0)}return offset},_findPos:function(obj){while(obj&&(obj.type=="hidden"||obj.nodeType!=1)){obj=obj.nextSibling}var position=$(obj).offset();return[position.left,position.top]},_hideDatepicker:function(input,duration){var inst=this._curInst;if(!inst){return }var rangeSelect=this._get(inst,"rangeSelect");if(rangeSelect&&this._stayOpen){this._selectDate("#"+inst.id,this._formatDate(inst,inst.currentDay,inst.currentMonth,inst.currentYear))}this._stayOpen=false;if(this._datepickerShowing){duration=(duration!=null?duration:this._get(inst,"duration"));var showAnim=this._get(inst,"showAnim");var postProcess=function(){$.datepicker._tidyDialog(inst)};if(duration!=""&&$.effects&&$.effects[showAnim]){inst.dpDiv.hide(showAnim,$.datepicker._get(inst,"showOptions"),duration,postProcess)}else{inst.dpDiv[(duration==""?"hide":(showAnim=="slideDown"?"slideUp":(showAnim=="fadeIn"?"fadeOut":"hide")))](duration,postProcess)}if(duration==""){this._tidyDialog(inst)}var onClose=this._get(inst,"onClose");if(onClose){onClose.apply((inst.input?inst.input[0]:null),[this._getDate(inst),inst])}this._datepickerShowing=false;this._lastInput=null;inst.settings.prompt=null;if(this._inDialog){this._dialogInput.css({position:"absolute",left:"0",top:"-100px"});if($.blockUI){$.unblockUI();$("body").append(this.dpDiv)}}this._inDialog=false}this._curInst=null},_tidyDialog:function(inst){inst.dpDiv.removeClass(this._dialogClass).unbind(".ui-datepicker");$("."+this._promptClass,inst.dpDiv).remove()},_checkExternalClick:function(event){if(!$.datepicker._curInst){return }var $target=$(event.target);if(($target.parents("#"+$.datepicker._mainDivId).length==0)&&!$target.hasClass($.datepicker.markerClassName)&&!$target.hasClass($.datepicker._triggerClass)&&$.datepicker._datepickerShowing&&!($.datepicker._inDialog&&$.blockUI)){$.datepicker._hideDatepicker(null,"")}},_adjustDate:function(id,offset,period){var target=$(id);var inst=$.data(target[0],PROP_NAME);this._adjustInstDate(inst,offset,period);this._updateDatepicker(inst)},_gotoToday:function(id){var target=$(id);var inst=$.data(target[0],PROP_NAME);if(this._get(inst,"gotoCurrent")&&inst.currentDay){inst.selectedDay=inst.currentDay;inst.drawMonth=inst.selectedMonth=inst.currentMonth;inst.drawYear=inst.selectedYear=inst.currentYear}else{var date=new Date();inst.selectedDay=date.getDate();inst.drawMonth=inst.selectedMonth=date.getMonth();inst.drawYear=inst.selectedYear=date.getFullYear()}this._adjustDate(target);this._notifyChange(inst)},_selectMonthYear:function(id,select,period){var target=$(id);var inst=$.data(target[0],PROP_NAME);inst._selectingMonthYear=false;inst[period=="M"?"drawMonth":"drawYear"]=select.options[select.selectedIndex].value-0;this._adjustDate(target);this._notifyChange(inst)},_clickMonthYear:function(id){var target=$(id);var inst=$.data(target[0],PROP_NAME);if(inst.input&&inst._selectingMonthYear&&!$.browser.msie){inst.input[0].focus()}inst._selectingMonthYear=!inst._selectingMonthYear},_changeFirstDay:function(id,day){var target=$(id);var inst=$.data(target[0],PROP_NAME);inst.settings.firstDay=day;this._updateDatepicker(inst)},_selectDay:function(id,month,year,td){if($(td).hasClass(this._unselectableClass)){return }var target=$(id);var inst=$.data(target[0],PROP_NAME);var rangeSelect=this._get(inst,"rangeSelect");if(rangeSelect){this._stayOpen=!this._stayOpen;if(this._stayOpen){$(".ui-datepicker td").removeClass(this._currentClass);$(td).addClass(this._currentClass)}}inst.selectedDay=inst.currentDay=$("a",td).html();inst.selectedMonth=inst.currentMonth=month;inst.selectedYear=inst.currentYear=year;if(this._stayOpen){inst.endDay=inst.endMonth=inst.endYear=null}else{if(rangeSelect){inst.endDay=inst.currentDay;inst.endMonth=inst.currentMonth;inst.endYear=inst.currentYear}}this._selectDate(id,this._formatDate(inst,inst.currentDay,inst.currentMonth,inst.currentYear));if(this._stayOpen){inst.rangeStart=this._daylightSavingAdjust(new Date(inst.currentYear,inst.currentMonth,inst.currentDay));this._updateDatepicker(inst)}else{if(rangeSelect){inst.selectedDay=inst.currentDay=inst.rangeStart.getDate();inst.selectedMonth=inst.currentMonth=inst.rangeStart.getMonth();inst.selectedYear=inst.currentYear=inst.rangeStart.getFullYear();inst.rangeStart=null;if(inst.inline){this._updateDatepicker(inst)}}}},_clearDate:function(id){var target=$(id);var inst=$.data(target[0],PROP_NAME);if(this._get(inst,"mandatory")){return }this._stayOpen=false;inst.endDay=inst.endMonth=inst.endYear=inst.rangeStart=null;this._selectDate(target,"")},_selectDate:function(id,dateStr){var target=$(id);var inst=$.data(target[0],PROP_NAME);dateStr=(dateStr!=null?dateStr:this._formatDate(inst));if(this._get(inst,"rangeSelect")&&dateStr){dateStr=(inst.rangeStart?this._formatDate(inst,inst.rangeStart):dateStr)+this._get(inst,"rangeSeparator")+dateStr}if(inst.input){inst.input.val(dateStr)}this._updateAlternate(inst);var onSelect=this._get(inst,"onSelect");if(onSelect){onSelect.apply((inst.input?inst.input[0]:null),[dateStr,inst])}else{if(inst.input){inst.input.trigger("change")}}if(inst.inline){this._updateDatepicker(inst)}else{if(!this._stayOpen){this._hideDatepicker(null,this._get(inst,"duration"));this._lastInput=inst.input[0];if(typeof (inst.input[0])!="object"){inst.input[0].focus()}this._lastInput=null}}},_updateAlternate:function(inst){var altField=this._get(inst,"altField");if(altField){var altFormat=this._get(inst,"altFormat");var date=this._getDate(inst);dateStr=(isArray(date)?(!date[0]&&!date[1]?"":this.formatDate(altFormat,date[0],this._getFormatConfig(inst))+this._get(inst,"rangeSeparator")+this.formatDate(altFormat,date[1]||date[0],this._getFormatConfig(inst))):this.formatDate(altFormat,date,this._getFormatConfig(inst)));$(altField).each(function(){$(this).val(dateStr)})}},noWeekends:function(date){var day=date.getDay();return[(day>0&&day<6),""]},iso8601Week:function(date){var checkDate=new Date(date.getFullYear(),date.getMonth(),date.getDate());var firstMon=new Date(checkDate.getFullYear(),1-1,4);var firstDay=firstMon.getDay()||7;firstMon.setDate(firstMon.getDate()+1-firstDay);if(firstDay<4&&checkDate<firstMon){checkDate.setDate(checkDate.getDate()-3);return $.datepicker.iso8601Week(checkDate)}else{if(checkDate>new Date(checkDate.getFullYear(),12-1,28)){firstDay=new Date(checkDate.getFullYear()+1,1-1,4).getDay()||7;if(firstDay>4&&(checkDate.getDay()||7)<firstDay-3){checkDate.setDate(checkDate.getDate()+3);return $.datepicker.iso8601Week(checkDate)}}}return Math.floor(((checkDate-firstMon)/86400000)/7)+1},dateStatus:function(date,inst){return $.datepicker.formatDate($.datepicker._get(inst,"dateStatus"),date,$.datepicker._getFormatConfig(inst))},parseDate:function(format,value,settings){if(format==null||value==null){throw"Invalid arguments"}value=(typeof value=="object"?value.toString():value+"");if(value==""){return null}var shortYearCutoff=(settings?settings.shortYearCutoff:null)||this._defaults.shortYearCutoff;var dayNamesShort=(settings?settings.dayNamesShort:null)||this._defaults.dayNamesShort;var dayNames=(settings?settings.dayNames:null)||this._defaults.dayNames;var monthNamesShort=(settings?settings.monthNamesShort:null)||this._defaults.monthNamesShort;var monthNames=(settings?settings.monthNames:null)||this._defaults.monthNames;var year=-1;var month=-1;var day=-1;var literal=false;var lookAhead=function(match){var matches=(iFormat+1<format.length&&format.charAt(iFormat+1)==match);if(matches){iFormat++}return matches};var getNumber=function(match){lookAhead(match);var origSize=(match=="@"?14:(match=="y"?4:2));var size=origSize;var num=0;while(size>0&&iValue<value.length&&value.charAt(iValue)>="0"&&value.charAt(iValue)<="9"){num=num*10+(value.charAt(iValue++)-0);size--}if(size==origSize){throw"Missing number at position "+iValue}return num};var getName=function(match,shortNames,longNames){var names=(lookAhead(match)?longNames:shortNames);var size=0;for(var j=0;j<names.length;j++){size=Math.max(size,names[j].length)}var name="";var iInit=iValue;while(size>0&&iValue<value.length){name+=value.charAt(iValue++);for(var i=0;i<names.length;i++){if(name==names[i]){return i+1}}size--}throw"Unknown name at position "+iInit};var checkLiteral=function(){if(value.charAt(iValue)!=format.charAt(iFormat)){throw"Unexpected literal at position "+iValue}iValue++};var iValue=0;for(var iFormat=0;iFormat<format.length;iFormat++){if(literal){if(format.charAt(iFormat)=="'"&&!lookAhead("'")){literal=false}else{checkLiteral()}}else{switch(format.charAt(iFormat)){case"d":day=getNumber("d");break;case"D":getName("D",dayNamesShort,dayNames);break;case"m":month=getNumber("m");break;case"M":month=getName("M",monthNamesShort,monthNames);break;case"y":year=getNumber("y");break;case"@":var date=new Date(getNumber("@"));year=date.getFullYear();month=date.getMonth()+1;day=date.getDate();break;case"'":if(lookAhead("'")){checkLiteral()}else{literal=true}break;default:checkLiteral()}}}if(year<100){year+=new Date().getFullYear()-new Date().getFullYear()%100+(year<=shortYearCutoff?0:-100)}var date=this._daylightSavingAdjust(new Date(year,month-1,day));if(date.getFullYear()!=year||date.getMonth()+1!=month||date.getDate()!=day){throw"Invalid date"}return date},ATOM:"yy-mm-dd",COOKIE:"D, dd M yy",ISO_8601:"yy-mm-dd",RFC_822:"D, d M y",RFC_850:"DD, dd-M-y",RFC_1036:"D, d M y",RFC_1123:"D, d M yy",RFC_2822:"D, d M yy",RSS:"D, d M y",TIMESTAMP:"@",W3C:"yy-mm-dd",formatDate:function(format,date,settings){if(!date){return""}var dayNamesShort=(settings?settings.dayNamesShort:null)||this._defaults.dayNamesShort;var dayNames=(settings?settings.dayNames:null)||this._defaults.dayNames;var monthNamesShort=(settings?settings.monthNamesShort:null)||this._defaults.monthNamesShort;var monthNames=(settings?settings.monthNames:null)||this._defaults.monthNames;var lookAhead=function(match){var matches=(iFormat+1<format.length&&format.charAt(iFormat+1)==match);if(matches){iFormat++}return matches};var formatNumber=function(match,value){return(lookAhead(match)&&value<10?"0":"")+value};var formatName=function(match,value,shortNames,longNames){return(lookAhead(match)?longNames[value]:shortNames[value])};var output="";var literal=false;if(date){for(var iFormat=0;iFormat<format.length;iFormat++){if(literal){if(format.charAt(iFormat)=="'"&&!lookAhead("'")){literal=false}else{output+=format.charAt(iFormat)}}else{switch(format.charAt(iFormat)){case"d":output+=formatNumber("d",date.getDate());break;case"D":output+=formatName("D",date.getDay(),dayNamesShort,dayNames);break;case"m":output+=formatNumber("m",date.getMonth()+1);break;case"M":output+=formatName("M",date.getMonth(),monthNamesShort,monthNames);break;case"y":output+=(lookAhead("y")?date.getFullYear():(date.getYear()%100<10?"0":"")+date.getYear()%100);break;case"@":output+=date.getTime();break;case"'":if(lookAhead("'")){output+="'"}else{literal=true}break;default:output+=format.charAt(iFormat)}}}}return output},_possibleChars:function(format){var chars="";var literal=false;for(var iFormat=0;iFormat<format.length;iFormat++){if(literal){if(format.charAt(iFormat)=="'"&&!lookAhead("'")){literal=false}else{chars+=format.charAt(iFormat)}}else{switch(format.charAt(iFormat)){case"d":case"m":case"y":case"@":chars+="0123456789";break;case"D":case"M":return null;case"'":if(lookAhead("'")){chars+="'"}else{literal=true}break;default:chars+=format.charAt(iFormat)}}}return chars},_get:function(inst,name){return inst.settings[name]!==undefined?inst.settings[name]:this._defaults[name]},_setDateFromField:function(inst){var dateFormat=this._get(inst,"dateFormat");var dates=inst.input?inst.input.val().split(this._get(inst,"rangeSeparator")):null;inst.endDay=inst.endMonth=inst.endYear=null;var date=defaultDate=this._getDefaultDate(inst);if(dates.length>0){var settings=this._getFormatConfig(inst);if(dates.length>1){date=this.parseDate(dateFormat,dates[1],settings)||defaultDate;inst.endDay=date.getDate();inst.endMonth=date.getMonth();inst.endYear=date.getFullYear()}try{date=this.parseDate(dateFormat,dates[0],settings)||defaultDate}catch(e){this.log(e);date=defaultDate}}inst.selectedDay=date.getDate();inst.drawMonth=inst.selectedMonth=date.getMonth();inst.drawYear=inst.selectedYear=date.getFullYear();inst.currentDay=(dates[0]?date.getDate():0);inst.currentMonth=(dates[0]?date.getMonth():0);inst.currentYear=(dates[0]?date.getFullYear():0);this._adjustInstDate(inst)},_getDefaultDate:function(inst){var date=this._determineDate(this._get(inst,"defaultDate"),new Date());var minDate=this._getMinMaxDate(inst,"min",true);var maxDate=this._getMinMaxDate(inst,"max");date=(minDate&&date<minDate?minDate:date);date=(maxDate&&date>maxDate?maxDate:date);return date},_determineDate:function(date,defaultDate){var offsetNumeric=function(offset){var date=new Date();date.setDate(date.getDate()+offset);return date};var offsetString=function(offset,getDaysInMonth){var date=new Date();var year=date.getFullYear();var month=date.getMonth();var day=date.getDate();var pattern=/([+-]?[0-9]+)\s*(d|D|w|W|m|M|y|Y)?/g;var matches=pattern.exec(offset);while(matches){switch(matches[2]||"d"){case"d":case"D":day+=(matches[1]-0);break;case"w":case"W":day+=(matches[1]*7);break;case"m":case"M":month+=(matches[1]-0);day=Math.min(day,getDaysInMonth(year,month));break;case"y":case"Y":year+=(matches[1]-0);day=Math.min(day,getDaysInMonth(year,month));break}matches=pattern.exec(offset)}return new Date(year,month,day)};date=(date==null?defaultDate:(typeof date=="string"?offsetString(date,this._getDaysInMonth):(typeof date=="number"?(isNaN(date)?defaultDate:offsetNumeric(date)):date)));date=(date&&date.toString()=="Invalid Date"?defaultDate:date);if(date){date.setHours(0);date.setMinutes(0);date.setSeconds(0);date.setMilliseconds(0)}return this._daylightSavingAdjust(date)},_daylightSavingAdjust:function(date){if(!date){return null}date.setHours(date.getHours()>12?date.getHours()+2:0);return date},_setDate:function(inst,date,endDate){var clear=!(date);date=this._determineDate(date,new Date());inst.selectedDay=inst.currentDay=date.getDate();inst.drawMonth=inst.selectedMonth=inst.currentMonth=date.getMonth();inst.drawYear=inst.selectedYear=inst.currentYear=date.getFullYear();if(this._get(inst,"rangeSelect")){if(endDate){endDate=this._determineDate(endDate,null);inst.endDay=endDate.getDate();inst.endMonth=endDate.getMonth();inst.endYear=endDate.getFullYear()}else{inst.endDay=inst.currentDay;inst.endMonth=inst.currentMonth;inst.endYear=inst.currentYear}}this._adjustInstDate(inst);if(inst.input){inst.input.val(clear?"":this._formatDate(inst)+(!this._get(inst,"rangeSelect")?"":this._get(inst,"rangeSeparator")+this._formatDate(inst,inst.endDay,inst.endMonth,inst.endYear)))}},_getDate:function(inst){var startDate=(!inst.currentYear||(inst.input&&inst.input.val()=="")?null:this._daylightSavingAdjust(new Date(inst.currentYear,inst.currentMonth,inst.currentDay)));if(this._get(inst,"rangeSelect")){return[inst.rangeStart||startDate,(!inst.endYear?inst.rangeStart||startDate:this._daylightSavingAdjust(new Date(inst.endYear,inst.endMonth,inst.endDay)))]}else{return startDate}},_generateDatepicker:function(inst){var today=new Date();today=this._daylightSavingAdjust(new Date(today.getFullYear(),today.getMonth(),today.getDate()));var showStatus=this._get(inst,"showStatus");var isRTL=this._get(inst,"isRTL");var clear=(this._get(inst,"mandatory")?"":'<div class="ui-datepicker-clear"><a onclick="jQuery.datepicker._clearDate(\'#'+inst.id+"');\""+(showStatus?this._addStatus(inst,this._get(inst,"clearStatus")||"&#xa0;"):"")+">"+this._get(inst,"clearText")+"</a></div>");var controls='<div class="ui-datepicker-control">'+(isRTL?"":clear)+'<div class="ui-datepicker-close"><a onclick="jQuery.datepicker._hideDatepicker();"'+(showStatus?this._addStatus(inst,this._get(inst,"closeStatus")||"&#xa0;"):"")+">"+this._get(inst,"closeText")+"</a></div>"+(isRTL?clear:"")+"</div>";var prompt=this._get(inst,"prompt");var closeAtTop=this._get(inst,"closeAtTop");var hideIfNoPrevNext=this._get(inst,"hideIfNoPrevNext");var navigationAsDateFormat=this._get(inst,"navigationAsDateFormat");var numMonths=this._getNumberOfMonths(inst);var stepMonths=this._get(inst,"stepMonths");var isMultiMonth=(numMonths[0]!=1||numMonths[1]!=1);var currentDate=this._daylightSavingAdjust((!inst.currentDay?new Date(9999,9,9):new Date(inst.currentYear,inst.currentMonth,inst.currentDay)));var minDate=this._getMinMaxDate(inst,"min",true);var maxDate=this._getMinMaxDate(inst,"max");var drawMonth=inst.drawMonth;var drawYear=inst.drawYear;if(maxDate){var maxDraw=this._daylightSavingAdjust(new Date(maxDate.getFullYear(),maxDate.getMonth()-numMonths[1]+1,maxDate.getDate()));maxDraw=(minDate&&maxDraw<minDate?minDate:maxDraw);while(this._daylightSavingAdjust(new Date(drawYear,drawMonth,1))>maxDraw){drawMonth--;if(drawMonth<0){drawMonth=11;drawYear--}}}var prevText=this._get(inst,"prevText");prevText=(!navigationAsDateFormat?prevText:this.formatDate(prevText,this._daylightSavingAdjust(new Date(drawYear,drawMonth-stepMonths,1)),this._getFormatConfig(inst)));var prev='<div class="ui-datepicker-prev">'+(this._canAdjustMonth(inst,-1,drawYear,drawMonth)?"<a onclick=\"jQuery.datepicker._adjustDate('#"+inst.id+"', -"+stepMonths+", 'M');\""+(showStatus?this._addStatus(inst,this._get(inst,"prevStatus")||"&#xa0;"):"")+">"+prevText+"</a>":(hideIfNoPrevNext?"":"<label>"+prevText+"</label>"))+"</div>";var nextText=this._get(inst,"nextText");nextText=(!navigationAsDateFormat?nextText:this.formatDate(nextText,this._daylightSavingAdjust(new Date(drawYear,drawMonth+stepMonths,1)),this._getFormatConfig(inst)));var next='<div class="ui-datepicker-next">'+(this._canAdjustMonth(inst,+1,drawYear,drawMonth)?"<a onclick=\"jQuery.datepicker._adjustDate('#"+inst.id+"', +"+stepMonths+", 'M');\""+(showStatus?this._addStatus(inst,this._get(inst,"nextStatus")||"&#xa0;"):"")+">"+nextText+"</a>":(hideIfNoPrevNext?"":"<label>"+nextText+"</label>"))+"</div>";var currentText=this._get(inst,"currentText");currentText=(!navigationAsDateFormat?currentText:this.formatDate(currentText,today,this._getFormatConfig(inst)));var html=(prompt?'<div class="'+this._promptClass+'">'+prompt+"</div>":"")+(closeAtTop&&!inst.inline?controls:"")+'<div class="ui-datepicker-links">'+(isRTL?next:prev)+(this._isInRange(inst,(this._get(inst,"gotoCurrent")&&inst.currentDay?currentDate:today))?'<div class="ui-datepicker-current"><a onclick="jQuery.datepicker._gotoToday(\'#'+inst.id+"');\""+(showStatus?this._addStatus(inst,this._get(inst,"currentStatus")||"&#xa0;"):"")+">"+currentText+"</a></div>":"")+(isRTL?prev:next)+"</div>";var firstDay=this._get(inst,"firstDay");var changeFirstDay=this._get(inst,"changeFirstDay");var dayNames=this._get(inst,"dayNames");var dayNamesShort=this._get(inst,"dayNamesShort");var dayNamesMin=this._get(inst,"dayNamesMin");var monthNames=this._get(inst,"monthNames");var beforeShowDay=this._get(inst,"beforeShowDay");var highlightWeek=this._get(inst,"highlightWeek");var showOtherMonths=this._get(inst,"showOtherMonths");var showWeeks=this._get(inst,"showWeeks");var calculateWeek=this._get(inst,"calculateWeek")||this.iso8601Week;var status=(showStatus?this._get(inst,"dayStatus")||"&#xa0;":"");var dateStatus=this._get(inst,"statusForDate")||this.dateStatus;var endDate=inst.endDay?this._daylightSavingAdjust(new Date(inst.endYear,inst.endMonth,inst.endDay)):currentDate;for(var row=0;row<numMonths[0];row++){for(var col=0;col<numMonths[1];col++){var selectedDate=this._daylightSavingAdjust(new Date(drawYear,drawMonth,inst.selectedDay));html+='<div class="ui-datepicker-one-month'+(col==0?" ui-datepicker-new-row":"")+'">'+this._generateMonthYearHeader(inst,drawMonth,drawYear,minDate,maxDate,selectedDate,row>0||col>0,showStatus,monthNames)+'<table class="ui-datepicker" cellpadding="0" cellspacing="0"><thead><tr class="ui-datepicker-title-row">'+(showWeeks?"<td>"+this._get(inst,"weekHeader")+"</td>":"");for(var dow=0;dow<7;dow++){var day=(dow+firstDay)%7;var dayStatus=(status.indexOf("DD")>-1?status.replace(/DD/,dayNames[day]):status.replace(/D/,dayNamesShort[day]));html+="<td"+((dow+firstDay+6)%7>=5?' class="ui-datepicker-week-end-cell"':"")+">"+(!changeFirstDay?"<span":"<a onclick=\"jQuery.datepicker._changeFirstDay('#"+inst.id+"', "+day+');"')+(showStatus?this._addStatus(inst,dayStatus):"")+' title="'+dayNames[day]+'">'+dayNamesMin[day]+(changeFirstDay?"</a>":"</span>")+"</td>"}html+="</tr></thead><tbody>";var daysInMonth=this._getDaysInMonth(drawYear,drawMonth);if(drawYear==inst.selectedYear&&drawMonth==inst.selectedMonth){inst.selectedDay=Math.min(inst.selectedDay,daysInMonth)}var leadDays=(this._getFirstDayOfMonth(drawYear,drawMonth)-firstDay+7)%7;var numRows=(isMultiMonth?6:Math.ceil((leadDays+daysInMonth)/7));var printDate=this._daylightSavingAdjust(new Date(drawYear,drawMonth,1-leadDays));for(var dRow=0;dRow<numRows;dRow++){html+='<tr class="ui-datepicker-days-row">'+(showWeeks?'<td class="ui-datepicker-week-col">'+calculateWeek(printDate)+"</td>":"");for(var dow=0;dow<7;dow++){var daySettings=(beforeShowDay?beforeShowDay.apply((inst.input?inst.input[0]:null),[printDate]):[true,""]);var otherMonth=(printDate.getMonth()!=drawMonth);var unselectable=otherMonth||!daySettings[0]||(minDate&&printDate<minDate)||(maxDate&&printDate>maxDate);html+='<td class="ui-datepicker-days-cell'+((dow+firstDay+6)%7>=5?" ui-datepicker-week-end-cell":"")+(otherMonth?" ui-datepicker-otherMonth":"")+(printDate.getTime()==selectedDate.getTime()&&drawMonth==inst.selectedMonth?" ui-datepicker-days-cell-over":"")+(unselectable?" "+this._unselectableClass:"")+(otherMonth&&!showOtherMonths?"":" "+daySettings[1]+(printDate.getTime()>=currentDate.getTime()&&printDate.getTime()<=endDate.getTime()?" "+this._currentClass:"")+(printDate.getTime()==today.getTime()?" ui-datepicker-today":""))+'"'+((!otherMonth||showOtherMonths)&&daySettings[2]?' title="'+daySettings[2]+'"':"")+(unselectable?(highlightWeek?" onmouseover=\"jQuery(this).parent().addClass('ui-datepicker-week-over');\" onmouseout=\"jQuery(this).parent().removeClass('ui-datepicker-week-over');\"":""):" onmouseover=\"jQuery(this).addClass('ui-datepicker-days-cell-over')"+(highlightWeek?".parent().addClass('ui-datepicker-week-over')":"")+";"+(!showStatus||(otherMonth&&!showOtherMonths)?"":"jQuery('#ui-datepicker-status-"+inst.id+"').html('"+(dateStatus.apply((inst.input?inst.input[0]:null),[printDate,inst])||"&#xa0;")+"');")+"\" onmouseout=\"jQuery(this).removeClass('ui-datepicker-days-cell-over')"+(highlightWeek?".parent().removeClass('ui-datepicker-week-over')":"")+";"+(!showStatus||(otherMonth&&!showOtherMonths)?"":"jQuery('#ui-datepicker-status-"+inst.id+"').html('&#xa0;');")+'" onclick="jQuery.datepicker._selectDay(\'#'+inst.id+"',"+drawMonth+","+drawYear+', this);"')+">"+(otherMonth?(showOtherMonths?printDate.getDate():"&#xa0;"):(unselectable?printDate.getDate():"<a>"+printDate.getDate()+"</a>"))+"</td>";printDate.setDate(printDate.getDate()+1);printDate=this._daylightSavingAdjust(printDate)}html+="</tr>"}drawMonth++;if(drawMonth>11){drawMonth=0;drawYear++}html+="</tbody></table></div>"}}html+=(showStatus?'<div style="clear: both;"></div><div id="ui-datepicker-status-'+inst.id+'" class="ui-datepicker-status">'+(this._get(inst,"initStatus")||"&#xa0;")+"</div>":"")+(!closeAtTop&&!inst.inline?controls:"")+'<div style="clear: both;"></div>'+($.browser.msie&&parseInt($.browser.version)<7&&!inst.inline?'<iframe src="javascript:false;" class="ui-datepicker-cover"></iframe>':"");return html},_generateMonthYearHeader:function(inst,drawMonth,drawYear,minDate,maxDate,selectedDate,secondary,showStatus,monthNames){minDate=(inst.rangeStart&&minDate&&selectedDate<minDate?selectedDate:minDate);var html='<div class="ui-datepicker-header">';if(secondary||!this._get(inst,"changeMonth")){html+=monthNames[drawMonth]+"&#xa0;"}else{var inMinYear=(minDate&&minDate.getFullYear()==drawYear);var inMaxYear=(maxDate&&maxDate.getFullYear()==drawYear);html+='<select class="ui-datepicker-new-month" onchange="jQuery.datepicker._selectMonthYear(\'#'+inst.id+"', this, 'M');\" onclick=\"jQuery.datepicker._clickMonthYear('#"+inst.id+"');\""+(showStatus?this._addStatus(inst,this._get(inst,"monthStatus")||"&#xa0;"):"")+">";for(var month=0;month<12;month++){if((!inMinYear||month>=minDate.getMonth())&&(!inMaxYear||month<=maxDate.getMonth())){html+='<option value="'+month+'"'+(month==drawMonth?' selected="selected"':"")+">"+monthNames[month]+"</option>"}}html+="</select>"}if(secondary||!this._get(inst,"changeYear")){html+=drawYear}else{var years=this._get(inst,"yearRange").split(":");var year=0;var endYear=0;if(years.length!=2){year=drawYear-10;endYear=drawYear+10}else{if(years[0].charAt(0)=="+"||years[0].charAt(0)=="-"){year=endYear=new Date().getFullYear();year+=parseInt(years[0],10);endYear+=parseInt(years[1],10)}else{year=parseInt(years[0],10);endYear=parseInt(years[1],10)}}year=(minDate?Math.max(year,minDate.getFullYear()):year);endYear=(maxDate?Math.min(endYear,maxDate.getFullYear()):endYear);html+='<select class="ui-datepicker-new-year" onchange="jQuery.datepicker._selectMonthYear(\'#'+inst.id+"', this, 'Y');\" onclick=\"jQuery.datepicker._clickMonthYear('#"+inst.id+"');\""+(showStatus?this._addStatus(inst,this._get(inst,"yearStatus")||"&#xa0;"):"")+">";for(;year<=endYear;year++){html+='<option value="'+year+'"'+(year==drawYear?' selected="selected"':"")+">"+year+"</option>"}html+="</select>"}html+="</div>";return html},_addStatus:function(inst,text){return" onmouseover=\"jQuery('#ui-datepicker-status-"+inst.id+"').html('"+text+"');\" onmouseout=\"jQuery('#ui-datepicker-status-"+inst.id+"').html('&#xa0;');\""},_adjustInstDate:function(inst,offset,period){var year=inst.drawYear+(period=="Y"?offset:0);var month=inst.drawMonth+(period=="M"?offset:0);var day=Math.min(inst.selectedDay,this._getDaysInMonth(year,month))+(period=="D"?offset:0);var date=this._daylightSavingAdjust(new Date(year,month,day));var minDate=this._getMinMaxDate(inst,"min",true);var maxDate=this._getMinMaxDate(inst,"max");date=(minDate&&date<minDate?minDate:date);date=(maxDate&&date>maxDate?maxDate:date);inst.selectedDay=date.getDate();inst.drawMonth=inst.selectedMonth=date.getMonth();inst.drawYear=inst.selectedYear=date.getFullYear();if(period=="M"||period=="Y"){this._notifyChange(inst)}},_notifyChange:function(inst){var onChange=this._get(inst,"onChangeMonthYear");if(onChange){onChange.apply((inst.input?inst.input[0]:null),[new Date(inst.selectedYear,inst.selectedMonth,1),inst])}},_getNumberOfMonths:function(inst){var numMonths=this._get(inst,"numberOfMonths");return(numMonths==null?[1,1]:(typeof numMonths=="number"?[1,numMonths]:numMonths))},_getMinMaxDate:function(inst,minMax,checkRange){var date=this._determineDate(this._get(inst,minMax+"Date"),null);return(!checkRange||!inst.rangeStart?date:(!date||inst.rangeStart>date?inst.rangeStart:date))},_getDaysInMonth:function(year,month){return 32-new Date(year,month,32).getDate()},_getFirstDayOfMonth:function(year,month){return new Date(year,month,1).getDay()},_canAdjustMonth:function(inst,offset,curYear,curMonth){var numMonths=this._getNumberOfMonths(inst);var date=this._daylightSavingAdjust(new Date(curYear,curMonth+(offset<0?offset:numMonths[1]),1));if(offset<0){date.setDate(this._getDaysInMonth(date.getFullYear(),date.getMonth()))}return this._isInRange(inst,date)},_isInRange:function(inst,date){var newMinDate=(!inst.rangeStart?null:this._daylightSavingAdjust(new Date(inst.selectedYear,inst.selectedMonth,inst.selectedDay)));newMinDate=(newMinDate&&inst.rangeStart<newMinDate?inst.rangeStart:newMinDate);var minDate=newMinDate||this._getMinMaxDate(inst,"min");var maxDate=this._getMinMaxDate(inst,"max");return((!minDate||date>=minDate)&&(!maxDate||date<=maxDate))},_getFormatConfig:function(inst){var shortYearCutoff=this._get(inst,"shortYearCutoff");shortYearCutoff=(typeof shortYearCutoff!="string"?shortYearCutoff:new Date().getFullYear()%100+parseInt(shortYearCutoff,10));return{shortYearCutoff:shortYearCutoff,dayNamesShort:this._get(inst,"dayNamesShort"),dayNames:this._get(inst,"dayNames"),monthNamesShort:this._get(inst,"monthNamesShort"),monthNames:this._get(inst,"monthNames")}},_formatDate:function(inst,day,month,year){if(!day){inst.currentDay=inst.selectedDay;inst.currentMonth=inst.selectedMonth;inst.currentYear=inst.selectedYear}var date=(day?(typeof day=="object"?day:this._daylightSavingAdjust(new Date(year,month,day))):this._daylightSavingAdjust(new Date(inst.currentYear,inst.currentMonth,inst.currentDay)));return this.formatDate(this._get(inst,"dateFormat"),date,this._getFormatConfig(inst))}});function extendRemove(target,props){$.extend(target,props);for(var name in props){if(props[name]==null||props[name]==undefined){target[name]=props[name]}}return target}function isArray(a){return(a&&(($.browser.safari&&typeof a=="object"&&a.length)||(a.constructor&&a.constructor.toString().match(/\Array\(\)/))))}$.fn.datepicker=function(options){var otherArgs=Array.prototype.slice.call(arguments,1);if(typeof options=="string"&&(options=="isDisabled"||options=="getDate")){return $.datepicker["_"+options+"Datepicker"].apply($.datepicker,[this[0]].concat(otherArgs))}return this.each(function(){typeof options=="string"?$.datepicker["_"+options+"Datepicker"].apply($.datepicker,[this].concat(otherArgs)):$.datepicker._attachDatepicker(this,options)})};$.datepicker=new Datepicker();$(document).ready(function(){$(document.body).append($.datepicker.dpDiv).mousedown($.datepicker._checkExternalClick)})})(jQuery);(function(C){C.effects=C.effects||{};C.extend(C.effects,{save:function(F,G){for(var E=0;E<G.length;E++){if(G[E]!==null){C.data(F[0],"ec.storage."+G[E],F[0].style[G[E]])}}},restore:function(F,G){for(var E=0;E<G.length;E++){if(G[E]!==null){F.css(G[E],C.data(F[0],"ec.storage."+G[E]))}}},setMode:function(E,F){if(F=="toggle"){F=E.is(":hidden")?"show":"hide"}return F},getBaseline:function(F,G){var H,E;switch(F[0]){case"top":H=0;break;case"middle":H=0.5;break;case"bottom":H=1;break;default:H=F[0]/G.height}switch(F[1]){case"left":E=0;break;case"center":E=0.5;break;case"right":E=1;break;default:E=F[1]/G.width}return{x:E,y:H}},createWrapper:function(F){if(F.parent().attr("id")=="fxWrapper"){return F}var E={width:F.outerWidth({margin:true}),height:F.outerHeight({margin:true}),"float":F.css("float")};F.wrap('<div id="fxWrapper" style="font-size:100%;background:transparent;border:none;margin:0;padding:0"></div>');var I=F.parent();if(F.css("position")=="static"){I.css({position:"relative"});F.css({position:"relative"})}else{var H=F.css("top");if(isNaN(parseInt(H))){H="auto"}var G=F.css("left");if(isNaN(parseInt(G))){G="auto"}I.css({position:F.css("position"),top:H,left:G,zIndex:F.css("z-index")}).show();F.css({position:"relative",top:0,left:0})}I.css(E);return I},removeWrapper:function(E){if(E.parent().attr("id")=="fxWrapper"){return E.parent().replaceWith(E)}return E},setTransition:function(F,G,E,H){H=H||{};C.each(G,function(J,I){unit=F.cssUnit(I);if(unit[0]>0){H[I]=unit[0]*E+unit[1]}});return H},animateClass:function(G,H,J,I){var E=(typeof J=="function"?J:(I?I:null));var F=(typeof J=="object"?J:null);return this.each(function(){var O={};var M=C(this);var N=M.attr("style")||"";if(typeof N=="object"){N=N["cssText"]}if(G.toggle){M.hasClass(G.toggle)?G.remove=G.toggle:G.add=G.toggle}var K=C.extend({},(document.defaultView?document.defaultView.getComputedStyle(this,null):this.currentStyle));if(G.add){M.addClass(G.add)}if(G.remove){M.removeClass(G.remove)}var L=C.extend({},(document.defaultView?document.defaultView.getComputedStyle(this,null):this.currentStyle));if(G.add){M.removeClass(G.add)}if(G.remove){M.addClass(G.remove)}for(var P in L){if(typeof L[P]!="function"&&L[P]&&P.indexOf("Moz")==-1&&P.indexOf("length")==-1&&L[P]!=K[P]&&(P.match(/color/i)||(!P.match(/color/i)&&!isNaN(parseInt(L[P],10))))&&(K.position!="static"||(K.position=="static"&&!P.match(/left|top|bottom|right/)))){O[P]=L[P]}}M.animate(O,H,F,function(){if(typeof C(this).attr("style")=="object"){C(this).attr("style")["cssText"]="";C(this).attr("style")["cssText"]=N}else{C(this).attr("style",N)}if(G.add){C(this).addClass(G.add)}if(G.remove){C(this).removeClass(G.remove)}if(E){E.apply(this,arguments)}})})}});C.fn.extend({_show:C.fn.show,_hide:C.fn.hide,__toggle:C.fn.toggle,_addClass:C.fn.addClass,_removeClass:C.fn.removeClass,_toggleClass:C.fn.toggleClass,effect:function(E,G,F,H){return C.effects[E]?C.effects[E].call(this,{method:E,options:G||{},duration:F,callback:H}):null},show:function(){if(!arguments[0]||(arguments[0].constructor==Number||/(slow|normal|fast)/.test(arguments[0]))){return this._show.apply(this,arguments)}else{var E=arguments[1]||{};E["mode"]="show";return this.effect.apply(this,[arguments[0],E,arguments[2]||E.duration,arguments[3]||E.callback])}},hide:function(){if(!arguments[0]||(arguments[0].constructor==Number||/(slow|normal|fast)/.test(arguments[0]))){return this._hide.apply(this,arguments)}else{var E=arguments[1]||{};E["mode"]="hide";return this.effect.apply(this,[arguments[0],E,arguments[2]||E.duration,arguments[3]||E.callback])}},toggle:function(){if(!arguments[0]||(arguments[0].constructor==Number||/(slow|normal|fast)/.test(arguments[0]))||(arguments[0].constructor==Function)){return this.__toggle.apply(this,arguments)}else{var E=arguments[1]||{};E["mode"]="toggle";return this.effect.apply(this,[arguments[0],E,arguments[2]||E.duration,arguments[3]||E.callback])}},addClass:function(F,E,H,G){return E?C.effects.animateClass.apply(this,[{add:F},E,H,G]):this._addClass(F)},removeClass:function(F,E,H,G){return E?C.effects.animateClass.apply(this,[{remove:F},E,H,G]):this._removeClass(F)},toggleClass:function(F,E,H,G){return E?C.effects.animateClass.apply(this,[{toggle:F},E,H,G]):this._toggleClass(F)},morph:function(E,G,F,I,H){return C.effects.animateClass.apply(this,[{add:G,remove:E},F,I,H])},switchClass:function(){return this.morph.apply(this,arguments)},cssUnit:function(E){var F=this.css(E),G=[];C.each(["em","px","%","pt"],function(H,I){if(F.indexOf(I)>0){G=[parseFloat(F),I]}});return G}});jQuery.each(["backgroundColor","borderBottomColor","borderLeftColor","borderRightColor","borderTopColor","color","outlineColor"],function(F,E){jQuery.fx.step[E]=function(G){if(G.state==0){G.start=D(G.elem,E);G.end=B(G.end)}G.elem.style[E]="rgb("+[Math.max(Math.min(parseInt((G.pos*(G.end[0]-G.start[0]))+G.start[0]),255),0),Math.max(Math.min(parseInt((G.pos*(G.end[1]-G.start[1]))+G.start[1]),255),0),Math.max(Math.min(parseInt((G.pos*(G.end[2]-G.start[2]))+G.start[2]),255),0)].join(",")+")"}});function B(F){var E;if(F&&F.constructor==Array&&F.length==3){return F}if(E=/rgb\(\s*([0-9]{1,3})\s*,\s*([0-9]{1,3})\s*,\s*([0-9]{1,3})\s*\)/.exec(F)){return[parseInt(E[1]),parseInt(E[2]),parseInt(E[3])]}if(E=/rgb\(\s*([0-9]+(?:\.[0-9]+)?)\%\s*,\s*([0-9]+(?:\.[0-9]+)?)\%\s*,\s*([0-9]+(?:\.[0-9]+)?)\%\s*\)/.exec(F)){return[parseFloat(E[1])*2.55,parseFloat(E[2])*2.55,parseFloat(E[3])*2.55]}if(E=/#([a-fA-F0-9]{2})([a-fA-F0-9]{2})([a-fA-F0-9]{2})/.exec(F)){return[parseInt(E[1],16),parseInt(E[2],16),parseInt(E[3],16)]}if(E=/#([a-fA-F0-9])([a-fA-F0-9])([a-fA-F0-9])/.exec(F)){return[parseInt(E[1]+E[1],16),parseInt(E[2]+E[2],16),parseInt(E[3]+E[3],16)]}if(E=/rgba\(0, 0, 0, 0\)/.exec(F)){return A["transparent"]}return A[jQuery.trim(F).toLowerCase()]}function D(G,E){var F;do{F=jQuery.curCSS(G,E);if(F!=""&&F!="transparent"||jQuery.nodeName(G,"body")){break}E="backgroundColor"}while(G=G.parentNode);return B(F)}var A={aqua:[0,255,255],azure:[240,255,255],beige:[245,245,220],black:[0,0,0],blue:[0,0,255],brown:[165,42,42],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgrey:[169,169,169],darkgreen:[0,100,0],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkviolet:[148,0,211],fuchsia:[255,0,255],gold:[255,215,0],green:[0,128,0],indigo:[75,0,130],khaki:[240,230,140],lightblue:[173,216,230],lightcyan:[224,255,255],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightyellow:[255,255,224],lime:[0,255,0],magenta:[255,0,255],maroon:[128,0,0],navy:[0,0,128],olive:[128,128,0],orange:[255,165,0],pink:[255,192,203],purple:[128,0,128],violet:[128,0,128],red:[255,0,0],silver:[192,192,192],white:[255,255,255],yellow:[255,255,0],transparent:[255,255,255]};jQuery.easing["jswing"]=jQuery.easing["swing"];jQuery.extend(jQuery.easing,{def:"easeOutQuad",swing:function(F,G,E,I,H){return jQuery.easing[jQuery.easing.def](F,G,E,I,H)},easeInQuad:function(F,G,E,I,H){return I*(G/=H)*G+E},easeOutQuad:function(F,G,E,I,H){return -I*(G/=H)*(G-2)+E},easeInOutQuad:function(F,G,E,I,H){if((G/=H/2)<1){return I/2*G*G+E}return -I/2*((--G)*(G-2)-1)+E},easeInCubic:function(F,G,E,I,H){return I*(G/=H)*G*G+E},easeOutCubic:function(F,G,E,I,H){return I*((G=G/H-1)*G*G+1)+E},easeInOutCubic:function(F,G,E,I,H){if((G/=H/2)<1){return I/2*G*G*G+E}return I/2*((G-=2)*G*G+2)+E},easeInQuart:function(F,G,E,I,H){return I*(G/=H)*G*G*G+E},easeOutQuart:function(F,G,E,I,H){return -I*((G=G/H-1)*G*G*G-1)+E},easeInOutQuart:function(F,G,E,I,H){if((G/=H/2)<1){return I/2*G*G*G*G+E}return -I/2*((G-=2)*G*G*G-2)+E},easeInQuint:function(F,G,E,I,H){return I*(G/=H)*G*G*G*G+E},easeOutQuint:function(F,G,E,I,H){return I*((G=G/H-1)*G*G*G*G+1)+E},easeInOutQuint:function(F,G,E,I,H){if((G/=H/2)<1){return I/2*G*G*G*G*G+E}return I/2*((G-=2)*G*G*G*G+2)+E},easeInSine:function(F,G,E,I,H){return -I*Math.cos(G/H*(Math.PI/2))+I+E},easeOutSine:function(F,G,E,I,H){return I*Math.sin(G/H*(Math.PI/2))+E},easeInOutSine:function(F,G,E,I,H){return -I/2*(Math.cos(Math.PI*G/H)-1)+E},easeInExpo:function(F,G,E,I,H){return(G==0)?E:I*Math.pow(2,10*(G/H-1))+E},easeOutExpo:function(F,G,E,I,H){return(G==H)?E+I:I*(-Math.pow(2,-10*G/H)+1)+E},easeInOutExpo:function(F,G,E,I,H){if(G==0){return E}if(G==H){return E+I}if((G/=H/2)<1){return I/2*Math.pow(2,10*(G-1))+E}return I/2*(-Math.pow(2,-10*--G)+2)+E},easeInCirc:function(F,G,E,I,H){return -I*(Math.sqrt(1-(G/=H)*G)-1)+E},easeOutCirc:function(F,G,E,I,H){return I*Math.sqrt(1-(G=G/H-1)*G)+E},easeInOutCirc:function(F,G,E,I,H){if((G/=H/2)<1){return -I/2*(Math.sqrt(1-G*G)-1)+E}return I/2*(Math.sqrt(1-(G-=2)*G)+1)+E},easeInElastic:function(F,H,E,L,K){var I=1.70158;var J=0;var G=L;if(H==0){return E}if((H/=K)==1){return E+L}if(!J){J=K*0.3}if(G<Math.abs(L)){G=L;var I=J/4}else{var I=J/(2*Math.PI)*Math.asin(L/G)}return -(G*Math.pow(2,10*(H-=1))*Math.sin((H*K-I)*(2*Math.PI)/J))+E},easeOutElastic:function(F,H,E,L,K){var I=1.70158;var J=0;var G=L;if(H==0){return E}if((H/=K)==1){return E+L}if(!J){J=K*0.3}if(G<Math.abs(L)){G=L;var I=J/4}else{var I=J/(2*Math.PI)*Math.asin(L/G)}return G*Math.pow(2,-10*H)*Math.sin((H*K-I)*(2*Math.PI)/J)+L+E},easeInOutElastic:function(F,H,E,L,K){var I=1.70158;var J=0;var G=L;if(H==0){return E}if((H/=K/2)==2){return E+L}if(!J){J=K*(0.3*1.5)}if(G<Math.abs(L)){G=L;var I=J/4}else{var I=J/(2*Math.PI)*Math.asin(L/G)}if(H<1){return -0.5*(G*Math.pow(2,10*(H-=1))*Math.sin((H*K-I)*(2*Math.PI)/J))+E}return G*Math.pow(2,-10*(H-=1))*Math.sin((H*K-I)*(2*Math.PI)/J)*0.5+L+E},easeInBack:function(F,G,E,J,I,H){if(H==undefined){H=1.70158}return J*(G/=I)*G*((H+1)*G-H)+E},easeOutBack:function(F,G,E,J,I,H){if(H==undefined){H=1.70158}return J*((G=G/I-1)*G*((H+1)*G+H)+1)+E},easeInOutBack:function(F,G,E,J,I,H){if(H==undefined){H=1.70158}if((G/=I/2)<1){return J/2*(G*G*(((H*=(1.525))+1)*G-H))+E}return J/2*((G-=2)*G*(((H*=(1.525))+1)*G+H)+2)+E},easeInBounce:function(F,G,E,I,H){return I-jQuery.easing.easeOutBounce(F,H-G,0,I,H)+E},easeOutBounce:function(F,G,E,I,H){if((G/=H)<(1/2.75)){return I*(7.5625*G*G)+E}else{if(G<(2/2.75)){return I*(7.5625*(G-=(1.5/2.75))*G+0.75)+E}else{if(G<(2.5/2.75)){return I*(7.5625*(G-=(2.25/2.75))*G+0.9375)+E}else{return I*(7.5625*(G-=(2.625/2.75))*G+0.984375)+E}}}},easeInOutBounce:function(F,G,E,I,H){if(G<H/2){return jQuery.easing.easeInBounce(F,G*2,0,I,H)*0.5+E}return jQuery.easing.easeOutBounce(F,G*2-H,0,I,H)*0.5+I*0.5+E}})})(jQuery);(function(A){A.effects.blind=function(B){return this.queue(function(){var D=A(this),C=["position","top","left"];var H=A.effects.setMode(D,B.options.mode||"hide");var G=B.options.direction||"vertical";A.effects.save(D,C);D.show();var J=A.effects.createWrapper(D).css({overflow:"hidden"});var E=(G=="vertical")?"height":"width";var I=(G=="vertical")?J.height():J.width();if(H=="show"){J.css(E,0)}var F={};F[E]=H=="show"?I:0;J.animate(F,B.duration,B.options.easing,function(){if(H=="hide"){D.hide()}A.effects.restore(D,C);A.effects.removeWrapper(D);if(B.callback){B.callback.apply(D[0],arguments)}D.dequeue()})})}})(jQuery);(function(A){A.effects.bounce=function(B){return this.queue(function(){var E=A(this),K=["position","top","left"];var J=A.effects.setMode(E,B.options.mode||"effect");var M=B.options.direction||"up";var C=B.options.distance||20;var D=B.options.times||5;var G=B.duration||250;if(/show|hide/.test(J)){K.push("opacity")}A.effects.save(E,K);E.show();A.effects.createWrapper(E);var F=(M=="up"||M=="down")?"top":"left";var O=(M=="up"||M=="left")?"pos":"neg";var C=B.options.distance||(F=="top"?E.outerHeight({margin:true})/3:E.outerWidth({margin:true})/3);if(J=="show"){E.css("opacity",0).css(F,O=="pos"?-C:C)}if(J=="hide"){C=C/(D*2)}if(J!="hide"){D--}if(J=="show"){var H={opacity:1};H[F]=(O=="pos"?"+=":"-=")+C;E.animate(H,G/2,B.options.easing);C=C/2;D--}for(var I=0;I<D;I++){var N={},L={};N[F]=(O=="pos"?"-=":"+=")+C;L[F]=(O=="pos"?"+=":"-=")+C;E.animate(N,G/2,B.options.easing).animate(L,G/2,B.options.easing);C=(J=="hide")?C*2:C/2}if(J=="hide"){var H={opacity:0};H[F]=(O=="pos"?"-=":"+=")+C;E.animate(H,G/2,B.options.easing,function(){E.hide();A.effects.restore(E,K);A.effects.removeWrapper(E);if(B.callback){B.callback.apply(this,arguments)}})}else{var N={},L={};N[F]=(O=="pos"?"-=":"+=")+C;L[F]=(O=="pos"?"+=":"-=")+C;E.animate(N,G/2,B.options.easing).animate(L,G/2,B.options.easing,function(){A.effects.restore(E,K);A.effects.removeWrapper(E);if(B.callback){B.callback.apply(this,arguments)}})}E.queue("fx",function(){E.dequeue()});E.dequeue()})}})(jQuery);(function(A){A.effects.clip=function(B){return this.queue(function(){var F=A(this),J=["position","top","left","height","width"];var I=A.effects.setMode(F,B.options.mode||"hide");var K=B.options.direction||"vertical";A.effects.save(F,J);F.show();var C=A.effects.createWrapper(F).css({overflow:"hidden"});var E=F[0].tagName=="IMG"?C:F;var G={size:(K=="vertical")?"height":"width",position:(K=="vertical")?"top":"left"};var D=(K=="vertical")?E.height():E.width();if(I=="show"){E.css(G.size,0);E.css(G.position,D/2)}var H={};H[G.size]=I=="show"?D:0;H[G.position]=I=="show"?0:D/2;E.animate(H,{queue:false,duration:B.duration,easing:B.options.easing,complete:function(){if(I=="hide"){F.hide()}A.effects.restore(F,J);A.effects.removeWrapper(F);if(B.callback){B.callback.apply(F[0],arguments)}F.dequeue()}})})}})(jQuery);(function(A){A.effects.drop=function(B){return this.queue(function(){var E=A(this),D=["position","top","left","opacity"];var I=A.effects.setMode(E,B.options.mode||"hide");var H=B.options.direction||"left";A.effects.save(E,D);E.show();A.effects.createWrapper(E);var F=(H=="up"||H=="down")?"top":"left";var C=(H=="up"||H=="left")?"pos":"neg";var J=B.options.distance||(F=="top"?E.outerHeight({margin:true})/2:E.outerWidth({margin:true})/2);if(I=="show"){E.css("opacity",0).css(F,C=="pos"?-J:J)}var G={opacity:I=="show"?1:0};G[F]=(I=="show"?(C=="pos"?"+=":"-="):(C=="pos"?"-=":"+="))+J;E.animate(G,{queue:false,duration:B.duration,easing:B.options.easing,complete:function(){if(I=="hide"){E.hide()}A.effects.restore(E,D);A.effects.removeWrapper(E);if(B.callback){B.callback.apply(this,arguments)}E.dequeue()}})})}})(jQuery);(function(A){A.effects.explode=function(B){return this.queue(function(){var I=B.options.pieces?Math.round(Math.sqrt(B.options.pieces)):3;var E=B.options.pieces?Math.round(Math.sqrt(B.options.pieces)):3;B.options.mode=B.options.mode=="toggle"?(A(this).is(":visible")?"hide":"show"):B.options.mode;var H=A(this).show().css("visibility","hidden");var J=H.offset();J.top-=parseInt(H.css("marginTop"))||0;J.left-=parseInt(H.css("marginLeft"))||0;var G=H.outerWidth(true);var C=H.outerHeight(true);for(var F=0;F<I;F++){for(var D=0;D<E;D++){H.clone().appendTo("body").wrap("<div></div>").css({position:"absolute",visibility:"visible",left:-D*(G/E),top:-F*(C/I)}).parent().addClass("effects-explode").css({position:"absolute",overflow:"hidden",width:G/E,height:C/I,left:J.left+D*(G/E)+(B.options.mode=="show"?(D-Math.floor(E/2))*(G/E):0),top:J.top+F*(C/I)+(B.options.mode=="show"?(F-Math.floor(I/2))*(C/I):0),opacity:B.options.mode=="show"?0:1}).animate({left:J.left+D*(G/E)+(B.options.mode=="show"?0:(D-Math.floor(E/2))*(G/E)),top:J.top+F*(C/I)+(B.options.mode=="show"?0:(F-Math.floor(I/2))*(C/I)),opacity:B.options.mode=="show"?1:0},B.duration||500)}}setTimeout(function(){B.options.mode=="show"?H.css({visibility:"visible"}):H.css({visibility:"visible"}).hide();if(B.callback){B.callback.apply(H[0])}H.dequeue();A(".effects-explode").remove()},B.duration||500)})}})(jQuery);(function(A){A.effects.fold=function(B){return this.queue(function(){var E=A(this),J=["position","top","left"];var G=A.effects.setMode(E,B.options.mode||"hide");var N=B.options.size||15;var M=!(!B.options.horizFirst);A.effects.save(E,J);E.show();var D=A.effects.createWrapper(E).css({overflow:"hidden"});var H=((G=="show")!=M);var F=H?["width","height"]:["height","width"];var C=H?[D.width(),D.height()]:[D.height(),D.width()];var I=/([0-9]+)%/.exec(N);if(I){N=parseInt(I[1])/100*C[G=="hide"?0:1]}if(G=="show"){D.css(M?{height:0,width:N}:{height:N,width:0})}var L={},K={};L[F[0]]=G=="show"?C[0]:N;K[F[1]]=G=="show"?C[1]:0;D.animate(L,B.duration/2,B.options.easing).animate(K,B.duration/2,B.options.easing,function(){if(G=="hide"){E.hide()}A.effects.restore(E,J);A.effects.removeWrapper(E);if(B.callback){B.callback.apply(E[0],arguments)}E.dequeue()})})}})(jQuery);(function(A){A.effects.highlight=function(B){return this.queue(function(){var E=A(this),D=["backgroundImage","backgroundColor","opacity"];var H=A.effects.setMode(E,B.options.mode||"show");var C=B.options.color||"#ffff99";var G=E.css("backgroundColor");A.effects.save(E,D);E.show();E.css({backgroundImage:"none",backgroundColor:C});var F={backgroundColor:G};if(H=="hide"){F["opacity"]=0}E.animate(F,{queue:false,duration:B.duration,easing:B.options.easing,complete:function(){if(H=="hide"){E.hide()}A.effects.restore(E,D);if(H=="show"&&jQuery.browser.msie){this.style.removeAttribute("filter")}if(B.callback){B.callback.apply(this,arguments)}E.dequeue()}})})}})(jQuery);(function(A){A.effects.pulsate=function(B){return this.queue(function(){var D=A(this);var F=A.effects.setMode(D,B.options.mode||"show");var E=B.options.times||5;if(F=="hide"){E--}if(D.is(":hidden")){D.css("opacity",0);D.show();D.animate({opacity:1},B.duration/2,B.options.easing);E=E-2}for(var C=0;C<E;C++){D.animate({opacity:0},B.duration/2,B.options.easing).animate({opacity:1},B.duration/2,B.options.easing)}if(F=="hide"){D.animate({opacity:0},B.duration/2,B.options.easing,function(){D.hide();if(B.callback){B.callback.apply(this,arguments)}})}else{D.animate({opacity:0},B.duration/2,B.options.easing).animate({opacity:1},B.duration/2,B.options.easing,function(){if(B.callback){B.callback.apply(this,arguments)}})}D.queue("fx",function(){D.dequeue()});D.dequeue()})}})(jQuery);(function(A){A.effects.puff=function(B){return this.queue(function(){var F=A(this);var C=A.extend(true,{},B.options);var H=A.effects.setMode(F,B.options.mode||"hide");var G=parseInt(B.options.percent)||150;C.fade=true;var E={height:F.height(),width:F.width()};var D=G/100;F.from=(H=="hide")?E:{height:E.height*D,width:E.width*D};C.from=F.from;C.percent=(H=="hide")?G:100;C.mode=H;F.effect("scale",C,B.duration,B.callback);F.dequeue()})};A.effects.scale=function(B){return this.queue(function(){var G=A(this);var D=A.extend(true,{},B.options);var J=A.effects.setMode(G,B.options.mode||"effect");var H=parseInt(B.options.percent)||(parseInt(B.options.percent)==0?0:(J=="hide"?0:100));var I=B.options.direction||"both";var C=B.options.origin;if(J!="effect"){D.origin=C||["middle","center"];D.restore=true}var F={height:G.height(),width:G.width()};G.from=B.options.from||(J=="show"?{height:0,width:0}:F);var E={y:I!="horizontal"?(H/100):1,x:I!="vertical"?(H/100):1};G.to={height:F.height*E.y,width:F.width*E.x};if(B.options.fade){if(J=="show"){G.from.opacity=0;G.to.opacity=1}if(J=="hide"){G.from.opacity=1;G.to.opacity=0}}D.from=G.from;D.to=G.to;D.mode=J;G.effect("size",D,B.duration,B.callback);G.dequeue()})};A.effects.size=function(B){return this.queue(function(){var C=A(this),N=["position","top","left","width","height","overflow","opacity"];var M=["position","top","left","overflow","opacity"];var J=["width","height","overflow"];var P=["fontSize"];var K=["borderTopWidth","borderBottomWidth","paddingTop","paddingBottom"];var F=["borderLeftWidth","borderRightWidth","paddingLeft","paddingRight"];var G=A.effects.setMode(C,B.options.mode||"effect");var I=B.options.restore||false;var E=B.options.scale||"both";var O=B.options.origin;var D={height:C.height(),width:C.width()};C.from=B.options.from||D;C.to=B.options.to||D;if(O){var H=A.effects.getBaseline(O,D);C.from.top=(D.height-C.from.height)*H.y;C.from.left=(D.width-C.from.width)*H.x;C.to.top=(D.height-C.to.height)*H.y;C.to.left=(D.width-C.to.width)*H.x}var L={from:{y:C.from.height/D.height,x:C.from.width/D.width},to:{y:C.to.height/D.height,x:C.to.width/D.width}};if(E=="box"||E=="both"){if(L.from.y!=L.to.y){N=N.concat(K);C.from=A.effects.setTransition(C,K,L.from.y,C.from);C.to=A.effects.setTransition(C,K,L.to.y,C.to)}if(L.from.x!=L.to.x){N=N.concat(F);C.from=A.effects.setTransition(C,F,L.from.x,C.from);C.to=A.effects.setTransition(C,F,L.to.x,C.to)}}if(E=="content"||E=="both"){if(L.from.y!=L.to.y){N=N.concat(P);C.from=A.effects.setTransition(C,P,L.from.y,C.from);C.to=A.effects.setTransition(C,P,L.to.y,C.to)}}A.effects.save(C,I?N:M);C.show();A.effects.createWrapper(C);C.css("overflow","hidden").css(C.from);if(E=="content"||E=="both"){K=K.concat(["marginTop","marginBottom"]).concat(P);F=F.concat(["marginLeft","marginRight"]);J=N.concat(K).concat(F);C.find("*[width]").each(function(){child=A(this);if(I){A.effects.save(child,J)}var Q={height:child.height(),width:child.width()};child.from={height:Q.height*L.from.y,width:Q.width*L.from.x};child.to={height:Q.height*L.to.y,width:Q.width*L.to.x};if(L.from.y!=L.to.y){child.from=A.effects.setTransition(child,K,L.from.y,child.from);child.to=A.effects.setTransition(child,K,L.to.y,child.to)}if(L.from.x!=L.to.x){child.from=A.effects.setTransition(child,F,L.from.x,child.from);child.to=A.effects.setTransition(child,F,L.to.x,child.to)}child.css(child.from);child.animate(child.to,B.duration,B.options.easing,function(){if(I){A.effects.restore(child,J)}})})}C.animate(C.to,{queue:false,duration:B.duration,easing:B.options.easing,complete:function(){if(G=="hide"){C.hide()}A.effects.restore(C,I?N:M);A.effects.removeWrapper(C);if(B.callback){B.callback.apply(this,arguments)}C.dequeue()}})})}})(jQuery);(function(A){A.effects.shake=function(B){return this.queue(function(){var E=A(this),K=["position","top","left"];var J=A.effects.setMode(E,B.options.mode||"effect");var M=B.options.direction||"left";var C=B.options.distance||20;var D=B.options.times||3;var G=B.duration||B.options.duration||140;A.effects.save(E,K);E.show();A.effects.createWrapper(E);var F=(M=="up"||M=="down")?"top":"left";var O=(M=="up"||M=="left")?"pos":"neg";var H={},N={},L={};H[F]=(O=="pos"?"-=":"+=")+C;N[F]=(O=="pos"?"+=":"-=")+C*2;L[F]=(O=="pos"?"-=":"+=")+C*2;E.animate(H,G,B.options.easing);for(var I=1;I<D;I++){E.animate(N,G,B.options.easing).animate(L,G,B.options.easing)}E.animate(N,G,B.options.easing).animate(H,G/2,B.options.easing,function(){A.effects.restore(E,K);A.effects.removeWrapper(E);if(B.callback){B.callback.apply(this,arguments)}});E.queue("fx",function(){E.dequeue()});E.dequeue()})}})(jQuery);(function(A){A.effects.slide=function(B){return this.queue(function(){var E=A(this),D=["position","top","left"];var I=A.effects.setMode(E,B.options.mode||"show");var H=B.options.direction||"left";A.effects.save(E,D);E.show();A.effects.createWrapper(E).css({overflow:"hidden"});var F=(H=="up"||H=="down")?"top":"left";var C=(H=="up"||H=="left")?"pos":"neg";var J=B.options.distance||(F=="top"?E.outerHeight({margin:true}):E.outerWidth({margin:true}));if(I=="show"){E.css(F,C=="pos"?-J:J)}var G={};G[F]=(I=="show"?(C=="pos"?"+=":"-="):(C=="pos"?"-=":"+="))+J;E.animate(G,{queue:false,duration:B.duration,easing:B.options.easing,complete:function(){if(I=="hide"){E.hide()}A.effects.restore(E,D);A.effects.removeWrapper(E);if(B.callback){B.callback.apply(this,arguments)}E.dequeue()}})})}})(jQuery);(function(A){A.effects.transfer=function(B){return this.queue(function(){var E=A(this);var G=A.effects.setMode(E,B.options.mode||"effect");var F=A(B.options.to);var C=E.offset();var D=A('<div class="ui-effects-transfer"></div>').appendTo(document.body);if(B.options.className){D.addClass(B.options.className)}D.addClass(B.options.className);D.css({top:C.top,left:C.left,height:E.outerHeight()-parseInt(D.css("borderTopWidth"))-parseInt(D.css("borderBottomWidth")),width:E.outerWidth()-parseInt(D.css("borderLeftWidth"))-parseInt(D.css("borderRightWidth")),position:"absolute"});C=F.offset();animation={top:C.top,left:C.left,height:F.outerHeight()-parseInt(D.css("borderTopWidth"))-parseInt(D.css("borderBottomWidth")),width:F.outerWidth()-parseInt(D.css("borderLeftWidth"))-parseInt(D.css("borderRightWidth"))};D.animate(animation,B.duration,B.options.easing,function(){D.remove();if(B.callback){B.callback.apply(E[0],arguments)}E.dequeue()})})}})(jQuery);