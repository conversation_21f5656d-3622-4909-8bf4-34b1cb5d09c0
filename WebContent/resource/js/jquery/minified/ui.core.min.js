;(function($){$.ui={plugin:{add:function(a,b,c){var d=$.ui[a].prototype;for(var i in c){d.plugins[i]=d.plugins[i]||[];d.plugins[i].push([b,c[i]])}},call:function(a,b,c){var d=a.plugins[b];if(!d){return}for(var i=0;i<d.length;i++){if(a.options[d[i][0]]){d[i][1].apply(a.element,c)}}}},cssCache:{},css:function(a){if($.ui.cssCache[a]){return $.ui.cssCache[a]}var b=$('<div class="ui-gen">').addClass(a).css({position:'absolute',top:'-5000px',left:'-5000px',display:'block'}).appendTo('body');$.ui.cssCache[a]=!!((!(/auto|default/).test(b.css('cursor'))||(/^[1-9]/).test(b.css('height'))||(/^[1-9]/).test(b.css('width'))||!(/none/).test(b.css('backgroundImage'))||!(/transparent|rgba\(0, 0, 0, 0\)/).test(b.css('backgroundColor'))));try{$('body').get(0).removeChild(b.get(0))}catch(e){}return $.ui.cssCache[a]},disableSelection:function(a){$(a).attr('unselectable','on').css('MozUserSelect','none')},enableSelection:function(a){$(a).attr('unselectable','off').css('MozUserSelect','')},hasScroll:function(e,a){var b=/top/.test(a||"top")?'scrollTop':'scrollLeft',has=false;if(e[b]>0)return true;e[b]=1;has=e[b]>0?true:false;e[b]=0;return has}};var j=$.fn.remove;$.fn.remove=function(){$("*",this).add(this).triggerHandler("remove");return j.apply(this,arguments)};function getter(a,b,c){var d=$[a][b].getter||[];d=(typeof d=="string"?d.split(/,?\s+/):d);return($.inArray(c,d)!=-1)}$.widget=function(g,h){var i=g.split(".")[0];g=g.split(".")[1];$.fn[g]=function(b){var c=(typeof b=='string'),args=Array.prototype.slice.call(arguments,1);if(c&&getter(i,g,b)){var d=$.data(this[0],g);return(d?d[b].apply(d,args):undefined)}return this.each(function(){var a=$.data(this,g);if(c&&a&&$.isFunction(a[b])){a[b].apply(a,args)}else if(!c){$.data(this,g,new $[i][g](this,b))}})};$[i][g]=function(c,d){var f=this;this.widgetName=g;this.widgetBaseClass=i+'-'+g;this.options=$.extend({},$.widget.defaults,$[i][g].defaults,d);this.element=$(c).bind('setData.'+g,function(e,a,b){return f.setData(a,b)}).bind('getData.'+g,function(e,a){return f.getData(a)}).bind('remove',function(){return f.destroy()});this.init()};$[i][g].prototype=$.extend({},$.widget.prototype,h)};$.widget.prototype={init:function(){},destroy:function(){this.element.removeData(this.widgetName)},getData:function(a){return this.options[a]},setData:function(a,b){this.options[a]=b;if(a=='disabled'){this.element[b?'addClass':'removeClass'](this.widgetBaseClass+'-disabled')}},enable:function(){this.setData('disabled',false)},disable:function(){this.setData('disabled',true)}};$.widget.defaults={disabled:false};$.ui.mouse={mouseInit:function(){var a=this;this.element.bind('mousedown.'+this.widgetName,function(e){return a.mouseDown(e)});if($.browser.msie){this._mouseUnselectable=this.element.attr('unselectable');this.element.attr('unselectable','on')}this.started=false},mouseDestroy:function(){this.element.unbind('.'+this.widgetName);($.browser.msie&&this.element.attr('unselectable',this._mouseUnselectable))},mouseDown:function(e){(this._mouseStarted&&this.mouseUp(e));this._mouseDownEvent=e;var a=this,btnIsLeft=(e.which==1),elIsCancel=(typeof this.options.cancel=="string"?$(e.target).parents().add(e.target).filter(this.options.cancel).length:false);if(!btnIsLeft||elIsCancel||!this.mouseCapture(e)){return true}this._mouseDelayMet=!this.options.delay;if(!this._mouseDelayMet){this._mouseDelayTimer=setTimeout(function(){a._mouseDelayMet=true},this.options.delay)}if(this.mouseDistanceMet(e)&&this.mouseDelayMet(e)){this._mouseStarted=(this.mouseStart(e)!==false);if(!this._mouseStarted){e.preventDefault();return true}}this._mouseMoveDelegate=function(e){return a.mouseMove(e)};this._mouseUpDelegate=function(e){return a.mouseUp(e)};$(document).bind('mousemove.'+this.widgetName,this._mouseMoveDelegate).bind('mouseup.'+this.widgetName,this._mouseUpDelegate);return false},mouseMove:function(e){if($.browser.msie&&!e.button){return this.mouseUp(e)}if(this._mouseStarted){this.mouseDrag(e);return false}if(this.mouseDistanceMet(e)&&this.mouseDelayMet(e)){this._mouseStarted=(this.mouseStart(this._mouseDownEvent,e)!==false);(this._mouseStarted?this.mouseDrag(e):this.mouseUp(e))}return!this._mouseStarted},mouseUp:function(e){$(document).unbind('mousemove.'+this.widgetName,this._mouseMoveDelegate).unbind('mouseup.'+this.widgetName,this._mouseUpDelegate);if(this._mouseStarted){this._mouseStarted=false;this.mouseStop(e)}return false},mouseDistanceMet:function(e){return(Math.max(Math.abs(this._mouseDownEvent.pageX-e.pageX),Math.abs(this._mouseDownEvent.pageY-e.pageY))>=this.options.distance)},mouseDelayMet:function(e){return this._mouseDelayMet},mouseStart:function(e){},mouseDrag:function(e){},mouseStop:function(e){},mouseCapture:function(e){return true}};$.ui.mouse.defaults={cancel:null,distance:1,delay:0}})(jQuery);