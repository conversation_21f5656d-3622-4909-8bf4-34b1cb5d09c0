// ajaxClass -- XMLHttp Object Pool and XMLHttp chunnel Pool
// == written by Apple <<EMAIL>> ===
// == Copyright (C) 2006 Apple Blog - http://www.applelife.cn ==
/**
 * <AUTHOR>
 * @description XMLHttp Object Pool and XMLHttp chunnel Pool
 */

var Request = new function(){
this.pool = new Array();
this.getXMLHttp = function (chunnel)
{
	
 if(chunnel != null)
 {
  for (var a = 0; a < this.pool.length; a++)
  {
   if(this.pool[a]["chunnel"] == chunnel)
   {
	if(this.pool[a]["obj"].readyState == 0 || this.pool[a]["obj"].readyState == 4)
    {
     return this.pool[a]["obj"];
    }
	else 
	{
      return "busy";
	}
   }
  }
  
  this.pool[this.pool.length] = new Array();
  this.pool[this.pool.length - 1]["obj"] = this.createXMLHttp();
  this.pool[this.pool.length - 1]["chunnel"] = chunnel;
  return this.pool[this.pool.length - 1]["obj"];
 
 }
	
 for (var i = 0; i < this.pool.length; i++)
 {
  if (this.pool[i]["obj"].readyState == 0 || this.pool[i]["obj"].readyState == 4)
  {
   return this.pool[i]["obj"];
  }
 }
 
 this.pool[this.pool.length] = new Array();
 this.pool[this.pool.length - 1]["obj"] = this.createXMLHttp();
 this.pool[this.pool.length - 1]["chunnel"] = "";
 return this.pool[this.pool.length - 1]["obj"];

}
this.createXMLHttp = function ()
{
 
 if(window.XMLHttpRequest)
 {
  var xmlObj = new XMLHttpRequest();
 } 
 else 
 {
  var MSXML = ['Microsoft.XMLHTTP', 'MSXML2.XMLHTTP.5.0', 'MSXML2.XMLHTTP.4.0', 'MSXML2.XMLHTTP.3.0', 'MSXML2.XMLHTTP'];
  for(var n = 0; n < MSXML.length; n++)
  {
   try
   {
    var xmlObj = new ActiveXObject(MSXML[n]);        
    break;
   }
   catch(e)
   {
   }
  }
 } 
 
 return xmlObj;

}


function showProgressBar(show){
	if(show){
		document.getElementById("mask").style.display="block";
		document.getElementById("loadingbarcontainer").style.display="block";
	}else{
		document.getElementById("mask").style.display="none";
		document.getElementById("loadingbarcontainer").style.display="none";
		
	}
}
// parameter url & chunnel is deprecated! by yhf 2009-2-17
this.reSend = this.callService = function(service_Name,method_Name,type_Name,callback,showLoading){
	var url=window.location.pathname;
	var chunnel=(new Date()).getTime();
	showLoading=typeof(showLoading)=="boolean"?showLoading:true;
	if(showLoading){
		util.createLoading();
		util.showLoading(true);
	}
	if(type_Name!=null)
		type_Name = encodeURI(encodeURI(type_Name));
 var objXMLHttp = this.getXMLHttp(chunnel);
 
 var data="Is_Submit=1&forward_Type=ajax&service_Name="+service_Name+"&method_Name="+method_Name+"&type_Name="+type_Name;
 
 if(typeof(objXMLHttp) != "object")
 {
   return ;
 }
 
 url += (url.indexOf("?") >= 0) ? "&nowtime=" + new Date().getTime() : "?nowtime=" + new Date().getTime();

 if(data == "")
 {
  objXMLHttp.open('GET' , url, true);
  objXMLHttp.send('');
 }
 else 
 { 
  objXMLHttp.open('POST' , url, true);
  objXMLHttp.setRequestHeader("Content-Length",data.length); 
  objXMLHttp.setRequestHeader("Content-Type","application/x-www-form-urlencoded");
  objXMLHttp.send(data);
 }
 
  objXMLHttp.onreadystatechange = function ()
  {
   if (objXMLHttp.readyState == 4)
   {
    if(objXMLHttp.status == 200 || objXMLHttp.status == 304)
    {
     if(typeof(showLoading)=="undefined" || showLoading){
    	 util.showLoading(false);
     }
     if(typeof(callback) == "function" )
     {
    	 callback(objXMLHttp) ;
     }
    }
    else
    {
     alert("Error loading page\n"+ objXMLHttp.status +":"+ objXMLHttp.statusText);
     util.showLoading(false);
    }
   }
  }

}

this.reSend = this.callService = function(service_Name,method_Name,type_Name,callback,showLoading,asyn){
	var url=window.location.pathname;
	var chunnel=(new Date()).getTime();
	showLoading=typeof(showLoading)=="boolean"?showLoading:true;
	if(showLoading){
		util.createLoading();
		util.showLoading(true);
	}
	if(type_Name!=null)
		type_Name = encodeURI(encodeURI(type_Name));
 var objXMLHttp = this.getXMLHttp(chunnel);
 
 var data="Is_Submit=1&forward_Type=ajax&service_Name="+service_Name+"&method_Name="+method_Name+"&type_Name="+type_Name;
 
 if(typeof(objXMLHttp) != "object")
 {
   return ;
 }
 
 url += (url.indexOf("?") >= 0) ? "&nowtime=" + new Date().getTime() : "?nowtime=" + new Date().getTime();

 if(data == "")
 {
  objXMLHttp.open('GET' , url, asyn);
  objXMLHttp.send('');
 }
 else 
 { 
  objXMLHttp.open('POST' , url, asyn);
  objXMLHttp.setRequestHeader("Content-Length",data.length); 
  objXMLHttp.setRequestHeader("Content-Type","application/x-www-form-urlencoded");
  objXMLHttp.send(data);
 }
 
  objXMLHttp.onreadystatechange = function ()
  {
   if (objXMLHttp.readyState == 4)
   {
    if(objXMLHttp.status == 200 || objXMLHttp.status == 304)
    {
     if(typeof(showLoading)=="undefined" || showLoading){
    	 util.showLoading(false);
     }
     if(typeof(callback) == "function" )
     {
    	 callback(objXMLHttp) ;
     }
    }
    else
    {
     alert("Error loading page\n"+ objXMLHttp.status +":"+ objXMLHttp.statusText);
     util.showLoading(false);
    }
   }
  }

}

}

