<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title></title>
<style type="text/css">
html,body{padding:0px;margin:0px;background-color: #fff;height:100%;}
</style>
<script type="text/javascript">
var noway=function (e){
	var evt=window.event?window.event:e;
	if(evt.keyCode==8){//禁用后退键
		return false;
	}
}
window.onload=function (){
	document.body.attachEvent?document.body.attachEvent("onkeydown",noway):document.body.addEventListener("keydown",noway,false);
}
</script>
</head>
<body>
<!-- 这个页面是所有弹出框的背景遮罩页面,他应该与dialog.js在同一个目录下 -->
</body>
</html>