var i18n=new Object();
i18n.common=new Object();
i18n.ajax=new Object();
i18n.dialog=new Object();
i18n.validate=new Object();
i18n.tree=new Object();
i18n.layout=new Object();

i18n.common.loadingText="正在加载...";
i18n.common.toomuchtabs="最多只能打开六个标签";

i18n.ajax.loadingText=i18n.common.loadingText;

i18n.dialog.defaultTitle="提示";
i18n.dialog.closeText="关闭";
i18n.dialog.okText="确认";
i18n.dialog.alertTitle="警告";
i18n.dialog.errorTitle="错误";
i18n.dialog.okTitle="成功";
i18n.dialog.infoTitle="消息";
i18n.dialog.confirmTitle="确认";
i18n.dialog.loadingTitle=i18n.common.loadingText;

i18n.validate.errorText="以下错误导致提交错误:\t\t\t\t";
i18n.validate.area=[];

i18n.tree.newNodeText=" 新节点 ";
i18n.tree.loadingText=i18n.common.loadingText;

i18n.layout.openText="打开";
i18n.layout.closeText="关闭";
i18n.layout.resizerText="调整大小";
i18n.layout.sliderText="停留";
i18n.fav=new Object();
i18n.fav.addsuccess="添加成功!";
i18n.fav.added="该项已经添加";
i18n.fav.adderror="添加收藏夹发生错误";
i18n.fav.removeerror="删除收藏夹失败";


