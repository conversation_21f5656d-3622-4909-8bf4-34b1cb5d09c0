@CHARSET "UTF-8";

/* INNER TAB  */
@media Screen , Projection {
	.ui-tabs-hide {
		DISPLAY: none
	}
}

@media Print {
	.ui-tabs-nav {
		DISPLAY: none
	}
}

.ui-tabs-nav {
	FONT-SIZE: 12px;
	/*FONT-FAMILY: "微软雅黑";*/
}

.ui-tabs-panel {
	FONT-SIZE: 12px;
}

.ui-tabs-nav {
	PADDING-RIGHT: 0px;
	PADDING-LEFT: 4px;
	PADDING-BOTTOM: 0px;
	MARGIN: 0px;
	PADDING-TOP: 0px;
	LIST-STYLE-TYPE: none;
}

.ui-tabs-nav:unknown {
	CLEAR: both;
	DISPLAY: block;
	content: " "
}

.ui-tabs-nav LI {
	MIN-WIDTH: 84px;
	FLOAT: left;
	MARGIN: 0px 3px 0px 1px
}

.ui-tabs-nav A {
	PADDING-RIGHT: 10px;
	DISPLAY: block;
	PADDING-LEFT: 10px;
	BACKGROUND: url(../images/innertab/innertab.gif) no-repeat;
	PADDING-BOTTOM: 0px;
	PADDING-TOP: 0px
}

.ui-tabs-nav A SPAN {
	PADDING-RIGHT: 10px;
	DISPLAY: block;
	PADDING-LEFT: 10px;
	BACKGROUND: url(../images/innertab/innertab.gif) no-repeat;
	PADDING-BOTTOM: 0px;
	PADDING-TOP: 0px
}

.ui-tabs-nav A {
	PADDING-LEFT: 0px;
	MARGIN: 1px 0px 0px;
	COLOR: #27537a;
	LINE-HEIGHT: 1.5;
	WHITE-SPACE: nowrap;
	TEXT-ALIGN: center;
	TEXT-DECORATION: none;
	outline: 0
}

.ui-tabs-nav .ui-tabs-selected A {
	MARGIN-TOP: 0px;
	Z-INDEX: 2;
	COLOR: #000;
	POSITION: relative;
	TOP: 1px
}

.ui-tabs-nav A SPAN {
	PADDING-RIGHT: 0px;
	MIN-WIDTH: 64px;
	MIN-HEIGHT: 18px;
	WIDTH: 64px;
	PADDING-TOP: 6px;
	HEIGHT: 18px
}

*>.ui-tabs-nav A SPAN {
	WIDTH: auto;
	HEIGHT: auto
}

.ui-tabs-nav .ui-tabs-selected A SPAN {
	PADDING-BOTTOM: 1px
}

.ui-tabs-nav .ui-tabs-selected A {
	BACKGROUND-POSITION: 100% -150px
}

.ui-tabs-nav A:hover {
	BACKGROUND-POSITION: 100% -150px
}

.ui-tabs-nav A:focus {
	BACKGROUND-POSITION: 100% -150px
}

.ui-tabs-nav A:active {
	BACKGROUND-POSITION: 100% -150px
}

.ui-tabs-nav A {
	BACKGROUND-POSITION: 100% -100px
}

.ui-tabs-nav .ui-tabs-disabled A:hover {
	BACKGROUND-POSITION: 100% -100px
}

.ui-tabs-nav .ui-tabs-disabled A:focus {
	BACKGROUND-POSITION: 100% -100px
}

.ui-tabs-nav .ui-tabs-disabled A:active {
	BACKGROUND-POSITION: 100% -100px
}

.ui-tabs-nav .ui-tabs-selected A SPAN {
	BACKGROUND-POSITION: 0px -50px
}

.ui-tabs-nav A:hover SPAN {
	BACKGROUND-POSITION: 0px -50px
}

.ui-tabs-nav A:focus SPAN {
	BACKGROUND-POSITION: 0px -50px
}

.ui-tabs-nav A:active SPAN {
	BACKGROUND-POSITION: 0px -50px
}

.ui-tabs-nav A SPAN {
	BACKGROUND-POSITION: 0px 0px
}

.ui-tabs-nav .ui-tabs-disabled A:hover SPAN {
	BACKGROUND-POSITION: 0px 0px
}

.ui-tabs-nav .ui-tabs-disabled A:focus SPAN {
	BACKGROUND-POSITION: 0px 0px
}

.ui-tabs-nav .ui-tabs-disabled A:active SPAN {
	BACKGROUND-POSITION: 0px 0px
}

.ui-tabs-nav .ui-tabs-selected A:link {
	CURSOR: text
}

.ui-tabs-nav .ui-tabs-selected A:visited {
	CURSOR: text
}

.ui-tabs-nav .ui-tabs-disabled A:link {
	CURSOR: text
}

.ui-tabs-nav .ui-tabs-disabled A:visited {
	CURSOR: text
}

.ui-tabs-nav A:hover {
	CURSOR: pointer
}

.ui-tabs-nav A:focus {
	CURSOR: pointer
}

.ui-tabs-nav A:active {
	CURSOR: pointer
}

.ui-tabs-nav .ui-tabs-unselect A:hover {
	CURSOR: pointer
}

.ui-tabs-nav .ui-tabs-unselect A:focus {
	CURSOR: pointer
}

.ui-tabs-nav .ui-tabs-unselect A:active {
	CURSOR: pointer
}

.ui-tabs-disabled {
	FILTER: alpha(opacity = 40);
	opacity: .4
}

.ui-tabs-panel {
	PADDING: 2px;
	BORDER: #97a5b0 1px solid;
	BACKGROUND: #fff;
}

.ui-tabs-loading EM {
	PADDING-RIGHT: 0px;
	PADDING-LEFT: 20px;
	/*BACKGROUND: url(images/loading.gif) no-repeat 0px 50%;*/
	PADDING-BOTTOM: 0px;
	PADDING-TOP: 0px
}

* HTML .ui-tabs-nav {
	DISPLAY: inline-block
}

* :first-child+HTML .ui-tabs-nav {
	DISPLAY: inline-block
}
.ui-tabs-container{
	clear: both;
	border:solid 1px #637a8c;
}
