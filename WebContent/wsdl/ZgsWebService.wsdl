<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions targetNamespace="http://webservices.dawnpro.com" xmlns:apachesoap="http://xml.apache.org/xml-soap" xmlns:impl="http://webservices.dawnpro.com" xmlns:intf="http://webservices.dawnpro.com" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:wsdlsoap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
<!--WSDL created by Apache Axis version: 1.4
Built on Apr 22, 2006 (06:55:48 PDT)-->
 <wsdl:types>
  <schema elementFormDefault="qualified" targetNamespace="http://webservices.dawnpro.com" xmlns="http://www.w3.org/2001/XMLSchema">
   <element name="uploadBaseHgzcs">
    <complexType>
     <sequence>
      <element name="jsonstr" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
   <element name="uploadBaseHgzcsResponse">
    <complexType>
     <sequence>
      <element name="uploadBaseHgzcsReturn" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
   <element name="helloWorld">
    <complexType/>
   </element>
   <element name="helloWorldResponse">
    <complexType>
     <sequence>
      <element name="helloWorldReturn" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
   <element name="uploadXcxxjc">
    <complexType>
     <sequence>
      <element name="jsonstr" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
   <element name="uploadXcxxjcResponse">
    <complexType>
     <sequence>
      <element name="uploadXcxxjcReturn" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
   <element name="getHgzinfo">
    <complexType>
     <sequence>
      <element name="jsonstr" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
   <element name="getHgzinfoResponse">
    <complexType>
     <sequence>
      <element name="getHgzinfoReturn" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
   <element name="uploadProductData">
    <complexType>
     <sequence>
      <element name="jsonstr" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
   <element name="uploadProductDataResponse">
    <complexType>
     <sequence>
      <element name="uploadProductDataReturn" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
  </schema>
 </wsdl:types>

   <wsdl:message name="uploadProductDataResponse">

      <wsdl:part element="impl:uploadProductDataResponse" name="parameters">

      </wsdl:part>

   </wsdl:message>

   <wsdl:message name="getHgzinfoRequest">

      <wsdl:part element="impl:getHgzinfo" name="parameters">

      </wsdl:part>

   </wsdl:message>

   <wsdl:message name="getHgzinfoResponse">

      <wsdl:part element="impl:getHgzinfoResponse" name="parameters">

      </wsdl:part>

   </wsdl:message>

   <wsdl:message name="uploadBaseHgzcsRequest">

      <wsdl:part element="impl:uploadBaseHgzcs" name="parameters">

      </wsdl:part>

   </wsdl:message>

   <wsdl:message name="uploadXcxxjcResponse">

      <wsdl:part element="impl:uploadXcxxjcResponse" name="parameters">

      </wsdl:part>

   </wsdl:message>

   <wsdl:message name="helloWorldRequest">

      <wsdl:part element="impl:helloWorld" name="parameters">

      </wsdl:part>

   </wsdl:message>

   <wsdl:message name="uploadBaseHgzcsResponse">

      <wsdl:part element="impl:uploadBaseHgzcsResponse" name="parameters">

      </wsdl:part>

   </wsdl:message>

   <wsdl:message name="helloWorldResponse">

      <wsdl:part element="impl:helloWorldResponse" name="parameters">

      </wsdl:part>

   </wsdl:message>

   <wsdl:message name="uploadXcxxjcRequest">

      <wsdl:part element="impl:uploadXcxxjc" name="parameters">

      </wsdl:part>

   </wsdl:message>

   <wsdl:message name="uploadProductDataRequest">

      <wsdl:part element="impl:uploadProductData" name="parameters">

      </wsdl:part>

   </wsdl:message>

   <wsdl:portType name="ZgsWebService">

      <wsdl:operation name="uploadBaseHgzcs">

         <wsdl:input message="impl:uploadBaseHgzcsRequest" name="uploadBaseHgzcsRequest">

       </wsdl:input>

         <wsdl:output message="impl:uploadBaseHgzcsResponse" name="uploadBaseHgzcsResponse">

       </wsdl:output>

      </wsdl:operation>

      <wsdl:operation name="helloWorld">

         <wsdl:input message="impl:helloWorldRequest" name="helloWorldRequest">

       </wsdl:input>

         <wsdl:output message="impl:helloWorldResponse" name="helloWorldResponse">

       </wsdl:output>

      </wsdl:operation>

      <wsdl:operation name="uploadXcxxjc">

         <wsdl:input message="impl:uploadXcxxjcRequest" name="uploadXcxxjcRequest">

       </wsdl:input>

         <wsdl:output message="impl:uploadXcxxjcResponse" name="uploadXcxxjcResponse">

       </wsdl:output>

      </wsdl:operation>

      <wsdl:operation name="getHgzinfo">

         <wsdl:input message="impl:getHgzinfoRequest" name="getHgzinfoRequest">

       </wsdl:input>

         <wsdl:output message="impl:getHgzinfoResponse" name="getHgzinfoResponse">

       </wsdl:output>

      </wsdl:operation>

      <wsdl:operation name="uploadProductData">

         <wsdl:input message="impl:uploadProductDataRequest" name="uploadProductDataRequest">

       </wsdl:input>

         <wsdl:output message="impl:uploadProductDataResponse" name="uploadProductDataResponse">

       </wsdl:output>

      </wsdl:operation>

   </wsdl:portType>

   <wsdl:binding name="ZgsWebServiceSoapBinding" type="impl:ZgsWebService">

      <wsdlsoap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

      <wsdl:operation name="uploadBaseHgzcs">

         <wsdlsoap:operation soapAction=""/>

         <wsdl:input name="uploadBaseHgzcsRequest">

            <wsdlsoap:body use="literal"/>

         </wsdl:input>

         <wsdl:output name="uploadBaseHgzcsResponse">

            <wsdlsoap:body use="literal"/>

         </wsdl:output>

      </wsdl:operation>

      <wsdl:operation name="helloWorld">

         <wsdlsoap:operation soapAction=""/>

         <wsdl:input name="helloWorldRequest">

            <wsdlsoap:body use="literal"/>

         </wsdl:input>

         <wsdl:output name="helloWorldResponse">

            <wsdlsoap:body use="literal"/>

         </wsdl:output>

      </wsdl:operation>

      <wsdl:operation name="uploadXcxxjc">

         <wsdlsoap:operation soapAction=""/>

         <wsdl:input name="uploadXcxxjcRequest">

            <wsdlsoap:body use="literal"/>

         </wsdl:input>

         <wsdl:output name="uploadXcxxjcResponse">

            <wsdlsoap:body use="literal"/>

         </wsdl:output>

      </wsdl:operation>

      <wsdl:operation name="getHgzinfo">

         <wsdlsoap:operation soapAction=""/>

         <wsdl:input name="getHgzinfoRequest">

            <wsdlsoap:body use="literal"/>

         </wsdl:input>

         <wsdl:output name="getHgzinfoResponse">

            <wsdlsoap:body use="literal"/>

         </wsdl:output>

      </wsdl:operation>

      <wsdl:operation name="uploadProductData">

         <wsdlsoap:operation soapAction=""/>

         <wsdl:input name="uploadProductDataRequest">

            <wsdlsoap:body use="literal"/>

         </wsdl:input>

         <wsdl:output name="uploadProductDataResponse">

            <wsdlsoap:body use="literal"/>

         </wsdl:output>

      </wsdl:operation>

   </wsdl:binding>

   <wsdl:service name="ZgsWebServiceService">

      <wsdl:port binding="impl:ZgsWebServiceSoapBinding" name="ZgsWebService">

         <wsdlsoap:address location="http://localhost:9290/schgz_dfm/services/ZgsWebService"/>

      </wsdl:port>

   </wsdl:service>

</wsdl:definitions>
