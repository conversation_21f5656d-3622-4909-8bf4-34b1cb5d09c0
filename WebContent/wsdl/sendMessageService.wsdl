<?xml version='1.0' encoding='UTF-8'?><wsdl:definitions name="ISendMessageServiceService" targetNamespace="http://ws.esb.dawnpro.com/" xmlns:ns1="http://schemas.xmlsoap.org/soap/http" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:tns="http://ws.esb.dawnpro.com/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <wsdl:types>
<xs:schema elementFormDefault="unqualified" targetNamespace="http://ws.esb.dawnpro.com/" version="1.0" xmlns:tns="http://ws.esb.dawnpro.com/" xmlns:xs="http://www.w3.org/2001/XMLSchema">
<xs:element name="sendMessage" type="tns:sendMessage"/>
<xs:element name="sendMessageResponse" type="tns:sendMessageResponse"/>
<xs:complexType name="sendMessage">
    <xs:sequence>
      <xs:element minOccurs="0" name="baseParams" type="xs:string"/>
      <xs:element minOccurs="0" name="bizParams" type="xs:string"/>
    </xs:sequence>
  </xs:complexType>
<xs:complexType name="sendMessageResponse">
    <xs:sequence>
      <xs:element minOccurs="0" name="return" type="xs:string"/>
    </xs:sequence>
  </xs:complexType>
</xs:schema>
  </wsdl:types>
  <wsdl:message name="sendMessageResponse">
    <wsdl:part element="tns:sendMessageResponse" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="sendMessage">
    <wsdl:part element="tns:sendMessage" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:portType name="ISendMessageService">
    <wsdl:operation name="sendMessage">
      <wsdl:input message="tns:sendMessage" name="sendMessage">
    </wsdl:input>
      <wsdl:output message="tns:sendMessageResponse" name="sendMessageResponse">
    </wsdl:output>
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="ISendMessageServiceServiceSoapBinding" type="tns:ISendMessageService">
    <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
    <wsdl:operation name="sendMessage">
      <soap:operation soapAction="" style="document"/>
      <wsdl:input name="sendMessage">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="sendMessageResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="ISendMessageServiceService">
    <wsdl:port binding="tns:ISendMessageServiceServiceSoapBinding" name="ISendMessageServicePort">
      <soap:address location="http://************:7080/ESB/sendMessageService"/>
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>
