### direct log messages to stdout ###
log4j.rootLogger=DEBUG,logfile
#,console
#ææ¥ææ ¼å¼çææ¥å¿
log4j.appender.logfile=org.apache.log4j.DailyRollingFileAppender
#æ¥å¿ä½ç½®
log4j.appender.logfile.File=${catalina.home}/logs/schgz_dfm.log
#æ¥å¿åéæ¹å¼ååå²æ¥å¿åç§°æ ¼å¼ï¼yyyy-MM-dd-HH-mm è¡¨ç¤ºæ¯åéçæ1ä¸ªæ¥å¿
log4j.appender.logfile.DataPattern='.'yyyy-MM-dd'.log'
#æå¤ä¿ççåå²æ¥å¿æä»¶ä¸ªæ°
#log4j.appender.logfile.maxBackupIndex=30
log4j.appender.logfile.encoding=UTF-8
log4j.appender.logfile.Append=true
log4j.appender.logfile.layout=org.apache.log4j.PatternLayout     
log4j.appender.logfile.layout.ConversionPattern=[%d{yyyy-MM-dd HH:mm:ss}][%-5p][%c{1}:%l]%n[%t]%x: %m%n

#log4j.appender.logfile=org.apache.log4j.DailyRollingFileAppender
#log4j.appender.logfile.File=D:/logs/dfmhgz/dfmhgz.log
#log4j.appender.logfile.DataPattern='.'yyyy-MM-dd-HH-mm'.log'
#log4j.appender.logfile.Threshold=debug
#log4j.appender.logfile.encoding=UTF-8
#log4j.appender.logfile.Append=false
#log4j.appender.logfile.layout=org.apache.log4j.PatternLayout     
#log4j.appender.logfile.layout.ConversionPattern=[%d{yyyy-MM-dd HH:mm:ss}][%-5p][%c{1}:%l]%n[%t]%x: %m%n

log4j.appender.console=org.apache.log4j.ConsoleAppender
log4j.appender.console.Target=System.out
log4j.appender.console.layout=org.apache.log4j.PatternLayout
log4j.appender.console.layout.ConversionPattern=%d{ABSOLUTE} %5p %c{1}:%L - %m%n

log4j.logger.PERFORMANCE=DEBUG
log4j.logger.BUSINESS=DEBUG
log4j.logger.INTERFACE=DEBUG
log4j.logger.SECURITY=DEBUG
log4j.logger.RUNTIME=DEBUG
log4j.logger.org.springframework=ERROR
log4j.logger.org.hibernate.transaction=ERROR
log4j.logger.org.ecside=ERROR
log4j.logger.org.apache=WARN