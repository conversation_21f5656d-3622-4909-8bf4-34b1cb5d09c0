<?xml version="1.0" encoding="GB2312"?>
<!-- 此文件用户配置csv导出  sql 里面可以用${condition}   -->
<exports>
	<csv id="cciclaim">
		<sql><![CDATA[select *
  from (SELECT c.claim_id,
               c.claim_number,
               vrepair.name repair,
               c.station_code,
               c.station_name,
               vinteger.name integername,
               c.workfilescode,
               vsaletype.name saletype,
               c.n_last_change_date,
               c.n_last_kilometre,
               c.n_last_hours,
               c.identify_user,
               c.service_manager,
               c.engine,
               c.db_code,
               r.record_so,
               a.xl,
               a.jx,
               ens.series_desc engineserial,
               r.record_produce engineproduce,
               nvl(r.warranty_times, 0) warranty_times,
               nvl(eaa.apparea_desc, c.e_apply) eapply,
               c.e_warranty_startday,
               r.record_first_kilometre,
               r.record_first_hours,
               c.e_steer_kilometre,
               c.e_steer_hours,
               c.trouble_date,
               vattain.name attain,
               c.trouble_complain,
               c.trouble_reason,
               c.trouble_remove,
               c.equip_type,
               c.equip_name,
               c.equip_buydate,
               c.equip_product,
               c.equip_underpan,
               c.equip_oem_code,
               c.equip_oem_name,
               cu.ck_clientele_unit,
               cu.ck_clientele_addr,
               cu.ck_clientele_name,
               cu.ck_clientele_phone,
               cu.ck_clientele_handset,
               cu.ck_clientele_notion,
               (select fn_strcat(td.ck_trouble_code) troublecode
                  from tw_claim_trouble_detail td
                 where td.claim_id = c.claim_id) troublecode,
               (select fn_strcat(td.ck_trouble_code || ':' || td.trouble_desc)
                  from tw_claim_trouble_detail td
                 where td.claim_id = c.claim_id) troubledesc,
               (select fn_strcat(td.ck_trouble_code || ':' ||
                                 nvl(td.impute_money, 0))
                  from tw_claim_trouble_detail td
                 where td.claim_id = c.claim_id) troubleimputemoney,
               (select fn_strcat(pd.old_part)
                  from tw_claim_part_detail pd
                 where pd.status = 20
                   and pd.claimid = c.claim_id
                   and pd.part_cause_state = 1) causeparts,
               pn.node_number,
               pn.node_name cstatus,
               da.da0parts_money,
               da.da0labor_money,
               da.da0travel_money,
               da.da0other_money,
               da.da0money_sum,
               da.da10parts_money,
               da.da10labor_money,
               da.da10travel_money,
               da.da10other_money,
               da.da10money_sum,
               wt10.actionmemo actionmemo10,
               da.da20parts_money,
               da.da20labor_money,
               da.da20travel_money,
               da.da20other_money,
               da.da20money_sum,
               wt20.actionmemo actionmemo20,
               w.backlist_money,
               w.audit_manage,
               w.reason,
               w.auditcomment,
               pnw.node_name wstatus
          FROM tbl_engine_record         r,
               atpunameplate             a,
               tw_claim                  c,
               wf_entitystate            es,
               v_dict                    vrepair,
               v_dict                    vinteger,
               TW_ENGINE_SERIES          ens,
               tw_engine_applicationarea eaa,
               v_dict                    vattain,
               tw_claim_custom           cu,
               v_claim_declare_audit     da,
               v_wfprocessnodes          pn,
               v_wftrace                 wt10,
               v_wftrace                 wt20,
               tw_claim_withdrawal       w,
               wf_entitystate            esw,
               v_wfprocessnodes          pnw,
               v_dict                    vsaletype
         WHERE a.so(+) = r.record_so
           and c.engine = r.record_engine(+)
           and c.c_type = 10
           and es.entity_name = 'com.dawnpro.service.commons.entity.TwClaim'
           and es.entity_id = c.claim_id
           and es.node_number = pn.node_number
           and pn.workflow_code = 'cci_claim_audit'
           and vrepair.type = 'repair'
           and vrepair.code(+) = c.c_repair
           and vinteger.type = 'integer'
           and vinteger.code(+) = c.c_integer
           and vsaletype.type = 'saletype'
           and vsaletype.code(+) = c.c_sale_type
           and ens.series_code(+) = a.xl
           and eaa.apparea_code(+) = c.e_apply
           and vattain.type = 'attaint_level'
           and vattain.code = c.trouble_level
           and cu.claim_id = c.claim_id
           and da.claim_id(+) = c.claim_id
           and wt10.entity_name(+) =
               'com.dawnpro.service.commons.entity.TwClaim'
           and wt10.entity_id(+) = c.claim_id
           and wt10.node_number(+) = 10
           and wt20.entity_name(+) =
               'com.dawnpro.service.commons.entity.TwClaim'
           and wt20.entity_id(+) = c.claim_id
           and wt20.node_number(+) = 20
           and w.claim_id(+) = c.claim_id
           and esw.entity_name(+) =
               'com.dawnpro.service.commons.entity.TwClaimWithdrawal'
           and esw.entity_id(+) = w.id
           and pnw.node_number(+) = esw.node_number
           and pnw.workflow_code(+) = 'withdraw')
 where 1 = 1 ${condition}]]></sql>
		<fields>
			<field column="claim_number"><![CDATA[索赔单号]]></field>
			<field column="repair"><![CDATA[保修性质]]></field>
			<field column="station_code"><![CDATA[经销商代码]]></field>
			<field column="station_name"><![CDATA[经销商名称]]></field>
			<field column="integername"><![CDATA[索赔类型]]></field>
			<field column="workfilescode"><![CDATA[工作档案号]]></field>
			<field column="saletype"><![CDATA[售前/售后]]></field>
			<field column="n_last_change_date"><![CDATA[新零件保修起始日]]></field>
			<field column="n_last_hours"><![CDATA[新零件已运行小时]]></field>
			<field column="n_last_kilometre"><![CDATA[新零件已行驶里程]]></field>
			<field column="n_last_kilometre"><![CDATA[新零件已行驶里程]]></field>
			<field column="service_manager"><![CDATA[服务人员]]></field>
			<field column="identify_user"><![CDATA[服务电话]]></field>
			<field column="engine"><![CDATA[发动机序列号]]></field>
			<field column="record_so"><![CDATA[SO号]]></field>
			<field column="jx"><![CDATA[发动机型号]]></field>
			<field column="engineproduce"><![CDATA[发动机生产日期]]></field>
			<field column="engineserial"><![CDATA[发动机系列]]></field>
			<field column="warranty_times"><![CDATA[维修次数]]></field>
			<field column="eapply"><![CDATA[应用领域]]></field>
			<field column="e_warranty_startday"><![CDATA[保修起始日]]></field>
			<field column="record_first_hours"><![CDATA[首次故障运行小时]]></field>
			<field column="record_first_kilometre"><![CDATA[首次故障行驶里程]]></field>
			<field column="e_steer_hours"><![CDATA[运行小时]]></field>
			<field column="e_steer_kilometre"><![CDATA[行驶里程]]></field>
			<field column="trouble_date"><![CDATA[故障时间]]></field>
			<field column="attain"><![CDATA[损坏程度]]></field>
			<field column="trouble_complain"><![CDATA[用户抱怨]]></field>
			<field column="trouble_reason"><![CDATA[故障原因]]></field>
			<field column="trouble_remove"><![CDATA[故障排除]]></field>
			<field column="equip_type"><![CDATA[设备型号]]></field>
			<field column="equip_name"><![CDATA[设备名称]]></field>
			<field column="equip_buydate"><![CDATA[购买日期]]></field>
			<field column="equip_product"><![CDATA[设备生产日期]]></field>
			<field column="equip_underpan"><![CDATA[底盘号/设备序列号]]></field>
			<field column="equip_oem_code"><![CDATA[OEM代码]]></field>
			<field column="equip_oem_name"><![CDATA[OEM名称]]></field>
			<field column="ck_clientele_unit"><![CDATA[用户单位]]></field>
			<field column="ck_clientele_addr"><![CDATA[维修地点]]></field>
			<field column="ck_clientele_name"><![CDATA[用户姓名]]></field>
			<field column="ck_clientele_phone"><![CDATA[用户电话]]></field>
			<field column="ck_clientele_handset"><![CDATA[手机]]></field>
			<field column="ck_clientele_notion"><![CDATA[用户意见]]></field>
			<field column="troublecode"><![CDATA[故障代码]]></field>
			<field column="troubledesc"><![CDATA[故障描述]]></field>
			<field column="troubleimputemoney"><![CDATA[待转嫁金额]]></field>
			<field column="causeparts"><![CDATA[元凶件]]></field>
			<field column="cstatus"><![CDATA[索赔单状态]]></field>
			<field column="da0parts_money"><![CDATA[录入零件费]]></field>
			<field column="da0labor_money"><![CDATA[录入工时费]]></field>
			<field column="da0travel_money"><![CDATA[录入差旅费]]></field>
			<field column="da0other_money"><![CDATA[录入其他费用]]></field>
			<field column="da0money_sum"><![CDATA[录入总费用]]></field>
			<field column="da10parts_money"><![CDATA[初审零件费]]></field>
			<field column="da10labor_money"><![CDATA[初审工时费]]></field>
			<field column="da10travel_money"><![CDATA[初审差旅费]]></field>
			<field column="da10other_money"><![CDATA[初审其他费]]></field>
			<field column="da10money_sum"><![CDATA[初审总费用]]></field>
			<field column="actionmemo10"><![CDATA[初审备注]]></field>
			<field column="da20parts_money"><![CDATA[终审零件费]]></field>
			<field column="da20labor_money"><![CDATA[终审工时费]]></field>
			<field column="da20travel_money"><![CDATA[终审差旅费]]></field>
			<field column="da20other_money"><![CDATA[终审其他费]]></field>
			<field column="da20money_sum"><![CDATA[终审总费用]]></field>
			<field column="actionmemo20"><![CDATA[终审备注]]></field>
			<field column="backlist_money"><![CDATA[倒扣金额]]></field>
			<field column="audit_manage"><![CDATA[倒扣管理费]]></field>
			<field column="reason"><![CDATA[倒扣原因]]></field>
			<field column="auditcomment"><![CDATA[倒扣审核意见]]></field>
			<field column="wstatus"><![CDATA[倒扣状态]]></field>
		</fields>
	</csv>
	
</exports>