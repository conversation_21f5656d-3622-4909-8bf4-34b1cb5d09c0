/**
 * VinUploadServiceImplService.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sinosoft.webservice;

public interface VinUploadServiceImplService extends javax.xml.rpc.Service {
    public java.lang.String getVinUploadServiceImplPortAddress();

    public com.sinosoft.webservice.VinUploadService getVinUploadServiceImplPort() throws javax.xml.rpc.ServiceException;

    public com.sinosoft.webservice.VinUploadService getVinUploadServiceImplPort(java.net.URL portAddress) throws javax.xml.rpc.ServiceException;
}
