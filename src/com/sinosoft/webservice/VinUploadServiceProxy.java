package com.sinosoft.webservice;

public class VinUploadServiceProxy implements com.sinosoft.webservice.VinUploadService {
  private String _endpoint = null;
  private com.sinosoft.webservice.VinUploadService vinUploadService = null;
  
  public VinUploadServiceProxy() {
    _initVinUploadServiceProxy();
  }
  
  public VinUploadServiceProxy(String endpoint) {
    _endpoint = endpoint;
    _initVinUploadServiceProxy();
  }
  
  private void _initVinUploadServiceProxy() {
    try {
      vinUploadService = (new com.sinosoft.webservice.VinUploadServiceImplServiceLocator()).getVinUploadServiceImplPort();
      if (vinUploadService != null) {
        if (_endpoint != null)
          ((javax.xml.rpc.Stub)vinUploadService)._setProperty("javax.xml.rpc.service.endpoint.address", _endpoint);
        else
          _endpoint = (String)((javax.xml.rpc.Stub)vinUploadService)._getProperty("javax.xml.rpc.service.endpoint.address");
      }
      
    }
    catch (javax.xml.rpc.ServiceException serviceException) {}
  }
  
  public String getEndpoint() {
    return _endpoint;
  }
  
  public void setEndpoint(String endpoint) {
    _endpoint = endpoint;
    if (vinUploadService != null)
      ((javax.xml.rpc.Stub)vinUploadService)._setProperty("javax.xml.rpc.service.endpoint.address", _endpoint);
    
  }
  
  public com.sinosoft.webservice.VinUploadService getVinUploadService() {
    if (vinUploadService == null)
      _initVinUploadServiceProxy();
    return vinUploadService;
  }
  
  public java.lang.String upload(java.lang.String rand, java.lang.String manuRelationId, java.lang.String vinList) throws java.rmi.RemoteException{
    if (vinUploadService == null)
      _initVinUploadServiceProxy();
    return vinUploadService.upload(rand, manuRelationId, vinList);
  }
  
  public java.lang.String download(java.lang.String rand, java.lang.String manuRelationId, java.lang.String vinCode) throws java.rmi.RemoteException{
    if (vinUploadService == null)
      _initVinUploadServiceProxy();
    return vinUploadService.download(rand, manuRelationId, vinCode);
  }
  
  public java.lang.String auth(java.lang.String rand, java.lang.String signature) throws java.rmi.RemoteException{
    if (vinUploadService == null)
      _initVinUploadServiceProxy();
    return vinUploadService.auth(rand, signature);
  }
  
  public java.lang.String getRand() throws java.rmi.RemoteException{
    if (vinUploadService == null)
      _initVinUploadServiceProxy();
    return vinUploadService.getRand();
  }
  
  
}