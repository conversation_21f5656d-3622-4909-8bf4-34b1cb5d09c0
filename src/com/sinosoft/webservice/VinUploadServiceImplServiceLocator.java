/**
 * VinUploadServiceImplServiceLocator.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sinosoft.webservice;

public class VinUploadServiceImplServiceLocator extends org.apache.axis.client.Service implements com.sinosoft.webservice.VinUploadServiceImplService {

    public VinUploadServiceImplServiceLocator() {
    }


    public VinUploadServiceImplServiceLocator(org.apache.axis.EngineConfiguration config) {
        super(config);
    }

    public VinUploadServiceImplServiceLocator(java.lang.String wsdlLoc, javax.xml.namespace.QName sName) throws javax.xml.rpc.ServiceException {
        super(wsdlLoc, sName);
    }

    // Use to get a proxy class for VinUploadServiceImplPort
    private java.lang.String VinUploadServiceImplPort_address = "http://www.cqccms.com.cn/incoc/webservice/vinUploadWebService";

    public java.lang.String getVinUploadServiceImplPortAddress() {
        return VinUploadServiceImplPort_address;
    }

    // The WSDD service name defaults to the port name.
    private java.lang.String VinUploadServiceImplPortWSDDServiceName = "VinUploadServiceImplPort";

    public java.lang.String getVinUploadServiceImplPortWSDDServiceName() {
        return VinUploadServiceImplPortWSDDServiceName;
    }

    public void setVinUploadServiceImplPortWSDDServiceName(java.lang.String name) {
        VinUploadServiceImplPortWSDDServiceName = name;
    }

    public com.sinosoft.webservice.VinUploadService getVinUploadServiceImplPort() throws javax.xml.rpc.ServiceException {
       java.net.URL endpoint;
        try {
            endpoint = new java.net.URL(VinUploadServiceImplPort_address);
        }
        catch (java.net.MalformedURLException e) {
            throw new javax.xml.rpc.ServiceException(e);
        }
        return getVinUploadServiceImplPort(endpoint);
    }

    public com.sinosoft.webservice.VinUploadService getVinUploadServiceImplPort(java.net.URL portAddress) throws javax.xml.rpc.ServiceException {
        try {
            com.sinosoft.webservice.VinUploadServiceImplServiceSoapBindingStub _stub = new com.sinosoft.webservice.VinUploadServiceImplServiceSoapBindingStub(portAddress, this);
            _stub.setPortName(getVinUploadServiceImplPortWSDDServiceName());
            return _stub;
        }
        catch (org.apache.axis.AxisFault e) {
            return null;
        }
    }

    public void setVinUploadServiceImplPortEndpointAddress(java.lang.String address) {
        VinUploadServiceImplPort_address = address;
    }

    /**
     * For the given interface, get the stub implementation.
     * If this service has no port for the given interface,
     * then ServiceException is thrown.
     */
    public java.rmi.Remote getPort(Class serviceEndpointInterface) throws javax.xml.rpc.ServiceException {
        try {
            if (com.sinosoft.webservice.VinUploadService.class.isAssignableFrom(serviceEndpointInterface)) {
                com.sinosoft.webservice.VinUploadServiceImplServiceSoapBindingStub _stub = new com.sinosoft.webservice.VinUploadServiceImplServiceSoapBindingStub(new java.net.URL(VinUploadServiceImplPort_address), this);
                _stub.setPortName(getVinUploadServiceImplPortWSDDServiceName());
                return _stub;
            }
        }
        catch (java.lang.Throwable t) {
            throw new javax.xml.rpc.ServiceException(t);
        }
        throw new javax.xml.rpc.ServiceException("There is no stub implementation for the interface:  " + (serviceEndpointInterface == null ? "null" : serviceEndpointInterface.getName()));
    }

    /**
     * For the given interface, get the stub implementation.
     * If this service has no port for the given interface,
     * then ServiceException is thrown.
     */
    public java.rmi.Remote getPort(javax.xml.namespace.QName portName, Class serviceEndpointInterface) throws javax.xml.rpc.ServiceException {
        if (portName == null) {
            return getPort(serviceEndpointInterface);
        }
        java.lang.String inputPortName = portName.getLocalPart();
        if ("VinUploadServiceImplPort".equals(inputPortName)) {
            return getVinUploadServiceImplPort();
        }
        else  {
            java.rmi.Remote _stub = getPort(serviceEndpointInterface);
            ((org.apache.axis.client.Stub) _stub).setPortName(portName);
            return _stub;
        }
    }

    public javax.xml.namespace.QName getServiceName() {
        return new javax.xml.namespace.QName("http://webservice.sinosoft.com/", "VinUploadServiceImplService");
    }

    private java.util.HashSet ports = null;

    public java.util.Iterator getPorts() {
        if (ports == null) {
            ports = new java.util.HashSet();
            ports.add(new javax.xml.namespace.QName("http://webservice.sinosoft.com/", "VinUploadServiceImplPort"));
        }
        return ports.iterator();
    }

    /**
    * Set the endpoint address for the specified port name.
    */
    public void setEndpointAddress(java.lang.String portName, java.lang.String address) throws javax.xml.rpc.ServiceException {
        
if ("VinUploadServiceImplPort".equals(portName)) {
            setVinUploadServiceImplPortEndpointAddress(address);
        }
        else 
{ // Unknown Port Name
            throw new javax.xml.rpc.ServiceException(" Cannot set Endpoint Address for Unknown Port" + portName);
        }
    }

    /**
    * Set the endpoint address for the specified port name.
    */
    public void setEndpointAddress(javax.xml.namespace.QName portName, java.lang.String address) throws javax.xml.rpc.ServiceException {
        setEndpointAddress(portName.getLocalPart(), address);
    }

}
