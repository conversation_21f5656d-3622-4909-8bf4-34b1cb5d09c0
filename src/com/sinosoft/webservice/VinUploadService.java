/**
 * VinUploadService.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sinosoft.webservice;

public interface VinUploadService extends java.rmi.Remote {
    public java.lang.String upload(java.lang.String rand, java.lang.String manuRelationId, java.lang.String vinList) throws java.rmi.RemoteException;
    public java.lang.String download(java.lang.String rand, java.lang.String manuRelationId, java.lang.String vinCode) throws java.rmi.RemoteException;
    public java.lang.String auth(java.lang.String rand, java.lang.String signature) throws java.rmi.RemoteException;
    public java.lang.String getRand() throws java.rmi.RemoteException;
}
