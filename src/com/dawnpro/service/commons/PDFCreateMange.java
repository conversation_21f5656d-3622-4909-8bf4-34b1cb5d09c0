package com.dawnpro.service.commons;

import java.io.File;

import com.dawnpro.commons.SystemParam;
import com.dawnpro.commons.util.DateTools;
import com.dawnpro.commons.util.StringTools;

import net.sf.jasperreports.engine.JRException;
import net.sf.jasperreports.engine.JasperExportManager;
import net.sf.jasperreports.engine.JasperPrint;

/**
 * PDF生成管理类
 * <AUTHOR>
 *
 */
public class PDFCreateMange {

	public static boolean exportMepPDF(JasperPrint jasperPrint, String vin,String savepath) {
		boolean bool = false;
		String mep_path = SystemParam.getKey("mep_savepath","D:/mep_pdf");
		if (StringTools.isBlank(savepath)) {
			savepath = mep_path + "/" + DateTools.getNowDate("yyyyMMdd");
		}
		else {
			savepath = mep_path + "/" + savepath;
		}
		
		File tempFold = new File(savepath);
		if (!tempFold.isDirectory()) {
			tempFold.mkdirs();
		}
		if (jasperPrint != null) {
			try {
				JasperExportManager.exportReportToPdfFile(jasperPrint, savepath + "/" + vin + ".pdf");
				bool = true;
			} catch (JRException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		return bool;
	}
	
	
}
