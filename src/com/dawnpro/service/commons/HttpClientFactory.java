package com.dawnpro.service.commons;

import javax.net.ssl.SSLContext;
import javax.net.ssl.X509TrustManager;

import org.apache.http.Consts;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLContexts;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.nio.charset.Charset;
import java.security.SecureRandom;
import java.util.Map;
import java.util.Set;

public class HttpClientFactory {
	
	 // 默认返回数据的编码格式
    private static Charset DEFAULT_ENCODING_CHARSET = Consts.UTF_8;
	
	 	private static CloseableHttpClient client;
	 
	    public static HttpClient getHttpsClient() throws Exception {
	 
	        if (client != null) {
	            return client;
	        }
	        SSLContext sslcontext = SSLContexts.custom().useSSL().build();
	        sslcontext.init(null, new X509TrustManager[]{new HttpsTrustManager()}, new SecureRandom());
	        SSLConnectionSocketFactory factory = new SSLConnectionSocketFactory(sslcontext,
	                SSLConnectionSocketFactory.BROWSER_COMPATIBLE_HOSTNAME_VERIFIER);
	        client = HttpClients.custom().setSSLSocketFactory(factory).build();
	 
	        return client;
	    }
	 
	    public static void releaseInstance() {
	        client = null;
	    }
	    
	    public static String doPostJson(String url, String postData, Map<String, String> header, String encoding) throws Exception {
	        return doPostMainJson(url, postData, header, encoding);
	    }
	    
	    private static String doPostMainJson(String url, String postData, Map<String, String> header, String encoding) throws Exception {

	        HttpPost post = new HttpPost(url);

	        //设置连接属性
//	        RequestConfig requestConfig = RequestConfig.custom()
//	                .setConnectTimeout(1000)
//	                .setConnectionRequestTimeout(1000)
//	                .setSocketTimeout(1000)
//	                .build();

//	        post.setConfig(requestConfig);

	        if (header != null) {
	            Set<String> headerNames = header.keySet();
	            for (String name : headerNames) {
	                post.addHeader(name, header.get(name));
	            }
	        }
	        if (postData != null) {
	            StringEntity entity = new StringEntity(postData, DEFAULT_ENCODING_CHARSET);
	            //entity.setContentEncoding("UTF-8");
	            entity.setContentType("application/json");
	            post.setEntity(entity);
	        }

	        return httpResult((CloseableHttpResponse) getHttpsClient().execute(post), encoding);
	    }
	    
	    private static String httpResult(CloseableHttpResponse response, String encoding) throws Exception {
	        String result = EntityUtils.toString(response.getEntity(), encoding == null ? DEFAULT_ENCODING_CHARSET : Charset.forName(encoding));
//	        if (response.getStatusLine().getStatusCode() != HttpStatus.SC_OK) {
//	            throw new Exception(response.getStatusLine().getStatusCode() + " " + result);
//	        }
	        response.close();
	        return result;
	    }
	    
}
