package com.dawnpro.service.commons;

import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;

import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPReply;

import com.dawnpro.core.log.Loggers;

import sun.net.ftp.FtpProtocolException;

/**
 * FTP文件上传、下载
 * 
 * <AUTHOR>
 * 
 */
public class FTPUtil {

	//FTP服务器地址
	private String ip = null;
	//FTP服务器端口
	private int port = 21;
	//FTP服务用户名
	private String username = null;
	//FTP服务用户密码
	private String password = null;
	
	/** 
     * 流缓冲区存放的大小 
     */  
	private int UPLOAD_BUFFER_SIZE=1024*1024;   

	private FTPClient ftpClient = null;

	public FTPUtil() {
	}

	public FTPUtil(String serverIP, int port, String username, String password) {
		this.ip = serverIP;
		this.username = username;
		this.password = password;
		this.port = port;
	}

	/**
	 * @category 连接FTP服务器
	 * 
	 */
	public boolean connectServer() {
		boolean ret = false;
		try {
			// ftpClient = FtpClient.create(this.ip);
			// 连接FTP服务器
			ftpClient = new FTPClient();
			ftpClient.connect(this.ip, this.port);
			ftpClient.login(this.username, this.password);
			ftpClient.setBufferSize(this.UPLOAD_BUFFER_SIZE);  
            
            //超时时间  
            int defaultTimeoutSecond=30*60 * 1000;  
            ftpClient.setDefaultTimeout(defaultTimeoutSecond);  
            ftpClient.setSoTimeout(defaultTimeoutSecond );  
            ftpClient.setDataTimeout(defaultTimeoutSecond); 
            
            if (!FTPReply.isPositiveCompletion(ftpClient.getReplyCode())) {  
                System.out.println("未连接到FTP，用户名或密码错误!");  
                ftpClient.logout();  
                ftpClient.disconnect();  
                ftpClient=null;  
            } else {  
            	ret = true;
            }
			
		} catch (Exception ex) {
			Loggers.PERFORMANCE.error("FTPUtil.connectServer", ex);
		}
		return ret;
	}

	/**
	 * @category 关闭连接
	 * 
	 */

	public void closeConnect() {
		try {
			if (ftpClient != null) {
				ftpClient.logout();
				ftpClient.disconnect();
			}
		} catch (IOException ex) {
		}
	}

	/**
	 * 
	 * 上传文件
	 * 
	 * @param localFile
	 *            本地文件
	 * @param remoteFile
	 *            远程文件
	 * @throws FtpProtocolException
	 */
	public boolean upload(String localFile, String remoteFile) {
		boolean ret = false;
		File file = new File(localFile);
		if (file.exists()) {
			InputStream in = null;
			try {
				// 设置PassiveMode被动模式-向服务发送传输请求  
		        ftpClient.enterLocalPassiveMode();  
				// 设置以二进制流的方式传输  
		        ftpClient.setFileType(FTPClient.BINARY_FILE_TYPE);
		        // 处理目录的操作  
		        createDirs(remoteFile);  
		        //添加超时监控线程  
	            //new DemaonThread(ftpClient).start();  
		        System.out.println("环保FTP上传：" + remoteFile);
				in = new BufferedInputStream(new FileInputStream(localFile));
				if (!ftpClient.storeFile(remoteFile, in)) {
					System.out.println("环保FTP上传失败！");
				}
				else {
					ret = true;
				}
			} 
			catch (Exception e) {
				e.printStackTrace();
			}
			finally {
				try {
					in.close();
				} catch (IOException ex) {
				}
			}
		}
		return ret;
		
		
	}
	
    /**
     * 在FTP服务器上建立指定的目录,当目录已经存在的情下不会影响目录下的文件,这样用以判断FTP
     * 上传文件时保证目录的存在目录格式必须以"/"根目录开头
     * @param pathList String
     * @throws Exception
     */
    public void createDirs(String remoteFile) throws Exception {
    	ftpClient.changeWorkingDirectory("/");  
    	if (remoteFile != null && remoteFile.indexOf("/") > 0) {
	    	remoteFile = remoteFile.substring(0,remoteFile.lastIndexOf("/"));
	        String[] dirs = remoteFile.split("/"); 
	        String dir = "";
	        for(int i = 0,max = dirs==null?0:dirs.length 
	                ; i < max ; i ++) { 
	        	dir=dirs[i];
	            //创建并进入不存在的目录  
	            if (!ftpClient.changeWorkingDirectory(dir)) {  
	                ftpClient.mkd(dir);  
	                ftpClient.changeWorkingDirectory(dir);  
	            }  
	        
	        }
    	}
    }
    
    /** 
     * 监控ftpclient超时守护线程 
     */  
    private class DemaonThread extends Thread {  
        private FTPClient ftpClient;  
        private int SCAN_NUMBER = 120;  
    	private int ONE_SECOND = 1000;    
        /** 
         * 每次休眠时间 
         */  
    	private int SCAN_SECOND = 10; 
        int num = 0;  
          
        /** 
         * @param ftpClient2 
         * @param fileName 
         */  
        public DemaonThread(FTPClient ftpClient2) {  
            this.ftpClient = ftpClient2;  
        }  
        public void run() {  
            while (num < SCAN_NUMBER && ftpClient.isConnected()) {  
                try {  
                    Thread.sleep(SCAN_SECOND*ONE_SECOND);  
                    num++;  
                } catch (Exception e) {  
                    e.printStackTrace();  
                }  
            }  
            try {  
                if (ftpClient.isConnected()) {
                	ftpClient.logout();
                    ftpClient.disconnect();  
                    ftpClient=null;  
                }  
            } catch (Exception e) {  
            }  
        }  
    }  

}
