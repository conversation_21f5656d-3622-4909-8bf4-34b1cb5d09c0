package com.dawnpro.vipservice;
import org.apache.commons.codec.digest.DigestUtils;

import com.alibaba.fastjson.JSON;
import com.dawnpro.commons.util.MapUtil;

import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
/**
 * 认证签名
 * <AUTHOR>
 *
 */
public class SignatureGenarator {

	public SignatureGenarator() {
		// TODO Auto-generated constructor stub
	}
	 /**
     * 移除小数点后没有意义的 0
     * <pre>
     * 1230.00 => 1230
     * 1230.10 => 1230.1
     * =====以下是不变的=====
     * 123000 => 123000
     * 1230.01 => 1230.01
     * </pre>
     */
    public static String removeLastZero(String str) {
    	if(str==null){
    		return str;
    	}	
        if (!str.contains(".")) {
            return str;
        }
        str = str.replaceAll("0+?$", "");
        return str.replaceAll("[.]$", "");
    }  
 
    public static Object formatZero(double num){

    	if(num % 1.0 == 0){

    	return (long)num;

    	}

    	return num;

    }   
    
	public static String generate(String token, String envData,
				String equipmentData,String processData,String resultData,String testData,String vehicleData) {
			final List<String> sortData = new ArrayList<String>();
			sortData.add(token);
			sortData.add(envData);
			sortData.add(equipmentData);
			sortData.add(processData);
			sortData.add(resultData);
			sortData.add(testData);
			sortData.add(vehicleData);
			Collections.sort(sortData);
			final StringBuilder builder = new StringBuilder();
			for (final String value : sortData) {
				builder.append(value);
			}
			return DigestUtils.sha1Hex(builder.toString());
		}
		public static void main(String[] args) {
			String c=DigestUtils.sha1Hex("abc");
			Double l=Double.valueOf(SignatureGenarator.removeLastZero("32.60"));
			Double l1=Double.valueOf(SignatureGenarator.removeLastZero("32.00"));
			Map process1=new HashMap();;
			process1.put("loc",SignatureGenarator.removeLastZero("32.00"));
			String processData1=JSON.toJSONString(process1);
			System.out.println(SignatureGenarator.removeLastZero("32.00"));
			System.out.println(l);
			System.out.println(processData1);
			System.out.println(l1);
			NumberFormat nf = NumberFormat.getInstance();
	         
	        process1.put("loc1",nf.format(l1));
	         
			System.out.println(c);
		}
}
