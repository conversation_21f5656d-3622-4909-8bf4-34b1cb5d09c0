package com.dawnpro.vipservice.mail;

import java.sql.SQLException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.GregorianCalendar;
import java.util.List;
import java.util.Map;

import javax.mail.MessagingException;
import javax.mail.internet.AddressException;

import com.dawnpro.commons.SystemParam;
import com.dawnpro.commons.util.MapUtil;
import com.dawnpro.commons.util.StringTools;
import com.dawnpro.commons.util.db.DAOException;
import com.dawnpro.commons.util.db.TransactionException;
import com.dawnpro.core.exception.AppException;
import com.dawnpro.service.BaseServer;

/**
 * 基础数据、需求数据审核 邮件提醒类
 * 
 * <AUTHOR>
 * 
 */
public class HgzMailManage extends BaseServer {
	
	public void autoSendMail() throws AppException{
		//查待传邮件
		 String sql = " select id,tousermail,title,content from tbl_base_mail m where m.deleteflag = '0' and status = '0' " ;
		    try {
		    	List resList = dao.queryForList(sql);
		      for(int i=0;i<resList.size();i++){
		    	  Map mailMap = (Map)resList.get(i);
		    	  String id = MapUtil.getMapValue(mailMap, "id", "");
		    	  String tousermail = MapUtil.getMapValue(mailMap, "tousermail", "");
		    	  String title = MapUtil.getMapValue(mailMap, "title", "");
		    	  String content = MapUtil.getMapValue(mailMap, "content", "");
		    	  //如果收件人邮箱为空则更新为传输失败
		    	  if("".equals(tousermail)){
		    		  String updatesql = "update tbl_base_mail m set status = '2',reason = '收件人邮箱为空',sendtime=now() where m.id = '"+id+"' " ;
		    		  this.dao.execute(updatesql);
		    	  }else{
		    		  String string = sendMail(tousermail,title,content);
		    		  String updatesql = "update tbl_base_mail m set status = '1',reason = '发送成功',sendtime=now() where m.id = '"+id+"' " ;
		    		  if("fail".equals(string)) {
		    			  updatesql = "update tbl_base_mail m set status = '2',reason = '发送失败',sendtime=now() where m.id = '"+id+"' " ;
		    		  }
		    		  this.dao.execute(updatesql);
		    	  }
		      }
		    } catch (Exception e) {
		    	e.printStackTrace();
		    	throw new AppException(1, "SYS-0027", null, e);
		    } 
	}

	public String sendMail(String tousermail,String title, String content) {

		String res = "fail";
		SystemParam.reLoad();
		String mail_server = SystemParam.getKey("mail_server");
		String mail_username = SystemParam.getKey("mail_username");
		String mail_password = SystemParam.getKey("mail_password");
		String mail_to = tousermail;
		SimpleMailSender sms = new SimpleMailSender(mail_server, mail_username, mail_password);
		try {
			String[] mtos = mail_to.split(",");
			if (mtos.length <= 1) {
				sms.send(mail_to, title, content);
			} else {
				List ads = new ArrayList();
				for (int i = 0; i < mtos.length; i++) {
					ads.add(mtos[i]);
				}
				sms.send(ads, title, content);
			}
		} catch (AddressException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (MessagingException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return res;
	}
}
