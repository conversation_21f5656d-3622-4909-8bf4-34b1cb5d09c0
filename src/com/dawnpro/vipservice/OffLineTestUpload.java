package com.dawnpro.vipservice;

import java.io.UnsupportedEncodingException;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.dawnpro.commons.util.MapUtil;
import com.dawnpro.commons.util.StringTools;
import com.dawnpro.commons.util.db.DAOException;
import com.dawnpro.commons.util.db.TransactionException;
import com.dawnpro.core.log.Loggers;
import com.dawnpro.entity.Errors;
import com.dawnpro.entity.FaData;
import com.dawnpro.entity.FailureResult;
import com.dawnpro.entity.LoginResult;
import com.dawnpro.entity.LugdownData;
import com.dawnpro.service.BaseServer;
import com.dawnpro.service.commons.HttpUtil;
import com.dawnpro.entity.AsmData;
import com.dawnpro.entity.EnvData;
import com.dawnpro.entity.EquipmentData;
import com.dawnpro.entity.ProcessData;
import com.dawnpro.entity.ResultData;
import com.dawnpro.entity.Root;
import com.dawnpro.entity.SdsData;
import com.dawnpro.entity.TestData;
import com.dawnpro.entity.VehicleData;
/**
 * 新车下线环保检验上传
 * <AUTHOR>
 *
 */
public class OffLineTestUpload extends BaseServer {

	/**
	 *登录验证
	 */
	private static final String loginUrl = "https://xcxx.vecc.org.cn/w/WSNewCar/login";
	/**
	 * 上报路径
	 */
	private static final String uploadUrl= "https://xcxx.vecc.org.cn/w/WSNewCar/uploadofflinetest";
	
	/**
	 * 登录VECC服务平台
	 * 验证报送账户密码获取token认证信息
	 */
	public String login(String username,String password, String ts_factory){
		String result="failure";
		Map map = new HashMap(); 
		map.put("username",username); 
		map.put("password",password); 
		String postData=JSON.toJSONString(map); 
		try {
			result=HttpUtil.doPostJson(loginUrl, postData);
			JSONObject jsonObject = JSONObject.parseObject(result);
			String trans=jsonObject.getString("status");
			//success
			if("success".equals(trans)){
				LoginResult loginResult = JSON.parseObject(result, LoginResult.class); 
				String token=loginResult.getData().getToken();
				String updatesql = "update tbl_up_account set token = '"+token+"' where deleteflag=0 and uptype='offlinetest' and factorycode='"+ts_factory+"'";
				this.dao.executeUpdate(updatesql);
				return token;
			}
			//failure
			if("failure".equals(trans)){
				FailureResult failResult = JSON.parseObject(result, FailureResult.class); 
				List<Errors> error=failResult.getErrors();
				String ERRMSG=error.get(0).getField();
				String FIELD=error.get(0).getErrmsg();
				String updatesql = "update tbl_up_account set ERRMSG = '"+ERRMSG+"' ,FIELD= '"+FIELD+"' where deleteflag=0 and uptype='offlinetest'  and factorycode='"+ts_factory+"'";
				this.dao.executeUpdate(updatesql);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}		
		return result;
	}
	/**
	 * 新车下线检验报告上传登录
	 * @param factory
	 * @return
	 */
	public String mepgkLogin(String ts_factory) {
		String result = "fail";	
		try {
			List tempList = this.dao.queryForList("select qybh,password from tbl_up_account where deleteflag='0' and uptype='offlinetest'  and factorycode='"+ts_factory+"' ");
			if (tempList != null && tempList.size() > 0) {
				String qybh = MapUtil.getMapValue((Map)tempList.get(0), "qybh", "11");
				String password = MapUtil.getMapValue((Map)tempList.get(0), "password", "22s");
				result = login(qybh, password,ts_factory);
			}
		} catch (DAOException e) {
			e.printStackTrace();
		} catch (SQLException e) {
			e.printStackTrace();
		} 
		return result;
	}
	

	/**
	 * @category 自动上传新车下线检验报告
	 * @createtime 2017年3月30日上午11:00:12
	 * @return
	 */
	public String mepgkAutoUpload() {
		String result = "fail";
		String userid = "104";
		StringBuffer sql = new StringBuffer();
	       try
	        {
	            List tempList = dao.queryForList("select factorycode ts_factory from tbl_up_account where deleteflag='0' and uptype='offlinetest' ");
	            if(tempList != null && tempList.size() > 0)
	            {
	                String ts_factory = "";
	                for(int i = 0; i < tempList.size(); i++)
	                {
	                    ts_factory = MapUtil.getMapValue((Map)tempList.get(i), "ts_factory", "");
	                    sql.delete(0, sql.length());
	                    sql.append((new StringBuilder("select * from tbl_offlinetest t where t.deleteflag=0 and t.upstate=1 and factorycode='")).append(ts_factory).append("' ").toString()).append(" and EXISTS (select * from  tbl_mepgk p where t.vin=p.vnm and p.vin_upstate = 2 ").append(" and p.pdf_upstate = 2  )").append(" LIMIT 0,100 ");
		   
		List resList = new ArrayList();	
		try {
			resList = this.dao.queryForList(sql.toString());
			String resultData = sendMepgkData(resList, ts_factory,userid);
			if(resultData.equals("ok"))
				result = "ok";
		} catch (DAOException e) {
			e.printStackTrace();
		} catch (SQLException e) {
			e.printStackTrace();
		} 
	                }
	            }
	        }
	            catch(DAOException e1)
	            {
	                e1.printStackTrace();
	            }
	            catch(SQLException e1)
	            {
	                e1.printStackTrace();
	            }
		return result;
 	}	
	
	/**
	 * 
	 * @category 新车下线检验报告数据上传
	 * @createtime 2019年11月27日上午10:19:37
	 * @param resList
	 * @param userid
	 * @return
	 */
	public String sendMepgkData(List resList,String ts_factory ,String userid) {
		String result = "fail";
		if (StringTools.isBlank(userid)) {
			userid = "0";
		}
		try {
			String token=mepgkLogin(ts_factory);
			List sqlList = new ArrayList();	
			if (!StringTools.isBlank(token) && !"fail".equals(token.trim())) {
				for (int i = 0; resList != null && i < resList.size(); i++) {
					Map map = (Map)resList.get(i);
					//检验环境参数 详见A.2,不允许为空，否则将报异常java.lang.NumberFormatException: empty String
					EnvData env=new EnvData();
					if(MapUtil.getMapValue(map, "rh",null)!=null){
						env.setRh(Double.valueOf(MapUtil.getMapValue(map, "rh",null)));
					}
					if(MapUtil.getMapValue(map, "ap",null)!=null){
						env.setAp(Double.valueOf(MapUtil.getMapValue(map, "ap",null)));
					}
					if(MapUtil.getMapValue(map, "et",null)!=null){
						env.setEt(Double.valueOf(MapUtil.getMapValue(map, "et",null)));
					}					
					String envData = JSON.toJSONString(env,SerializerFeature.WriteMapNullValue);
					//检验设备信息 详见A.6
					EquipmentData equipment= new com.dawnpro.entity.EquipmentData(
							MapUtil.getMapValue(map, "analyManuf", ""),
							MapUtil.getMapValue(map, "analyName", ""),
							MapUtil.getMapValue(map, "analyModel", ""),
							MapUtil.getMapValue(map, "analyDate", ""),//允许空，YYYYMMDD
							MapUtil.getMapValue(map, "dynoModel", ""),
							MapUtil.getMapValue(map, "dynoManuf", ""));
					String equipmentData = JSON.toJSONString(equipment,SerializerFeature.WriteMapNullValue);
					//检验过程信息 详见A.4
					String fdjmoduleid=MapUtil.getMapValue(map, "fdjmoduleid", "");
					com.dawnpro.entity.ModuleData module1=new com.dawnpro.entity.ModuleData(
							MapUtil.getMapValue(map, "fdjmoduleid", ""),
							MapUtil.getMapValue(map, "fdjcalid", ""),
							MapUtil.getMapValue(map, "fdjcvn", ""));
					String hclmoduleid=MapUtil.getMapValue(map, "hclmoduleid", "");
					com.dawnpro.entity.ModuleData module2=new com.dawnpro.entity.ModuleData(
							MapUtil.getMapValue(map, "hclmoduleid", ""),
							MapUtil.getMapValue(map, "hclcalid", ""),
							MapUtil.getMapValue(map, "hclcvn", ""));
					String othermoduleid=MapUtil.getMapValue(map, "othermoduleid", "");
					com.dawnpro.entity.ModuleData module3=new com.dawnpro.entity.ModuleData(
							MapUtil.getMapValue(map, "othermoduleid", ""),
							MapUtil.getMapValue(map, "othercalid", ""),
							MapUtil.getMapValue(map, "othercvn", ""));
					List<com.dawnpro.entity.ModuleData> moduleData=new ArrayList<com.dawnpro.entity.ModuleData>();
					if(!StringTools.isBlank(fdjmoduleid)){
						moduleData.add(module1);
					}
					if(!StringTools.isBlank(hclmoduleid)){
						moduleData.add(module2);
					}
					if(!StringTools.isBlank(othermoduleid)){
						moduleData.add(module3);
					}
					if(StringTools.isBlank(fdjmoduleid)&&StringTools.isBlank(hclmoduleid)&&StringTools.isBlank(othermoduleid)){
						moduleData.add(module1);
					}
					ProcessData process=new ProcessData();
					if(MapUtil.getMapValue(map, "odo",null)==null)
						process.setOdo(null);
					else{
						process.setOdo(Double.valueOf(MapUtil.getMapValue(map, "odo",null)));
					}
					process.setObd(MapUtil.getMapValue(map, "obd", ""));
					process.setModuleData(moduleData);
					String processData = JSON.toJSONString(process,SerializerFeature.WriteMapNullValue);
					Map process1=new HashMap();
					process1.put("obd", MapUtil.getMapValue(map, "obd", ""));
					process1.put("odo",MapUtil.getMapValue(map, "odo",null));
					process1.put("moduleData",moduleData);
					String processData1=JSON.toJSONString(process1,SerializerFeature.WriteMapNullValue); 
					//检验结果信息 详见A.5
					ResultData resultInfo=new ResultData();
					//1-双怠速法
					SdsData sdsData=null;
					if("1".equals(MapUtil.getMapValue(map, "testtype","0"))){
						sdsData=new SdsData(
								Double.valueOf(MapUtil.getMapValue(map, "hlco",null)), 
								Double.valueOf(MapUtil.getMapValue(map, "lrco",null)), 
								Double.valueOf(MapUtil.getMapValue(map, "reac",null)), 
								Integer.valueOf(MapUtil.getMapValue(map, "lrhc",null)), 
								Integer.valueOf(MapUtil.getMapValue(map, "hrhc",null)), 
								Double.valueOf(MapUtil.getMapValue(map, "hrco",null)), 
								Double.valueOf(MapUtil.getMapValue(map, "leacmin",null)), 
								Double.valueOf(MapUtil.getMapValue(map, "leacmax",null)), 
								Integer.valueOf(MapUtil.getMapValue(map, "llhc",null)), 
								Double.valueOf(MapUtil.getMapValue(map, "llco",null)), 
								Integer.valueOf(MapUtil.getMapValue(map, "hlhc",null)));
						String sds=JSON.toJSONString(sdsData,SerializerFeature.WriteMapNullValue);
						resultInfo.setSdsData(sdsData);						
					}
					//2-稳态工况法
					AsmData asmData=null;
					if("2".equals(MapUtil.getMapValue(map, "testtype","0"))){
						resultInfo.setAsmData(new AsmData(
								Double.valueOf(SignatureGenarator.removeLastZero(MapUtil.getMapValue(map, "alco2540",null))), 
								Double.valueOf(SignatureGenarator.removeLastZero(MapUtil.getMapValue(map, "alco5025",null))), 
								Double.valueOf(SignatureGenarator.removeLastZero(MapUtil.getMapValue(map, "arco5025",null))), 
								Double.valueOf(SignatureGenarator.removeLastZero(MapUtil.getMapValue(map, "arco2540",null))), 
								Integer.valueOf(SignatureGenarator.removeLastZero(MapUtil.getMapValue(map, "alhc2540",null))), 
								Integer.valueOf(SignatureGenarator.removeLastZero(MapUtil.getMapValue(map, "arhc2540",null))), 
								Integer.valueOf(SignatureGenarator.removeLastZero(MapUtil.getMapValue(map, "alhc5025",null))), 
								Integer.valueOf(SignatureGenarator.removeLastZero(MapUtil.getMapValue(map, "alnox5025",null))), 
								Integer.valueOf(SignatureGenarator.removeLastZero(MapUtil.getMapValue(map, "arhc5025",null))), 
								Integer.valueOf(SignatureGenarator.removeLastZero(MapUtil.getMapValue(map, "arnox2540",null))), 
								Integer.valueOf(SignatureGenarator.removeLastZero(MapUtil.getMapValue(map, "arnox5025",null))), 
								Integer.valueOf(SignatureGenarator.removeLastZero(MapUtil.getMapValue(map, "alnox2540",null)))));						
					}
					//4-加载减速法
					LugdownData lugdownData=null;
					if("4".equals(MapUtil.getMapValue(map, "testtype","0"))){
						lugdownData=new LugdownData(
								Integer.valueOf(SignatureGenarator.removeLastZero(MapUtil.getMapValue(map, "rev100",null))),
								Integer.valueOf(SignatureGenarator.removeLastZero(MapUtil.getMapValue(map, "rateRevUp",null))),
								Double.valueOf(SignatureGenarator.removeLastZero(MapUtil.getMapValue(map, "smokeLimit",null))),
								Integer.valueOf(SignatureGenarator.removeLastZero(MapUtil.getMapValue(map, "nox",null))),
								Double.valueOf(SignatureGenarator.removeLastZero(MapUtil.getMapValue(map, "smoke100",null))),
								Integer.valueOf(SignatureGenarator.removeLastZero(MapUtil.getMapValue(map, "rateRevDown",null))),
								Double.valueOf(SignatureGenarator.removeLastZero(MapUtil.getMapValue(map, "maxPower",null))), 
								Double.valueOf(SignatureGenarator.removeLastZero(MapUtil.getMapValue(map, "maxPowerLimit",null))),
								Double.valueOf(SignatureGenarator.removeLastZero(MapUtil.getMapValue(map, "smoke80",null))), 
								Integer.valueOf(SignatureGenarator.removeLastZero(MapUtil.getMapValue(map, "noxLimit",null))));
						resultInfo.setLugdownData(lugdownData);	
					}
					//6-自由加速法
					if("6".equals(MapUtil.getMapValue(map, "testtype","0"))){
						FaData faData=new FaData(
								Integer.valueOf(SignatureGenarator.removeLastZero(MapUtil.getMapValue(map, "rateRev",null))),
								Integer.valueOf(SignatureGenarator.removeLastZero(MapUtil.getMapValue(map, "rev",null))),
								Double.valueOf(SignatureGenarator.removeLastZero(MapUtil.getMapValue(map, "smokeK1",null))),
								Double.valueOf(SignatureGenarator.removeLastZero(MapUtil.getMapValue(map, "smokeK2",null))),
								Double.valueOf(SignatureGenarator.removeLastZero(MapUtil.getMapValue(map, "smokeK3",null))),
								Double.valueOf(SignatureGenarator.removeLastZero(MapUtil.getMapValue(map, "smokeAvg",null))),
								Double.valueOf(SignatureGenarator.removeLastZero(MapUtil.getMapValue(map, "smokeKLimit",null))));
						resultInfo.setFaData(faData);
					}
					String resultData=JSON.toJSONString(resultInfo,SerializerFeature.WriteMapNullValue);
					//检验检测信息 详见A.3
					TestData test=new com.dawnpro.entity.TestData(
							Long.valueOf(SignatureGenarator.removeLastZero(MapUtil.getMapValue(map, "testtype",null))),
							MapUtil.getMapValue(map, "testno",""),
							MapUtil.getMapValue(map, "testdate",""),
							Long.valueOf(SignatureGenarator.removeLastZero(MapUtil.getMapValue(map, "opass",null))),
							MapUtil.getMapValue(map, "otestdate",""),
							Long.valueOf(SignatureGenarator.removeLastZero(MapUtil.getMapValue(map, "result",null))),
							MapUtil.getMapValue(map, "ctest",""),
							MapUtil.getMapValue(map, "ctestlocation",""));
					//A3中testType字段为0时testdate要求统一传空字符串
					if("0".equals(MapUtil.getMapValue(map, "testtype","0"))){
						test.setTestDate("");
					}
					if(MapUtil.getMapValue(map, "apass",null)!=null){
						test.setApass(Long.valueOf(MapUtil.getMapValue(map, "apass","")));
					}	
					if(MapUtil.getMapValue(map, "epass",null)!=null){
						test.setEpass(Long.valueOf(MapUtil.getMapValue(map, "epass","")));
					}	
					String testData=JSON.toJSONString(test,SerializerFeature.WriteMapNullValue);
					//检验车辆数据 详见A.1
					VehicleData vehicle = new VehicleData(
							MapUtil.getMapValue(map, "vehiclemodel",""),
							MapUtil.getMapValue(map, "vin",""),
							MapUtil.getMapValue(map, "xxgkbh",""),
							MapUtil.getMapValue(map, "sb",""),
							MapUtil.getMapValue(map, "sccdz",""),
							MapUtil.getMapValue(map, "scdate",""),
							MapUtil.getMapValue(map, "fdjh",""),
							MapUtil.getMapValue(map, "fdjsb",""),
							MapUtil.getMapValue(map, "fdjsccdz",""),
							MapUtil.getMapValue(map, "sccmc","东风商用车有限公司"));
					String vehicleData=JSON.toJSONString(vehicle,SerializerFeature.WriteMapNullValue);
					String sign=SignatureGenarator.generate(token,envData,equipmentData,processData,resultData,testData,vehicleData);
					Root root=new Root(vehicle,
							           env,
							           test,
							           process,
							           resultInfo,
							           equipment,
							           token,
							           sign);		
					    String rootData=JSON.toJSONString(root,SerializerFeature.WriteMapNullValue); 
					    String id=MapUtil.getMapValue(map, "id", "");
					    String vin=MapUtil.getMapValue(map, "vin", "");
						//发送数据
						String res=null;
						String trans=null;
						try {
							   res=HttpUtil.doPostJson(uploadUrl, rootData);
							   JSONObject jsonObject = JSONObject.parseObject(res);
							   trans=jsonObject.getString("status");
						} catch (Exception e) {
							e.printStackTrace();
						}
						if ("success".equals(trans)) {
							sqlList.add("update TBL_OFFLINETEST_REPORT set uptime=sysdate,upstate=2,upmemo='上传成功' where vin='" +vin + "' and id="+id+"");
							if (this.dao.excuteBatch(sqlList)) {
								result = "ok";
								Loggers.PERFORMANCE.info("[新车下线检验报告上传] 上传结果：成功-" + vin);
							}
							sqlList.removeAll(sqlList);
						} else {
							FailureResult failResult = JSON.parseObject(res, FailureResult.class); 
							List<Errors> error=failResult.getErrors();
							String resStr ="";
							for(int i1=0;i1<error.size();i1++){
								resStr+=error.get(i1).getField();
								resStr+=":";
								resStr+=error.get(i1).getErrmsg();
								resStr+=";";
							}
							//失败日志限制100个字节
							if(getWordCount(resStr)>100)
							resStr=bSubstring(resStr,100);
									if(-1!=(resStr.indexOf("超过48小时"))){
										sqlList.add("update tbl_offlinetest set uptime=sysdate,upstate=1,upmemo='该Vin数据已上传超过48小时，不能覆盖更新' where vin='" + vin + "' and id="+id+"");
									}
									else if(-1!=(resStr.indexOf("验签失败:006;"))){
										//验签失败:
										sqlList.add("update tbl_offlinetest set uptime=sysdate,upstate=1,upmemo='" + resStr+"，请联系管理员！" + "' where vin='" + vin + "' and id="+id+"");
									}
									else if(-1!=(resStr.indexOf("token凭据已过期"))){
										//token凭据已过期:
										token=mepgkLogin(ts_factory);
										sqlList.add("update tbl_offlinetest set uptime=sysdate,upstate=1,upmemo='" + resStr+"，请联系管理员！" + "' where vin='" + vin + "' and id="+id+"");
									}
									else{
										sqlList.add("update tbl_offlinetest set uptime=sysdate,upstate=1,upmemo='" + resStr + "' where vin='" +vin + "' and id="+id+" ");
									}
							this.dao.excuteBatch(sqlList);
							Loggers.PERFORMANCE.info("[新车下线检验报告数据上传] 上传结果：失败-" + vin + ",原因-" + resStr);
							sqlList.removeAll(sqlList);
						}
				}
			}
		} catch (DAOException e) {
			e.printStackTrace();
		} catch (TransactionException e) {
			e.printStackTrace();
		}
		return result;
	}
	public static void main(String[] args) {
		String n ="4";
		String m ="5";
		System.out.println(Double.valueOf(n));
		System.out.println(Double.parseDouble(m));
		
	}
	/**
	 * 按照字节长度进行计算长度
	 * @param s
	 * @return
	 */
    public  int getWordCount(String s)
    {

        s = s.replaceAll("[^\\x00-\\xff]", "**");
        int length = s.length();
        return length;
    }
    /**
     * 按照字节长度进行截取字符串
     * @param s
     * @param length
     * @return
     * @throws Exception
     */
    public String bSubstring(String s, int length)
    {
        byte[] bytes = null;
		try {
			bytes = s.getBytes("Unicode");
        int n = 0; // 表示当前的字节数
        int i = 2; // 要截取的字节数，从第3个字节开始
        for (; i < bytes.length && n < length; i++)
        {
            // 奇数位置，如3、5、7等，为UCS2编码中两个字节的第二个字节
            if (i % 2 == 1)
            {
                n++; // 在UCS2第二个字节时n加1
            }
            else
            {
                // 当UCS2编码的第一个字节不等于0时，该UCS2字符为汉字，一个汉字算两个字节
                if (bytes[i] != 0)
                {
                    n++;
                }
            }
        }
        // 如果i为奇数时，处理成偶数
        if (i % 2 == 1)

        {
            // 该UCS2字符是汉字时，去掉这个截一半的汉字
            if (bytes[i - 1] != 0)
                i = i - 1;
            // 该UCS2字符是字母或数字，则保留该字符
            else
                i = i + 1;
        }
			return new String(bytes, 0, i, "Unicode");
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
		}
		return s;
    }
}
