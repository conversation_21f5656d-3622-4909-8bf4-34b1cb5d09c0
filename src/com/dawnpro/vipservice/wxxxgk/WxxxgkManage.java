package com.dawnpro.vipservice.wxxxgk;

import java.io.UnsupportedEncodingException;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import com.dawnpro.commons.SystemParam;
import com.dawnpro.commons.util.MapUtil;
import com.dawnpro.commons.util.db.DAOException;
import com.dawnpro.commons.util.db.TransactionException;
import com.dawnpro.core.log.Loggers;
import com.dawnpro.entity.bsjx.BsjxHgzinfo;
import com.dawnpro.entity.bsjx.BsjxResult;
import com.dawnpro.entity.wxxxgk.WxxxgkHgzinfo;
import com.dawnpro.entity.wxxxgk.WxxxgkResult;
import com.dawnpro.service.BaseServer;
import com.dawnpro.service.commons.HttpUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import sun.misc.BASE64Encoder;

/**
 * 维修信息公开与召回系统
 * <AUTHOR>
 *
 */
public class WxxxgkManage extends BaseServer {
	
	/**
	 * 上传车型首张合格证信息到维修信息公开与召回系统
	 */
	public void uploadHgzToWxxxgk()
	{
		Loggers.INTERFACE.info("维修信息公开-接口:自动上传接口执行开始！");
		Loggers.INTERFACE.info((new StringBuilder("维修信息公开-接口：新增上传开始:")).append((new GregorianCalendar()).getTime()).toString());
		this.callService("1");
		Loggers.INTERFACE.info((new StringBuilder("维修信息公开-接口：新增上传结束:")).append((new GregorianCalendar()).getTime()).toString());
		Loggers.INTERFACE.info((new StringBuilder("维修信息公开-接口：补充上传开始:")).append((new GregorianCalendar()).getTime()).toString());
		this.callService("2");
		Loggers.INTERFACE.info((new StringBuilder("维修信息公开-接口：补充上传结束:")).append((new GregorianCalendar()).getTime()).toString());
		Loggers.INTERFACE.info("维修信息公开-接口:自动上传接口执行结束！");
	}
	
	private void callService(String type) {
		List datalist = getUploadData(type);
		
		String loginUrl = SystemParam.getKey("wxxxgk_upload_url", "https://apitest.dfmc.com.cn/WXGL_BCK/mp/rio/job/INF_ORSTF?aInterfaceID=INF_BCKCARINFO");
		String appid = SystemParam.getKey("wxxxgk_appid", "10225");
		String userKey = SystemParam.getKey("wxxxgk_userkey", "11eb7ceb2b284c4085fa7ab3704b969a");
		String userSecret = SystemParam.getKey("wxxxgk_usersecret", "49887d8a8e684028");
		
		final BASE64Encoder encoder = new BASE64Encoder();
		final String text = userKey+":"+userSecret;
		byte[] textByte = null;
		try {
			textByte = text.getBytes("UTF-8");
		} catch (UnsupportedEncodingException e2) {
			e2.printStackTrace();
		}
		//编码
		final String encodedText = encoder.encode(textByte);
		
		HashMap headers = new HashMap();
		headers.put("X-App-Id", appid);
		headers.put("X-Sequence-No", UUID.randomUUID().toString().replace("-", "").toLowerCase());
		headers.put("X-Timestamp", String.valueOf(System.currentTimeMillis()));
		headers.put("Authorization", "Basic "+encodedText);
		headers.put("Content-Type", "application/json");
		
		
		Loggers.INTERFACE.info("维修信息公开-接口:待上传数据条数："+datalist.size());
		for (int i = 0; i < datalist.size(); i++)
		{
			ObjectMapper mapper = new ObjectMapper();
			String postData = null;
			WxxxgkHgzinfo wxxxgkHgzinfo = new WxxxgkHgzinfo();
			String id = "";
			Map hgzmap = new HashMap();
			hgzmap =(Map) datalist.get(i);
			
			id = MapUtil.getMapValue(hgzmap, "id", "");
			wxxxgkHgzinfo = (WxxxgkHgzinfo) hgzmap.get("hgz");
			
			try {
				postData = mapper.writeValueAsString(wxxxgkHgzinfo);
				Loggers.INTERFACE.debug("维修信息公开-接口，数据id["+id+"]，发送报文："+postData);
				String result = HttpUtil.doPostJson(loginUrl, postData,headers,null);
				Loggers.INTERFACE.debug("维修信息公开-接口，数据id["+id+"]，接口反馈："+result);
				
				WxxxgkResult bResult=mapper.readValue(result,WxxxgkResult.class);
				
				updateWxxxgk(bResult,id);
				
			} catch (JsonProcessingException e1) {
				Loggers.INTERFACE.debug("维修信息公开-接口：数据转换json报错");
				e1.printStackTrace();
			}	catch (Exception e) {
				Loggers.INTERFACE.debug("维修信息公开-接口：报错："+e.toString());
				e.printStackTrace();
			}
		}
	}

	private void updateWxxxgk(WxxxgkResult bResult,String id) {
		// TODO Auto-generated method stub
		Loggers.INTERFACE.debug("维修信息公开-接口：回填结果数据开始");
		String sendflag="0";
		String sendmemo="error";
		if(!"".equals(id)) {
			if("OK".equals(bResult.getState())) {
				sendflag="1";
				sendmemo=bResult.getInfo();
			}else {
				sendflag="0";
				sendmemo=bResult.getInfo();
			}
			String sql = "update tif_to_xxgk_hgzinfo set sendflag='"+sendflag+"',sendtime=now(),sendmemo='"+sendmemo+"' where id="+id;
			try {
				this.dao.execute(sql);
			} catch (DAOException | TransactionException e) {
				e.printStackTrace();
				Loggers.INTERFACE.info("维修信息公开-接口：回填结果执行SQL报错"+e.toString());
			}
		}
		
		Loggers.INTERFACE.debug("维修信息公开-接口：回填结果数据结束");
	}

	private List getUploadData(String type) {

		StringBuffer sqlBuffer = new StringBuffer();
		sqlBuffer
				.append("select id,qymc,subcompany,pp,clxh,vnm,DATE_FORMAT(fzrq,'%Y-%m-%d') fzrq,mllb,wkc,wkk,wkg,")
				.append("zs,zj,qlj,hlj,zzl,zbzl,case when edzk='' or edzk is null then jsszcrs else edzk end edzk,jsszcrs,ryzl,fdjxh,pl,ltgg,lts ")
				.append(" FROM tif_to_xxgk_hgzinfo ");
		if("1".equals(type)) {
			sqlBuffer.append(" where sendflag = 0 and sendtime is null order by id limit 0,100");
		}else {
			sqlBuffer.append(" where sendflag = 0 and sendtime is not null order by sendtime asc limit 0,100");
		}
		Map map = null;
		Map tempMap = null;
		List hgzlist = new ArrayList();
		List tempList = null;
		try {
			tempList = this.dao.queryForList(sqlBuffer.toString());
			for (int i = 0; tempList != null && i < tempList.size(); i++) {
				WxxxgkHgzinfo hgz = new WxxxgkHgzinfo();
				hgz.setProductNm(MapUtil.getMapValue((Map) tempList.get(i), "qymc", ""));
				hgz.setFactoryCode(MapUtil.getMapValue((Map) tempList.get(i), "subcompany", ""));
				hgz.setBrandNm(MapUtil.getMapValue((Map) tempList.get(i), "pp", ""));
				hgz.setCarModel(MapUtil.getMapValue((Map) tempList.get(i), "clxh", ""));
				hgz.setVinCode(MapUtil.getMapValue((Map) tempList.get(i), "vnm", ""));
				hgz.setFirstPrintDate(MapUtil.getMapValue((Map) tempList.get(i), "fzrq", ""));
				hgz.setCarType(MapUtil.getMapValue((Map) tempList.get(i), "mllb", ""));
				hgz.setShapeLength(MapUtil.getMapValue((Map) tempList.get(i), "wkc", ""));
				hgz.setShapeWidth(MapUtil.getMapValue((Map) tempList.get(i), "wkk", ""));
				hgz.setShapeHigh(MapUtil.getMapValue((Map) tempList.get(i), "wkg", ""));
				hgz.setAxleNum(MapUtil.getMapValue((Map) tempList.get(i), "zs", ""));
				hgz.setAxleSpread(MapUtil.getMapValue((Map) tempList.get(i), "zj", ""));
				hgz.setFrontTrack(MapUtil.getMapValue((Map) tempList.get(i), "qlj", ""));
				hgz.setRearTrack(MapUtil.getMapValue((Map) tempList.get(i), "hlj", ""));
				hgz.setTotalQty(MapUtil.getMapValue((Map) tempList.get(i), "zzl", ""));
				hgz.setCurbQty(MapUtil.getMapValue((Map) tempList.get(i), "zbzl", ""));
				hgz.setRatedPassenger(MapUtil.getMapValue((Map) tempList.get(i), "edzk", ""));
				hgz.setFuelType(MapUtil.getMapValue((Map) tempList.get(i), "ryzl", ""));
				hgz.setEngineCode(MapUtil.getMapValue((Map) tempList.get(i), "fdjxh", ""));
				hgz.setEngineDisplacement(MapUtil.getMapValue((Map) tempList.get(i), "pl", ""));
				hgz.setFrontTireModel(MapUtil.getMapValue((Map) tempList.get(i), "ltgg", ""));
				hgz.setRearTireModel(MapUtil.getMapValue((Map) tempList.get(i), "ltgg", ""));
				hgz.setTireNum(MapUtil.getMapValue((Map) tempList.get(i), "lts", ""));
				hgz.setTireCode(MapUtil.getMapValue((Map) tempList.get(i), "ltgg", ""));
			
				
				tempMap = new HashMap();
				tempMap.put("hgz", hgz);
				tempMap.put("id", MapUtil.getMapValue((Map) tempList.get(i), "id", ""));
				hgzlist.add(tempMap);
			}
		} catch (DAOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return hgzlist;
	}
	
	public static void main(String[] args) {
//		String result = "{\"code\":0,\"message\":\"添加成功\",\"data\":null}";
//		Loggers.INTERFACE.debug("标识解析-接口 ,接口反馈："+result);
//		
//		ObjectMapper mapper = new ObjectMapper();
//		String postData = null;
//		try {
//			WxxxgkResult bResult=mapper.readValue(result,WxxxgkResult.class);
//			System.out.println(bResult.getInfo());
//		} catch (JsonMappingException e) {
//			e.printStackTrace();
//		} catch (JsonProcessingException e) {
//			e.printStackTrace();
//		}
		
	}

	
	
		
}
