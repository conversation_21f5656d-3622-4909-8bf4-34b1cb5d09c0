package com.dawnpro.vipservice.zgs;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.naming.Context;
import javax.naming.InitialContext;
import javax.naming.NamingException;
import javax.sql.DataSource;

import org.hibernate.dialect.function.NvlFunction;

import com.dawnpro.commons.util.StringTools;
import com.dawnpro.core.log.Loggers;
import com.dawnpro.entity.zgs.BaseHgzcs;
import com.dawnpro.entity.zgs.BaseXcxxjccs;
import com.dawnpro.entity.zgs.Hgzinfo;
import com.dawnpro.entity.zgs.ProductData;
import com.dawnpro.entity.zgs.QueryHgzxx;

public class ZgsManage{
	
	/**
	 * 检查数据库连接是否正常
	 */
	public void checkDBconnect() {
		Connection conn = getDBconnect();
		CallableStatement proc = null;
		Statement stmt = null;
		
        try {
        	// 使用Connection来创建一个Statement对象
            stmt = conn.createStatement();
         // 执行SQL,返回boolean值表示是否包含ResultSet
			boolean hasResultSet = stmt.execute("select now() DBtime");
			if(hasResultSet) {
            	// 如果执行后有ResultSet结果集
	            ResultSet rs = stmt.getResultSet();
	            while(rs !=null && rs.next()){
	            	String time = rs.getString("DBtime");
	            	Loggers.INTERFACE.info("子单位接口-数据库心跳正常，数据库时间戳："+time);
				}
			}else {
				Loggers.INTERFACE.info("子单位接口-数据库心跳异常，请重启接口用");
			}
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}finally {
			try {
				if (proc != null) {
					proc.close();
				}
				if (stmt != null) {
					stmt.close();
				}
				if (conn != null) {
					conn.close();
				}
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}
	}
	
	public Connection getDBconnect() {
		Connection conn =null;
		Context initCtx;
		try {
			initCtx = new InitialContext();
			DataSource ds = (DataSource) initCtx.lookup("java:comp/env/jdbc_hgzdb");
			try {
				conn = ds.getConnection();
			} catch (SQLException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		} catch (NamingException e1) {
			// TODO Auto-generated catch block
			e1.printStackTrace();
		}
		return conn;
	}
	
	public String uploadProductData(ProductData productData) {

		String res = "fail";
		String rescheck = checkProductData(productData);
		if (!"0".equals(rescheck)) {
			// 接口数据长度或格式校验不通过，反馈不通过信息
			res = rescheck;
		} else {
			Connection conn = getDBconnect();
			CallableStatement proc = null;
			Statement stmt = null;

			try {
				proc = conn.prepareCall("{call proc_zgs_mescs_insert(?,?,?,?,?,?,?,?,?,?,?,?)}");
				proc.setString(1, productData.getDph());
				proc.setString(2, productData.getCsys());
				proc.setString(3, productData.getFdjh());
				proc.setString(4, productData.getScrq());
				proc.setString(5, productData.getBz());
				proc.setString(6, productData.getQtxx());
				proc.setString(7, productData.getVinbsyy());
				proc.setString(8, productData.getHgzfl());
				proc.setString(9, productData.getScm());
				proc.setString(10, productData.getIsyclz());
				proc.setString(11, productData.getFactorycode());
				proc.registerOutParameter(12, java.sql.Types.VARCHAR);
				proc.execute();

				res = proc.getString(12);

			} catch (Exception e) {
				e.printStackTrace();
			} finally {
				try {
					if (proc != null) {
						proc.close();
					}
					if (stmt != null) {
						stmt.close();
					}
					if (conn != null) {
						conn.close();
					}
				} catch (SQLException e) {
					e.printStackTrace();
				}
			}
		}
		return res;
	}
	
	private String checkProductData(ProductData productData) {
		 String res="0";
		 String dph=productData.getDph();
		 String csys=productData.getCsys();
		 String fdjh=productData.getFdjh();
		 String scrq=productData.getScrq();
		 String bz=productData.getBz();
		 String qtxx=productData.getQtxx();
		 String vinbsyy=productData.getVinbsyy();
		 String hgzfl=productData.getHgzfl();
		 String scm=productData.getScm();
		 String isyclz=productData.getIsyclz();
		 String factorycode=productData.getFactorycode();
		 
		 if(StringTools.isBlank(dph )) {
			 res="底盘号不能为空";
			 return res;
		 }
		 if(dph.trim().length()!=8) {
			 res="底盘号长度只能为8个字符";
			 return res;
		 }
		 
		 if(StringTools.isBlank(csys )) {
			 res="车身颜色不能为空";
			 return res;
		 }
		 if(csys.trim().length()>70) {
			 res="车身颜色长度只能小于70个字符";
			 return res;
		 }
		 
		 
		 if(!StringTools.isBlank(fdjh)) {
			 if(fdjh.trim().length()>30) {
				 res="发动机号长度只能小于30个字符";
				 return res;
			 }
		 }
		 
		 
		 if(StringTools.isBlank(scrq)) {
			 res="生产日期不能为空";
			 return res;
		 }
		 if(scrq.trim().length()!=10) {
			 res="生产日期长度只能为10个字符，且格式应为yyyy-mm-dd";
			 return res;
		 }
		 if(!isDate(scrq)) {
			 res="生产日期长度只能为10个字符，且格式应为yyyy-mm-dd";
			 return res;
		 }
		 
		 
		 if(!StringTools.isBlank(bz)) {
			 if(bz.trim().length()>260) {
				 res="备注只能小于260个字符";
				 return res;
			 }
			 if(length(bz)>260) {
				 res="备注长度只能小于130个汉字或260个英文";
				 return res;
			 }
		 }
		 
		 if(!StringTools.isBlank(qtxx)) {
			 
			 if("VOYAH".equals(factorycode)) {
				 if(qtxx.length()>400) {
					 res="其他信息不能大于400个字符";
					 return res;
				 }
				 if(length(qtxx)>400) {
					 res="其他信息不能大于200个汉字或400个英文";
					 return res;
				 }
			 }else {
				 if(qtxx.trim().length()>100) {
					 res="其他信息只能小于100个字符";
					 return res;
				 }
			 }
			 
		 }
		 
		 if(StringTools.isBlank(vinbsyy)) {
			 res="VIN重新标示原因不能为空";
			 return res;
		 }
		 if(vinbsyy.trim().length()!=1) {
			 res="VIN重新标示原因只能为1个字符";
			 return res;
		 }
		 if(!"0".equals(vinbsyy.trim()) && !"1".equals(vinbsyy.trim()) 
				 &&!"2".equals(vinbsyy) &&!"3".equals(vinbsyy)  ) {
			 res="VIN重新标示原因只能在[0、1、2、3]中";
			 return res;
		 }
		 
		 if(StringTools.isBlank(hgzfl)) {
			 res="合格证分类不能为空";
			 return res;
		 }
		 if(hgzfl.length()!=1) {
			 res="合格证分类只能为1个字符";
			 return res;
		 }
		 if(!"0".equals(hgzfl) && !"1".equals(hgzfl)  ) {
			 res="合格证分类的值只能在[0、1]中";
			 return res;
		 }
		 
		 if(StringTools.isBlank(scm)) {
			 res="生产码不能为空";
			 return res;
		 }
		 if(scm.length()>100) {
			 res="生产码只能小于100个字符";
			 return res;
		 }
		 
		 if(StringTools.isBlank(isyclz)) {
			 res="是否一车两证不能为空";
			 return res;
		 }
		 if(isyclz.length()!=1) {
			 res="是否一车两证只能为1个字符";
			 return res;
		 }
		 if(!"0".equals(isyclz) && !"1".equals(isyclz)  ) {
			 res="是否一车两证的值只能在[0、1]中";
			 return res;
		 }
		 
		 if(StringTools.isBlank(factorycode)) {
			 res="工厂代码不能为空";
			 return res;
		 }
		 if(factorycode.length()>20) {
			 res="工厂代码只能小于20个字符";
			 return res;
		 }
		 
		 return res;

	}
	
	/**
	 * 上传合格证基础参数信息
	 * @param info
	 * @return
	 */
	public String uploadBaseHgzcs(BaseHgzcs info) {

		String res = "fail";
		String rescheck = checkBaseHgzcs(info);
		if (!"0".equals(rescheck)) {
			// 接口数据长度或格式校验不通过，反馈不通过信息
			res = rescheck;
		} else {
			Connection conn = getDBconnect();
			CallableStatement proc = null;
			Statement stmt = null;

			try {
				//57个参数+1个反馈参数
				proc = conn.prepareCall("{call proc_zgs_hgzcs_insert(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}");
				
				proc.setString(1, info.getScm());
				proc.setString(2, info.getQymc());
				proc.setString(3, info.getPp());
				proc.setString(4, info.getClmc());
				proc.setString(5, info.getMllb());
				proc.setString(6, info.getClxh());
				proc.setString(7, info.getVnm());
				proc.setString(8, info.getDpxh());
				proc.setString(9, info.getDpid());
				proc.setString(10, info.getFdjxh());
				proc.setString(11, info.getRyzl());
				proc.setString(12, info.getPl());
				proc.setString(13, info.getGl());
				proc.setString(14, info.getPfbz());
				proc.setString(15, info.getYh());
				proc.setString(16, info.getWkc());
				proc.setString(17, info.getWkk());
				proc.setString(18, info.getWkg());
				proc.setString(19, info.getHxc());
				proc.setString(20, info.getHxk());
				proc.setString(21, info.getHxg());
				proc.setString(22, info.getThps());
				proc.setString(23, info.getLts());
				proc.setString(24, info.getLtgg());
				proc.setString(25, info.getQlj());
				proc.setString(26, info.getHlj());
				proc.setString(27, info.getZj());
				proc.setString(28, info.getZh());
				proc.setString(29, info.getZs());
				proc.setString(30, info.getZxxs());
				proc.setString(31, info.getZzl());
				proc.setString(32, info.getZbzl());
				proc.setString(33, info.getEdzl());
				proc.setString(34, info.getZzlxs());
				proc.setString(35, info.getZqyzl());
				proc.setString(36, info.getBganzzdzl());
				proc.setString(37, info.getJsszcrs());
				proc.setString(38, info.getEdzk());
				proc.setString(39, info.getZgcs());
				proc.setString(40, info.getBz());
				proc.setString(41, info.getQybz());
				proc.setString(42, info.getClscdwmc());
				proc.setString(43, info.getScdz());
				proc.setString(44, info.getQtxx());
				proc.setString(45, info.getCpno());
				proc.setString(46, info.getPh());
				proc.setString(47, info.getSxrq());
				proc.setString(48, info.getPzxlh());
				proc.setString(49, info.getJfpzid());
				proc.setString(50, info.getMzxny());
				proc.setString(51, info.getJsgc());
				proc.setString(52, info.getIscxnf());
				proc.setString(53, info.getZyzycmsbs());
				proc.setString(54, info.getZxzs());
				proc.setString(55, info.getCddbj());
				proc.setString(56, info.getDpcpno());
				proc.setString(57, info.getFactorycode());

				proc.registerOutParameter(58, java.sql.Types.VARCHAR);
				proc.execute();

				res = proc.getString(58);

			} catch (Exception e) {
				e.printStackTrace();
			} finally {
				try {
					if (proc != null) {
						proc.close();
					}
					if (stmt != null) {
						stmt.close();
					}
					if (conn != null) {
						conn.close();
					}
				} catch (SQLException e) {
					e.printStackTrace();
				}
			}
		}
		return res;
	}
	
	private String checkBaseHgzcs(BaseHgzcs info) {
		 String res="0";
		 
		 String scm=info.getScm();
		 String qymc=info.getQymc();
		 String pp=info.getPp();
		 String clmc=info.getClmc();
		 String mllb=info.getMllb();
		 String clxh=info.getClxh();
		 String vnm=info.getVnm();
		 String dpxh=info.getDpxh();
		 String dpid=info.getDpid();
		 String fdjxh=info.getFdjxh();
		 String ryzl=info.getRyzl();
		 String pl=info.getPl();
		 String gl=info.getGl();
		 String pfbz=info.getPfbz();
		 String yh=info.getYh();
		 String wkc=info.getWkc();
		 String wkk=info.getWkk();
		 String wkg=info.getWkg();
		 String hxc=info.getHxc();
		 String hxk=info.getHxk();
		 String hxg=info.getHxg();
		 String thps=info.getThps();
		 String lts=info.getLts();
		 String ltgg=info.getLtgg();
		 String qlj=info.getQlj();
		 String hlj=info.getHlj();
		 String zj=info.getZj();
		 String zh=info.getZh();
		 String zs=info.getZs();
		 String zxxs=info.getZxxs();
		 String zzl=info.getZzl();
		 String zbzl=info.getZbzl();
		 String edzl=info.getEdzl();
		 String zzlxs=info.getZzlxs();
		 String zqyzl=info.getZqyzl();
		 String bganzzdzl=info.getBganzzdzl();
		 String jsszcrs=info.getJsszcrs();
		 String edzk=info.getEdzk();
		 String zgcs=info.getZgcs();
		 String bz=info.getBz();
		 String qybz=info.getQybz();
		 String clscdwmc=info.getClscdwmc();
		 String scdz=info.getScdz();
		 String qtxx=info.getQtxx();
		 String cpno=info.getCpno();
		 String ph=info.getPh();
		 String sxrq=info.getSxrq();
		 String pzxlh=info.getPzxlh();
		 String jfpzid=info.getJfpzid();
		 String mzxny=info.getMzxny();
		 String jsgc=info.getJsgc();
		 String iscxnf=info.getIscxnf();
		 String zyzycmsbs=info.getZyzycmsbs();
		 String zxzs=info.getZxzs();
		 String cddbj=info.getCddbj();
		 String dpcpno=info.getDpcpno();
		 String factorycode=info.getFactorycode();
		 
		 
		//scm
		 if(StringTools.isBlank(scm)) {
			 res="生产码不能为空";
			 return res;
		 }
		 if(scm.length()>100) {
			 res="生产码长度不能大于100个字符";
			 return res;
		 }
		//qymc
		 if(StringTools.isBlank(qymc)) {
			 res="企业名称不能为空";
			 return res;
		 }
		 if(qymc.length()>64) {
			 res="企业名称长度不能大于64个字符";
			 return res;
		 }
		//pp
		 if(StringTools.isBlank(pp)) {
			 res="车辆品牌不能为空";
			 return res;
		 }
		 if(pp.length()>30) {
			 res="车辆品牌长度不能大于30个字符";
			 return res;
		 }
		//clmc
		 if(StringTools.isBlank(clmc)) {
			 res="车辆名称不能为空";
			 return res;
		 }
		 if(clmc.length()>54) {
			 res="车辆名称长度不能大于54个字符";
			 return res;
		 }
		//mllb 0-11
		 if(StringTools.isBlank(mllb)) {
			 res="车辆分类不能为空";
			 return res;
		 }
		 if(mllb.length()>2) {
			 res="车辆分类长度不能大于2个字符";
			 return res;
		 }
		 if("0,1,2,3,4,5,6,7,8,9,10,11,".indexOf(mllb+",")==-1 ) {
			 res="车辆分类只能在[0,1,2,3,4,5,6,7,8,9,10,11]中";
			 return res;
		 }
		//clxh
		 if(StringTools.isBlank(clxh)) {
			 res="车辆型号不能为空";
			 return res;
		 }
		 if(clxh.length()>30) {
			 res="车辆型号长度不能大于30个字符";
			 return res;
		 }
		//vnm
		 if(StringTools.isBlank(vnm)) {
			 res="车辆识别代号前8位不能为空";
			 return res;
		 }
		 if(vnm.length()!=8) {
			 res="车辆识别代号前8位长度应为8个字符";
			 return res;
		 }
		//dpxh
		 if(!StringTools.isBlank(dpxh)) {
			 if(dpxh.length()>30) {
				 res="底盘型号长度不能大于30个字符";
				 return res;
			 }
		 }
		 
		//dpid
		 if(!StringTools.isBlank(dpid)) {
			 if(dpid.length()>7) {
				 res="底盘ID长度不能大于7个字符";
				 return res;
			 }
		 }
		//fdjxh
		 if(!StringTools.isBlank(fdjxh)) {
			 if(fdjxh.length()>20) {
				 res="发动机型号长度不能大于20个字符";
				 return res;
			 }
		 }
		//ryzl
		 if(StringTools.isBlank(ryzl)) {
			 res="燃料种类不能为空";
			 return res;
		 }
		 if(ryzl.length()>30) {
			 res="燃料种类长度不能大于30个字符";
			 return res;
		 }
		//pl
		 if(!StringTools.isBlank(pl)) {
			 if(pl.length()>5) {
				 res="排量长度不能大于5个字符";
				 return res;
			 }
		 }
		//gl
		 if(StringTools.isBlank(gl)) {
			 res="功率不能为空";
			 return res;
		 }
		 if(gl.length()>7) {
			 res="功率长度不能大于7个字符";
			 return res;
		 }
		//pfbz
		 if(StringTools.isBlank(pfbz)) {
			 res="排放标准不能为空";
			 return res;
		 }
		 if(pfbz.length()>60) {
			 res="排放标准长度不能大于60个字符";
			 return res;
		 }
		//yh
		 if(!StringTools.isBlank(yh)) {
			 if(yh.length()>30) {
				 res="油耗不能大于30个字符";
				 return res;
			 }
		 }
		//wkc
		 if(StringTools.isBlank(wkc)) {
			 res="外廓尺寸长不能为空";
			 return res;
		 }
		 if(wkc.length()>5) {
			 res="外廓尺寸长长度不能大于5个字符";
			 return res;
		 }
		//wkk
		 if(StringTools.isBlank(wkk)) {
			 res="外廓尺寸宽不能为空";
			 return res;
		 }
		 if(wkk.length()>4) {
			 res="外廓尺寸宽长度不能大于4个字符";
			 return res;
		 }
		//wkg
		 if(!StringTools.isBlank(wkg)) {
			 if(wkg.length()>4) {
				 res="外廓尺寸高长度不能大于4个字符";
				 return res;
			 }
		 }
		//hxc
		 if(!StringTools.isBlank(hxc)) {
			 if(hxc.length()>5) {
				 res="货厢内部尺寸长不能大于5个字符";
				 return res;
			 }
		 }
		//hxk
		 if(!StringTools.isBlank(hxk)) {
			 if(hxk.length()>4) {
				 res="货厢内部尺寸宽不能大于4个字符";
				 return res;
			 }
		 }
		//hxg
		 if(!StringTools.isBlank(hxg)) {
			 if(hxg.length()>4) {
				 res="货厢内部尺寸高不能大于4个字符";
				 return res;
			 }
		 }
		//thps
		 if(!StringTools.isBlank(thps)) {
			 if(thps.length()>30) {
				 res="钢板弹簧片数不能大于30个字符";
				 return res;
			 }
		 }
		//lts
		 if(StringTools.isBlank(lts)) {
			 res="轮胎数不能为空";
			 return res;
		 }
		 if(lts.length()>2) {
			 res="轮胎数长度不能大于2个字符";
			 return res;
		 }
		//ltgg
		 if(StringTools.isBlank(ltgg)) {
			 res="轮胎规格不能为空";
			 return res;
		 }
		 if(ltgg.length()>35) {
			 res="轮胎规格长度不能大于35个字符";
			 return res;
		 }
		//qlj
		 if(!StringTools.isBlank(qlj)) {
			 if(qlj.length()>9) {
				 res="轮距前不能大于9个字符";
				 return res;
			 }
		 }
		//hlj
		 if(!StringTools.isBlank(hlj)) {
			 if(hlj.length()>54) {
				 res="轮距后不能大于54个字符";
				 return res;
			 }
		 }
		//zj
		 if(StringTools.isBlank(zj)) {
			 res="轴距不能为空";
			 return res;
		 }
		 if(zj.length()>60) {
			 res="轴距长度不能大于60个字符";
			 return res;
		 }
		//zh
		 if(!StringTools.isBlank(zh)) {
			 if(zh.length()>30) {
				 res="轴荷不能大于30个字符";
				 return res;
			 }
		 }
		//zs
		 if(StringTools.isBlank(zs)) {
			 res="轴数不能为空";
			 return res;
		 }
		 if(zs.length()>1) {
			 res="轴数长度不能大于1个字符";
			 return res;
		 }
		//zxxs
		 if(!StringTools.isBlank(zxxs)) {
			 if(zxxs.length()>6) {
				 res="转向形式不能大于6个字符";
				 return res;
			 }
		 }
		//zzl
		 if(StringTools.isBlank(zzl)) {
			 res="总质量不能为空";
			 return res;
		 }
		 if(zzl.length()>8) {
			 res="总质量长度不能大于8个字符";
			 return res;
		 }
		//zbzl
		 if(StringTools.isBlank(zbzl)) {
			 res="整备质量不能为空";
			 return res;
		 }
		 if(zbzl.length()>8) {
			 res="整备质量长度不能大于8个字符";
			 return res;
		 }
		//edzl
		 if(!StringTools.isBlank(edzl)) {
			 if(edzl.length()>8) {
				 res="额定载质量不能大于8个字符";
				 return res;
			 }
		 }
		//zzlxs
		 if(!StringTools.isBlank(zzlxs)) {
			 if(zzlxs.length()>30) {
				 res="载质量利用系数不能大于30个字符";
				 return res;
			 }
		 }
		//zqyzl
		 if(!StringTools.isBlank(zqyzl)) {
			 if(zqyzl.length()>8) {
				 res="准牵引总质量不能大于8个字符";
				 return res;
			 }
		 }
		//bganzzdzl
		 if(!StringTools.isBlank(bganzzdzl)) {
			 if(bganzzdzl.length()>6) {
				 res="半挂车鞍座最大允许总质量不能大于8个字符";
				 return res;
			 }
		 }
		//jsszcrs
		 if(!StringTools.isBlank(jsszcrs)) {
			 if(jsszcrs.length()>3) {
				 res="驾驶室准乘人数不能大于3个字符";
				 return res;
			 }
		 }
		//edzk
		 if(!StringTools.isBlank(edzk)) {
			 if(edzk.length()>5) {
				 res="额定载客不能大于5个字符";
				 return res;
			 }
		 }
		//zgcs
		 if(!StringTools.isBlank(zgcs)) {
			 if(zgcs.length()>5) {
				 res="最高设计车速不能大于5个字符";
				 return res;
			 }
		 }
		//bz
		 if(!StringTools.isBlank(bz)) {
			 if(bz.length()>260) {
				 res="备注不能大于260个字符";
				 return res;
			 }
			 if(length(bz)>260) {
				 res="备注长度不能大于130个汉字或260个英文";
				 return res;
			 }
		 }
		//qybz
		 if(StringTools.isBlank(qybz)) {
			 res="企业标准不能为空";
			 return res;
		 }
		 if(qybz.length()>200) {
			 res="企业标准长度不能大于200个字符";
			 return res;
		 }
		//clscdwmc
		 if(StringTools.isBlank(clscdwmc)) {
			 res="车辆生产单位名称不能为空";
			 return res;
		 }
		 if(clscdwmc.length()>64) {
			 res="车辆生产单位名称长度不能大于64个字符";
			 return res;
		 }
		//scdz
		 if(StringTools.isBlank(scdz)) {
			 res="车辆生产单位地址不能为空";
			 return res;
		 }
		 if(scdz.length()>70) {
			 res="车辆生产单位地址长度不能大于70个字符";
			 return res;
		 }
		//qtxx
		 if(!StringTools.isBlank(qtxx)) {
			 if("VOYAH".equals(factorycode)) {
				 if(qtxx.length()>400) {
					 res="其他信息不能大于400个字符";
					 return res;
				 }
				 if(length(qtxx)>400) {
					 res="其他信息不能大于200个汉字或400个英文";
					 return res;
				 }
			 }else {
				 if(qtxx.length()>200) {
					 res="其他信息不能大于200个字符";
					 return res;
				 }
				 if(length(qtxx)>200) {
					 res="其他信息不能大于100个汉字或200个英文";
					 return res;
				 }
			 }
		 }
		//cpno
		 if(StringTools.isBlank(cpno)) {
			 res="产品公告号不能为空";
			 return res;
		 }
		 if(cpno.length()>50) {
			 res="产品公告号长度不能大于50个字符";
			 return res;
		 }
		//ph
		 if(StringTools.isBlank(ph)) {
			 res="公告批次不能为空";
			 return res;
		 }
		 if(ph.length()>50) {
			 res="公告批次长度不能大于50个字符";
			 return res;
		 }
		//sxrq
		 if(StringTools.isBlank(sxrq)) {
			 res="公告生效日期不能为空";
			 return res;
		 }
		 if(sxrq.trim().length()!=10) {
			 res="公告生效日期长度只能为10个字符，且格式应为yyyy-mm-dd";
			 return res;
		 }
		 if(!isDate(sxrq)) {
			 res="公告生效日期长度只能为10个字符，且格式应为yyyy-mm-dd";
			 return res;
		 }
		//pzxlh
		 if(!StringTools.isBlank(pzxlh)) {
			 if(pzxlh.length()>25) {
				 res="配置序列号不能大于25个字符";
				 return res;
			 }
		 }
		//jfpzid
		 if(!StringTools.isBlank(jfpzid)) {
			 if(jfpzid.length()>50) {
				 res="双积分配置ID不能大于50个字符";
				 return res;
			 }
		 }
		//mzxny
		 if(StringTools.isBlank(mzxny)) {
			 res="是否免征新能源不能为空";
			 return res;
		 }
		 if(mzxny.length()>1) {
			 res="是否免征新能源不能大于1个字符";
			 return res;
		 }
		 if("0,1,".indexOf(mzxny+",")==-1 ) {
			 res="是否免征新能源只能在[0,1]中";
			 return res;
		 }
		//jsgc
		 if(StringTools.isBlank(jsgc)) {
			 res="是否减税挂车不能为空";
			 return res;
		 }
		 if(jsgc.length()>1) {
			 res="是否减税挂车不能大于1个字符";
			 return res;
		 }
		 if("0,1,".indexOf(jsgc+",")==-1 ) {
			 res="是否减税挂车只能在[0,1]中";
			 return res;
		 }
		//iscxnf
		 if(StringTools.isBlank(iscxnf)) {
			 res="是否车型年份不能为空";
			 return res;
		 }
		 if(iscxnf.length()>1) {
			 res="是否车型年份不能大于1个字符";
			 return res;
		 }
		 if("0,1,".indexOf(iscxnf+",")==-1 ) {
			 res="是否车型年份只能在[0,1]中";
			 return res;
		 }
		//zyzycmsbs
		 if(StringTools.isBlank(zyzycmsbs)) {
			 res="是否专用作业车免税不能为空";
			 return res;
		 }
		 if(zyzycmsbs.length()>1) {
			 res="是否专用作业车免税不能大于1个字符";
			 return res;
		 }
		 if("0,1,".indexOf(zyzycmsbs+",")==-1 ) {
			 res="是否专用作业车免税只能在[0,1]中";
			 return res;
		 }
		//zxzs
		 if(StringTools.isBlank(zxzs)) {
			 res="转向轴个数不能为空";
			 return res;
		 }
		 if(zxzs.length()>1) {
			 res="转向轴个数不能大于1个字符";
			 return res;
		 }
		//cddbj
		 if(StringTools.isBlank(cddbj)) {
			 res="纯电动标记不能为空";
			 return res;
		 }
		 if(cddbj.length()>1) {
			 res="纯电动标记不能大于1个字符";
			 return res;
		 }
		 if("1,2,".indexOf(cddbj+",")==-1 ) {
			 res="纯电动标记只能在[1,2]中";
			 return res;
		 }
		//dpcpno
		 if(!StringTools.isBlank(dpcpno)) {
			 if(dpcpno.length()>50) {
				 res="底盘产品公告号不能大于50个字符";
				 return res;
			 }
		 }
		//factorycode
		 if(StringTools.isBlank(factorycode)) {
			 res="工厂代码不能为空";
			 return res;
		 }
		 if(factorycode.trim().length()>20) {
			 res="工厂代码只能小于20个字符";
			 return res;
		 }
		 return res;

	}
	
	
	public Map queryHgzxx(QueryHgzxx info) {
		Map resmap = new HashMap();
		String resflag = "1";//0查询得到hgzid，1 查询不到hgzid
		String resmemo = "";//flag=1时  保存反馈信息
		String reshgzid = "0";//flag=0时  保存hgzid
		String reshgzsource = "tbl";//flag=0时 保存hgzid来源：tbl 合格证信息在在用库tbl_hgzmain,tbl_hgzpara中； his  合格证信息在历史库tbl_hgzmain_his,tbl_hgzpara_his中

		Hgzinfo hgzinfo = new Hgzinfo();
		String rescheck = checkQueryHgzxx(info);
		if (!"0".equals(rescheck)) {
			// 接口数据长度或格式校验不通过，反馈不通过信息
			resflag="1";
			resmemo = rescheck;
		} else {
			Connection conn = getDBconnect();
			CallableStatement proc = null;
			Statement stmt = null;

			try {
				proc = conn.prepareCall("{call proc_zgs_queryhgzxx(?,?,?,?,?,?)}");
				proc.setString(1, info.getDph());
				proc.setString(2, info.getFactorycode());
				proc.registerOutParameter(3, java.sql.Types.VARCHAR);
				proc.registerOutParameter(4, java.sql.Types.VARCHAR);
				proc.registerOutParameter(5, java.sql.Types.VARCHAR);
				proc.registerOutParameter(6, java.sql.Types.VARCHAR);
				proc.execute();

				resflag = proc.getString(3);
				resmemo= proc.getString(4);
				reshgzid = proc.getString(5);
				reshgzsource = proc.getString(6);
				
				//查询合格证信息开始
				if(resflag.equals("0")) {
					
					String sqlhead= "SELECT\r\n" + 
							"dph,\r\n" + 
							"subcompany factorycode,\r\n" + 
							"scm,\r\n" + 
							"cycle_status cyclestatus,\r\n" + 
							"upload_status uploadstatus,\r\n" + 
							"if(ifnull(printtime,'')='','',DATE_FORMAT(printtime,'%Y-%m-%d')) printtime,\r\n" + 
							"if(ifnull(uptime,'')='','',DATE_FORMAT(uptime,'%Y-%m-%d')) uploadtime,\r\n" + 
							"hgzfl,\r\n" + 
							"hgzbh,\r\n" + 
							"fzrq,\r\n" + 
							"qymc,\r\n" + 
							"pp,\r\n" + 
							"clmc,\r\n" + 
							"mllb,\r\n" + 
							"clxh,\r\n" + 
							"vnm,\r\n" + 
							"csys,\r\n" + 
							"dpxh,\r\n" + 
							"dpid,\r\n" + 
							"dphgzbh,\r\n" + 
							"fdjxh,\r\n" + 
							"fdjh,\r\n" + 
							"ryzl,\r\n" + 
							"pl,\r\n" + 
							"gl,\r\n" + 
							"pfbz,\r\n" + 
							"yh,\r\n" + 
							"wkc,\r\n" + 
							"wkk,\r\n" + 
							"wkg,\r\n" + 
							"hxc,\r\n" + 
							"hxk,\r\n" + 
							"hxg,\r\n" + 
							"thps,\r\n" + 
							"lts,\r\n" + 
							"ltgg,\r\n" + 
							"qlj,\r\n" + 
							"hlj,\r\n" + 
							"zj,\r\n" + 
							"zh,\r\n" + 
							"zs,\r\n" + 
							"zxxs,\r\n" + 
							"zzl,\r\n" + 
							"zbzl,\r\n" + 
							"edzl,\r\n" + 
							"zzlxs,\r\n" + 
							"zqyzl,\r\n" + 
							"bganzdzl bganzzdzl,\r\n" + 
							"jsszcrs,\r\n" + 
							"edzk,\r\n" + 
							"zgcs,\r\n" + 
							"scrq,\r\n" + 
							"bz,\r\n" + 
							"qybz,\r\n" + 
							"clscdwmc,\r\n" + 
							"scdz,\r\n" + 
							"qtxx,\r\n" + 
							"hgzxh,\r\n" + 
							"cpno,\r\n" + 
							"ph,\r\n" + 
							"sxrq,\r\n" + 
							"pzxlh,\r\n" + 
							"jfpzid,\r\n" + 
							"mzxny,\r\n" + 
							"jsgc,\r\n" + 
							"iscxnf,\r\n" + 
							"vinbsyy,\r\n" + 
							"zyzycmsbs,\r\n" + 
							"qyid,\r\n" + 
							"zxzs,\r\n" + 
							"jyw,\r\n" + 
							"cddbj,\r\n" + 
							"dywym,\r\n" + 
							"dpcpno,\r\n" + 
							"fkxlh\r\n";
					String sqlend = "from tbl_hgzmain_his m,tbl_hgzpara_his p where m.hgz_id=p.hgz_id \r\n" + 
							"and m.hgz_id="+reshgzid;
					
					if(reshgzsource.equals("tbl")) {
						sqlend = "from tbl_hgzmain m,tbl_hgzpara p where m.hgz_id=p.hgz_id \r\n" + 
								"and m.hgz_id="+reshgzid;
					}

					// 使用Connection来创建一个Statement对象
		            stmt = conn.createStatement();
		         // 执行SQL,返回boolean值表示是否包含ResultSet
		            boolean hasResultSet = stmt.execute(sqlhead+sqlend);
		            if(hasResultSet) {
		            	// 如果执行后有ResultSet结果集
			            ResultSet rs = stmt.getResultSet();
			            hgzinfo = changeResutlts(rs);
		            }else {
		            	resflag="1";
		            	resmemo="合格证系统执行合格证数据查询SQL查询不到数据！";
		            }
				}
				//查询合格证信息结束

			} catch (Exception e) {
				resflag="1";
				resmemo="合格证系统执行合格证数据查询SQL报错！";
				e.printStackTrace();
			} finally {
				try {
					if (proc != null) {
						proc.close();
					}
					if (stmt != null) {
						stmt.close();
					}
					if (conn != null) {
						conn.close();
					}
				} catch (SQLException e) {
					e.printStackTrace();
				}
			}
		}
		resmap.put("flag", resflag);
		resmap.put("memo", resmemo);
		resmap.put("hgzinfo", hgzinfo);
		return resmap;
	}
	
	
	private Hgzinfo changeResutlts(ResultSet rs) {
		Hgzinfo hgzinfo = new Hgzinfo();
		try {
			while(rs !=null && rs.next()){
				hgzinfo.setDph(nvl(rs.getString("dph")));
				hgzinfo.setFactorycode(nvl(rs.getString("factorycode")));
				hgzinfo.setScm(nvl(rs.getString("scm")));
				hgzinfo.setCyclestatus(nvl(rs.getString("cyclestatus")));
				hgzinfo.setUploadstatus(nvl(rs.getString("uploadstatus")));
				hgzinfo.setPrinttime(nvl(rs.getString("printtime")));
				hgzinfo.setUploadtime(nvl(rs.getString("uploadtime")));
				hgzinfo.setHgzfl(nvl(rs.getString("hgzfl")));
				hgzinfo.setHgzbh(nvl(rs.getString("hgzbh")));
				hgzinfo.setFzrq(nvl(rs.getString("fzrq")));
				hgzinfo.setQymc(nvl(rs.getString("qymc")));
				hgzinfo.setPp(nvl(rs.getString("pp")));
				hgzinfo.setClmc(nvl(rs.getString("clmc")));
				hgzinfo.setMllb(nvl(rs.getString("mllb")));
				hgzinfo.setClxh(nvl(rs.getString("clxh")));
				hgzinfo.setVnm(nvl(rs.getString("vnm")));
				hgzinfo.setCsys(nvl(rs.getString("csys")));
				hgzinfo.setDpxh(nvl(rs.getString("dpxh")));
				hgzinfo.setDpid(nvl(rs.getString("dpid")));
				hgzinfo.setDphgzbh(nvl(rs.getString("dphgzbh")));
				hgzinfo.setFdjxh(nvl(rs.getString("fdjxh")));
				hgzinfo.setFdjh(nvl(rs.getString("fdjh")));
				hgzinfo.setRyzl(nvl(rs.getString("ryzl")));
				hgzinfo.setPl(nvl(rs.getString("pl")));
				hgzinfo.setGl(nvl(rs.getString("gl")));
				hgzinfo.setPfbz(nvl(rs.getString("pfbz")));
				hgzinfo.setYh(nvl(rs.getString("yh")));
				hgzinfo.setWkc(nvl(rs.getString("wkc")));
				hgzinfo.setWkk(nvl(rs.getString("wkk")));
				hgzinfo.setWkg(nvl(rs.getString("wkg")));
				hgzinfo.setHxc(nvl(rs.getString("hxc")));
				hgzinfo.setHxk(nvl(rs.getString("hxk")));
				hgzinfo.setHxg(nvl(rs.getString("hxg")));
				hgzinfo.setThps(nvl(rs.getString("thps")));
				hgzinfo.setLts(nvl(rs.getString("lts")));
				hgzinfo.setLtgg(nvl(rs.getString("ltgg")));
				hgzinfo.setQlj(nvl(rs.getString("qlj")));
				hgzinfo.setHlj(nvl(rs.getString("hlj")));
				hgzinfo.setZj(nvl(rs.getString("zj")));
				hgzinfo.setZh(nvl(rs.getString("zh")));
				hgzinfo.setZs(nvl(rs.getString("zs")));
				hgzinfo.setZxxs(nvl(rs.getString("zxxs")));
				hgzinfo.setZzl(nvl(rs.getString("zzl")));
				hgzinfo.setZbzl(nvl(rs.getString("zbzl")));
				hgzinfo.setEdzl(nvl(rs.getString("edzl")));
				hgzinfo.setZzlxs(nvl(rs.getString("zzlxs")));
				hgzinfo.setZqyzl(nvl(rs.getString("zqyzl")));
				hgzinfo.setBganzzdzl(nvl(rs.getString("bganzzdzl")));
				hgzinfo.setJsszcrs(nvl(rs.getString("jsszcrs")));
				hgzinfo.setEdzk(nvl(rs.getString("edzk")));
				hgzinfo.setZgcs(nvl(rs.getString("zgcs")));
				hgzinfo.setScrq(nvl(rs.getString("scrq")));
				hgzinfo.setBz(nvl(rs.getString("bz")));
				hgzinfo.setQybz(nvl(rs.getString("qybz")));
				hgzinfo.setClscdwmc(nvl(rs.getString("clscdwmc")));
				hgzinfo.setScdz(nvl(rs.getString("scdz")));
				hgzinfo.setQtxx(nvl(rs.getString("qtxx")));
				hgzinfo.setHgzxh(nvl(rs.getString("hgzxh")));
				hgzinfo.setCpno(nvl(rs.getString("cpno")));
				hgzinfo.setPh(nvl(rs.getString("ph")));
				hgzinfo.setSxrq(nvl(rs.getString("sxrq")));
				hgzinfo.setPzxlh(nvl(rs.getString("pzxlh")));
				hgzinfo.setJfpzid(nvl(rs.getString("jfpzid")));
				hgzinfo.setMzxny(nvl(rs.getString("mzxny")));
				hgzinfo.setJsgc(nvl(rs.getString("jsgc")));
				hgzinfo.setIscxnf(nvl(rs.getString("iscxnf")));
				hgzinfo.setVinbsyy(nvl(rs.getString("vinbsyy")));
				hgzinfo.setZyzycmsbs(nvl(rs.getString("zyzycmsbs")));
				hgzinfo.setQyid(nvl(rs.getString("qyid")));
				hgzinfo.setZxzs(nvl(rs.getString("zxzs")));
				hgzinfo.setJyw(nvl(rs.getString("jyw")));
				hgzinfo.setCddbj(nvl(rs.getString("cddbj")));
				hgzinfo.setDywym(nvl(rs.getString("dywym")));
				hgzinfo.setDpcpno(nvl(rs.getString("dpcpno")));
				hgzinfo.setFkxlh(nvl(rs.getString("fkxlh")));
			}

		} catch (SQLException e) {
			e.printStackTrace();
		}finally {
			if (rs != null) {
				try {
					rs.close();
				} catch (SQLException e) {
					e.printStackTrace();
				}
			}
		}
		return hgzinfo;
	}

	private String checkQueryHgzxx(QueryHgzxx info) {
		String res = "0";
		String dph = info.getDph();
		String factorycode = info.getFactorycode();

		if (StringTools.isBlank(dph)) {
			res = "底盘号不能为空";
			return res;
		}
		if (dph.trim().length() != 8) {
			res = "底盘号长度只能为8个字符";
			return res;
		}

		if (StringTools.isBlank(factorycode)) {
			res = "工厂代码不能为空";
			return res;
		}
		if (factorycode.length() > 20) {
			res = "工厂代码只能小于20个字符";
			return res;
		}

		return res;
	}
	
	
	public String uploadBaseXcxxjccs(BaseXcxxjccs info)  {

		String res = "合格证系统执行存储过程失败！";
		String rescheck = checkBaseXcxxjccs(info);
		if (!"0".equals(rescheck)) {
			// 接口数据长度或格式校验不通过，反馈不通过信息
			res = rescheck;
		} else {
			Connection conn = getDBconnect();
			
			CallableStatement proc = null;
			Statement stmt = null;

			try {
				//110个参数+1个反馈参数
				proc = conn.prepareCall("{call proc_zgs_xcxxjccs_insert("
						+"?,?,?,?,?,?,?,?,?,?,"
						+"?,?,?,?,?,?,?,?,?,?,"
						+"?,?,?,?,?,?,?,?,?,?,"
						+"?,?,?,?,?,?,?,?,?,?,"
						+"?,?,?,?,?,?,?,?,?,?,"
						+"?,?,?,?,?,?,?,?,?,?,"
						+"?,?,?,?,?,?,?,?,?,?,"
						+"?,?,?,?,?,?,?,?,?,?,"
						+"?,?,?,?,?,?,?,?,?,?,"
						+"?,?,?,?,?,?,?,?,?,?,"
						+"?,?,?,?,?,?,?,?,?,?,"
						+"?"
						+")}");
				
				proc.setString(1, info.getDph());
				proc.setString(2, info.getFactorycode());
				proc.setString(3, info.getRllx());
				proc.setString(4, info.getTestno());
				proc.setString(5, info.getAlltestdate());
				proc.setString(6, info.getSb());
				proc.setString(7, info.getSccdz());
				proc.setString(8, info.getPfjd());
				proc.setString(9, info.getRygjxt());
				proc.setString(10, info.getEdzs());
				proc.setString(11, info.getBsq());
				proc.setString(12, info.getChzhqxh());
				proc.setString(13, info.getJzzl());
				proc.setString(14, info.getQgs());
				proc.setString(15, info.getRygjfs());
				proc.setString(16, info.getDdjxh());
				proc.setString(17, info.getCnzzxh());
				proc.setString(18, info.getDcrl());
				proc.setString(19, info.getObdjkwz());
				proc.setString(20, info.getPqhcllx());
				proc.setString(21, info.getPqhclxh());
				proc.setString(22, info.getApass());
				proc.setString(23, info.getObdtx());
				proc.setString(24, info.getObd());
				proc.setString(25, info.getOdo());
				proc.setString(26, info.getFdjmoduleid());
				proc.setString(27, info.getFdjcalid());
				proc.setString(28, info.getFdjcvn());
				proc.setString(29, info.getHclmoduleid());
				proc.setString(30, info.getHclcalid());
				proc.setString(31, info.getHclcvn());
				proc.setString(32, info.getOthermoduleid());
				proc.setString(33, info.getOthercalid());
				proc.setString(34, info.getOthercvn());
				proc.setString(35, info.getOpass());
				proc.setString(36, info.getOtestdate());
				proc.setString(37, info.getObdinspector());
				proc.setString(38, info.getRh());
				proc.setString(39, info.getEt());
				proc.setString(40, info.getAp());
				proc.setString(41, info.getTesttype());
				proc.setString(42, info.getTestdate());
				proc.setString(43, info.getAnalymodel());
				proc.setString(44, info.getAnalymanuf());
				proc.setString(45, info.getAnalyname());
				proc.setString(46, info.getAnalydate());
				proc.setString(47, info.getDynomanuf());
				proc.setString(48, info.getDynomodel());
				proc.setString(49, info.getSvrco());
//				proc.setString(50, info.getSvlco());
				proc.setString(50, info.getSvlco());
				proc.setString(51, info.getVrhcnox());
				proc.setString(52, info.getVlhcnox());
				proc.setString(53, info.getVrhc());
				proc.setString(54, info.getVlhc());
				proc.setString(55, info.getVrco());
				proc.setString(56, info.getVlco());
				proc.setString(57, info.getVrnox());
				proc.setString(58, info.getVlnox());
				proc.setString(59, info.getArhc());
				proc.setString(60, info.getAlhc());
				proc.setString(61, info.getArco());
				proc.setString(62, info.getAlco());
				proc.setString(63, info.getArnox());
				proc.setString(64, info.getAlnox());
				proc.setString(65, info.getArhc5025());
				proc.setString(66, info.getAlhc5025());
				proc.setString(67, info.getArco5025());
				proc.setString(68, info.getAlco5025());
				proc.setString(69, info.getArnox5025());
				proc.setString(70, info.getAlnox5025());
				proc.setString(71, info.getArhc2540());
				proc.setString(72, info.getAlhc2540());
				proc.setString(73, info.getArco2540());
				proc.setString(74, info.getAlco2540());
				proc.setString(75, info.getArnox2540());
				proc.setString(76, info.getAlnox2540());
				proc.setString(77, info.getLeacmax());
				proc.setString(78, info.getLeacmin());
				proc.setString(79, info.getReac());
				proc.setString(80, info.getLrco());
				proc.setString(81, info.getLlco());
				proc.setString(82, info.getLrhc());
				proc.setString(83, info.getLlhc());
				proc.setString(84, info.getHrco());
				proc.setString(85, info.getHlco());
				proc.setString(86, info.getHrhc());
				proc.setString(87, info.getHlhc());
				proc.setString(88, info.getRaterev());
				proc.setString(89, info.getRev());
				proc.setString(90, info.getSmokek1());
				proc.setString(91, info.getSmokek2());
				proc.setString(92, info.getSmokek3());
				proc.setString(93, info.getSmokeavg());
				proc.setString(94, info.getSmokeklimit());
				proc.setString(95, info.getRaterevup());
				proc.setString(96, info.getRaterevdown());
				proc.setString(97, info.getRev100());
				proc.setString(98, info.getMaxpower());
				proc.setString(99, info.getMaxpowerlimit());
				proc.setString(100, info.getSmoke100());
				proc.setString(101, info.getSmoke80());
				proc.setString(102, info.getSmokelimit());
				proc.setString(103, info.getNox());
				proc.setString(104, info.getNoxlimit());
				proc.setString(105, info.getEpass());
				proc.setString(106, info.getTestinspector());
				proc.setString(107, info.getCtest());
				proc.setString(108, info.getTestlocation());
				proc.setString(109, info.getResult());
				proc.setString(110, info.getFdjsccdz());

				proc.registerOutParameter(111, java.sql.Types.VARCHAR);
				proc.execute();

				res = proc.getString(111);

			} catch (Exception e) {
				e.printStackTrace();
				res=e.toString();
			} finally {
				try {
					if (proc != null) {
						proc.close();
					}
					if (stmt != null) {
						stmt.close();
					}
					if (conn != null) {
						conn.close();
					}
				} catch (SQLException e) {
					e.printStackTrace();
				}
			}
		}
		return res;
	}

	private String checkBaseXcxxjccs(BaseXcxxjccs info) {
		 String res="0";
		 
		 String dph=info.getDph();
		 String factorycode=info.getFactorycode();
		 String rllx=info.getRllx();
		 String testno=info.getTestno();
		 String alltestdate=info.getAlltestdate();
		 String sb=info.getSb();
		 String sccdz=info.getSccdz();
		 String pfjd=info.getPfjd();
		 String rygjxt=info.getRygjxt();
		 String edzs=info.getEdzs();
		 String bsq=info.getBsq();
		 String chzhqxh=info.getChzhqxh();
		 String jzzl=info.getJzzl();
		 String qgs=info.getQgs();
		 String rygjfs=info.getRygjfs();
		 String ddjxh=info.getDdjxh();
		 String cnzzxh=info.getCnzzxh();
		 String dcrl=info.getDcrl();
		 String obdjkwz=info.getObdjkwz();
		 String pqhcllx=info.getPqhcllx();
		 String pqhclxh=info.getPqhclxh();
		 String apass=info.getApass();
		 String obdtx=info.getObdtx();
		 String obd=info.getObd();
		 String odo=info.getOdo();
		 String fdjmoduleid=info.getFdjmoduleid();
		 String fdjcalid=info.getFdjcalid();
		 String fdjcvn=info.getFdjcvn();
		 String hclmoduleid=info.getHclmoduleid();
		 String hclcalid=info.getHclcalid();
		 String hclcvn=info.getHclcvn();
		 String othermoduleid=info.getOthermoduleid();
		 String othercalid=info.getOthercalid();
		 String othercvn=info.getOthercvn();
		 String opass=info.getOpass();
		 String otestdate=info.getOtestdate();
		 String obdinspector=info.getObdinspector();
		 String rh=info.getRh();
		 String et=info.getEt();
		 String ap=info.getAp();
		 String testtype=info.getTesttype();
		 String testdate=info.getTestdate();
		 String analymodel=info.getAnalymodel();
		 String analymanuf=info.getAnalymanuf();
		 String analyname=info.getAnalyname();
		 String analydate=info.getAnalydate();
		 String dynomanuf=info.getDynomanuf();
		 String dynomodel=info.getDynomodel();
		 String svrco=info.getSvrco();
		 String svlco=info.getSvlco();
		 String vrhcnox=info.getVrhcnox();
		 String vlhcnox=info.getVlhcnox();
		 String vrhc=info.getVrhc();
		 String vlhc=info.getVlhc();
		 String vrco=info.getVrco();
		 String vlco=info.getVlco();
		 String vrnox=info.getVrnox();
		 String vlnox=info.getVlnox();
		 String arhc=info.getArhc();
		 String alhc=info.getAlhc();
		 String arco=info.getArco();
		 String alco=info.getAlco();
		 String arnox=info.getArnox();
		 String alnox=info.getAlnox();
		 String arhc5025=info.getArhc5025();
		 String alhc5025=info.getAlhc5025();
		 String arco5025=info.getArco5025();
		 String alco5025=info.getAlco5025();
		 String arnox5025=info.getArnox5025();
		 String alnox5025=info.getAlnox5025();
		 String arhc2540=info.getArhc2540();
		 String alhc2540=info.getAlhc2540();
		 String arco2540=info.getArco2540();
		 String alco2540=info.getAlco2540();
		 String arnox2540=info.getArnox2540();
		 String alnox2540=info.getAlnox2540();
		 String leacmax=info.getLeacmax();
		 String leacmin=info.getLeacmin();
		 String reac=info.getReac();
		 String lrco=info.getLrco();
		 String llco=info.getLlco();
		 String lrhc=info.getLrhc();
		 String llhc=info.getLlhc();
		 String hrco=info.getHrco();
		 String hlco=info.getHlco();
		 String hrhc=info.getHrhc();
		 String hlhc=info.getHlhc();
		 String raterev=info.getRaterev();
		 String rev=info.getRev();
		 String smokek1=info.getSmokek1();
		 String smokek2=info.getSmokek2();
		 String smokek3=info.getSmokek3();
		 String smokeavg=info.getSmokeavg();
		 String smokeklimit=info.getSmokeklimit();
		 String raterevup=info.getRaterevup();
		 String raterevdown=info.getRaterevdown();
		 String rev100=info.getRev100();
		 String maxpower=info.getMaxpower();
		 String maxpowerlimit=info.getMaxpowerlimit();
		 String smoke100=info.getSmoke100();
		 String smoke80=info.getSmoke80();
		 String smokelimit=info.getSmokelimit();
		 String nox=info.getNox();
		 String noxlimit=info.getNoxlimit();
		 String epass=info.getEpass();
		 String testinspector=info.getTestinspector();
		 String ctest=info.getCtest();
		 String testlocation=info.getTestlocation();
		 String result=info.getResult();
		 String fdjsccdz=info.getFdjsccdz();
		 
		//dph    VARCHAR 8    底盘号
		 if(StringTools.isBlank(dph)) {
			 res="底盘号不能为空";
			 return res;
		 }
		 if(dph.length()!=8) {
			 res="底盘号长度应为8个字符";
			 return res;
		 }
		//factorycode    VARCHAR 20    工厂代码
		 if(StringTools.isBlank(factorycode)) {
			 res="工厂代码不能为空";
			 return res;
		 }
		 if(factorycode.length()>20) {
			 res="工厂代码长度不能大于20个字符";
			 return res;
		 }
		//rllx    VARCHAR 1    燃料类型
		 if(StringTools.isBlank(rllx)) {
			 res="燃料类型不能为空";
			 return res;
		 }
		 if(rllx.length()>1) {
			 res="燃料类型长度不能大于1个字符";
			 return res;
		 }
		 if(",1,2,".indexOf(","+rllx+",")==-1 ) {
			 res="燃料类型只能在[1,2]中";
			 return res;
		 }
		//testno    VARCHAR 18    检测报告编号
		 if(StringTools.isBlank(testno)) {
			 res="检测报告编号不能为空";
			 return res;
		 }
		 if(testno.length()!=18) {
			 res="检测报告编号长度必须为18个字符";
			 return res;
		 }
		//alltestdate    VARCHAR 8    检验报告打印日期
		 if(StringTools.isBlank(alltestdate)) {
			 res="检验报告打印日期不能为空";
			 return res;
		 }
		 if(alltestdate.trim().length()!=8) {
			 res="检验报告打印日期长度只能为8个字符，且格式应为yyyymmdd";
			 return res;
		 }
		 if(!isDateYYYYMMDD(alltestdate)) {
			 res="检验报告打印日期长度只能为8个字符，且格式应为yyyymmdd";
		 }
		//sb    VARCHAR 100    商标
		 if(StringTools.isBlank(sb)) {
			 res="商标不能为空";
			 return res;
		 }
		 if(sb.length()>100) {
			 res="商标长度不能大于100个字符";
			 return res;
		 }
		//sccdz    VARCHAR 100    生产厂地址
		 if(StringTools.isBlank(sccdz)) {
			 res="生产厂地址不能为空";
			 return res;
		 }
		 if(sccdz.length()>100) {
			 res="生产厂地址长度不能大于100个字符";
			 return res;
		 }
		//pfjd    VARCHAR 100    车辆排放阶段
		 if(StringTools.isBlank(pfjd)) {
			 res="车辆排放阶段不能为空";
			 return res;
		 }
		 if(pfjd.length()>100) {
			 res="车辆排放阶段不能大于100个字符";
			 return res;
		 }
		//rygjxt    VARCHAR 100    燃油供给系统（柴油）
		 if("1".equals(rllx)) {//汽油
			 if(!StringTools.isBlank(rygjxt)) {
				 res="燃料类型为[1]时燃油供给系统（柴油）应为空";
				 return res;
			 }
		 }else {
			 if(StringTools.isBlank(rygjxt)) {
				 res="燃油供给系统（柴油）不能为空";
				 return res;
			 }
			 if(rygjxt.length()>100) {
				 res="燃油供给系统（柴油）不能大于100个字符";
				 return res;
			 }
		 }
		//edzs    VARCHAR 100    额定转速（rpm）
		 if("2".equals(rllx)) {//柴油
			 if(StringTools.isBlank(edzs)) {
				 res="额定转速不能为空";
				 return res;
			 }
			 if(edzs.length()>100) {
				 res="额定转速不能大于100个字符";
				 return res;
			 }
		 }else {
			 if(!StringTools.isBlank(edzs)) {
				 res="燃料类型为[1]时额定转速应为空";
				 return res;
			 }
			 
		 }
		//bsq    VARCHAR 100    变速器型式
		 if("1".equals(rllx)) {//汽油
			 if(StringTools.isBlank(bsq)) {
				 res="变速器型式不能为空";
				 return res;
			 }
			 if(bsq.length()>100) {
				 res="变速器型式不能大于100个字符";
				 return res;
			 }
		 }else {
			 if(!StringTools.isBlank(bsq)) {
				 res="燃料类型为[2]时变速器型式应为空";
				 return res;
			 }
			 
		 }
		//chzhqxh    VARCHAR 100    催化转化器型号
		 if("1".equals(rllx)) {//汽油
			 if(StringTools.isBlank(chzhqxh)) {
				 res="催化转化器型号不能为空";
				 return res;
			 }
			 if(chzhqxh.length()>100) {
				 res="催化转化器型号不能大于100个字符";
				 return res;
			 }
		 }else {
			 if(!StringTools.isBlank(chzhqxh)) {
				 res="燃料类型为[2]时催化转化器型号应为空";
				 return res;
			 }
			 
		 }
		//jzzl    VARCHAR 100    基准质量（kg）
		 if(StringTools.isBlank(jzzl)) {
			 res="基准质量不能为空";
			 return res;
		 }
		 if(jzzl.length()>100) {
			 res="基准质量不能大于100个字符";
			 return res;
		 }
		//qgs    VARCHAR 100    气缸数
		 if(StringTools.isBlank(qgs)) {
			 res="气缸数不能为空";
			 return res;
		 }
		 if(qgs.length()>100) {
			 res="气缸数不能大于100个字符";
			 return res;
		 }
		//rygjfs    VARCHAR 100    燃油供给方式
		 if("1".equals(rllx)) {//汽油
			 if(StringTools.isBlank(rygjfs)) {
				 res=" 燃油供给方式不能为空";
				 return res;
			 }
			 if(rygjfs.length()>100) {
				 res=" 燃油供给方式不能大于100个字符";
				 return res;
			 }
		 }else {
			 if(!StringTools.isBlank(rygjfs)) {
				 res="燃料类型为[2]时 燃油供给方式应为空";
				 return res;
			 }
			 
		 }
		//ddjxh    VARCHAR 100    电动机型号
		 if(StringTools.isBlank(ddjxh)) {
			 if(ddjxh.length()>100) {
				 res="电动机型号不能大于100个字符";
				 return res;
			 }
		 }
		 
		//cnzzxh    VARCHAR 100    储能装置型号
		 if(StringTools.isBlank(cnzzxh)) {
			 if(cnzzxh.length()>100) {
				 res="储能装置型号不能大于100个字符";
				 return res;
			 }
		 }
		//dcrl    VARCHAR 100    电池容量
		 if(StringTools.isBlank(dcrl)) {
			 if(dcrl.length()>100) {
				 res="电池容量不能大于100个字符";
				 return res;
			 }
		 }
		//obdjkwz    VARCHAR 100    OBD接口位置
		 if(StringTools.isBlank(obdjkwz)) {
			 if(obdjkwz.length()>100) {
				 res="OBD接口位置不能大于100个字符";
				 return res;
			 }
		 }
		//pqhcllx    VARCHAR 100    后处理类型
		 if(StringTools.isBlank(pqhcllx)) {
			 if(pqhcllx.length()>100) {
				 res=" 后处理类型不能大于100个字符";
				 return res;
			 }
		 }
		//pqhclxh    VARCHAR 100    后处理型号
		 if(StringTools.isBlank(pqhclxh)) {
			 if(pqhclxh.length()>100) {
				 res="后处理型号不能大于100个字符";
				 return res;
			 }
		 }
		//apass    VARCHAR 1    外观检验判定
		 if(StringTools.isBlank(apass)) {
			 if(apass.length()>1) {
				 res=" 外观检验判定不能大于1个字符";
				 return res;
			 }
			 if(",0,1,".indexOf(","+apass+",")==-1 ) {
				 res="外观检验判定只能在[0,1]中";
				 return res;
			 }
		 }
		//obdtx    VARCHAR 1    OBD通讯是否正常
		 if(StringTools.isBlank(obdtx)) {
			 res="OBD通讯是否正常不能为空";
			 return res;
		 }
		 if(obdtx.length()>1) {
			 res="OBD通讯是否正常不能大于1个字符";
			 return res;
		 }
		 if(",0,1,".indexOf(","+obdtx+",")==-1 ) {
			 res="OBD通讯是否正常只能在[0,1]中";
			 return res;
		 }
		//obd    VARCHAR 2    型式检验时的OBD要求
		 if(StringTools.isBlank(obd)) {
			 res="型式检验时的OBD要求不能为空";
			 return res;
		 }
		 if(obd.length()>2) {
			 res="型式检验时的OBD要求不能大于2个字符";
			 return res;
		 }

		//odo    NUMBER 8,1    车辆行驶里程
		//fdjmoduleid    VARCHAR 11    发动机控制单元控制单位模块ID
		//fdjcalid    VARCHAR 20    发动机控制单元:CAL ID
		//fdjcvn    VARCHAR 20    发动机控制单元:CVN
		//hclmoduleid    VARCHAR 11    后处理控制单位模块ID
		//hclcalid    VARCHAR 20    后处理控制单元（如适用）:CAL ID
		//hclcvn    VARCHAR 20    后处理控制单元（如适用）:CVN
		//othermoduleid    VARCHAR 11    其他控制单位模块ID
		//othercalid    VARCHAR 20    其他控制单元（如适用）:CAL ID
		//othercvn    VARCHAR 20    其他控制单元（如适用）:CVN
		//opass    VARCHAR 1    OBD检查判定
		 if(StringTools.isBlank(opass)) {
			 res="OBD检查判定不能为空";
			 return res;
		 }
		 if(opass.length()>1) {
			 res="OBD检查判定不能大于1个字符";
			 return res;
		 }
		 if(",0,1,".indexOf(","+opass+",")==-1 ) {
			 res="OBD检查判定只能在[0,1]中";
			 return res;
		 }
		//otestdate    VARCHAR 8    OBD检测日期
		 if(StringTools.isBlank(otestdate)) {
			 res="OBD检测日期 不能为空";
			 return res;
		 }
		 if(otestdate.trim().length()!=8) {
			 res="OBD检测日期 长度只能为8个字符，且格式应为yyyymmdd";
			 return res;
		 }
		 if(!isDateYYYYMMDD(otestdate)) {
			 res="OBD检测日期 长度只能为8个字符，且格式应为yyyymmdd";
		 }
		//obdinspector    VARCHAR 100    检查员(OBD检查)
		 if(StringTools.isBlank(obdinspector)) {
			 res="检查员(OBD检查) 不能为空";
			 return res;
		 }
		 if(obdinspector.length()>100) {
			 res="检查员(OBD检查) 不能大于100个字符";
			 return res;
		 }
		//rh    NUMBER 4,2    相对湿度（%）
		 
		//et    NUMBER 5,2    环境温度（℃）
		//ap    NUMBER 5,2    大气压（kPa）
		//testtype    NUMBER 1    排气检测方法
		 if(StringTools.isBlank(testtype)) {
			 res="testtype不能为空";
			 return res;
		 }
		 if(testtype.length()>1) {
			 res="testtype不能大于1个字符";
			 return res;
		 }
		 if(",0,1,2,3,4,6,7,8,9,".indexOf(","+testtype+",")==-1 ) {
			 res="testtype只能在[0,1,2,3,4,6,7,8,9]中";
			 return res;
		 }
		//testdate    VARCHAR 8    检测日期
		 if("0".equals(testtype) ) {
			 if(!StringTools.isBlank(testdate)) {
				 res="testtype=0时，testdate必须为空";
				 return res;
			 }
		 }else {
			 if(StringTools.isBlank(testdate)) {
				 res="testtype不为0时，testdate不能为空";
				 return res;
			 }else {
				 if(testdate.trim().length()!=8) {
					 res="testdate 长度只能为8个字符，且格式应为yyyymmdd";
					 return res;
				 }
				 if(!isDateYYYYMMDD(testdate)) {
					 res="testdate 长度只能为8个字符，且格式应为yyyymmdd";
				 }
			 }
		 }
		//analymodel    VARCHAR 50    分析仪型号
		//analymanuf    VARCHAR 30    分析仪制造厂
		//analyname    VARCHAR 30    分析仪名称
		//analydate    VARCHAR 8    分析仪检定日期
		//dynomanuf    VARCHAR 50    测功机生产厂
		//dynomodel    VARCHAR 50    测功机型号
		//svrco    NUMBER 3,2    CO结果  A.5.6瞬态工况法
		//svlco    NUMBER 3,2    CO限值  A.5.7瞬态工况法
		//vrhcnox    NUMBER 3,2    HCNOX结果  A.5.8瞬态工况法
		//vlhcnox    NUMBER 3,2    HCNOX限值  A.5.9瞬态工况法
		//vrhc    NUMBER 3,2    HC结果  A.5.3简易瞬态工况法
		//vlhc    NUMBER 3,2    HC限值  A.5.4简易瞬态工况法
		//vrco    NUMBER 3,2    CO结果  A.5.5简易瞬态工况法
		//vlco    NUMBER 3,2    CO限值  A.5.6简易瞬态工况法
		//vrnox    NUMBER 3,2    NOX结果  A.5.7简易瞬态工况法
		//vlnox    NUMBER 3,2    NOX限值  A.5.8简易瞬态工况法
		//arhc    NUMBER 4    HC(10-6)实测值  A.5.2 稳态工况法
		//alhc    NUMBER 4    HC(10-6)企业限值  A.5.3 稳态工况法
		//arco    NUMBER 3,2    CO(%)实测值  A.5.4 稳态工况法
		//alco    NUMBER 3,2    CO(%)企业限值  A.5.5 稳态工况法
		//arnox    NUMBER 4    NO(10-6)实测值  A.5.6 稳态工况法
		//alnox    NUMBER 4    NO(10-6)企业限值  A.5.7 稳态工况法
		//arhc5025    NUMBER 4    5025HC结果  A.5.2 稳态工况法
		//alhc5025    NUMBER 4    5025HC限值  A.5.2 稳态工况法
		//arco5025    NUMBER 3,2    5025CO结果  A.5.2 稳态工况法
		//alco5025    NUMBER 3,2    5025CO限值  A.5.2 稳态工况法
		//arnox5025    NUMBER 4    5025NO结果  A.5.2 稳态工况法
		//alnox5025    NUMBER 4    5025NO限值  A.5.2 稳态工况法
		//arhc2540    NUMBER 4    2540HC结果  A.5.2 稳态工况法
		//alhc2540    NUMBER 4    2540HC限值  A.5.2 稳态工况法
		//arco2540    NUMBER 3,2    2540CO结果  A.5.2 稳态工况法
		//alco2540    NUMBER 3,2    2540CO限值  A.5.2 稳态工况法
		//arnox2540    NUMBER 4    2540NO结果  A.5.2 稳态工况法
		//alnox2540    NUMBER 4    2540NO限值  A.5.2 稳态工况法
		//leacmax    NUMBER 3,2    双怠速过量空气系数上限  A.5.1 双怠速法
		//leacmin    NUMBER 3,2    双怠速过量空气系数下限  A.5.1 双怠速法
		//reac    NUMBER 3,2    过量空气系数结果  A.5.1 双怠速法
		//lrco    NUMBER 3,2    低怠速CO（%）实测值  A.5.1 双怠速法
		//llco    NUMBER 3,2    低怠速CO（%）企业限值  A.5.1 双怠速法
		//lrhc    NUMBER 4    低怠速HC(10-6)实测值  A.5.1 双怠速法
		//llhc    NUMBER 4    低怠速HC(10-6)企业限值  A.5.1 双怠速法
		//hrco    NUMBER 3,2    高怠速CO（%）实测值  A.5.1 双怠速法
		//hlco    NUMBER 3,2    高怠速CO（%）企业限值  A.5.1 双怠速法
		//hrhc    NUMBER 4    高怠速HC(10-6)实测值  A.5.1 双怠速法
		//hlhc    NUMBER 4    高怠速HC(10-6)企业限值  A.5.1 双怠速法
		//raterev    NUMBER 5    额定转速（r/min）  A.5.5 自由加速法
		//rev    NUMBER 5    实测转速（r/min）  A.5.5 自由加速法
		//smokek1    NUMBER 3,2    第一次烟度测量值  A.5.5 自由加速法
		//smokek2    NUMBER 3,2    第二次烟度测量值  A.5.5 自由加速法
		//smokek3    NUMBER 3,2    第三次烟度测量值  A.5.5 自由加速法
		//smokeavg    NUMBER 3,2    烟度测量平均值  A.5.5 自由加速法
		//smokeklimit    NUMBER 3,2    烟度限值  A.5.5 自由加速法
		//raterevup    NUMBER 5    额定转速上限  A.5.4 加载减速法
		//raterevdown    NUMBER 5    额定转速下限  A.5.4 加载减速法
		//rev100    NUMBER 5    实测转速  A.5.4 加载减速法
		//maxpower    NUMBER 4,1    实测最大轮边功率  A.5.4 加载减速法
		//maxpowerlimit    NUMBER 4,1    最大轮边功率限值  A.5.4 加载减速法
		//smoke100    NUMBER 3,2    100%点烟度  A.5.4 加载减速法
		//smoke80    NUMBER 3,2    80%点烟度  A.5.4 加载减速法
		//smokelimit    NUMBER 3,2    光吸收系数（m-1）或不透光度(%)企业限值  A.5.4 加载减速法
		//nox    NUMBER 4    氮氧化物测量值  A.5.4 加载减速法
		//noxlimit    NUMBER 4    氮氧化物限值  A.5.4 加载减速法
		//epass    VARCHAR 1    排放检验结果  A.3检测信息   0-不通过1-通过，免检为空
		 if("0".equals(testtype) ) {
			 if(!StringTools.isBlank(epass)) {
				 res="testtype=0时，epass必须为空";
				 return res;
			 }
		 }else {
			 if(StringTools.isBlank(epass)) {
				 res="testtype不为0时，epass不能为空";
				 return res;
			 }else {
				 if(",0,1,".indexOf(","+epass+",")==-1 ) {
					 res="epass只能在[0,1]中";
					 return res;
				 }
				 if("0".equals(epass) ) {
					 res="epass需要为检测通过的数据";
					 return res;
				 }
			 }
		 }
		 
		//testinspector    VARCHAR 100    检验员(排气污染物检测)  A.3检测信息
		//ctest    VARCHAR 100    *委托的检测机构  A.3检测信息
		//testlocation    VARCHAR 200    检测地点  A.3检测信息
		//result    VARCHAR 1    最终判定  A.3检测信息
		//fdjsccdz    VARCHAR 100    发动机生产地址
		 return res;

	}

	/** 
     * 得到一个字符串的长度,显示的长度,一个汉字或日韩文长度为2,英文字符长度为1 
     * @param String s 需要得到长度的字符串 
     * @return int 得到的字符串长度 
     */ 
    public static int length(String s) {
        if (s == null)
            return 0;
        char[] c = s.toCharArray();
        int len = 0;
        for (int i = 0; i < c.length; i++) {
            len++;
            if (!isLetter(c[i])) {
                len++;
            }
        }
        return len;
    }
    
    public static boolean isLetter(char c) { 
        int k = 0x80; 
        return c / k == 0 ? true : false; 
    }
    
	public static boolean isDate(String str) {
		String path = "\\d{4}-\\d{2}-\\d{2}";// 定义匹配规则
		Pattern p = Pattern.compile(path);// 实例化Pattern
		Matcher m = p.matcher(str);// 验证字符串内容是否合法
		return m.matches();
	}
	
	public static boolean isDateYYYYMMDD(String str) {
		String path = "\\d{4}\\d{2}\\d{2}";// 定义匹配规则
		Pattern p = Pattern.compile(path);// 实例化Pattern
		Matcher m = p.matcher(str);// 验证字符串内容是否合法
		return m.matches();
	}

	public static String nvl(String str) {
		return StringTools.isBlank(str)?"":str;
	}
	

}
