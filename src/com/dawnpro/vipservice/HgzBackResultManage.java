package com.dawnpro.vipservice;

import info.vidc.www.certificate.operation.CertificateInfo;
import info.vidc.www.certificate.operation.CertificateRequestVIPProxy;

import java.rmi.RemoteException;
import java.sql.SQLException;
import java.sql.Types;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.GregorianCalendar;
import java.util.List;
import java.util.Map;

import com.dawnpro.commons.SystemParam;
import com.dawnpro.commons.util.MapUtil;
import com.dawnpro.commons.util.StringTools;
import com.dawnpro.commons.util.db.DAOException;
import com.dawnpro.commons.util.db.TransactionException;
import com.dawnpro.core.exception.AppException;
import com.dawnpro.core.log.Loggers;
import com.dawnpro.entity.HgzConfig;
import com.dawnpro.service.BaseServer;

/**
 * <p>Title: 合格证回填上传结果处理类</p>
 * <p>Description: 合格证回填上传结果处理类</p>
 * <p>Copyright: Copyright dawnpro(c) 2010</p>
 * <p>Company: Dawnpro</p>
 * <AUTHOR>
 * @version 1.0
 * Created Date: 2010-6-15
 * Change History
 */
public class HgzBackResultManage extends BaseServer {
	
	/**
	 * 合格证回填上传结果(自动回填)
	 */
	public void backResult()
	{
		CertificateRequestVIPProxy service = new CertificateRequestVIPProxy();
        
        HgzConfig dflOnfig = getConfigData("东风汽车集团有限公司");

        Loggers.PERFORMANCE.info("东风汽车集团有限合格证回填上传开始:" + new GregorianCalendar().getTime());
    	this.callService(service,dflOnfig,2,0);//修改上传
    	this.callService(service,dflOnfig,3,0);//撤销上传
    	this.callService(service,dflOnfig,4,0);//补传上传
    	this.callService(service,dflOnfig,5,0);//VIP修改上传
    	this.callService(service,dflOnfig,6,0);//回填反馈序列号
    	Loggers.PERFORMANCE.info("东风汽车集团有限合格证回填上传结束:" + new GregorianCalendar().getTime());
	}
	
	/**
	 * 合格证回填上传结果(按上传类型)
	 * @param type 上传类别 2 修改/3 撤销/4 补传/5 VIP修改  /6 回填反馈序列号
	 */
	public String backResult(String type)
	{
		Loggers.PERFORMANCE.info("合格证回填开始:" + new GregorianCalendar().getTime());
		
		String result = "fail";
		CertificateRequestVIPProxy service = new CertificateRequestVIPProxy();       
        HgzConfig dflOnfig = getConfigData("东风汽车集团有限公司");
        this.callService(service,dflOnfig,Integer.parseInt(type),0);
        result = "ok";  	
        Loggers.PERFORMANCE.info("合格证回填结束:" + new GregorianCalendar().getTime());
    	return result;
    	
    	
	}
	
	/**
	 * 调用接口服务获取国家数据,并回填上传结果
	 * @param service 接口服务
	 * @param hgzConfig 合格证配置信息
	 * @param type 上传类别 2 修改/3 撤销/4 补传/5 VIP修改
	 */
	private void callService(CertificateRequestVIPProxy service,HgzConfig hgzConfig,int type,int qy) {
		
		CertificateInfo[] infos = null;
		Map map = null;
		String hgz_id = null;
		String hgzbh = null;
		String vnm = null;
		String scgj = "1";
		StringBuffer sqlBuffer = null;
		
		List dataList = getHTData(type, qy);
		for (int i = 0; dataList != null && i < dataList.size(); i++) {
			sqlBuffer = new StringBuffer();
			sqlBuffer.delete(0, sqlBuffer.length());
			map = (Map)dataList.get(i);
			hgz_id = MapUtil.getMapValue(map, "hgz_id", "");
			hgzbh = MapUtil.getMapValue(map, "hgzbh", "");
			vnm = MapUtil.getMapValue(map, "vnm", "");
			
			try {
				infos = service.queryCertificateSingle(hgzConfig.getYcyh(), hgzConfig.getYcmm(), hgzbh, vnm);
				if (infos == null) {
					Loggers.PERFORMANCE.info("合格证回填[type:"+type+"]-[hgzid:"+hgz_id+"]从国家接口获取数据为空!");
					//对于撤销后未查询到合格证记录的数据，认为是撤销完成，进行正常撤销回填
					if(type==3) {
						scgj = "0";
						List tempList = null;
						List paramlist = new ArrayList();
						int[] rt = {Types.VARCHAR};
						String[] paramtype = {"String", "String", "String"};
						paramlist.add(String.valueOf(type));
						paramlist.add(hgz_id);
						paramlist.add(scgj);
						tempList = this.dao.excuteprc_new("proc_uploadhgz_back", paramtype, paramlist, rt);
						if (tempList == null || tempList.size() <= 0 || "0".equals(tempList.get(0))) {
							Loggers.PERFORMANCE.info("回填上传结果的存储过程执行失败:proc_uploadhgz_back");
						}
					}
				}
				else if(type != 6) {
				 if (type == 3 && infos.length == 0) {
						scgj = "0";
					} else if ((type == 3 || type == 1 || type == 4) && infos != null && infos.length > 0 && hgz_id != null && !"".equals(hgz_id)) {
						CertificateInfo info = infos[0];
						if (info.getVEHICLE_STATUS() != null && ",3,4,5,".indexOf(("," + info.getVEHICLE_STATUS() + ",")) >= 0) {
							scgj = "0";
						}
					} else if ((type == 2 || type == 5) && infos != null && infos.length > 0 && hgz_id != null && !"".equals(hgz_id)) {
						for (int j = 0; j < infos.length; j++) {
							CertificateInfo info = infos[j];
							if (!StringTools.isBlank(info.getUPSEND_TAG()) && info.getUPDATETIME() != null) {
								if (info.getVEHICLE_STATUS() != null && ",3,4,5,".indexOf(("," + info.getVEHICLE_STATUS() + ",")) >= 0) {
									scgj = "0";
								}
							}
						}
					}
					if (sqlBuffer.length() > 0) {
						this.dao.executeUpdate(sqlBuffer.toString());
					}
					List tempList = null;
					List paramlist = new ArrayList();
					int[] rt = {Types.VARCHAR};
					String[] paramtype = {"String", "String", "String"};
					paramlist.add(String.valueOf(type));
					paramlist.add(hgz_id);
					paramlist.add(scgj);
					tempList = this.dao.excuteprc_new("proc_uploadhgz_back", paramtype, paramlist, rt);
					if (tempList == null || tempList.size() <= 0 || "0".equals(tempList.get(0))) {
						Loggers.PERFORMANCE.info("回填上传结果的存储过程执行失败:proc_uploadhgz_back");
					}
				}else {
					if(infos.length == 0) {
						Loggers.PERFORMANCE.info("合格证回填[type:"+type+"]-[hgzid:"+hgz_id+"]从国家接口获取数据长度为0!");
					}else{
						for (int j = 0; j < infos.length; j++) {
							CertificateInfo info = infos[j];
							if (!StringTools.isBlank(info.getUPSEND_TAG()) && info.getUPDATETIME() != null) {
								if (info.getWZHGZBH().equals(hgzbh)) {
									Loggers.PERFORMANCE.info("合格证回填[type:"+type+"]-[hgzid:"+hgz_id+"]从国家接口获取数据[fkxhl:"+info.getH_ID()+"]!");
									this.dao.execute("update tbl_hgzpara set fkxlh='"+info.getH_ID()+"'  where hgz_id='"+hgz_id+"'");
								}
							}
						}
						
					}
					
					
				}
			} catch (AppException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			} catch (RemoteException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}  catch (DAOException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			} catch (TransactionException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}

	}
	
	/**
	 * 获取待回填的数据
	 * @param type 上传类别 2 修改/3 撤销/4 补传/5 VIP修改
	 * @return List 待回填的数据集
	 */
	private List getHTData(int type,int qy) {
		List resList = new ArrayList();	
		StringBuffer sqlBuffer = new StringBuffer();
						
		try {
			switch (type) {
				case 2:
					sqlBuffer.append("select t.hgz_id,t.hgzbh,t.vnm from tbl_hgzpara t inner join ")
						.append(" (select HGZ_ID from tbl_hgzmain where (cycle_status=20  or cycle_status=17) and upload_status=30 and upload_type=2 ")
						.append(" ) t1 on t.hgz_id=t1.hgz_id");
					break;
				case 3:
					sqlBuffer.append("select t.hgz_id,t.hgzbh,t.vnm from tbl_hgzpara t inner join ")
						.append(" (select HGZ_ID from tbl_hgzmain where cycle_status=30 and upload_status=30 and upload_type=3 ")
						.append(" ) t1 on t.hgz_id=t1.hgz_id");
					break;
				case 4:
					sqlBuffer.append("select t.hgz_id,t.hgzbh,t.vnm from tbl_hgzpara t inner join ")
						.append(" (select HGZ_ID from tbl_hgzmain where (cycle_status=20 or cycle_status=17) and upload_status=30 and upload_type in (1,4) ")
						.append(" ) t1 on t.hgz_id=t1.hgz_id");
					break;
				case 5:
					sqlBuffer.append("select t.hgz_id,t.hgzbh,t.vnm from tbl_hgzpara t inner join ")
						.append(" (select HGZ_ID from tbl_hgzmain where (cycle_status=20 or cycle_status=17) and upload_status=30 and upload_type=5 ")
						.append(" ) t1 on t.hgz_id=t1.hgz_id");
					break;
				case 6:
					sqlBuffer.append("select t.hgz_id,t.hgzbh,t.vnm from tbl_hgzpara t inner join ")
						.append(" (select HGZ_ID from tbl_hgzmain where cycle_status=20 and upload_status=40 and business_type1 in (1,2) and uptime>='2021-7-23' ")
						.append(" ) t1 on t.hgz_id=t1.hgz_id where fkxlh is null or fkxlh=''");
					break;
				default:
					break;
			}

			resList = this.dao.queryForList(sqlBuffer.toString());
			
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (DAOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return resList;
	}
	
	/**
	 * 获取上传用配置信息
	 * @return
	 */
	private HgzConfig getConfigData(String qymc) {
		HgzConfig hgzConfig = new HgzConfig();
		String ukey = null;
		String qydm = null;
		if ("东风汽车集团有限公司".equals(qymc)) {
			ukey = SystemParam.getKey("Upload_UKey_DFM", "");
			qydm = SystemParam.getKey("Upload_QYDM_DFM", "");
		}
		hgzConfig.setUkey(ukey);
		hgzConfig.setQydm(qydm);
		StringBuffer sqlBuffer = new StringBuffer("select ycyh,ycmm from tbl_qymc where qymc='").append(qymc).append("' and deleteflag='0' limit 1");
		try {
			List tempList = this.dao.queryForList(sqlBuffer.toString());
			if (tempList != null && tempList.size() > 0) {
				Map map = (Map)tempList.get(0);
				hgzConfig.setYcyh(MapUtil.getMapValue(map, "ycyh", ""));
				hgzConfig.setYcmm(MapUtil.getMapValue(map, "ycmm", ""));
			}
		} catch (DAOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return hgzConfig;
	}
	
	public CertificateInfo[] queryCertificateSingle(String hgzbh) {
		CertificateRequestVIPProxy service = new CertificateRequestVIPProxy();
        HgzConfig hgzConfig = getConfigData("东风汽车集团有限公司");
        CertificateInfo[] infos = null;
        try {
			infos = service.queryCertificateSingle(hgzConfig.getYcyh(), hgzConfig.getYcmm(), hgzbh, "");
		} catch (RemoteException e) {
			e.printStackTrace();
		}
        return infos;
	}
	
	public static void main (String[] args) {
//		CertificateRequestVIPLocator service = new CertificateRequestVIPLocator(); 
//		CertificateInfo[] infos  = null;
//		try {
//			Loggers.PERFORMANCE.info(service.getCertificateRequestVIPSoap().helloWorld());
//			infos = service.getCertificateRequestVIPSoap().queryCertificateSingle("HX231006U003", "SYC222749", "WAC24Z135136456", "LGAX5D653E8077736");
//			Loggers.PERFORMANCE.info(infos);
//		} catch (RemoteException e) {
//			// TODO Auto-generated catch block
//			e.printStackTrace();
//		} catch (ServiceException e) {
//			// TODO Auto-generated catch block
//			e.printStackTrace();
//		}
	}
	
}
