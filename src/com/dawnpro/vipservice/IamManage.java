package com.dawnpro.vipservice;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.dawnpro.commons.SystemParam;
import com.dawnpro.commons.util.MapUtil;
import com.dawnpro.commons.util.StringTools;
import com.dawnpro.commons.util.db.DAOException;
import com.dawnpro.commons.util.db.TransactionException;
import com.dawnpro.core.log.Loggers;
import com.dawnpro.entity.iam.LoginRequest;
import com.dawnpro.entity.iam.LoginResult;
import com.dawnpro.entity.iam.LogoutRequest;
import com.dawnpro.entity.iam.LogoutResult;
import com.dawnpro.entity.iam.PullFinishRequest;
import com.dawnpro.entity.iam.PullFinishResult;
import com.dawnpro.entity.iam.PullTaskNormalResult;
import com.dawnpro.entity.iam.PullTaskOrg;
import com.dawnpro.entity.iam.PullTaskOrgResult;
import com.dawnpro.entity.iam.PullTaskRequest;
import com.dawnpro.entity.iam.PullTaskUser;
import com.dawnpro.entity.iam.PullTaskUserResult;
import com.dawnpro.service.BaseServer;
import com.dawnpro.service.commons.HttpUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * 集团IAM接口管理
 * <AUTHOR>
 * @version 1.0
 * 
 */
public class IamManage extends BaseServer {
	
	/**
	 * 开始登陆IAM系统
	 * @return
	 */
	public String iamLogin() {
		Loggers.INTERFACE.info("1-0 IAM系统登陆：开始");
		String result = null;	
		String re = "fail";
		try {
			List tempList = this.dao.queryForList("select qybh,password from tbl_up_account where deleteflag='0' and uptype='iam'");
			if (tempList != null && tempList.size() > 0) {
				
				Loggers.INTERFACE.info("1-0 IAM系统登陆：找到用户名密码，开始登陆");
				
				String qybh = MapUtil.getMapValue((Map)tempList.get(0), "qybh", "");
				String password = MapUtil.getMapValue((Map)tempList.get(0), "password", "");
				LoginRequest loginRequest  = new LoginRequest();
				loginRequest.setSystemCode(qybh);
				loginRequest.setIntegrationKey(password);
				loginRequest.setForce("false");
				loginRequest.setTimestamp(String.valueOf(System.currentTimeMillis()));
				
				result = login(loginRequest);
				
				if (!StringTools.isBlank(result) && !"fail".equals(result.trim())) {
					this.dao.execute("update tbl_up_account set token='"+result+"',updatetime=now() where deleteflag='0' and uptype='iam'");
					re="ok";
				}else {
					Loggers.INTERFACE.info("1-0 IAM系统登陆：token获取为空，请联系IAM系统");
				}
				
			}else {
				Loggers.INTERFACE.info("1-0 IAM系统登陆：未找到登陆用户名及密码，请联系管理员维护");
			}
			
		} catch (DAOException e) {
			e.printStackTrace();
			Loggers.INTERFACE.info("1-0 IAM系统登陆：登陆报错：");
			Loggers.INTERFACE.debug("1-0 IAM系统登陆：登陆报错："+e.toString());
		} catch (SQLException e) {
			e.printStackTrace();
			Loggers.INTERFACE.info("1-0 IAM系统登陆：登陆报错：");
			Loggers.INTERFACE.debug("1-0 IAM系统登陆：登陆报错："+e.toString());
		} catch (TransactionException e) {
			e.printStackTrace();
			Loggers.INTERFACE.info("1-0 IAM系统登陆：登陆报错：");
			Loggers.INTERFACE.debug("1-0 IAM系统登陆：登陆报错："+e.toString());
		} 
		Loggers.INTERFACE.info("1-0 IAM系统登陆：结束");
		return re;
	}
	
	/**
	 * 登陆集团IAM系统
	 * 获取token
	 * @return
	 */
	public String login(LoginRequest loginRequest) {
		Loggers.INTERFACE.info("1-1 IAM系统登陆接口：开始");
		String result = null;	
		String re = "fail";	
		
		String loginUrl = SystemParam.getKey("iam_login_url", "http://10.87.0.115:32174/bim-server/integration/api.json?method=login&request=");
		Loggers.INTERFACE.debug("1-1 IAM系统登陆接口：登陆URL："+loginUrl);

		ObjectMapper mapper = new ObjectMapper();
		String postData = null;
		try {
			postData = mapper.writeValueAsString(loginRequest);
			postData=URLEncoder.encode(postData,"utf-8");
			Loggers.INTERFACE.debug("1-1 IAM系统登陆接口：发送数据："+postData);
			result=HttpUtil.doGet(loginUrl+postData);
			Loggers.INTERFACE.info("1-1 IAM系统登陆接口：登陆成功 ,接口反馈："+result);
			
//			LoginResult loginResult=mapper.readValue(result,LoginResult.class);
			JsonNode tree = mapper.readTree(result);
			String success = tree.get("success").asText();
			String tokenid = tree.get("tokenId").asText();
			String message = tree.get("message").asText();
			if("true".equals(success)) {
				Loggers.INTERFACE.info("1-1 IAM系统登陆接口：登陆反馈成功 token："+tokenid);
				re=tokenid;
			}else {
				Loggers.INTERFACE.info("1-1 IAM系统登陆接口：登陆反馈失败 message："+message);
			}
			
		} catch (JsonProcessingException e1) {
			Loggers.INTERFACE.info("1-1 IAM系统登陆接口：登陆数据转换json报错");
			e1.printStackTrace();
		}	catch (Exception e) {
			Loggers.INTERFACE.info("1-1 IAM系统登陆接口：登陆报错：");
			Loggers.INTERFACE.debug("1-1 IAM系统登陆接口：登陆报错："+e.toString());
			e.printStackTrace();
		}
		Loggers.INTERFACE.info("1-1 IAM系统登陆接口：结束");
		return re;
	}
	
	/**
	 * 开始登出IAM系统
	 * @return
	 */
	public String iamLogout() {
		Loggers.INTERFACE.info("4-0 IAM系统登出：开始");
		String result = null;	
		String re = "fail";	
		try {
			List tempList = this.dao.queryForList("select token,updatetime from tbl_up_account where deleteflag='0' and uptype='iam'");
			if (tempList != null && tempList.size() > 0) {
				
				String token = MapUtil.getMapValue((Map)tempList.get(0), "token", "");
				String updatetime = MapUtil.getMapValue((Map)tempList.get(0), "updatetime", "");
				Loggers.INTERFACE.info("4-0 IAM系统登出：找到token["+token+"]，updateatime["+updatetime+"]，开始登出");
				
				LogoutRequest logoutRequest = new LogoutRequest();
				logoutRequest.setTokenId(token);
				logoutRequest.setTimestamp(String.valueOf(System.currentTimeMillis()));

				result = logout(logoutRequest);
				
				if (!StringTools.isBlank(result) && !"fail".equals(result.trim())) {
					this.dao.execute("update tbl_up_account set token='',updatetime=now() where deleteflag='0' and uptype='iam'");
					re="ok";
				}else {
					Loggers.INTERFACE.info("4-0 IAM系统登出：登出反馈异常，请联系管理员");
				}
			}else {
				Loggers.INTERFACE.info("4-0 IAM系统登出：合格证系统中未找到token，请联系管理员");
			}
			
		} catch (DAOException e) {
			e.printStackTrace();
			Loggers.INTERFACE.info("4-0 IAM系统登出：登出报错：");
			Loggers.INTERFACE.debug("4-0 IAM系统登出：登出报错："+e.toString());
		} catch (SQLException e) {
			e.printStackTrace();
			Loggers.INTERFACE.info("4-0 IAM系统登出：登出报错：");
			Loggers.INTERFACE.debug("4-0 IAM系统登出：登出报错："+e.toString());
		} catch (TransactionException e) {
			e.printStackTrace();
			Loggers.INTERFACE.info("4-0 IAM系统登出：登出报错：");
			Loggers.INTERFACE.debug("4-0 IAM系统登出：登出报错："+e.toString());
		} 
		Loggers.INTERFACE.info("4-0 IAM系统登出：结束");
		return re;
	}
	
	/**
	 * 登出集团IAM系统
	 * @return
	 */
	public String logout(LogoutRequest logoutRequest) {
		Loggers.INTERFACE.info("4-1 IAM系统登出接口：开始");
		String rs = "fail";	
		String result = null;	
		
		String loginUrl = SystemParam.getKey("iam_logout_url", "http://10.87.0.115:32174/bim-server/integration/api.json?method=logout&request=");
		Loggers.INTERFACE.debug("4-1 IAM系统登出接口：登陆URL："+loginUrl);
		

		ObjectMapper mapper = new ObjectMapper();
		String postData = null;
		try {
			postData = mapper.writeValueAsString(logoutRequest);
			
			Loggers.INTERFACE.debug("4-1 IAM系统登出接口：发送数据："+postData);
			postData=URLEncoder.encode(postData,"utf-8");
			result=HttpUtil.doGet(loginUrl+postData);
			Loggers.INTERFACE.info("4-1 IAM系统登出接口：接口成功 ,接口反馈："+result);
			
//			LogoutResult logoutResult=mapper.readValue(result,LogoutResult.class);
			JsonNode tree = mapper.readTree(result);
			String success = tree.get("success").asText();
			if("true".equals(success)) {
				Loggers.INTERFACE.info("4-1 IAM系统登出接口：接口成功   success：");
				rs="ok";
			}else {
				Loggers.INTERFACE.info("4-1 IAM系统登出接口：接口反馈登出失败 ");
			}
			
		} catch (JsonProcessingException e1) {
			Loggers.INTERFACE.info("4-1 IAM系统登出接口：登陆数据转换json报错");
			e1.printStackTrace();
		}	catch (Exception e) {
			Loggers.INTERFACE.info("4-1 IAM系统登出接口：登陆报错：");
			Loggers.INTERFACE.debug("4-1 IAM系统登出接口：登陆报错："+e.toString());
			e.printStackTrace();
		}
		Loggers.INTERFACE.info("4-1 IAM系统登出接口：结束");
		return rs;
	
	}
	
	
	/**
	 * 开始从pullTask接口获取数据
	 * @return
	 */
	public String iamPullTask() {
		Loggers.INTERFACE.info("2-0 IAM系统pullTask接口：开始");
		String result = "fail";

		try {
			List tempList = this.dao.queryForList("select token from tbl_up_account where deleteflag='0' and uptype='iam'");
			if (tempList != null && tempList.size() > 0) {

				String token = MapUtil.getMapValue((Map) tempList.get(0), "token", "");
				Loggers.INTERFACE.info("2-0 IAM系统pullTask接口：找到token[" + token + "]，开始pullTask");

				PullTaskRequest pTaskRequest = new PullTaskRequest();
				pTaskRequest.setTokenId(token);
				pTaskRequest.setTimestamp(String.valueOf(System.currentTimeMillis()));
				
				int count=100;
				Loggers.INTERFACE.info("2-0 IAM系统pullTask接口：第"+1+"/"+count+"获取数据");
				result = pullTask(pTaskRequest);
				Loggers.INTERFACE.info("2-0 IAM系统pullTask接口：第"+1+"/"+count+"获取数据,反馈："+result);
				
				// 每次循环获取100次 如果100次内获取完毕，反馈end;如果还未获取完毕，反馈countion;如果异常反馈fail。
				for (int i = 2; i <= count; i++) {
					if ("countion".equals(result)) {
						Loggers.INTERFACE.info("2-0 IAM系统pullTask接口：第"+i+"/"+count+"获取数据");
						result = pullTask(pTaskRequest);
						Loggers.INTERFACE.info("2-0 IAM系统pullTask接口：第"+i+"/"+count+"获取数据,反馈："+result);
					}
				}

				if (!StringTools.isBlank(result)) {
					
					if("fail".equals(result)) {
						Loggers.INTERFACE.info("2-0 IAM系统pullTask接口：获取数据结束,反馈："+result);
					}
					else if ("end".equals(result)) {
						Loggers.INTERFACE.info("2-0 IAM系统pullTask接口：获取数据结束,反馈："+result);
					}else {
						Loggers.INTERFACE.info("2-0 IAM系统pullTask接口：获取数据结束,反馈："+result);
					}
					
				} else {
					Loggers.INTERFACE.info("2-0 IAM系统pullTask接口：获取数据完毕后异常，请联系管理员");
				}
			} else {
				Loggers.INTERFACE.info("2-0 IAM系统pullTask接口：合格证系统中未找到token，请联系管理员");
			}
		} catch (DAOException e) {
			Loggers.INTERFACE.info("2-0 IAM系统pullTask接口：从数据库获取token异常，本次任务停止");
			Loggers.INTERFACE.debug("2-0 IAM系统pullTask接口：从数据库获取token异常，本次任务停止"+e.toString());
			e.printStackTrace();
		} catch (SQLException e) {
			Loggers.INTERFACE.info("2-0 IAM系统pullTask接口：从数据库获取token异常，本次任务停止");
			Loggers.INTERFACE.debug("2-0 IAM系统pullTask接口：从数据库获取token异常，本次任务停止"+e.toString());
			e.printStackTrace();
		}

		Loggers.INTERFACE.info("2-0 IAM系统pullTask接口：结束");
		return result;
	}
	
	/**
	 * 从IAM获取待处理数据，并进行处理
	 * @param pTaskRequest
	 * @return
	 */
	private String pullTask(PullTaskRequest  pTaskRequest) {
		String tokenid = pTaskRequest.getTokenId();
		String taskid = null;
		
		Loggers.INTERFACE.info("2-1 IAM系统pulltask接口：开始");
		String result = "";	
		String re = "fail";	
		
		String loginUrl = SystemParam.getKey("iam_pulltask_url", "http://10.87.0.115:32174/bim-server/integration/api.json?method=pullTask&request=");
		Loggers.INTERFACE.debug("2-1 IAM系统pulltask接口：登陆URL："+loginUrl);

		ObjectMapper mapper = new ObjectMapper();
		String postData = null;
		try {
			postData = mapper.writeValueAsString(pTaskRequest);
			
			Loggers.INTERFACE.debug("2-1 IAM系统pulltask接口：发送数据："+postData);
			postData=URLEncoder.encode(postData,"utf-8");
			result=HttpUtil.doGet(loginUrl+postData);
			Loggers.INTERFACE.info("2-1 IAM系统pulltask接口：联通成功 ,接口反馈："+result);
			
			//pulltask接口反馈为空时，直接反馈接口失败。避免空指针异常提示
			if(StringTools.isBlank(result)) {
				Loggers.INTERFACE.info("2-1 IAM系统pulltask接口：联通接口反馈空，本次连接停止");
				return re;
			}
			
			
//			PullTaskNormalResult pNormalResult=mapper.readValue(result,PullTaskNormalResult.class);
			JsonNode tree = mapper.readTree(result);
			String success = tree.get("success").asText();
			String objectCode = tree.get("objectCode").asText();
			String objectType = tree.get("objectType").asText();
			String taskId = tree.get("taskId").asText();
			if("true".equals(success)) {
				Loggers.INTERFACE.info("2-1 IAM系统pulltask接口：接口反馈成功 ");
				//判断有无待获取数据
				if(!StringTools.isBlank(objectCode)) {
					//有数据
					if("TARGET_ACCOUNT".endsWith(objectType)) {
						Loggers.INTERFACE.info("2-1 IAM系统pulltask接口：获取到账号数据，进行处理");
						//为账号数据--进行账号处理
						PullTaskUserResult pUserResult= mapper.readValue(result,PullTaskUserResult.class);
						taskid = pUserResult.getTaskId();
						PullTaskUser pUser = pUserResult.getData();
						saveUser(tokenid,taskid,pUser);
						re="countion";
						
					}else if("TARGET_ORGANIZATION".endsWith(objectType)) {
						Loggers.INTERFACE.info("2-1 IAM系统pulltask接口：获取到组织机构数据，进行处理");
						//为机构数据--进行组织机构处理
						PullTaskOrgResult pOrgResult= mapper.readValue(result,PullTaskOrgResult.class);
						taskid = pOrgResult.getTaskId();
						PullTaskOrg pOrg = pOrgResult.getData();
						saveOrg(tokenid,taskid,pOrg);
						re="countion";
					}else {
						//为其它数据
						re="countion";
						String guid = String.valueOf(System.currentTimeMillis());
//						taskid = pNormalResult.getTaskId();
						Loggers.INTERFACE.info("2-1 IAM系统pulltask接口：获取的非账号或机构数据，数据丢弃，反馈当前时间戳： "+guid);
						//执行pullFinish()
						pullFinish(tokenid,taskid,true,guid);
					}
				}else {
					//无数据
					re="end";
					String guid = String.valueOf(System.currentTimeMillis());
//					taskid = pNormalResult.getTaskId();
					Loggers.INTERFACE.info("2-1 IAM系统pulltask接口：无待获取数据，反馈当前时间戳： "+guid);
					//执行pullFinish()
					pullFinish(tokenid,taskid,true,guid);
				}
			}else {
				Loggers.INTERFACE.info("2-1 IAM系统pulltask接口：登陆反馈成功 ");
			}
			
		} catch (JsonProcessingException e1) {
			Loggers.INTERFACE.info("2-1 IAM系统pulltask接口：登陆数据转换json报错");
			e1.printStackTrace();
		}	catch (Exception e) {
			Loggers.INTERFACE.info("2-1 IAM系统pulltask接口：登陆报错：");
			Loggers.INTERFACE.debug("2-1 IAM系统pulltask接口：登陆报错："+e.toString());
			e.printStackTrace();
		}
		Loggers.INTERFACE.info("2-1 IAM系统pulltask接口：结束");
		return re;
	}
	
	
	/**
	 * 保存用户信息
	 * @param tokenid
	 * @param taskid
	 * @param pUser
	 */
	private void saveUser(String tokenid, String taskid, PullTaskUser pUser) {
		String id = createID();
		String sqld = "update ts_user_info_iam set deleteflag=1,deletetime=now() where deleteflag='0' and user='"+pUser.getUser()+"'";
		String sqli = "insert into ts_user_info_iam(id,user,organization,username,password,fullname,disable,locked,createat,updateat) " + 
				"values('"+id
				+"','"+pUser.getUser()
				+"','"+pUser.getOrganization()
				+"','"+pUser.getUsername()
				+"','"+pUser.getPassword()
				+"','"+pUser.getFullname()
				+"','"+(pUser.isDisable()?1:0)
				+"','"+(pUser.isLocked()?1:0)
				+"','"+pUser.getCreateAt()
				+"','"+pUser.getUpdateAt()
				+"')";
		List list = new ArrayList();
		list.add(sqld);
		list.add(sqli);
		try {
			if(this.dao.excuteBatch(list)) {
				Loggers.INTERFACE.info("2-1 IAM系统pulltask接口：账号数据处理成功");
				pullFinish(tokenid,taskid,true,id);
			}else{
				Loggers.INTERFACE.info("2-1 IAM系统pulltask接口：账号数据处理失败");
				pullFinish(tokenid,taskid,false,"执行数据插入失败");
			}
		} catch (DAOException e) {
			e.printStackTrace();
			pullFinish(tokenid,taskid,false,"执行数据插入异常");
		} catch (TransactionException e) {
			e.printStackTrace();
			pullFinish(tokenid,taskid,false,"执行数据插入异常");
		}
		Loggers.INTERFACE.info("2-1 IAM系统pulltask接口：获取到账号数据，进行处理");
	}
	
	/**
	 * 保存组织机构信息
	 * @param tokenid
	 * @param taskid
	 * @param pOrg
	 */
	private void saveOrg(String tokenid, String taskid, PullTaskOrg pOrg) {
		String id = createID();
		String sqld = "update ts_dept_info_iam set deleteflag=1,deletetime=now() where deleteflag='0' and code='"+pOrg.getCode()+"'";
		String sqli = "insert into ts_dept_info_iam(id,parent,organization,code,name,fullname,description,sequence,disable,createat,updateat,parentcode) " + 
				"values('"+id+"','"+pOrg.getParent()
				+"','"+pOrg.getOrganization()
				+"','"+pOrg.getCode()
				+"','"+pOrg.getName()
				+"','"+pOrg.getFullname()
				+"','"+pOrg.getDescription()
				+"','"+pOrg.getSequence()
				+"','"+(pOrg.isDisable()?1:0)
				+"','"+pOrg.getCreateAt()
				+"','"+pOrg.getUpdateAt()
				+"','"+pOrg.getParentCode()
				+"')";
		List list = new ArrayList();
		list.add(sqld);
		list.add(sqli);
		try {
			if(this.dao.excuteBatch(list)) {
				pullFinish(tokenid,taskid,true,id);
			}else{
				pullFinish(tokenid,taskid,false,"执行数据插入失败");
			}
		} catch (DAOException e) {
			e.printStackTrace();
			pullFinish(tokenid,taskid,false,"执行数据插入异常");
		} catch (TransactionException e) {
			e.printStackTrace();
			pullFinish(tokenid,taskid,false,"执行数据插入异常");
		}
	}
	
	/**
	 * 将数据处理结果反馈给IAM
	 * @param tokenid
	 * @param taskid
	 * @param b
	 * @param guid
	 */
	public void pullFinish(String tokenid, String taskid, boolean b, String guid) {
		String result = "fail";	
		Loggers.INTERFACE.info("3-0 IAM系统pullfinish接口：开始");
		
		String loginUrl = SystemParam.getKey("iam_pullfinish_url", "http://10.87.0.115:32174/bim-server/integration/api.json?method=pullFinish&request=");
		Loggers.INTERFACE.debug("3-0 IAM系统pullfinish接口：登陆URL："+loginUrl);
		
		PullFinishRequest pFinishRequest = new PullFinishRequest();
		pFinishRequest.setTokenId(tokenid);
		pFinishRequest.setTaskId(taskid);
		pFinishRequest.setSuccess(b);
		if(b) {
			pFinishRequest.setGuid(guid);
		}else {
			pFinishRequest.setMessage(guid);
		}
		pFinishRequest.setTimestamp(System.currentTimeMillis());
		
		ObjectMapper mapper = new ObjectMapper();
		String postData;
		try {
			postData = mapper.writeValueAsString(pFinishRequest);
			Loggers.INTERFACE.debug("3-0 IAM系统pullfinish接口：发送数据："+postData);
			postData=URLEncoder.encode(postData,"utf-8");
			result=HttpUtil.doGet(loginUrl+postData);
			Loggers.INTERFACE.info("3-0 IAM系统pullfinish接口：联通成功 ,接口反馈："+result);
				
			PullFinishResult pFinishResult=mapper.readValue(result,PullFinishResult.class);
			if (pFinishResult.isSuccess()) {
				Loggers.INTERFACE.info("3-0 IAM系统pullfinish接口：接口反馈成功");
			} else {
				Loggers.INTERFACE.info("3-0 IAM系统pullfinish接口：接口反馈异常。message["+pFinishResult.getMessage()+"],exception["+pFinishResult.getException()+"]");
			}
		} catch (JsonProcessingException e) {
			Loggers.INTERFACE.info("3-0 IAM系统pullfinish接口：实体转换成json报错");
			Loggers.INTERFACE.debug("3-0 IAM系统pullfinish接口：实体转换成json报错，"+e.toString());
			e.printStackTrace();
		} catch (Exception e) {
			Loggers.INTERFACE.info("3-0 IAM系统pullfinish接口：访问接口报错");
			Loggers.INTERFACE.debug("3-0 IAM系统pullfinish接口：访问接口报错:"+e.toString());
			e.printStackTrace();
		}
		Loggers.INTERFACE.info("3-0 IAM系统pullfinish接口：结束");
		
	}
	
	/**
	 * 自动调用iam接口获取数据
	 */
	public void autoIam() {
		Loggers.INTERFACE.info("0-1/3 IAM系统自动获取数据：开始");
		String flag = iamLogin();//登陆
		try {
			if("ok".equals(flag)) {
				//登陆成功，开始下拉数据、处理、反馈拉取结果
				String pullflag = iamPullTask();
				if("fail".equals(pullflag)) {
					Loggers.INTERFACE.info("0-2/3 IAM系统自动获取数据：获取失败");
				}
				else if ("end".equals(pullflag)) {
					Loggers.INTERFACE.info("0-2/3 IAM系统自动获取数据：获取所有数据完成");
				}else {
					Loggers.INTERFACE.info("0-2/3 IAM系统自动获取数据：已获取100条数据，还有待获取数据待下次再获取");
				}
			}
		} finally {
			//登出
			iamLogout();
		}
		Loggers.INTERFACE.info("0-3/3 IAM系统自动获取数据：结束");
	}
	
	public static void main(String[] args) {
		String aString = "{\"systemCode\":\"DFMHGZ\",\"integrationKey\":\"T64cB87#d*c5b3x49L4\",\"force\":\"false\",\"timestamp\":\"1623139690024\"}";
		System.out.println(aString);
		try {
			aString=URLEncoder.encode(aString, "utf-8");
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
		}
		System.out.println(aString);
		
		String b="{\r\n" + 
				"  \"success\" : true,\r\n" + 
				"  \"message\" : null,\r\n" + 
				"  \"exception\" : null,\r\n" + 
				"  \"interrupt\" : false,\r\n" + 
				"  \"timestamp\" : 1623146170926,\r\n" + 
				"  \"tokenId\" : \"550de51e-64bd-473c-8da7-12e983752fa0\",\r\n" + 
				"  \"systemId\" : \"20210531105134767-88CA-EFA0061F9\",\r\n" + 
				"  \"systemCode\" : \"DFMHGZ\",\r\n" + 
				"  \"systemName\" : \"东风公司合格证管理系统\",\r\n" + 
				"  \"schemas\" : [ {\r\n" + 
				"    \"objectType\" : \"TARGET_ORGANIZATION\",\r\n" + 
				"    \"objectId\" : \"20210531105141159-E068-5452E86FC\",\r\n" + 
				"    \"objectCode\" : \"DFMHGZ_ORG\",\r\n" + 
				"    \"objectName\" : \"东风公司合格证管理系统机构\",\r\n" + 
				"    \"objectAttributes\" : [ {\r\n" + 
				"      \"id\" : null,\r\n" + 
				"      \"type\" : \"STRING\",\r\n" + 
				"      \"provisionMethod\" : \"AUTO\",\r\n" + 
				"      \"reconcileMethod\" : \"AUTO\",\r\n" + 
				"      \"parentCode\" : null,\r\n" + 
				"      \"code\" : \"_parent\",\r\n" + 
				"      \"name\" : \"父机构\",\r\n" + 
				"      \"length\" : null,\r\n" + 
				"      \"description\" : null,\r\n" + 
				"      \"isRequired\" : false,\r\n" + 
				"      \"isUniqued\" : false,\r\n" + 
				"      \"isMultiValue\" : false,\r\n" + 
				"      \"isEncrypted\" : false,\r\n" + 
				"      \"lookupDefinitionCode\" : null,\r\n" + 
				"      \"options\" : null,\r\n" + 
				"      \"prompt\" : null\r\n" + 
				"    }, {\r\n" + 
				"      \"id\" : null,\r\n" + 
				"      \"type\" : \"STRING\",\r\n" + 
				"      \"provisionMethod\" : \"AUTO\",\r\n" + 
				"      \"reconcileMethod\" : \"AUTO\",\r\n" + 
				"      \"parentCode\" : null,\r\n" + 
				"      \"code\" : \"_organization\",\r\n" + 
				"      \"name\" : \"机构\",\r\n" + 
				"      \"length\" : null,\r\n" + 
				"      \"description\" : null,\r\n" + 
				"      \"isRequired\" : false,\r\n" + 
				"      \"isUniqued\" : false,\r\n" + 
				"      \"isMultiValue\" : false,\r\n" + 
				"      \"isEncrypted\" : false,\r\n" + 
				"      \"lookupDefinitionCode\" : null,\r\n" + 
				"      \"options\" : null,\r\n" + 
				"      \"prompt\" : null\r\n" + 
				"    }, {\r\n" + 
				"      \"id\" : null,\r\n" + 
				"      \"type\" : \"STRING\",\r\n" + 
				"      \"provisionMethod\" : \"AUTO\",\r\n" + 
				"      \"reconcileMethod\" : \"AUTO\",\r\n" + 
				"      \"parentCode\" : null,\r\n" + 
				"      \"code\" : \"code\",\r\n" + 
				"      \"name\" : \"代码\",\r\n" + 
				"      \"length\" : 128,\r\n" + 
				"      \"description\" : null,\r\n" + 
				"      \"isRequired\" : false,\r\n" + 
				"      \"isUniqued\" : false,\r\n" + 
				"      \"isMultiValue\" : false,\r\n" + 
				"      \"isEncrypted\" : false,\r\n" + 
				"      \"lookupDefinitionCode\" : null,\r\n" + 
				"      \"options\" : null,\r\n" + 
				"      \"prompt\" : null\r\n" + 
				"    }, {\r\n" + 
				"      \"id\" : null,\r\n" + 
				"      \"type\" : \"STRING\",\r\n" + 
				"      \"provisionMethod\" : \"AUTO\",\r\n" + 
				"      \"reconcileMethod\" : \"AUTO\",\r\n" + 
				"      \"parentCode\" : null,\r\n" + 
				"      \"code\" : \"name\",\r\n" + 
				"      \"name\" : \"名称\",\r\n" + 
				"      \"length\" : 128,\r\n" + 
				"      \"description\" : null,\r\n" + 
				"      \"isRequired\" : true,\r\n" + 
				"      \"isUniqued\" : false,\r\n" + 
				"      \"isMultiValue\" : false,\r\n" + 
				"      \"isEncrypted\" : false,\r\n" + 
				"      \"lookupDefinitionCode\" : null,\r\n" + 
				"      \"options\" : null,\r\n" + 
				"      \"prompt\" : null\r\n" + 
				"    }, {\r\n" + 
				"      \"id\" : null,\r\n" + 
				"      \"type\" : \"STRING\",\r\n" + 
				"      \"provisionMethod\" : \"AUTO\",\r\n" + 
				"      \"reconcileMethod\" : \"AUTO\",\r\n" + 
				"      \"parentCode\" : null,\r\n" + 
				"      \"code\" : \"fullname\",\r\n" + 
				"      \"name\" : \"机构全名\",\r\n" + 
				"      \"length\" : 128,\r\n" + 
				"      \"description\" : null,\r\n" + 
				"      \"isRequired\" : false,\r\n" + 
				"      \"isUniqued\" : false,\r\n" + 
				"      \"isMultiValue\" : false,\r\n" + 
				"      \"isEncrypted\" : false,\r\n" + 
				"      \"lookupDefinitionCode\" : null,\r\n" + 
				"      \"options\" : null,\r\n" + 
				"      \"prompt\" : null\r\n" + 
				"    }, {\r\n" + 
				"      \"id\" : null,\r\n" + 
				"      \"type\" : \"STRING\",\r\n" + 
				"      \"provisionMethod\" : \"AUTO\",\r\n" + 
				"      \"reconcileMethod\" : \"AUTO\",\r\n" + 
				"      \"parentCode\" : null,\r\n" + 
				"      \"code\" : \"description\",\r\n" + 
				"      \"name\" : \"描述\",\r\n" + 
				"      \"length\" : 512,\r\n" + 
				"      \"description\" : null,\r\n" + 
				"      \"isRequired\" : false,\r\n" + 
				"      \"isUniqued\" : false,\r\n" + 
				"      \"isMultiValue\" : false,\r\n" + 
				"      \"isEncrypted\" : false,\r\n" + 
				"      \"lookupDefinitionCode\" : null,\r\n" + 
				"      \"options\" : null,\r\n" + 
				"      \"prompt\" : null\r\n" + 
				"    }, {\r\n" + 
				"      \"id\" : null,\r\n" + 
				"      \"type\" : \"INTEGER\",\r\n" + 
				"      \"provisionMethod\" : \"AUTO\",\r\n" + 
				"      \"reconcileMethod\" : \"AUTO\",\r\n" + 
				"      \"parentCode\" : null,\r\n" + 
				"      \"code\" : \"sequence\",\r\n" + 
				"      \"name\" : \"序号\",\r\n" + 
				"      \"length\" : 10,\r\n" + 
				"      \"description\" : null,\r\n" + 
				"      \"isRequired\" : false,\r\n" + 
				"      \"isUniqued\" : false,\r\n" + 
				"      \"isMultiValue\" : false,\r\n" + 
				"      \"isEncrypted\" : false,\r\n" + 
				"      \"lookupDefinitionCode\" : null,\r\n" + 
				"      \"options\" : null,\r\n" + 
				"      \"prompt\" : null\r\n" + 
				"    }, {\r\n" + 
				"      \"id\" : null,\r\n" + 
				"      \"type\" : \"BOOLEAN\",\r\n" + 
				"      \"provisionMethod\" : \"AUTO\",\r\n" + 
				"      \"reconcileMethod\" : \"AUTO\",\r\n" + 
				"      \"parentCode\" : null,\r\n" + 
				"      \"code\" : \"isDisabled\",\r\n" + 
				"      \"name\" : \"禁用\",\r\n" + 
				"      \"length\" : 1,\r\n" + 
				"      \"description\" : null,\r\n" + 
				"      \"isRequired\" : false,\r\n" + 
				"      \"isUniqued\" : false,\r\n" + 
				"      \"isMultiValue\" : false,\r\n" + 
				"      \"isEncrypted\" : false,\r\n" + 
				"      \"lookupDefinitionCode\" : null,\r\n" + 
				"      \"options\" : null,\r\n" + 
				"      \"prompt\" : null\r\n" + 
				"    }, {\r\n" + 
				"      \"id\" : null,\r\n" + 
				"      \"type\" : \"TIMESTAMP\",\r\n" + 
				"      \"provisionMethod\" : \"AUTO\",\r\n" + 
				"      \"reconcileMethod\" : \"AUTO\",\r\n" + 
				"      \"parentCode\" : null,\r\n" + 
				"      \"code\" : \"createAt\",\r\n" + 
				"      \"name\" : \"创建日期\",\r\n" + 
				"      \"length\" : null,\r\n" + 
				"      \"description\" : null,\r\n" + 
				"      \"isRequired\" : false,\r\n" + 
				"      \"isUniqued\" : false,\r\n" + 
				"      \"isMultiValue\" : false,\r\n" + 
				"      \"isEncrypted\" : false,\r\n" + 
				"      \"lookupDefinitionCode\" : null,\r\n" + 
				"      \"options\" : null,\r\n" + 
				"      \"prompt\" : null\r\n" + 
				"    }, {\r\n" + 
				"      \"id\" : null,\r\n" + 
				"      \"type\" : \"TIMESTAMP\",\r\n" + 
				"      \"provisionMethod\" : \"AUTO\",\r\n" + 
				"      \"reconcileMethod\" : \"AUTO\",\r\n" + 
				"      \"parentCode\" : null,\r\n" + 
				"      \"code\" : \"updateAt\",\r\n" + 
				"      \"name\" : \"更新日期\",\r\n" + 
				"      \"length\" : null,\r\n" + 
				"      \"description\" : null,\r\n" + 
				"      \"isRequired\" : false,\r\n" + 
				"      \"isUniqued\" : false,\r\n" + 
				"      \"isMultiValue\" : false,\r\n" + 
				"      \"isEncrypted\" : false,\r\n" + 
				"      \"lookupDefinitionCode\" : null,\r\n" + 
				"      \"options\" : null,\r\n" + 
				"      \"prompt\" : null\r\n" + 
				"    }, {\r\n" + 
				"      \"id\" : null,\r\n" + 
				"      \"type\" : \"STRING\",\r\n" + 
				"      \"provisionMethod\" : \"AUTO\",\r\n" + 
				"      \"reconcileMethod\" : \"AUTO\",\r\n" + 
				"      \"parentCode\" : null,\r\n" + 
				"      \"code\" : \"mocsCode\",\r\n" + 
				"      \"name\" : \"MOCS组织编码\",\r\n" + 
				"      \"length\" : 64,\r\n" + 
				"      \"description\" : \"\",\r\n" + 
				"      \"isRequired\" : false,\r\n" + 
				"      \"isUniqued\" : false,\r\n" + 
				"      \"isMultiValue\" : false,\r\n" + 
				"      \"isEncrypted\" : false,\r\n" + 
				"      \"lookupDefinitionCode\" : null,\r\n" + 
				"      \"options\" : null,\r\n" + 
				"      \"prompt\" : null\r\n" + 
				"    }, {\r\n" + 
				"      \"id\" : null,\r\n" + 
				"      \"type\" : \"STRING\",\r\n" + 
				"      \"provisionMethod\" : \"AUTO\",\r\n" + 
				"      \"reconcileMethod\" : \"AUTO\",\r\n" + 
				"      \"parentCode\" : null,\r\n" + 
				"      \"code\" : \"parentCode\",\r\n" + 
				"      \"name\" : \"父机构编码\",\r\n" + 
				"      \"length\" : 64,\r\n" + 
				"      \"description\" : null,\r\n" + 
				"      \"isRequired\" : false,\r\n" + 
				"      \"isUniqued\" : false,\r\n" + 
				"      \"isMultiValue\" : false,\r\n" + 
				"      \"isEncrypted\" : false,\r\n" + 
				"      \"lookupDefinitionCode\" : null,\r\n" + 
				"      \"options\" : null,\r\n" + 
				"      \"prompt\" : null\r\n" + 
				"    }, {\r\n" + 
				"      \"id\" : null,\r\n" + 
				"      \"type\" : \"STRING\",\r\n" + 
				"      \"provisionMethod\" : \"AUTO\",\r\n" + 
				"      \"reconcileMethod\" : \"AUTO\",\r\n" + 
				"      \"parentCode\" : null,\r\n" + 
				"      \"code\" : \"mocsParentCode\",\r\n" + 
				"      \"name\" : \"MOCS父组织编码\",\r\n" + 
				"      \"length\" : 64,\r\n" + 
				"      \"description\" : \"\",\r\n" + 
				"      \"isRequired\" : false,\r\n" + 
				"      \"isUniqued\" : false,\r\n" + 
				"      \"isMultiValue\" : false,\r\n" + 
				"      \"isEncrypted\" : false,\r\n" + 
				"      \"lookupDefinitionCode\" : null,\r\n" + 
				"      \"options\" : null,\r\n" + 
				"      \"prompt\" : null\r\n" + 
				"    } ]\r\n" + 
				"  }, {\r\n" + 
				"    \"objectType\" : \"TARGET_ACCOUNT\",\r\n" + 
				"    \"objectId\" : \"*****************-731D-D75E1D7BA\",\r\n" + 
				"    \"objectCode\" : \"DFMHGZ_ACC\",\r\n" + 
				"    \"objectName\" : \"东风公司合格证管理系统账号\",\r\n" + 
				"    \"objectAttributes\" : [ {\r\n" + 
				"      \"id\" : null,\r\n" + 
				"      \"type\" : \"STRING\",\r\n" + 
				"      \"provisionMethod\" : \"AUTO\",\r\n" + 
				"      \"reconcileMethod\" : \"AUTO\",\r\n" + 
				"      \"parentCode\" : null,\r\n" + 
				"      \"code\" : \"_user\",\r\n" + 
				"      \"name\" : \"用户\",\r\n" + 
				"      \"length\" : null,\r\n" + 
				"      \"description\" : null,\r\n" + 
				"      \"isRequired\" : false,\r\n" + 
				"      \"isUniqued\" : false,\r\n" + 
				"      \"isMultiValue\" : false,\r\n" + 
				"      \"isEncrypted\" : false,\r\n" + 
				"      \"lookupDefinitionCode\" : null,\r\n" + 
				"      \"options\" : null,\r\n" + 
				"      \"prompt\" : null\r\n" + 
				"    }, {\r\n" + 
				"      \"id\" : null,\r\n" + 
				"      \"type\" : \"STRING\",\r\n" + 
				"      \"provisionMethod\" : \"AUTO\",\r\n" + 
				"      \"reconcileMethod\" : \"AUTO\",\r\n" + 
				"      \"parentCode\" : null,\r\n" + 
				"      \"code\" : \"_organization\",\r\n" + 
				"      \"name\" : \"所属机构\",\r\n" + 
				"      \"length\" : null,\r\n" + 
				"      \"description\" : null,\r\n" + 
				"      \"isRequired\" : false,\r\n" + 
				"      \"isUniqued\" : false,\r\n" + 
				"      \"isMultiValue\" : false,\r\n" + 
				"      \"isEncrypted\" : false,\r\n" + 
				"      \"lookupDefinitionCode\" : null,\r\n" + 
				"      \"options\" : null,\r\n" + 
				"      \"prompt\" : null\r\n" + 
				"    }, {\r\n" + 
				"      \"id\" : null,\r\n" + 
				"      \"type\" : \"STRING\",\r\n" + 
				"      \"provisionMethod\" : \"AUTO\",\r\n" + 
				"      \"reconcileMethod\" : \"AUTO\",\r\n" + 
				"      \"parentCode\" : null,\r\n" + 
				"      \"code\" : \"username\",\r\n" + 
				"      \"name\" : \"账号名\",\r\n" + 
				"      \"length\" : 64,\r\n" + 
				"      \"description\" : null,\r\n" + 
				"      \"isRequired\" : true,\r\n" + 
				"      \"isUniqued\" : false,\r\n" + 
				"      \"isMultiValue\" : false,\r\n" + 
				"      \"isEncrypted\" : false,\r\n" + 
				"      \"lookupDefinitionCode\" : null,\r\n" + 
				"      \"options\" : null,\r\n" + 
				"      \"prompt\" : null\r\n" + 
				"    }, {\r\n" + 
				"      \"id\" : null,\r\n" + 
				"      \"type\" : \"STRING\",\r\n" + 
				"      \"provisionMethod\" : \"AUTO\",\r\n" + 
				"      \"reconcileMethod\" : \"AUTO\",\r\n" + 
				"      \"parentCode\" : null,\r\n" + 
				"      \"code\" : \"password\",\r\n" + 
				"      \"name\" : \"密码\",\r\n" + 
				"      \"length\" : 64,\r\n" + 
				"      \"description\" : null,\r\n" + 
				"      \"isRequired\" : false,\r\n" + 
				"      \"isUniqued\" : false,\r\n" + 
				"      \"isMultiValue\" : false,\r\n" + 
				"      \"isEncrypted\" : true,\r\n" + 
				"      \"lookupDefinitionCode\" : null,\r\n" + 
				"      \"options\" : null,\r\n" + 
				"      \"prompt\" : null\r\n" + 
				"    }, {\r\n" + 
				"      \"id\" : null,\r\n" + 
				"      \"type\" : \"STRING\",\r\n" + 
				"      \"provisionMethod\" : \"AUTO\",\r\n" + 
				"      \"reconcileMethod\" : \"AUTO\",\r\n" + 
				"      \"parentCode\" : null,\r\n" + 
				"      \"code\" : \"fullname\",\r\n" + 
				"      \"name\" : \"姓名\",\r\n" + 
				"      \"length\" : 64,\r\n" + 
				"      \"description\" : null,\r\n" + 
				"      \"isRequired\" : true,\r\n" + 
				"      \"isUniqued\" : false,\r\n" + 
				"      \"isMultiValue\" : false,\r\n" + 
				"      \"isEncrypted\" : false,\r\n" + 
				"      \"lookupDefinitionCode\" : null,\r\n" + 
				"      \"options\" : null,\r\n" + 
				"      \"prompt\" : null\r\n" + 
				"    }, {\r\n" + 
				"      \"id\" : null,\r\n" + 
				"      \"type\" : \"BOOLEAN\",\r\n" + 
				"      \"provisionMethod\" : \"AUTO\",\r\n" + 
				"      \"reconcileMethod\" : \"AUTO\",\r\n" + 
				"      \"parentCode\" : null,\r\n" + 
				"      \"code\" : \"isDisabled\",\r\n" + 
				"      \"name\" : \"禁用\",\r\n" + 
				"      \"length\" : 1,\r\n" + 
				"      \"description\" : null,\r\n" + 
				"      \"isRequired\" : false,\r\n" + 
				"      \"isUniqued\" : false,\r\n" + 
				"      \"isMultiValue\" : false,\r\n" + 
				"      \"isEncrypted\" : false,\r\n" + 
				"      \"lookupDefinitionCode\" : null,\r\n" + 
				"      \"options\" : null,\r\n" + 
				"      \"prompt\" : null\r\n" + 
				"    }, {\r\n" + 
				"      \"id\" : null,\r\n" + 
				"      \"type\" : \"BOOLEAN\",\r\n" + 
				"      \"provisionMethod\" : \"AUTO\",\r\n" + 
				"      \"reconcileMethod\" : \"AUTO\",\r\n" + 
				"      \"parentCode\" : null,\r\n" + 
				"      \"code\" : \"isLocked\",\r\n" + 
				"      \"name\" : \"锁定\",\r\n" + 
				"      \"length\" : 1,\r\n" + 
				"      \"description\" : null,\r\n" + 
				"      \"isRequired\" : false,\r\n" + 
				"      \"isUniqued\" : false,\r\n" + 
				"      \"isMultiValue\" : false,\r\n" + 
				"      \"isEncrypted\" : false,\r\n" + 
				"      \"lookupDefinitionCode\" : null,\r\n" + 
				"      \"options\" : null,\r\n" + 
				"      \"prompt\" : null\r\n" + 
				"    }, {\r\n" + 
				"      \"id\" : null,\r\n" + 
				"      \"type\" : \"TIMESTAMP\",\r\n" + 
				"      \"provisionMethod\" : \"AUTO\",\r\n" + 
				"      \"reconcileMethod\" : \"AUTO\",\r\n" + 
				"      \"parentCode\" : null,\r\n" + 
				"      \"code\" : \"createAt\",\r\n" + 
				"      \"name\" : \"创建日期\",\r\n" + 
				"      \"length\" : null,\r\n" + 
				"      \"description\" : null,\r\n" + 
				"      \"isRequired\" : false,\r\n" + 
				"      \"isUniqued\" : false,\r\n" + 
				"      \"isMultiValue\" : false,\r\n" + 
				"      \"isEncrypted\" : false,\r\n" + 
				"      \"lookupDefinitionCode\" : null,\r\n" + 
				"      \"options\" : null,\r\n" + 
				"      \"prompt\" : null\r\n" + 
				"    }, {\r\n" + 
				"      \"id\" : null,\r\n" + 
				"      \"type\" : \"TIMESTAMP\",\r\n" + 
				"      \"provisionMethod\" : \"AUTO\",\r\n" + 
				"      \"reconcileMethod\" : \"AUTO\",\r\n" + 
				"      \"parentCode\" : null,\r\n" + 
				"      \"code\" : \"updateAt\",\r\n" + 
				"      \"name\" : \"更新日期\",\r\n" + 
				"      \"length\" : null,\r\n" + 
				"      \"description\" : null,\r\n" + 
				"      \"isRequired\" : false,\r\n" + 
				"      \"isUniqued\" : false,\r\n" + 
				"      \"isMultiValue\" : false,\r\n" + 
				"      \"isEncrypted\" : false,\r\n" + 
				"      \"lookupDefinitionCode\" : null,\r\n" + 
				"      \"options\" : null,\r\n" + 
				"      \"prompt\" : null\r\n" + 
				"    }, {\r\n" + 
				"      \"id\" : null,\r\n" + 
				"      \"type\" : \"BOOLEAN\",\r\n" + 
				"      \"provisionMethod\" : \"AUTO\",\r\n" + 
				"      \"reconcileMethod\" : \"AUTO\",\r\n" + 
				"      \"parentCode\" : null,\r\n" + 
				"      \"code\" : \"isSystem\",\r\n" + 
				"      \"name\" : \"系统账号\",\r\n" + 
				"      \"length\" : 1,\r\n" + 
				"      \"description\" : null,\r\n" + 
				"      \"isRequired\" : false,\r\n" + 
				"      \"isUniqued\" : false,\r\n" + 
				"      \"isMultiValue\" : false,\r\n" + 
				"      \"isEncrypted\" : false,\r\n" + 
				"      \"lookupDefinitionCode\" : null,\r\n" + 
				"      \"options\" : null,\r\n" + 
				"      \"prompt\" : null\r\n" + 
				"    }, {\r\n" + 
				"      \"id\" : null,\r\n" + 
				"      \"type\" : \"BOOLEAN\",\r\n" + 
				"      \"provisionMethod\" : \"AUTO\",\r\n" + 
				"      \"reconcileMethod\" : \"AUTO\",\r\n" + 
				"      \"parentCode\" : null,\r\n" + 
				"      \"code\" : \"isPublic\",\r\n" + 
				"      \"name\" : \"公共账号\",\r\n" + 
				"      \"length\" : 1,\r\n" + 
				"      \"description\" : null,\r\n" + 
				"      \"isRequired\" : false,\r\n" + 
				"      \"isUniqued\" : false,\r\n" + 
				"      \"isMultiValue\" : false,\r\n" + 
				"      \"isEncrypted\" : false,\r\n" + 
				"      \"lookupDefinitionCode\" : null,\r\n" + 
				"      \"options\" : null,\r\n" + 
				"      \"prompt\" : null\r\n" + 
				"    }, {\r\n" + 
				"      \"id\" : null,\r\n" + 
				"      \"type\" : \"BOOLEAN\",\r\n" + 
				"      \"provisionMethod\" : \"AUTO\",\r\n" + 
				"      \"reconcileMethod\" : \"AUTO\",\r\n" + 
				"      \"parentCode\" : null,\r\n" + 
				"      \"code\" : \"isMaster\",\r\n" + 
				"      \"name\" : \"主账号\",\r\n" + 
				"      \"length\" : 1,\r\n" + 
				"      \"description\" : null,\r\n" + 
				"      \"isRequired\" : false,\r\n" + 
				"      \"isUniqued\" : false,\r\n" + 
				"      \"isMultiValue\" : false,\r\n" + 
				"      \"isEncrypted\" : false,\r\n" + 
				"      \"lookupDefinitionCode\" : null,\r\n" + 
				"      \"options\" : null,\r\n" + 
				"      \"prompt\" : null\r\n" + 
				"    }, {\r\n" + 
				"      \"id\" : null,\r\n" + 
				"      \"type\" : \"DATE\",\r\n" + 
				"      \"provisionMethod\" : \"AUTO\",\r\n" + 
				"      \"reconcileMethod\" : \"AUTO\",\r\n" + 
				"      \"parentCode\" : null,\r\n" + 
				"      \"code\" : \"startDate\",\r\n" + 
				"      \"name\" : \"开始日期\",\r\n" + 
				"      \"length\" : null,\r\n" + 
				"      \"description\" : null,\r\n" + 
				"      \"isRequired\" : false,\r\n" + 
				"      \"isUniqued\" : false,\r\n" + 
				"      \"isMultiValue\" : false,\r\n" + 
				"      \"isEncrypted\" : false,\r\n" + 
				"      \"lookupDefinitionCode\" : null,\r\n" + 
				"      \"options\" : null,\r\n" + 
				"      \"prompt\" : null\r\n" + 
				"    }, {\r\n" + 
				"      \"id\" : null,\r\n" + 
				"      \"type\" : \"DATE\",\r\n" + 
				"      \"provisionMethod\" : \"AUTO\",\r\n" + 
				"      \"reconcileMethod\" : \"AUTO\",\r\n" + 
				"      \"parentCode\" : null,\r\n" + 
				"      \"code\" : \"endDate\",\r\n" + 
				"      \"name\" : \"结束日期\",\r\n" + 
				"      \"length\" : null,\r\n" + 
				"      \"description\" : null,\r\n" + 
				"      \"isRequired\" : false,\r\n" + 
				"      \"isUniqued\" : false,\r\n" + 
				"      \"isMultiValue\" : false,\r\n" + 
				"      \"isEncrypted\" : false,\r\n" + 
				"      \"lookupDefinitionCode\" : null,\r\n" + 
				"      \"options\" : null,\r\n" + 
				"      \"prompt\" : null\r\n" + 
				"    }, {\r\n" + 
				"      \"id\" : null,\r\n" + 
				"      \"type\" : \"STRING\",\r\n" + 
				"      \"provisionMethod\" : \"AUTO\",\r\n" + 
				"      \"reconcileMethod\" : \"AUTO\",\r\n" + 
				"      \"parentCode\" : null,\r\n" + 
				"      \"code\" : \"idCard\",\r\n" + 
				"      \"name\" : \"身份证号\",\r\n" + 
				"      \"length\" : 64,\r\n" + 
				"      \"description\" : \"\",\r\n" + 
				"      \"isRequired\" : false,\r\n" + 
				"      \"isUniqued\" : false,\r\n" + 
				"      \"isMultiValue\" : false,\r\n" + 
				"      \"isEncrypted\" : false,\r\n" + 
				"      \"lookupDefinitionCode\" : null,\r\n" + 
				"      \"options\" : null,\r\n" + 
				"      \"prompt\" : null\r\n" + 
				"    }, {\r\n" + 
				"      \"id\" : null,\r\n" + 
				"      \"type\" : \"STRING\",\r\n" + 
				"      \"provisionMethod\" : \"AUTO\",\r\n" + 
				"      \"reconcileMethod\" : \"AUTO\",\r\n" + 
				"      \"parentCode\" : null,\r\n" + 
				"      \"code\" : \"email\",\r\n" + 
				"      \"name\" : \"邮箱\",\r\n" + 
				"      \"length\" : 64,\r\n" + 
				"      \"description\" : null,\r\n" + 
				"      \"isRequired\" : false,\r\n" + 
				"      \"isUniqued\" : false,\r\n" + 
				"      \"isMultiValue\" : false,\r\n" + 
				"      \"isEncrypted\" : false,\r\n" + 
				"      \"lookupDefinitionCode\" : null,\r\n" + 
				"      \"options\" : null,\r\n" + 
				"      \"prompt\" : null\r\n" + 
				"    }, {\r\n" + 
				"      \"id\" : null,\r\n" + 
				"      \"type\" : \"STRING\",\r\n" + 
				"      \"provisionMethod\" : \"AUTO\",\r\n" + 
				"      \"reconcileMethod\" : \"AUTO\",\r\n" + 
				"      \"parentCode\" : null,\r\n" + 
				"      \"code\" : \"mobile\",\r\n" + 
				"      \"name\" : \"手机号\",\r\n" + 
				"      \"length\" : 64,\r\n" + 
				"      \"description\" : null,\r\n" + 
				"      \"isRequired\" : false,\r\n" + 
				"      \"isUniqued\" : false,\r\n" + 
				"      \"isMultiValue\" : false,\r\n" + 
				"      \"isEncrypted\" : false,\r\n" + 
				"      \"lookupDefinitionCode\" : null,\r\n" + 
				"      \"options\" : null,\r\n" + 
				"      \"prompt\" : null\r\n" + 
				"    }, {\r\n" + 
				"      \"id\" : null,\r\n" + 
				"      \"type\" : \"STRING\",\r\n" + 
				"      \"provisionMethod\" : \"AUTO\",\r\n" + 
				"      \"reconcileMethod\" : \"AUTO\",\r\n" + 
				"      \"parentCode\" : null,\r\n" + 
				"      \"code\" : \"orgCode\",\r\n" + 
				"      \"name\" : \"机构编码\",\r\n" + 
				"      \"length\" : 64,\r\n" + 
				"      \"description\" : null,\r\n" + 
				"      \"isRequired\" : false,\r\n" + 
				"      \"isUniqued\" : false,\r\n" + 
				"      \"isMultiValue\" : false,\r\n" + 
				"      \"isEncrypted\" : false,\r\n" + 
				"      \"lookupDefinitionCode\" : null,\r\n" + 
				"      \"options\" : null,\r\n" + 
				"      \"prompt\" : null\r\n" + 
				"    }, {\r\n" + 
				"      \"id\" : null,\r\n" + 
				"      \"type\" : \"STRING\",\r\n" + 
				"      \"provisionMethod\" : \"AUTO\",\r\n" + 
				"      \"reconcileMethod\" : \"AUTO\",\r\n" + 
				"      \"parentCode\" : null,\r\n" + 
				"      \"code\" : \"iamEmpno\",\r\n" + 
				"      \"name\" : \"iamEmpno\",\r\n" + 
				"      \"length\" : 64,\r\n" + 
				"      \"description\" : null,\r\n" + 
				"      \"isRequired\" : false,\r\n" + 
				"      \"isUniqued\" : false,\r\n" + 
				"      \"isMultiValue\" : false,\r\n" + 
				"      \"isEncrypted\" : false,\r\n" + 
				"      \"lookupDefinitionCode\" : null,\r\n" + 
				"      \"options\" : null,\r\n" + 
				"      \"prompt\" : null\r\n" + 
				"    }, {\r\n" + 
				"      \"id\" : null,\r\n" + 
				"      \"type\" : \"STRING\",\r\n" + 
				"      \"provisionMethod\" : \"AUTO\",\r\n" + 
				"      \"reconcileMethod\" : \"AUTO\",\r\n" + 
				"      \"parentCode\" : null,\r\n" + 
				"      \"code\" : \"hiType\",\r\n" + 
				"      \"name\" : \"用户类型\",\r\n" + 
				"      \"length\" : 64,\r\n" + 
				"      \"description\" : null,\r\n" + 
				"      \"isRequired\" : false,\r\n" + 
				"      \"isUniqued\" : false,\r\n" + 
				"      \"isMultiValue\" : false,\r\n" + 
				"      \"isEncrypted\" : false,\r\n" + 
				"      \"lookupDefinitionCode\" : null,\r\n" + 
				"      \"options\" : null,\r\n" + 
				"      \"prompt\" : null\r\n" + 
				"    } ]\r\n" + 
				"  } ],\r\n" + 
				"  \"enableSync\" : true,\r\n" + 
				"  \"enablePull\" : true,\r\n" + 
				"  \"enablePush\" : true,\r\n" + 
				"  \"objectCodes4Push\" : [ \"DFMHGZ_ORG\", \"DFMHGZ_ACC\" ],\r\n" + 
				"  \"maxThreadsSync\" : null,\r\n" + 
				"  \"maxThreadsPush\" : null,\r\n" + 
				"  \"maxThreadsPull\" : null,\r\n" + 
				"  \"debug\" : false\r\n" + 
				"}";
		ObjectMapper mapper = new ObjectMapper();
		JsonNode tree = null;
		try {
			tree = mapper.readTree(b);
		} catch (JsonMappingException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (JsonProcessingException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}

		List<JsonNode> uidlist = tree.findValues("tokenId");
		System.out.println(uidlist.get(0).toString());
		JsonNode c = tree.get("tokenId");
		System.out.println(c.asText());
		JsonNode d = tree.get("success");
		System.out.println(d.asText());
//		try {
//			LoginResult loginResult=mapper.readValue(b,LoginResult.class);
//		} catch (JsonMappingException e) {
//			// TODO Auto-generated catch block
//			e.printStackTrace();
//		} catch (JsonProcessingException e) {
//			// TODO Auto-generated catch block
//			e.printStackTrace();
//		}
		
		String aa ="{\r\n" + 
				"  \"success\" : true,\r\n" + 
				"  \"message\" : null,\r\n" + 
				"  \"exception\" : null,\r\n" + 
				"  \"interrupt\" : false,\r\n" + 
				"  \"timestamp\" : 1623205150554,\r\n" + 
				"  \"taskId\" : \"20210531114523638-25AF-D89996E19\",\r\n" + 
				"  \"objectType\" : \"TARGET_ORGANIZATION\",\r\n" + 
				"  \"objectCode\" : \"DFMHGZ_ORG\",\r\n" + 
				"  \"effectOn\" : \"CREATED\",\r\n" + 
				"  \"data\" : {\r\n" + 
				"    \"_parent\" : null,\r\n" + 
				"    \"_organization\" : \"东风汽车集团有限公司\",\r\n" + 
				"    \"code\" : \"20000001\",\r\n" + 
				"    \"name\" : \"东风汽车集团有限公司\",\r\n" + 
				"    \"fullname\" : \"东风汽车集团有限公司\",\r\n" + 
				"    \"description\" : null,\r\n" + 
				"    \"sequence\" : 0,\r\n" + 
				"    \"isDisabled\" : false,\r\n" + 
				"    \"createAt\" : \"2021-05-31 11:45:23.542\",\r\n" + 
				"    \"updateAt\" : \"2021-05-31 11:45:23.911\",\r\n" + 
				"    \"mocsCode\" : null,\r\n" + 
				"    \"parentCode\" : null,\r\n" + 
				"    \"mocsParentCode\" : null\r\n" + 
				"  },\r\n" + 
				"  \"guid\" : null,\r\n" + 
				"  \"id\" : \"20210531114523542-7FAE-E66963EFE\"\r\n" + 
				"}";
		
//		ObjectMapper mapperaa = new ObjectMapper();
//		try {
////			PullTaskNormalResult pNormalResult=mapperaa.readValue(aa,PullTaskNormalResult.class);
//			PullTaskOrgResult pOrgResult= mapper.readValue(aa,PullTaskOrgResult.class);
//			System.out.println(pOrgResult.getId());
//		} catch (JsonMappingException e) {
//			// TODO Auto-generated catch block
//			e.printStackTrace();
//		} catch (JsonProcessingException e) {
//			// TODO Auto-generated catch block
//			e.printStackTrace();
//		}
		
		String bb="{\r\n" + 
				"  \"success\" : true,\r\n" + 
				"  \"message\" : null,\r\n" + 
				"  \"exception\" : null,\r\n" + 
				"  \"interrupt\" : false,\r\n" + 
				"  \"timestamp\" : 1623219131932\r\n" + 
				"}";
//		ObjectMapper mapperbb = new ObjectMapper();
//		try {
////			PullTaskNormalResult pNormalResult=mapperaa.readValue(aa,PullTaskNormalResult.class);
//			PullFinishResult pOrgResult= mapperbb.readValue(bb,PullFinishResult.class);
//			System.out.println(pOrgResult.getTimestamp());
//		} catch (JsonMappingException e) {
//			// TODO Auto-generated catch block
//			e.printStackTrace();
//		} catch (JsonProcessingException e) {
//			// TODO Auto-generated catch block
//			e.printStackTrace();
//		}
		
		String cc="{\r\n" + 
				"  \"success\" : true,\r\n" + 
				"  \"message\" : null,\r\n" + 
				"  \"exception\" : null,\r\n" + 
				"  \"interrupt\" : false,\r\n" + 
				"  \"timestamp\" : *************,\r\n" + 
				"  \"taskId\" : \"*****************-3690-50361DC4F\",\r\n" + 
				"  \"objectType\" : \"TARGET_ACCOUNT\",\r\n" + 
				"  \"objectCode\" : \"DFMHGZ_ACC\",\r\n" + 
				"  \"effectOn\" : \"CREATED\",\r\n" + 
				"  \"data\" : {\r\n" + 
				"    \"_user\" : \"8146789\",\r\n" + 
				"    \"_organization\" : \"2106091444136241079\",\r\n" + 
				"    \"username\" : \"8146789\",\r\n" + 
				"    \"password\" : null,\r\n" + 
				"    \"fullname\" : \"陈曦\",\r\n" + 
				"    \"isDisabled\" : false,\r\n" + 
				"    \"isLocked\" : false,\r\n" + 
				"    \"createAt\" : \"2021-06-04 16:59:43.880\",\r\n" + 
				"    \"updateAt\" : \"2021-06-04 17:00:06.098\",\r\n" + 
				"    \"isSystem\" : false,\r\n" + 
				"    \"isPublic\" : false,\r\n" + 
				"    \"isMaster\" : true,\r\n" + 
				"    \"startDate\" : null,\r\n" + 
				"    \"endDate\" : null,\r\n" + 
				"    \"idCard\" : \"230604198311280220\",\r\n" + 
				"    \"email\" : \"<EMAIL>\",\r\n" + 
				"    \"mobile\" : \"***********\",\r\n" + 
				"    \"orgCode\" : \"240\",\r\n" + 
				"    \"iamEmpno\" : null,\r\n" + 
				"    \"hiType\" : null\r\n" + 
				"  },\r\n" + 
				"  \"guid\" : \"8146789\",\r\n" + 
				"  \"id\" : \"20210604165943757-ECAD-884470F1C\"\r\n" + 
				"}";
		ObjectMapper mappercc = new ObjectMapper();
		try {
//			PullTaskNormalResult pNormalResult=mapperaa.readValue(aa,PullTaskNormalResult.class);
//			PullFinishResult pOrgResult= mappercc.readValue(cc,PullFinishResult.class);
			PullTaskUserResult pUserResult= mapper.readValue(cc,PullTaskUserResult.class);
			System.out.println(pUserResult.getId());
		} catch (JsonMappingException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (JsonProcessingException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		
	}
		
}
