package com.dawnpro.vipservice.api;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Types;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.naming.Context;
import javax.naming.InitialContext;
import javax.naming.NamingException;
import javax.sql.DataSource;

import com.dawnpro.entity.zgs.*;
import org.hibernate.dialect.function.NvlFunction;
import org.hibernate.dialect.function.VarArgsSQLFunction;
import org.springframework.aop.framework.adapter.AdvisorAdapterRegistrationManager;

import com.dawnpro.commons.spring.ServiceFactory;
import com.dawnpro.commons.util.MapUtil;
import com.dawnpro.commons.util.StringTools;
import com.dawnpro.commons.util.db.DAOException;
import com.dawnpro.commons.util.db.TransactionException;
import com.dawnpro.core.exception.AppException;
import com.dawnpro.core.log.Loggers;
import com.dawnpro.entity.api.Car;
import com.dawnpro.entity.api.HgzGg;
import com.dawnpro.entity.api.HgzList;
import com.dawnpro.entity.api.HgzStatusInfo;
import com.dawnpro.entity.api.QueryHgzExchangeStatus;
import com.dawnpro.entity.api.QueryHgzGg;
import com.dawnpro.entity.api.QueryHgzList;
import com.dawnpro.service.BaseServer;
import com.dawnpro.vipservice.HgzBackResultManage;
import com.sun.jersey.core.util.StringIgnoreCaseKeyComparator;
import com.sun.org.apache.bcel.internal.generic.Select;
import com.sun.swing.internal.plaf.basic.resources.basic;

import info.vidc.www.certificate.operation.CertificateInfo;

public class HgzApiToolManage extends BaseServer{
	
	/**
	 * 查询变更清单记录
	 * @param info
	 * @return
	 */
	public Map getHgzList(QueryHgzList info) {
		Map resmap = new HashMap();
		String resflag = "1";//0查询得到hgzid，1 查询不到hgzid
		String resmemo = "";//flag=1时  保存反馈信息
		List<HgzList> hgzlist = new ArrayList<HgzList>();

		StringBuffer sql = new StringBuffer();
		sql.append("select p.vnm vin,date_format(m.printtime,'%Y-%m-%d %T') printtime,date_format(m.uptime,'%Y-%m-%d %T') uptime ")
		.append("from tbl_hgzmain m, tbl_hgzpara p where m.hgz_id=p.hgz_id and p.subcompany='").append(info.getFactorycode()).append("' ") 
		.append(" and m.cycle_status='20' and m.cxtime is null ") 
		.append("	and ((m.printtime>='").append(info.getStarttime()).append("' and m.printtime<='").append(info.getEndtime()).append("')" ) 
		.append("	or ( m.uptime>='").append(info.getStarttime()).append("' and m.uptime<='").append(info.getEndtime()).append("') )");
		
		
		try {
			List list = this.dao.queryForList(sql.toString());
			if(list.size()>0) {
				resflag="0";
				resmemo="变更清单数据"+list.size()+"条";
				hgzlist = list;
			}else {
				resflag="1";
				resmemo="无变更数据";
			}
		} catch (DAOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		
		resmap.put("flag", resflag);
		resmap.put("memo", resmemo);
		resmap.put("hgzinfo", hgzlist);
		return resmap;
	}

	/**
	 * 查询合格证公告
	 * @param info
	 * @return
	 */
	public Map getHgzGg(QueryHgzGg info) {
		Map resmap = new HashMap();
		String resflag = "1";//0查询得到hgzid，1 查询不到hgzid
		String resmemo = "";//flag=1时  保存反馈信息

		StringBuffer sql = new StringBuffer();
		sql.append("select ") 
		.append("cpno,") 
		.append("ph,") 
		.append("sxrq ggsxrq,") 
		.append("cxfl,") 
		.append("qymc,") 
		.append("clxh,") 
		.append("clmc,") 
		.append("pp,") 
		.append("vnm,") 
		.append("fdjxh,") 
		.append("pl,") 
		.append("gl,") 
		.append("dpxh,") 
		.append("qlj,") 
		.append("hlj,") 
		.append("wkc,") 
		.append("wkk,") 
		.append("wkg,") 
		.append("ryzl,") 
		.append("pfbz,") 
		.append("zxxs,") 
		.append("hxc,") 
		.append("hxk,") 
		.append("hxg,") 
		.append("zs,") 
		.append("zj,") 
		.append("thps,") 
		.append("lts,") 
		.append("ltgg,") 
		.append("zzl,") 
		.append("edzl,") 
		.append("zbzl,") 
		.append("zqyzl,") 
		.append("zzlxs,") 
		.append("bgazzdzl bganzzdzl,") 
		.append("edzk,") 
		.append("jsszzrs jsszcrs,") 
		.append("zgcs,") 
		.append("bz,") 
		.append("zxzs,") 
		.append("yh,") 
		.append("qyid,") 
		.append("qydm,") 
		.append("qydz,") 
		.append("clscdwmc,") 
		.append("scdz,") 
		.append("zh,") 
		.append("dpid,") 
		.append(" case when MLLB = '乘用车及客车' then 0") 
		.append(" when MLLB = '客车' then 0") 
		.append(" when MLLB = '乘用车' then 0") 
		.append(" when MLLB = '货车' then 1") 
		.append(" when MLLB = '半挂牵引车' then 2") 
		.append(" when MLLB = '半挂车' then 3") 
		.append(" when MLLB = '挂车' then 3") 
		.append(" when MLLB = '两轮摩托车和两轮轻便摩托车' then 4") 
		.append(" when MLLB = '两轮摩托车' then 4") 
		.append(" when MLLB = '两轮轻便摩托车' then 4") 
		.append(" when MLLB = '三轮摩托车和三轮轻便摩托车' then 5") 
		.append(" when MLLB = '三轮摩托车' then 5") 
		.append(" when MLLB = '三轮轻便摩托车' then 5") 
		.append(" when MLLB = '三轮汽车' then 6") 
		.append(" when MLLB = '低速货车' then 7") 
		.append(" when MLLB = '专用汽车' then 8") 
		.append(" when MLLB = '专用车' then 8") 
		.append(" when MLLB = '未列入公告车辆' then 9") 
		.append(" when MLLB = '二类底盘' then 10") 
		.append(" when MLLB = '三类底盘' then 11 end mllb,") 
		.append(" cddbj,") 
		.append(" xnybj sfxny,") 
		.append(" xnylb xnyzl") 
		.append(" from tbl_cljscsb ") 
		.append(" where csb_type='0' and cpno is not null and cpno='").append(info.getCpno()).append("' limit 0,1");
		
		try {
			List list = this.dao.queryForList(sql.toString());
			if(list.size()>0) {
				resflag="0";
				resmemo="获取公告数据成功";
				HgzGg hgzGg = new HgzGg();
				Map map = (Map) list.get(0);
				hgzGg = changeMap(map);
				resmap.put("flag", resflag);
				resmap.put("memo", resmemo);
				resmap.put("hgzgg", hgzGg);
				return resmap;
			}else {
				resflag="1";
				resmemo="无对应公告数据";
				resmap.put("flag", resflag);
				resmap.put("memo", resmemo);
				return resmap;
			}
		} catch (DAOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		
		resmap.put("flag", resflag);
		resmap.put("memo", resmemo);
		return resmap;
	}

	private HgzGg changeMap(Map map) {
		HgzGg hgzGg = new HgzGg();
		hgzGg.setCpno(MapUtil.getMapValue(map, "cpno", ""));
		hgzGg.setPh(MapUtil.getMapValue(map, "ph", ""));
		hgzGg.setGgsxrq(MapUtil.getMapValue(map, "ggsxrq", ""));
		hgzGg.setCxfl(MapUtil.getMapValue(map, "cxfl", ""));
		hgzGg.setQymc(MapUtil.getMapValue(map, "qymc", ""));
		hgzGg.setClxh(MapUtil.getMapValue(map, "clxh", ""));
		hgzGg.setClmc(MapUtil.getMapValue(map, "clmc", ""));
		hgzGg.setPp(MapUtil.getMapValue(map, "pp", ""));
		hgzGg.setVnm(MapUtil.getMapValue(map, "vnm", ""));
		hgzGg.setFdjxh(MapUtil.getMapValue(map, "fdjxh", ""));
		hgzGg.setPl(MapUtil.getMapValue(map, "pl", ""));
		hgzGg.setGl(MapUtil.getMapValue(map, "gl", ""));
		hgzGg.setDpxh(MapUtil.getMapValue(map, "dpxh", ""));
		hgzGg.setQlj(MapUtil.getMapValue(map, "qlj", ""));
		hgzGg.setHlj(MapUtil.getMapValue(map, "hlj", ""));
		hgzGg.setWkc(MapUtil.getMapValue(map, "wkc", ""));
		hgzGg.setWkk(MapUtil.getMapValue(map, "wkk", ""));
		hgzGg.setWkg(MapUtil.getMapValue(map, "wkg", ""));
		hgzGg.setRyzl(MapUtil.getMapValue(map, "ryzl", ""));
		hgzGg.setPfbz(MapUtil.getMapValue(map, "pfbz", ""));
		hgzGg.setZxxs(MapUtil.getMapValue(map, "zxxs", ""));
		hgzGg.setHxc(MapUtil.getMapValue(map, "hxc", ""));
		hgzGg.setHxk(MapUtil.getMapValue(map, "hxk", ""));
		hgzGg.setHxg(MapUtil.getMapValue(map, "hxg", ""));
		hgzGg.setZs(MapUtil.getMapValue(map, "zs", ""));
		hgzGg.setZj(MapUtil.getMapValue(map, "zj", ""));
		hgzGg.setThps(MapUtil.getMapValue(map, "thps", ""));
		hgzGg.setLts(MapUtil.getMapValue(map, "lts", ""));
		hgzGg.setLtgg(MapUtil.getMapValue(map, "ltgg", ""));
		hgzGg.setZzl(MapUtil.getMapValue(map, "zzl", ""));
		hgzGg.setEdzl(MapUtil.getMapValue(map, "edzl", ""));
		hgzGg.setZbzl(MapUtil.getMapValue(map, "zbzl", ""));
		hgzGg.setZqyzl(MapUtil.getMapValue(map, "zqyzl", ""));
		hgzGg.setZzlxs(MapUtil.getMapValue(map, "zzlxs", ""));
		hgzGg.setBganzzdzl(MapUtil.getMapValue(map, "bganzzdzl", ""));
		hgzGg.setEdzk(MapUtil.getMapValue(map, "edzk", ""));
		hgzGg.setJsszcrs(MapUtil.getMapValue(map, "jsszcrs", ""));
		hgzGg.setZgcs(MapUtil.getMapValue(map, "zgcs", ""));
		hgzGg.setBz(MapUtil.getMapValue(map, "bz", ""));
		hgzGg.setZxzs(MapUtil.getMapValue(map, "zxzs", ""));
		hgzGg.setYh(MapUtil.getMapValue(map, "yh", ""));
		hgzGg.setQyid(MapUtil.getMapValue(map, "qyid", ""));
		hgzGg.setQydm(MapUtil.getMapValue(map, "qydm", ""));
		hgzGg.setQydz(MapUtil.getMapValue(map, "qydz", ""));
		hgzGg.setClscdwmc(MapUtil.getMapValue(map, "clscdwmc", ""));
		hgzGg.setScdz(MapUtil.getMapValue(map, "scdz", ""));
		hgzGg.setZh(MapUtil.getMapValue(map, "zh", ""));
		hgzGg.setDpid(MapUtil.getMapValue(map, "dpid", ""));
		hgzGg.setMllb(MapUtil.getMapValue(map, "mllb", ""));
		hgzGg.setCddbj(MapUtil.getMapValue(map, "cddbj", ""));
		hgzGg.setSfxny(MapUtil.getMapValue(map, "sfxny", ""));
		hgzGg.setXnyzl(MapUtil.getMapValue(map, "xnyzl", ""));
		
		return hgzGg;
	}
	
	/**
	 * 插入、接口查询，反馈合格证交换信息
	 * @param info
	 * @return
	 */
	public Map getHgzExchangeStatus(QueryHgzExchangeStatus info) {
		Map resmap = new HashMap();
		String resflag = "1";
		String resmemo = "";
		
		String batch = UUID.randomUUID().toString().toLowerCase().replace("-", "");
		//将请求数据插入到合格证交换状态表中
		List carlist =info.getCarlist();
		List sqList = new ArrayList<>();
		for (int i = 0; i < carlist.size(); i++) {
			Car car = (Car)carlist.get(i);
			String vin= StringTools.isBlank(car.getVin())?"":car.getVin();
			String hgzbh= StringTools.isBlank(car.getHgzbh())?"":car.getHgzbh();
			String insql = "insert into tbl_hgzexchangestatus_batch (vin, hgzbh, status, batch) VALUES('"+vin+"', '"+hgzbh+"', '0', '"+batch+"')";
			sqList.add(insql);
		}
		try {
			this.dao.excuteBatch(sqList);
		} catch (DAOException e1) {
			e1.printStackTrace();
		} catch (TransactionException e1) {
			e1.printStackTrace();
		}
		//对合格证交换状态表中的本批次数据进行校验
		String procname = "proc_hgzexc_check";
		String[] paramtype = {"String","String"};
		List<String> vlist = new ArrayList();
		vlist.add(batch);
		vlist.add("1");
		String flag=null;
		int rt[] = {Types.VARCHAR};
		try {
			List tempList=this.dao.excuteprc_new(procname, paramtype, vlist, rt);
		} catch (AppException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		
		//对校验合格的数据请求国家状态
		String qString= "select id,vin,hgzbh from tbl_hgzexchangestatus_batch where status='21' and batch='"+batch+"'";
		List reList;
		try {
			reList = this.dao.queryForList(qString);
			Map reMap = new HashMap<>();
			if(reList.size()>0) {
				for (int i = 0; i < reList.size(); i++) {
					reMap=(Map) reList.get(i);
					String vin = MapUtil.getMapValue(reMap, "vin", "");
					String hgzbh = MapUtil.getMapValue(reMap, "hgzbh", "");
					sycHgzExchageStatus(vin, hgzbh);
				}
			}
		} catch (DAOException | SQLException e) {
			e.printStackTrace();
		}
		
		
		//将国家接口从请求表同步到接口表中
		List<String> vlistb = new ArrayList();
		vlistb.add(batch);
		vlistb.add("2");
		try {
			List tempListb=this.dao.excuteprc_new(procname, paramtype, vlistb, rt);
		} catch (AppException e) {
			e.printStackTrace();
		}
		
		//将该批次接口表中的数据反馈给子单位
		String sql = " SELECT vin,hgzbh,case status when '1' then '0'  else '1'  end status,statusmsg, date_format(querytime,'%Y-%m-%d %T') statustime,"
				+ "garesult, date_format(gaaudittime,'%Y-%m-%d %T') gaaudittime,"
				+ "taxresult, date_format(taxaudittime,'%Y-%m-%d %T') taxaudittime "
				+ "FROM tbl_hgzexchangestatus_batch  where batch ='"+batch+"'";
		List hgzlist = new ArrayList<HgzStatusInfo>();
		try {
			hgzlist = this.dao.queryForList(sql);
			resflag="0";
			resmemo="";
		} catch (DAOException e) {
			e.printStackTrace();
		} catch (SQLException e) {
			e.printStackTrace();
		}
		resmap.put("flag", resflag);
		resmap.put("memo", resmemo);
		resmap.put("hgzxx", hgzlist);
		
		return resmap;
	}
	
	/**
	 * 自动获取国家合格证交换状态数据
	 */
	public void autoGetHgzExchangeStatus() {
		Loggers.INTERFACE.info("装备中心VIP-合格证交换状态查询接口,系统自动获取数据：开始");

		List tempList;
		try {
			tempList = dao.queryForList("select id,vin,hgzbh from tbl_hgzexchangestatus where status in ('0','2') order by querytime asc ");
			if (tempList != null && tempList.size() > 0) {
				Map reMap = new HashMap<>();
				for (int i = 0; i < tempList.size(); i++) {
					reMap=(Map) tempList.get(i);
					String vin = MapUtil.getMapValue(reMap, "vin", "");
					String hgzbh = MapUtil.getMapValue(reMap, "hgzbh", "");
					sycHgzExchageStatus(vin, hgzbh);
				}
			} else {
				Loggers.PERFORMANCE.info("装备中心VIP-合格证交换状态查询接口,系统自动获取数据：无待获取数据");
			}
		
		} catch (DAOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (SQLException e) {
			e.printStackTrace();
		}
		

		Loggers.INTERFACE.info("装备中心VIP-合格证交换状态查询接口,系统自动获取数据：结束");
	}
	
	/**
	 * 单条请求表数据更新国家交换状态
	 * @param vin
	 * @param hgzbh
	 */
	private void sycHgzExchageStatus(String vin,String hgzbh) {
		String sql = "";
		EidcToolManage eToolManage = new EidcToolManage();
		EdicgetExchangeStatusRes eStatusRes = eToolManage.getExchangeStatus(hgzbh);
		if(eStatusRes.getCode().equals("0000")) {
			EdicgetExchangeStatusResData resData= eStatusRes.getData();
			sql = "UPDATE tbl_hgzexchangestatus SET  status='1', statusmsg='', querytime=now(), "
					+ "rescode='"+eStatusRes.getCode()+"', "
					+ "resmsg='"+eStatusRes.getMsg()+"', "
					+ "garesult='"+resData.getGaResult()+"', "
					+ "gaaudittime=date_format('"+resData.getGaAuditTime()+"','%Y-%m-%d %H:%i:%s'), "
					+ "taxresult='"+resData.getTaxResult()+"', "
					+ "taxaudittime=date_format('"+resData.getTaxAuditTime()+"','%Y-%m-%d %H:%i:%s') "
					+ "WHERE vin='"+vin+"'";
		}else {
			sql = "UPDATE tbl_hgzexchangestatus SET  status='2', statusmsg='"+eStatusRes.getMsg()+"' , querytime=now(), "
					+ "rescode='"+eStatusRes.getCode()+"', "
					+ "resmsg='"+eStatusRes.getMsg()+"' "
					+ " WHERE vin='"+vin+"'";
		}
		try {
			this.dao.execute(sql);
		} catch (DAOException e) {
			e.printStackTrace();
		} catch (TransactionException e) {
			e.printStackTrace();
		}
	}

	/**
	 * 查询合格证交换状态
	 * @param info
	 * @return
	 */
	public Map searchHgzExchangeStatus(String batch, QueryHgzExchangeStatus info) {
		Map resmap = new HashMap();
		String resflag = "1";//0查询得到hgzid，1 查询不到hgzid
		String resmemo = "";//flag=1时  保存反馈信息
		
		List hgzlist = new ArrayList<HgzStatusInfo>();
		List reslist = new ArrayList<HgzStatusInfo>();
		String sql = " SELECT vin,hgzbh,case rescode when '0000' then '0'  else '1'  end status,statusmsg, date_format(updatedtime,'%Y-%m-%d %T') statustime,"
				+ "rescode,resmsg,garesult, date_format(gaaudittime,'%Y-%m-%d %T') gaaudittime,"
				+ "taxresult, date_format(taxaudittime,'%Y-%m-%d %T') taxaudittime "
				+ "FROM tbl_hgzexchangestatus  where vin in (";
		String sqlb = "";
		List carlist =info.getCarlist();
		
		if(carlist.size()>0) {
			int allnum = carlist.size();
			String vins = ""; 
			//循环查询数据库并获得结果
			for (int i = 0; i < allnum; i++) {
				Car car = (Car) carlist.get(i);
				vins = vins + "'"+car.getVin()+"',";
				if (i % 50 == 0) {
					vins = vins.substring(0,vins.length()-1);
					sqlb = sql + vins +")";
					List list =  new ArrayList<>();
					try {
						list = this.dao.queryForList(sql.toString());
						for (int j = 0; j < list.size(); j++) {
							hgzlist.add(changeMapToHgzStatusInfo((Map)list.get(i)));
						}
					} catch (DAOException e) {
						e.printStackTrace();
					} catch (SQLException e) {
						e.printStackTrace();
					}
					vins ="";
					sqlb = "";
				}
			}
		}
		//对结果顺序按查询顺序进行排列
		for (int i = 0; i < carlist.size(); i++) {
			Car car = (Car) carlist.get(i);
			for (int j = 0; j < hgzlist.size(); j++) {
				HgzStatusInfo hInfo = (HgzStatusInfo) hgzlist.get(j);
				if(car.getVin().equals(hInfo.getVin())) {
					reslist.add(hInfo);
				}
			}
		}
		resmap.put("hgzxx", reslist);
		return resmap;
	}
	
	private HgzStatusInfo changeMapToHgzStatusInfo(Map map) {
		HgzStatusInfo hInfo = new HgzStatusInfo();
		hInfo.setVin(MapUtil.getMapValue(map, "vin", ""));
		hInfo.setHgzbh(MapUtil.getMapValue(map, "hgzbh", ""));
		hInfo.setStatustime(MapUtil.getMapValue(map, "statustime", ""));
		hInfo.setStatus(MapUtil.getMapValue(map, "status", ""));
		hInfo.setMessage(MapUtil.getMapValue(map, "statusmsg", ""));
		hInfo.setTaxresult(MapUtil.getMapValue(map, "garesult", ""));
		hInfo.setTaxaudittime(MapUtil.getMapValue(map, "gaaudittime", ""));
		hInfo.setTaxresult(MapUtil.getMapValue(map, "taxresult", ""));
		hInfo.setTaxaudittime(MapUtil.getMapValue(map, "taxaudittime", ""));
		
		return hInfo;
	}

	public boolean checkFactorycode(String factorycode) {
		String sql = "select 1 from ts_dept_info t where t.deleteflag='0' and t.extend3 in (0,1) and extend1='"+factorycode+"'";
		try {
			int i = this.dao.executeToInt(sql);
			if(i==1) {
				return true;
			}
		} catch (DAOException e) {
			return false;
		}
		return false;
	}
	
	public Map uploadBaseHgzcsNew(BaseHgzcsNew info) {
		Map resmap = new HashMap();
		String resflag = "1";//0成功  1失败
		String resmemo = "";//flag=1时  保存反馈信息

		StringBuffer sql = new StringBuffer();
		
		List resultList = new ArrayList();
		String rs = "fail";
		try {
			int returntype[] = {java.sql.Types.VARCHAR};
			String prcname = "proc_zgs_hgzcs_insert_new";
			String[] paramtype ={"String","String","String","String","String","String",
					"String","String","String","String","String","String",
					"String","String","String","String","String","String",
					"String","String","String","String","String","String",
					"String","String","String","String","String","String",
					"String","String","String","String","String","String",
					"String","String","String","String","String","String",
					"String","String","String","String","String","String",
					"String","String","String","String","String","String",
					"String","String","String","String","String","String"};
			List valuelist=new ArrayList();
			valuelist.add(info.getScm());
			valuelist.add(info.getQymc());
			valuelist.add(info.getPp());
			valuelist.add(info.getClmc());
			valuelist.add(info.getMllb());
			valuelist.add(info.getClxh());
			valuelist.add(info.getVnm());
			valuelist.add(info.getDpxh());
			valuelist.add(info.getDpid());
			valuelist.add(info.getFdjxh());
			valuelist.add(info.getRyzl());
			valuelist.add(info.getPl());
			valuelist.add(info.getGl());
			valuelist.add(info.getPfbz());
			valuelist.add(info.getYh());
			valuelist.add(info.getWkc());
			valuelist.add(info.getWkk());
			valuelist.add(info.getWkg());
			valuelist.add(info.getHxc());
			valuelist.add(info.getHxk());
			valuelist.add(info.getHxg());
			valuelist.add(info.getThps());
			valuelist.add(info.getLts());
			valuelist.add(info.getLtgg());
			valuelist.add(info.getQlj());
			valuelist.add(info.getHlj());
			valuelist.add(info.getZj());
			valuelist.add(info.getZh());
			valuelist.add(info.getZs());
			valuelist.add(info.getZxxs());
			valuelist.add(info.getZzl());
			valuelist.add(info.getZbzl());
			valuelist.add(info.getEdzl());
			valuelist.add(info.getZzlxs());
			valuelist.add(info.getZqyzl());
			valuelist.add(info.getBganzzdzl());
			valuelist.add(info.getJsszcrs());
			valuelist.add(info.getEdzk());
			valuelist.add(info.getZgcs());
			valuelist.add(info.getBz());
			valuelist.add(info.getQybz());
			valuelist.add(info.getClscdwmc());
			valuelist.add(info.getScdz());
			valuelist.add(info.getQtxx());
			valuelist.add(info.getCpno());
			valuelist.add(info.getPh());
			valuelist.add(info.getSxrq());
			valuelist.add(info.getPzxlh());
			valuelist.add(info.getJfpzid());
			valuelist.add(info.getMzxny());
			valuelist.add(info.getJsgc());
			valuelist.add(info.getIscxnf());
			valuelist.add(info.getZyzycmsbs());
			valuelist.add(info.getXnyqcjmsbs());
			valuelist.add(info.getHdmsbs());
			valuelist.add(info.getXnyqczl());
			valuelist.add(info.getZxzs());
			valuelist.add(info.getCddbj());
			valuelist.add(info.getDpcpno());
			valuelist.add(info.getFactorycode());
			
			Loggers.PERFORMANCE.debug(valuelist);
			resultList = this.dao.excuteprc_new(prcname, paramtype, valuelist, returntype);
			String flag = resultList.get(0).toString();
			
			if(flag.equals("0")) {
				resflag="0";
				resmemo="上传成功";
				resmap.put("flag", resflag);
				resmap.put("memo", resmemo);
				return resmap;
			}else {
				resflag="1";
				resmemo=flag;
				resmap.put("flag", resflag);
				resmap.put("memo", resmemo);
				return resmap;
			}
		} catch (AppException e) {
			e.printStackTrace();
			resflag="1";
			resmemo="数据库执行异常";
		}
		resmap.put("flag", resflag);
		resmap.put("memo", resmemo);
		return resmap;
	}
	
	public Map uploadBaseHgzcs(BaseHgzcs info) {
		Map resmap = new HashMap();
		String resflag = "1";//0成功  1失败
		String resmemo = "";//flag=1时  保存反馈信息

		StringBuffer sql = new StringBuffer();
		
		List resultList = new ArrayList();
		String rs = "fail";
		try {
			int returntype[] = {java.sql.Types.VARCHAR};
			String prcname = "proc_zgs_hgzcs_insert";
			String[] paramtype ={"String","String","String","String","String","String",
					"String","String","String","String","String","String",
					"String","String","String","String","String","String",
					"String","String","String","String","String","String",
					"String","String","String","String","String","String",
					"String","String","String","String","String","String",
					"String","String","String","String","String","String",
					"String","String","String","String","String","String",
					"String","String","String","String","String","String",
					"String","String","String"};
			List valuelist=new ArrayList();
			valuelist.add(info.getScm());
			valuelist.add(info.getQymc());
			valuelist.add(info.getPp());
			valuelist.add(info.getClmc());
			valuelist.add(info.getMllb());
			valuelist.add(info.getClxh());
			valuelist.add(info.getVnm());
			valuelist.add(info.getDpxh());
			valuelist.add(info.getDpid());
			valuelist.add(info.getFdjxh());
			valuelist.add(info.getRyzl());
			valuelist.add(info.getPl());
			valuelist.add(info.getGl());
			valuelist.add(info.getPfbz());
			valuelist.add(info.getYh());
			valuelist.add(info.getWkc());
			valuelist.add(info.getWkk());
			valuelist.add(info.getWkg());
			valuelist.add(info.getHxc());
			valuelist.add(info.getHxk());
			valuelist.add(info.getHxg());
			valuelist.add(info.getThps());
			valuelist.add(info.getLts());
			valuelist.add(info.getLtgg());
			valuelist.add(info.getQlj());
			valuelist.add(info.getHlj());
			valuelist.add(info.getZj());
			valuelist.add(info.getZh());
			valuelist.add(info.getZs());
			valuelist.add(info.getZxxs());
			valuelist.add(info.getZzl());
			valuelist.add(info.getZbzl());
			valuelist.add(info.getEdzl());
			valuelist.add(info.getZzlxs());
			valuelist.add(info.getZqyzl());
			valuelist.add(info.getBganzzdzl());
			valuelist.add(info.getJsszcrs());
			valuelist.add(info.getEdzk());
			valuelist.add(info.getZgcs());
			valuelist.add(info.getBz());
			valuelist.add(info.getQybz());
			valuelist.add(info.getClscdwmc());
			valuelist.add(info.getScdz());
			valuelist.add(info.getQtxx());
			valuelist.add(info.getCpno());
			valuelist.add(info.getPh());
			valuelist.add(info.getSxrq());
			valuelist.add(info.getPzxlh());
			valuelist.add(info.getJfpzid());
			valuelist.add(info.getMzxny());
			valuelist.add(info.getJsgc());
			valuelist.add(info.getIscxnf());
			valuelist.add(info.getZyzycmsbs());
			valuelist.add(info.getZxzs());
			valuelist.add(info.getCddbj());
			valuelist.add(info.getDpcpno());
			valuelist.add(info.getFactorycode());
			
			Loggers.PERFORMANCE.debug(valuelist);
			resultList = this.dao.excuteprc_new(prcname, paramtype, valuelist, returntype);
			String flag = resultList.get(0).toString();
			
			if(flag.equals("0")) {
				resflag="0";
				resmemo="上传成功";
				resmap.put("flag", resflag);
				resmap.put("memo", resmemo);
				return resmap;
			}else {
				resflag="1";
				resmemo=flag;
				resmap.put("flag", resflag);
				resmap.put("memo", resmemo);
				return resmap;
			}
		} catch (AppException e) {
			e.printStackTrace();
			resflag="1";
			resmemo="数据库执行异常";
		}
		resmap.put("flag", resflag);
		resmap.put("memo", resmemo);
		return resmap;
	}

	public Map uploadProductData(ProductData info) {
		Map resmap = new HashMap();
		String resflag = "1";//0成功  1失败
		String resmemo = "";//flag=1时  保存反馈信息

		StringBuffer sql = new StringBuffer();
		
		List resultList = new ArrayList();
		String rs = "fail";
		try {
			int returntype[] = {java.sql.Types.VARCHAR};
			String prcname = "proc_zgs_mescs_insert";
			String[] paramtype ={"String","String","String","String","String","String",
					"String","String","String","String","String"};
			List valuelist=new ArrayList();
			valuelist.add(info.getDph());
			valuelist.add(info.getCsys());
			valuelist.add(info.getFdjh());
			valuelist.add(info.getScrq());
			valuelist.add(info.getBz());
			valuelist.add(info.getQtxx());
			valuelist.add(info.getVinbsyy());
			valuelist.add(info.getHgzfl());
			valuelist.add(info.getScm());
			valuelist.add(info.getIsyclz());
			valuelist.add(info.getFactorycode());
			
			Loggers.PERFORMANCE.debug(valuelist);
			resultList = this.dao.excuteprc_new(prcname, paramtype, valuelist, returntype);
			String flag = resultList.get(0).toString();
			
			if(flag.equals("0")) {
				resflag="0";
				resmemo="上传成功";
				resmap.put("flag", resflag);
				resmap.put("memo", resmemo);
				return resmap;
			}else {
				resflag="1";
				resmemo=flag;
				resmap.put("flag", resflag);
				resmap.put("memo", resmemo);
				return resmap;
			}
		} catch (AppException e) {
			e.printStackTrace();
			resflag="1";
			resmemo="数据库执行异常";
		}
		resmap.put("flag", resflag);
		resmap.put("memo", resmemo);
		return resmap;
	}
	
	
	
	public Map uploadProductDataSwt(ProductDataSwt info) {
		Map resmap = new HashMap();
		String resflag = "1";//0成功  1失败
		String resmemo = "";//flag=1时  保存反馈信息

		StringBuffer sql = new StringBuffer();
		
		List resultList = new ArrayList();
		String rs = "fail";
		try {
			int returntype[] = {java.sql.Types.VARCHAR};
			String prcname = "proc_zgs_mescs_swt_insert";
			String[] paramtype ={"String","String","String","String","String","String",
					"String","String","String","String","String","String","String","String"};
			List valuelist=new ArrayList();
			valuelist.add(info.getDph());
			valuelist.add(info.getCsys());
			valuelist.add(info.getFdjh());
			valuelist.add(info.getScrq());
			valuelist.add(info.getBz());
			valuelist.add(info.getQtxx());
			valuelist.add(info.getVinbsyy());
			valuelist.add(info.getHgzfl());
			valuelist.add(info.getScm());
			valuelist.add(info.getIsyclz());
			valuelist.add(info.getSwtqymc());
			valuelist.add(info.getSwtqyscdz());
			valuelist.add(info.getSwtqytyshxydm());
			valuelist.add(info.getFactorycode());
			
			Loggers.PERFORMANCE.debug(valuelist);
			resultList = this.dao.excuteprc_new(prcname, paramtype, valuelist, returntype);
			String flag = resultList.get(0).toString();
			
			if(flag.equals("0")) {
				resflag="0";
				resmemo="上传成功";
				resmap.put("flag", resflag);
				resmap.put("memo", resmemo);
				return resmap;
			}else {
				resflag="1";
				resmemo=flag;
				resmap.put("flag", resflag);
				resmap.put("memo", resmemo);
				return resmap;
			}
		} catch (AppException e) {
			e.printStackTrace();
			resflag="1";
			resmemo="数据库执行异常";
		}
		resmap.put("flag", resflag);
		resmap.put("memo", resmemo);
		return resmap;
	}

	public Map queryHgzxxSwt(QueryHgzxx info) {
		Map resmap = new HashMap();
		String resflag = "1";//0成功  1失败
		String resmemo = "";//flag=1时  保存反馈信息
		String reshgzid = "0";//flag=0时  保存hgzid
		String reshgzsource = "tbl";//flag=0时 保存hgzid来源：tbl 合格证信息在在用库tbl_hgzmain,tbl_hgzpara中； his  合格证信息在历史库tbl_hgzmain_his,tbl_hgzpara_his中
		HgzSwtinfo hgzinfo = new HgzSwtinfo();

		StringBuffer sql = new StringBuffer();

		List resultList = new ArrayList();
		String rs = "fail";
		try {
			int returntype[] = {java.sql.Types.VARCHAR,
					java.sql.Types.VARCHAR,
					java.sql.Types.VARCHAR,
					java.sql.Types.VARCHAR};
			String prcname = "proc_zgs_queryhgzxx";
			String[] paramtype ={"String","String","String","String","String","String"};
			List valuelist=new ArrayList();
			valuelist.add(info.getDph());
			valuelist.add(info.getFactorycode());

			Loggers.PERFORMANCE.debug(valuelist);
			resultList = this.dao.excuteprc_new(prcname, paramtype, valuelist, returntype);
			String flag = resultList.get(0).toString();
			resmemo= resultList.get(1).toString();
			reshgzid = resultList.get(2).toString();
			reshgzsource = resultList.get(3).toString();
			//查询合格证信息开始
			if(flag.equals("0")) {

				String sqlhead= "SELECT\r\n" +
						"dph,\r\n" +
						"subcompany factorycode,\r\n" +
						"scm,\r\n" +
						"cycle_status cyclestatus,\r\n" +
						"upload_status uploadstatus,\r\n" +
						"if(ifnull(printtime,'')='','',DATE_FORMAT(printtime,'%Y-%m-%d')) printtime,\r\n" +
						"if(ifnull(uptime,'')='','',DATE_FORMAT(uptime,'%Y-%m-%d')) uploadtime,\r\n" +
						"hgzfl,\r\n" +
						"hgzbh,\r\n" +
						"fzrq,\r\n" +
						"qymc,\r\n" +
						"pp,\r\n" +
						"clmc,\r\n" +
						"mllb,\r\n" +
						"clxh,\r\n" +
						"vnm,\r\n" +
						"csys,\r\n" +
						"dpxh,\r\n" +
						"dpid,\r\n" +
						"dphgzbh,\r\n" +
						"fdjxh,\r\n" +
						"fdjh,\r\n" +
						"ryzl,\r\n" +
						"pl,\r\n" +
						"gl,\r\n" +
						"pfbz,\r\n" +
						"yh,\r\n" +
						"wkc,\r\n" +
						"wkk,\r\n" +
						"wkg,\r\n" +
						"hxc,\r\n" +
						"hxk,\r\n" +
						"hxg,\r\n" +
						"thps,\r\n" +
						"lts,\r\n" +
						"ltgg,\r\n" +
						"qlj,\r\n" +
						"hlj,\r\n" +
						"zj,\r\n" +
						"zh,\r\n" +
						"zs,\r\n" +
						"zxxs,\r\n" +
						"zzl,\r\n" +
						"zbzl,\r\n" +
						"edzl,\r\n" +
						"zzlxs,\r\n" +
						"zqyzl,\r\n" +
						"bganzdzl bganzzdzl,\r\n" +
						"jsszcrs,\r\n" +
						"edzk,\r\n" +
						"zgcs,\r\n" +
						"scrq,\r\n" +
						"bz,\r\n" +
						"qybz,\r\n" +
						"clscdwmc,\r\n" +
						"scdz,\r\n" +
						"qtxx,\r\n" +
						"hgzxh,\r\n" +
						"cpno,\r\n" +
						"ph,\r\n" +
						"sxrq,\r\n" +
						"pzxlh,\r\n" +
						"jfpzid,\r\n" +
						"mzxny,\r\n" +
						"jsgc,\r\n" +
						"iscxnf,\r\n" +
						"vinbsyy,\r\n" +
						"zyzycmsbs,\r\n" +
						"qyid,\r\n" +
						"zxzs,\r\n" +
						"jyw,\r\n" +
						"cddbj,\r\n" +
						"dywym,\r\n" +
						"dpcpno,\r\n" +
						"fkxlh,\r\n" +
						"swtqymc,\r\n" +
						"swtqyscdz,\r\n" +
						"swtqytyshxydm\r\n" ;
				String sqlend = "from tbl_hgzmain_his m,tbl_hgzpara_his p where m.hgz_id=p.hgz_id \r\n" +
						"and m.hgz_id="+reshgzid;

				if(reshgzsource.equals("tbl")) {
					sqlend = "from tbl_hgzmain m,tbl_hgzpara p where m.hgz_id=p.hgz_id \r\n" +
							"and m.hgz_id="+reshgzid;
				}
                List list = null;
                try {
                    list = this.dao.queryForList(sql.toString());
					if(list.size()>0) {
						resflag="0";
						hgzinfo= (HgzSwtinfo) changeMapToHgzSwtInfo((Map) list.get(0));
					}else{
						resflag="1";
						resmemo=flag;
					}
                } catch (DAOException e) {
                    throw new RuntimeException(e);
                } catch (SQLException e) {
                    throw new RuntimeException(e);
                }
			}else {
				resflag="1";
				resmemo=flag;
			}
		} catch (AppException e) {
			e.printStackTrace();
			resflag="1";
			resmemo="数据库执行异常";
		}
		resmap.put("flag", resflag);
		resmap.put("memo", resmemo);
		resmap.put("hgzinfo", hgzinfo);
		return resmap;
	}

	private HgzSwtinfo changeMapToHgzSwtInfo(Map map) {
		HgzSwtinfo hgzinfo = new HgzSwtinfo();

		hgzinfo.setDph(MapUtil.getMapValue(map, "dph", ""));
		hgzinfo.setFactorycode(MapUtil.getMapValue(map, "factorycode", ""));
		hgzinfo.setScm(MapUtil.getMapValue(map, "scm", ""));
		hgzinfo.setCyclestatus(MapUtil.getMapValue(map, "cyclestatus", ""));
		hgzinfo.setUploadstatus(MapUtil.getMapValue(map, "uploadstatus", ""));
		hgzinfo.setPrinttime(MapUtil.getMapValue(map, "printtime", ""));
		hgzinfo.setUploadtime(MapUtil.getMapValue(map, "uploadtime", ""));
		hgzinfo.setHgzfl(MapUtil.getMapValue(map, "hgzfl", ""));
		hgzinfo.setHgzbh(MapUtil.getMapValue(map, "hgzbh", ""));
		hgzinfo.setFzrq(MapUtil.getMapValue(map, "fzrq", ""));
		hgzinfo.setQymc(MapUtil.getMapValue(map, "qymc", ""));
		hgzinfo.setPp(MapUtil.getMapValue(map, "pp", ""));
		hgzinfo.setClmc(MapUtil.getMapValue(map, "clmc", ""));
		hgzinfo.setMllb(MapUtil.getMapValue(map, "mllb", ""));
		hgzinfo.setClxh(MapUtil.getMapValue(map, "clxh", ""));
		hgzinfo.setVnm(MapUtil.getMapValue(map, "vnm", ""));
		hgzinfo.setCsys(MapUtil.getMapValue(map, "csys", ""));
		hgzinfo.setDpxh(MapUtil.getMapValue(map, "dpxh", ""));
		hgzinfo.setDpid(MapUtil.getMapValue(map, "dpid", ""));
		hgzinfo.setDphgzbh(MapUtil.getMapValue(map, "dphgzbh", ""));
		hgzinfo.setFdjxh(MapUtil.getMapValue(map, "fdjxh", ""));
		hgzinfo.setFdjh(MapUtil.getMapValue(map, "fdjh", ""));
		hgzinfo.setRyzl(MapUtil.getMapValue(map, "ryzl", ""));
		hgzinfo.setPl(MapUtil.getMapValue(map, "pl", ""));
		hgzinfo.setGl(MapUtil.getMapValue(map, "gl", ""));
		hgzinfo.setPfbz(MapUtil.getMapValue(map, "pfbz", ""));
		hgzinfo.setYh(MapUtil.getMapValue(map, "yh", ""));
		hgzinfo.setWkc(MapUtil.getMapValue(map, "wkc", ""));
		hgzinfo.setWkk(MapUtil.getMapValue(map, "wkk", ""));
		hgzinfo.setWkg(MapUtil.getMapValue(map, "wkg", ""));
		hgzinfo.setHxc(MapUtil.getMapValue(map, "hxc", ""));
		hgzinfo.setHxk(MapUtil.getMapValue(map, "hxk", ""));
		hgzinfo.setHxg(MapUtil.getMapValue(map, "hxg", ""));
		hgzinfo.setThps(MapUtil.getMapValue(map, "thps", ""));
		hgzinfo.setLts(MapUtil.getMapValue(map, "lts", ""));
		hgzinfo.setLtgg(MapUtil.getMapValue(map, "ltgg", ""));
		hgzinfo.setQlj(MapUtil.getMapValue(map, "qlj", ""));
		hgzinfo.setHlj(MapUtil.getMapValue(map, "hlj", ""));
		hgzinfo.setZj(MapUtil.getMapValue(map, "zj", ""));
		hgzinfo.setZh(MapUtil.getMapValue(map, "zh", ""));
		hgzinfo.setZs(MapUtil.getMapValue(map, "zs", ""));
		hgzinfo.setZxxs(MapUtil.getMapValue(map, "zxxs", ""));
		hgzinfo.setZzl(MapUtil.getMapValue(map, "zzl", ""));
		hgzinfo.setZbzl(MapUtil.getMapValue(map, "zbzl", ""));
		hgzinfo.setEdzl(MapUtil.getMapValue(map, "edzl", ""));
		hgzinfo.setZzlxs(MapUtil.getMapValue(map, "zzlxs", ""));
		hgzinfo.setZqyzl(MapUtil.getMapValue(map, "zqyzl", ""));
		hgzinfo.setBganzzdzl(MapUtil.getMapValue(map, "bganzzdzl", ""));
		hgzinfo.setJsszcrs(MapUtil.getMapValue(map, "jsszcrs", ""));
		hgzinfo.setEdzk(MapUtil.getMapValue(map, "edzk", ""));
		hgzinfo.setZgcs(MapUtil.getMapValue(map, "zgcs", ""));
		hgzinfo.setScrq(MapUtil.getMapValue(map, "scrq", ""));
		hgzinfo.setBz(MapUtil.getMapValue(map, "bz", ""));
		hgzinfo.setQybz(MapUtil.getMapValue(map, "qybz", ""));
		hgzinfo.setClscdwmc(MapUtil.getMapValue(map, "clscdwmc", ""));
		hgzinfo.setScdz(MapUtil.getMapValue(map, "scdz", ""));
		hgzinfo.setQtxx(MapUtil.getMapValue(map, "qtxx", ""));
		hgzinfo.setHgzxh(MapUtil.getMapValue(map, "hgzxh", ""));
		hgzinfo.setCpno(MapUtil.getMapValue(map, "cpno", ""));
		hgzinfo.setPh(MapUtil.getMapValue(map, "ph", ""));
		hgzinfo.setSxrq(MapUtil.getMapValue(map, "sxrq", ""));
		hgzinfo.setPzxlh(MapUtil.getMapValue(map, "pzxlh", ""));
		hgzinfo.setJfpzid(MapUtil.getMapValue(map, "jfpzid", ""));
		hgzinfo.setMzxny(MapUtil.getMapValue(map, "mzxny", ""));
		hgzinfo.setJsgc(MapUtil.getMapValue(map, "jsgc", ""));
		hgzinfo.setIscxnf(MapUtil.getMapValue(map, "iscxnf", ""));
		hgzinfo.setVinbsyy(MapUtil.getMapValue(map, "vinbsyy", ""));
		hgzinfo.setZyzycmsbs(MapUtil.getMapValue(map, "zyzycmsbs", ""));
		hgzinfo.setQyid(MapUtil.getMapValue(map, "qyid", ""));
		hgzinfo.setZxzs(MapUtil.getMapValue(map, "zxzs", ""));
		hgzinfo.setJyw(MapUtil.getMapValue(map, "jyw", ""));
		hgzinfo.setCddbj(MapUtil.getMapValue(map, "cddbj", ""));
		hgzinfo.setDywym(MapUtil.getMapValue(map, "dywym", ""));
		hgzinfo.setDpcpno(MapUtil.getMapValue(map, "dpcpno", ""));
		hgzinfo.setFkxlh(MapUtil.getMapValue(map, "fkxlh", ""));
		hgzinfo.setSwtqymc(MapUtil.getMapValue(map, "swtqymc", ""));
		hgzinfo.setSwtqyscdz(MapUtil.getMapValue(map, "swtqyscdz", ""));
		hgzinfo.setSwtqytyshxydm(MapUtil.getMapValue(map, "swtqytyshxydm", ""));

		return hgzinfo;
	}

	/**
	 * 单条请求国家合格证完整信息
	 * @param vin
	 * @param hgzbh
	 */
	public Map getHgzWzxx(String hgzbh) {
		Map resmap = new HashMap();
		String resflag = "1";//0查询得到，1 查询不到
		String resmemo = "";//flag=1时  保存反馈信息
		
		//访问国家接口接收反馈串
		HgzBackResultManage hBackResultManage = (HgzBackResultManage) ServiceFactory.getBeanByName("HgzBackResultManage");
		CertificateInfo[] info = null;
		info=hBackResultManage.queryCertificateSingle(hgzbh);
		if(info != null && info.length > 0){
			resmap.put("flag", "0");
			resmap.put("memo", "");
			resmap.put("hgzwzxx", info[0]);
		}else {
			resmap.put("flag", "1");
			resmap.put("memo", "合格证编号["+hgzbh+"]无对应国家合格证完整信息数据");
		}
		
		return resmap;
	}



}
