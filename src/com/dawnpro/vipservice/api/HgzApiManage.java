package com.dawnpro.vipservice.api;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.naming.Context;
import javax.naming.InitialContext;
import javax.naming.NamingException;
import javax.sql.DataSource;

import org.hibernate.dialect.function.NvlFunction;

import com.dawnpro.commons.spring.ServiceFactory;
import com.dawnpro.commons.util.StringTools;
import com.dawnpro.commons.util.db.DAOException;
import com.dawnpro.core.log.Loggers;
import com.dawnpro.entity.api.Car;
import com.dawnpro.entity.api.HgzList;
import com.dawnpro.entity.api.QueryHgzExchangeStatus;
import com.dawnpro.entity.api.QueryHgzGg;
import com.dawnpro.entity.api.QueryHgzList;
import com.dawnpro.entity.api.QueryHgzWzxx;
import com.dawnpro.entity.zgs.BaseHgzcs;
import com.dawnpro.entity.zgs.BaseHgzcsNew;
import com.dawnpro.entity.zgs.BaseXcxxjccs;
import com.dawnpro.entity.zgs.Hgzinfo;
import com.dawnpro.entity.zgs.ProductData;
import com.dawnpro.entity.zgs.ProductDataSwt;
import com.dawnpro.entity.zgs.QueryHgzxx;
import com.dawnpro.service.BaseServer;

public class HgzApiManage {
	
	/**
	 * 获取合格证变更清单
	 * @param info
	 * @return
	 */
	public Map getHgzList(QueryHgzList info) {
		Map resmap = new HashMap();
		String resflag = "1";//0查询得到变更记录，1 查询记录失败
		String resmemo = "";//flag=1时  保存反馈信息

		List<HgzList> hgzlist = new ArrayList<HgzList>();
		String rescheck = checkQueryHgzList(info);
		if (!"0".equals(rescheck)) {
			// 接口数据长度或格式校验不通过，反馈不通过信息
			resflag="1";
			resmemo = rescheck;
			resmap.put("flag", resflag);
			resmap.put("memo", resmemo);
			return resmap;
		} else {
			HgzApiToolManage hToolManage = (HgzApiToolManage) ServiceFactory.getBeanByName("HgzApiToolManage");
			if(!hToolManage.checkFactorycode(info.getFactorycode())) {
				resmap.put("flag", "1");
				resmap.put("memo", "工厂代码不存在");
				return resmap;
			}else {
				resmap=hToolManage.getHgzList(info);
				return resmap;
			}
		}
	}
	
	/**
	 * 获取合格证公告
	 * @param info
	 * @return
	 */
	public Map getHgzGg(QueryHgzGg info) {
		Map resmap = new HashMap();
		String resflag = "1";//0查询得到公告数据，1 查询公告数据失败
		String resmemo = "";//flag=1时  保存反馈信息

		List<HgzList> hgzlist = new ArrayList<HgzList>();
		String rescheck = checkQueryHgzGg(info);
		if (!"0".equals(rescheck)) {
			// 接口数据长度或格式校验不通过，反馈不通过信息
			resflag="1";
			resmemo = rescheck;
			resmap.put("flag", resflag);
			resmap.put("memo", resmemo);
			return resmap;
		} else {
			HgzApiToolManage hToolManage = (HgzApiToolManage) ServiceFactory.getBeanByName("HgzApiToolManage");
			if(!hToolManage.checkFactorycode(info.getFactorycode())) {
				resmap.put("flag", "1");
				resmap.put("memo", "工厂代码不存在");
				return resmap;
			}else {
				resmap=hToolManage.getHgzGg(info);
				return resmap;
			}
			
		}
	}
	
	/**
	 * 获取合格证交换状态
	 * @param info
	 * @return
	 */
	public Map getHgzExchangeStatus(QueryHgzExchangeStatus info) {
		Map resmap = new HashMap();
		String resflag = "1";//0接口正常，1接口校验不通过
		String resmemo = "";//flag=1时  保存反馈信息

		List<HgzList> hgzlist = new ArrayList<HgzList>();
		String rescheck = checkQueryHgzExchangeStatus(info);
		if (!"0".equals(rescheck)) {
			// 接口数据长度或格式校验不通过，反馈不通过信息
			resflag="1";
			resmemo = rescheck;
			resmap.put("flag", resflag);
			resmap.put("memo", resmemo);
			return resmap;
		} else {
			HgzApiToolManage hToolManage = (HgzApiToolManage) ServiceFactory.getBeanByName("HgzApiToolManage");
			if(!hToolManage.checkFactorycode(info.getFactorycode())) {
				resmap.put("flag", "1");
				resmap.put("memo", "工厂代码不存在");
				return resmap;
			}else {
				resmap=hToolManage.getHgzExchangeStatus(info);
				return resmap;
			}
			
		}
	}
	
	public Map uploadBaseHgzcsNew(BaseHgzcsNew info) {
		Map resmap = new HashMap();
		String resflag = "1";//0接口正常，1接口校验不通过
		String resmemo = "";//flag=1时  保存反馈信息

		List<HgzList> hgzlist = new ArrayList<HgzList>();
		String rescheck = checkBaseHgzcsNew(info);
		if (!"0".equals(rescheck)) {
			// 接口数据长度或格式校验不通过，反馈不通过信息
			resflag="1";
			resmemo = rescheck;
			resmap.put("flag", resflag);
			resmap.put("memo", resmemo);
			return resmap;
		} else {
			HgzApiToolManage hToolManage = (HgzApiToolManage) ServiceFactory.getBeanByName("HgzApiToolManage");
			if(!hToolManage.checkFactorycode(info.getFactorycode())) {
				resmap.put("flag", "1");
				resmap.put("memo", "工厂代码不存在");
				return resmap;
			}else {
				resmap=hToolManage.uploadBaseHgzcsNew(info);
				return resmap;
			}
			
		}
	}
	
	public Map uploadBaseHgzcs(BaseHgzcs info) {
		Map resmap = new HashMap();
		String resflag = "1";//0接口正常，1接口校验不通过
		String resmemo = "";//flag=1时  保存反馈信息

		List<HgzList> hgzlist = new ArrayList<HgzList>();
		String rescheck = checkBaseHgzcs(info);
		if (!"0".equals(rescheck)) {
			// 接口数据长度或格式校验不通过，反馈不通过信息
			resflag="1";
			resmemo = rescheck;
			resmap.put("flag", resflag);
			resmap.put("memo", resmemo);
			return resmap;
		} else {
			HgzApiToolManage hToolManage = (HgzApiToolManage) ServiceFactory.getBeanByName("HgzApiToolManage");
			if(!hToolManage.checkFactorycode(info.getFactorycode())) {
				resmap.put("flag", "1");
				resmap.put("memo", "工厂代码不存在");
				return resmap;
			}else {
				resmap=hToolManage.uploadBaseHgzcs(info);
				return resmap;
			}
			
		}
	}
	
	public Map uploadProductData(ProductData info) {
		Map resmap = new HashMap();
		String resflag = "1";//0接口正常，1接口校验不通过
		String resmemo = "";//flag=1时  保存反馈信息

		List<HgzList> hgzlist = new ArrayList<HgzList>();
		String rescheck = checkProductData(info);
		if (!"0".equals(rescheck)) {
			// 接口数据长度或格式校验不通过，反馈不通过信息
			resflag="1";
			resmemo = rescheck;
			resmap.put("flag", resflag);
			resmap.put("memo", resmemo);
			return resmap;
		} else {
			HgzApiToolManage hToolManage = (HgzApiToolManage) ServiceFactory.getBeanByName("HgzApiToolManage");
			if(!hToolManage.checkFactorycode(info.getFactorycode())) {
				resmap.put("flag", "1");
				resmap.put("memo", "工厂代码不存在");
				return resmap;
			}else {
				resmap=hToolManage.uploadProductData(info);
				return resmap;
			}
			
		}
	}
	
	/**
	 * 获取合格证完整信息
	 * @param info
	 * @return
	 */
	public Map queryHgzWzxx(QueryHgzWzxx info) {
		Map resmap = new HashMap();
		String resflag = "1";//0接口正常，1接口校验不通过
		String resmemo = "";//flag=1时  保存反馈信息

		List<HgzList> hgzlist = new ArrayList<HgzList>();
		String rescheck = checkQueryHgzWzxx(info);
		if (!"0".equals(rescheck)) {
			// 接口数据长度或格式校验不通过，反馈不通过信息
			resflag="1";
			resmemo = rescheck;
			resmap.put("flag", resflag);
			resmap.put("memo", resmemo);
			return resmap;
		} else {
			HgzApiToolManage hToolManage = (HgzApiToolManage) ServiceFactory.getBeanByName("HgzApiToolManage");
			if(!hToolManage.checkFactorycode(info.getFactorycode())) {
				resmap.put("flag", "1");
				resmap.put("memo", "工厂代码不存在");
				return resmap;
			}else {
				resmap=hToolManage.getHgzWzxx(info.getHgzbh().trim().toUpperCase());
				return resmap;
			}
			
		}
	}
	
	
	public Map uploadProductDataSwt(ProductDataSwt info) {
		Map resmap = new HashMap();
		String resflag = "1";//0接口正常，1接口校验不通过
		String resmemo = "";//flag=1时  保存反馈信息

		List<HgzList> hgzlist = new ArrayList<HgzList>();
		String rescheck = checkProductDataSwt(info);
		if (!"0".equals(rescheck)) {
			// 接口数据长度或格式校验不通过，反馈不通过信息
			resflag="1";
			resmemo = rescheck;
			resmap.put("flag", resflag);
			resmap.put("memo", resmemo);
			return resmap;
		} else {
			HgzApiToolManage hToolManage = (HgzApiToolManage) ServiceFactory.getBeanByName("HgzApiToolManage");
			if(!hToolManage.checkFactorycode(info.getFactorycode())) {
				resmap.put("flag", "1");
				resmap.put("memo", "工厂代码不存在");
				return resmap;
			}else {
				resmap=hToolManage.uploadProductDataSwt(info);
				return resmap;
			}
			
		}
	}

	/**
	 * 获取合格证完整信息
	 * @param info
	 * @return
	 */
	public Map queryHgzxxSwt(QueryHgzxx info) {
		Map resmap = new HashMap();
		String resflag = "1";//0接口正常，1接口校验不通过
		String resmemo = "";//flag=1时  保存反馈信息

		List<HgzList> hgzlist = new ArrayList<HgzList>();
		String rescheck = checkQueryHgzxx(info);
		if (!"0".equals(rescheck)) {
			// 接口数据长度或格式校验不通过，反馈不通过信息
			resflag="1";
			resmemo = rescheck;
			resmap.put("flag", resflag);
			resmap.put("memo", resmemo);
			return resmap;
		} else {
			HgzApiToolManage hToolManage = (HgzApiToolManage) ServiceFactory.getBeanByName("HgzApiToolManage");
			if(!hToolManage.checkFactorycode(info.getFactorycode())) {
				resmap.put("flag", "1");
				resmap.put("memo", "工厂代码不存在");
				return resmap;
			}else {
				resmap=hToolManage.queryHgzxxSwt(info);
				return resmap;
			}

		}
	}
	
	/**
	 * 校验QueryHgzList
	 * @param info
	 * @return
	 */
	private String checkQueryHgzList(QueryHgzList info) {
		String res = "0";
		String factorycode = info.getFactorycode();
		String starttime = info.getStarttime();
		String endtime = info.getEndtime();
		
		if (StringTools.isBlank(factorycode)) {
			res = "工厂代码不能为空";
			return res;
		}
		if (factorycode.length() > 20) {
			res = "工厂代码只能小于20个字符";
			return res;
		}

		if (StringTools.isBlank(starttime)) {
			res = "starttime不能为空";
			return res;
		}
		if(starttime.trim().length()!=19) {
			 res="starttime长度只能为19个字符，且格式应为yyyy-mm-dd hh:mi:ss";
			 return res;
		 }
		if(!isValidDate(starttime,null)) {
			res = "starttime不为标准日期格式，格式应为yyyy-mm-dd hh:mi:ss";
			return res;
		}
		
		if (StringTools.isBlank(endtime)) {
			res = "endtime不能为空";
			return res;
		}
		if(endtime.trim().length()!=19) {
			 res="endtime长度只能为19个字符，且格式应为yyyy-mm-dd hh:mi:ss";
			 return res;
		 }
		if(!isValidDate(endtime,null)) {
			res = "endtime不为标准日期格式，格式应为yyyy-mm-dd hh:mi:ss";
			return res;
		}

		return res;
	}
	
	private String checkQueryHgzGg(QueryHgzGg info) {
		String res = "0";
		String factorycode = info.getFactorycode();
		String cpno = info.getCpno();
		
		if (StringTools.isBlank(factorycode)) {
			res = "工厂代码不能为空";
			return res;
		}
		if (factorycode.length() > 20) {
			res = "工厂代码只能小于20个字符";
			return res;
		}

		if (StringTools.isBlank(cpno)) {
			res = "cpno不能为空";
			return res;
		}

		return res;
	}
	
	private String checkQueryHgzExchangeStatus(QueryHgzExchangeStatus info) {
		String res = "0";
		String factorycode = info.getFactorycode();
		List<Car> cars = info.getCarlist();
		
		if (StringTools.isBlank(factorycode)) {
			res = "工厂代码不能为空";
			return res;
		}
		if (factorycode.length() > 20) {
			res = "工厂代码只能小于20个字符";
			return res;
		}
		
		if(cars.size()<1) {
			res = "cars数组不能为空";
			return res;
		}

		for (int i = 0; i < cars.size(); i++) {
			Car car = cars.get(i);
			String vin = car.getVin();
			String hgzbh = car.getHgzbh();
			if(StringTools.isBlank(vin) && StringTools.isBlank(hgzbh)) {
				res = "vin与hgzbh需至少一个有值";
				return res;
			}else {
				if (!StringTools.isBlank(vin)) {
					if (vin.length() != 17) {
						res = "vin只能17个字符";
						return res;
					}
				}
				if (!StringTools.isBlank(hgzbh)) {
					if (hgzbh.length() != 15) {
						res = "hgzbh只能15个字符";
						return res;
					}
				}
			}
			
		}

		return res;
	}
	
	private String checkBaseHgzcsNew(BaseHgzcsNew info) {
		 String res="0";
		 
		 String scm=info.getScm();
		 String qymc=info.getQymc();
		 String pp=info.getPp();
		 String clmc=info.getClmc();
		 String mllb=info.getMllb();
		 String clxh=info.getClxh();
		 String vnm=info.getVnm();
		 String dpxh=info.getDpxh();
		 String dpid=info.getDpid();
		 String fdjxh=info.getFdjxh();
		 String ryzl=info.getRyzl();
		 String pl=info.getPl();
		 String gl=info.getGl();
		 String pfbz=info.getPfbz();
		 String yh=info.getYh();
		 String wkc=info.getWkc();
		 String wkk=info.getWkk();
		 String wkg=info.getWkg();
		 String hxc=info.getHxc();
		 String hxk=info.getHxk();
		 String hxg=info.getHxg();
		 String thps=info.getThps();
		 String lts=info.getLts();
		 String ltgg=info.getLtgg();
		 String qlj=info.getQlj();
		 String hlj=info.getHlj();
		 String zj=info.getZj();
		 String zh=info.getZh();
		 String zs=info.getZs();
		 String zxxs=info.getZxxs();
		 String zzl=info.getZzl();
		 String zbzl=info.getZbzl();
		 String edzl=info.getEdzl();
		 String zzlxs=info.getZzlxs();
		 String zqyzl=info.getZqyzl();
		 String bganzzdzl=info.getBganzzdzl();
		 String jsszcrs=info.getJsszcrs();
		 String edzk=info.getEdzk();
		 String zgcs=info.getZgcs();
		 String bz=info.getBz();
		 String qybz=info.getQybz();
		 String clscdwmc=info.getClscdwmc();
		 String scdz=info.getScdz();
		 String qtxx=info.getQtxx();
		 String cpno=info.getCpno();
		 String ph=info.getPh();
		 String sxrq=info.getSxrq();
		 String pzxlh=info.getPzxlh();
		 String jfpzid=info.getJfpzid();
		 String mzxny=info.getMzxny();
		 String jsgc=info.getJsgc();
		 String iscxnf=info.getIscxnf();
		 String zyzycmsbs=info.getZyzycmsbs();
		 
		 String xnyqcjmsbs=info.getXnyqcjmsbs();
		 String hdmsbs=info.getHdmsbs();
		 String xnyqczl=info.getXnyqczl();
		 
		 String zxzs=info.getZxzs();
		 String cddbj=info.getCddbj();
		 String dpcpno=info.getDpcpno();
		 String factorycode=info.getFactorycode();
		 
		 
		//scm
		 if(StringTools.isBlank(scm)) {
			 res="生产码不能为空";
			 return res;
		 }
		 if(scm.length()>100) {
			 res="生产码长度不能大于100个字符";
			 return res;
		 }
		//qymc
		 if(StringTools.isBlank(qymc)) {
			 res="企业名称不能为空";
			 return res;
		 }
		 if(qymc.length()>64) {
			 res="企业名称长度不能大于64个字符";
			 return res;
		 }
		//pp
		 if(StringTools.isBlank(pp)) {
			 res="车辆品牌不能为空";
			 return res;
		 }
		 if(pp.length()>30) {
			 res="车辆品牌长度不能大于30个字符";
			 return res;
		 }
		//clmc
		 if(StringTools.isBlank(clmc)) {
			 res="车辆名称不能为空";
			 return res;
		 }
		 if(clmc.length()>54) {
			 res="车辆名称长度不能大于54个字符";
			 return res;
		 }
		//mllb 0-11
		 if(StringTools.isBlank(mllb)) {
			 res="车辆分类不能为空";
			 return res;
		 }
		 if(mllb.length()>2) {
			 res="车辆分类长度不能大于2个字符";
			 return res;
		 }
		 if("0,1,2,3,4,5,6,7,8,9,10,11,".indexOf(mllb+",")==-1 ) {
			 res="车辆分类只能在[0,1,2,3,4,5,6,7,8,9,10,11]中";
			 return res;
		 }
		//clxh
		 if(StringTools.isBlank(clxh)) {
			 res="车辆型号不能为空";
			 return res;
		 }
		 if(clxh.length()>30) {
			 res="车辆型号长度不能大于30个字符";
			 return res;
		 }
		//vnm
		 if(StringTools.isBlank(vnm)) {
			 res="车辆识别代号前8位不能为空";
			 return res;
		 }
		 if(vnm.length()!=8) {
			 res="车辆识别代号前8位长度应为8个字符";
			 return res;
		 }
		//dpxh
		 if(!StringTools.isBlank(dpxh)) {
			 if(dpxh.length()>30) {
				 res="底盘型号长度不能大于30个字符";
				 return res;
			 }
		 }
		 
		//dpid
		 if(!StringTools.isBlank(dpid)) {
			 if(dpid.length()>7) {
				 res="底盘ID长度不能大于7个字符";
				 return res;
			 }
		 }
		//fdjxh
		 if(!StringTools.isBlank(fdjxh)) {
			 if(fdjxh.length()>20) {
				 res="发动机型号长度不能大于20个字符";
				 return res;
			 }
		 }
		//ryzl
		 if(StringTools.isBlank(ryzl)) {
			 res="燃料种类不能为空";
			 return res;
		 }
		 if(ryzl.length()>30) {
			 res="燃料种类长度不能大于30个字符";
			 return res;
		 }
		//pl
		 if(!StringTools.isBlank(pl)) {
			 if(pl.length()>5) {
				 res="排量长度不能大于5个字符";
				 return res;
			 }
		 }
		//gl
		 if(StringTools.isBlank(gl)) {
			 res="功率不能为空";
			 return res;
		 }
		 if(gl.length()>7) {
			 res="功率长度不能大于7个字符";
			 return res;
		 }
		//pfbz
		 if(StringTools.isBlank(pfbz)) {
			 res="排放标准不能为空";
			 return res;
		 }
		 if(pfbz.length()>60) {
			 res="排放标准长度不能大于60个字符";
			 return res;
		 }
		//yh
		 if(!StringTools.isBlank(yh)) {
			 if(yh.length()>30) {
				 res="油耗不能大于30个字符";
				 return res;
			 }
		 }
		//wkc
		 if(StringTools.isBlank(wkc)) {
			 res="外廓尺寸长不能为空";
			 return res;
		 }
		 if(wkc.length()>5) {
			 res="外廓尺寸长长度不能大于5个字符";
			 return res;
		 }
		//wkk
		 if(StringTools.isBlank(wkk)) {
			 res="外廓尺寸宽不能为空";
			 return res;
		 }
		 if(wkk.length()>4) {
			 res="外廓尺寸宽长度不能大于4个字符";
			 return res;
		 }
		//wkg
		 if(!StringTools.isBlank(wkg)) {
			 if(wkg.length()>4) {
				 res="外廓尺寸高长度不能大于4个字符";
				 return res;
			 }
		 }
		//hxc
		 if(!StringTools.isBlank(hxc)) {
			 if(hxc.length()>5) {
				 res="货厢内部尺寸长不能大于5个字符";
				 return res;
			 }
		 }
		//hxk
		 if(!StringTools.isBlank(hxk)) {
			 if(hxk.length()>4) {
				 res="货厢内部尺寸宽不能大于4个字符";
				 return res;
			 }
		 }
		//hxg
		 if(!StringTools.isBlank(hxg)) {
			 if(hxg.length()>4) {
				 res="货厢内部尺寸高不能大于4个字符";
				 return res;
			 }
		 }
		//thps
		 if(!StringTools.isBlank(thps)) {
			 if(thps.length()>30) {
				 res="钢板弹簧片数不能大于30个字符";
				 return res;
			 }
		 }
		//lts
		 if(StringTools.isBlank(lts)) {
			 res="轮胎数不能为空";
			 return res;
		 }
		 if(lts.length()>2) {
			 res="轮胎数长度不能大于2个字符";
			 return res;
		 }
		//ltgg
		 if(StringTools.isBlank(ltgg)) {
			 res="轮胎规格不能为空";
			 return res;
		 }
		 if(ltgg.length()>30) {
			 res="轮胎规格长度不能大于30个字符";
			 return res;
		 }
		//qlj
		 if(!StringTools.isBlank(qlj)) {
			 if(qlj.length()>9) {
				 res="轮距前不能大于9个字符";
				 return res;
			 }
		 }
		//hlj
		 if(!StringTools.isBlank(hlj)) {
			 if(hlj.length()>54) {
				 res="轮距后不能大于54个字符";
				 return res;
			 }
		 }
		//zj
		 if(StringTools.isBlank(zj)) {
			 res="轴距不能为空";
			 return res;
		 }
		 if(zj.length()>60) {
			 res="轴距长度不能大于60个字符";
			 return res;
		 }
		//zh
		 if(!StringTools.isBlank(zh)) {
			 if(zh.length()>30) {
				 res="轴荷不能大于30个字符";
				 return res;
			 }
		 }
		//zs
		 if(StringTools.isBlank(zs)) {
			 res="轴数不能为空";
			 return res;
		 }
		 if(zs.length()>1) {
			 res="轴数长度不能大于1个字符";
			 return res;
		 }
		//zxxs
		 if(!StringTools.isBlank(zxxs)) {
			 if(zxxs.length()>6) {
				 res="转向形式不能大于6个字符";
				 return res;
			 }
		 }
		//zzl
		 if(StringTools.isBlank(zzl)) {
			 res="总质量不能为空";
			 return res;
		 }
		 if(zzl.length()>8) {
			 res="总质量长度不能大于8个字符";
			 return res;
		 }
		//zbzl
		 if(StringTools.isBlank(zbzl)) {
			 res="整备质量不能为空";
			 return res;
		 }
		 if(zbzl.length()>8) {
			 res="整备质量长度不能大于8个字符";
			 return res;
		 }
		//edzl
		 if(!StringTools.isBlank(edzl)) {
			 if(edzl.length()>8) {
				 res="额定载质量不能大于8个字符";
				 return res;
			 }
		 }
		//zzlxs
		 if(!StringTools.isBlank(zzlxs)) {
			 if(zzlxs.length()>30) {
				 res="载质量利用系数不能大于30个字符";
				 return res;
			 }
		 }
		//zqyzl
		 if(!StringTools.isBlank(zqyzl)) {
			 if(zqyzl.length()>8) {
				 res="准牵引总质量不能大于8个字符";
				 return res;
			 }
		 }
		//bganzzdzl
		 if(!StringTools.isBlank(bganzzdzl)) {
			 if(bganzzdzl.length()>6) {
				 res="半挂车鞍座最大允许总质量不能大于8个字符";
				 return res;
			 }
		 }
		//jsszcrs
		 if(!StringTools.isBlank(jsszcrs)) {
			 if(jsszcrs.length()>3) {
				 res="驾驶室准乘人数不能大于3个字符";
				 return res;
			 }
		 }
		//edzk
		 if(!StringTools.isBlank(edzk)) {
			 if(edzk.length()>5) {
				 res="额定载客不能大于5个字符";
				 return res;
			 }
		 }
		//zgcs
		 if(!StringTools.isBlank(zgcs)) {
			 if(zgcs.length()>5) {
				 res="最高设计车速不能大于5个字符";
				 return res;
			 }
		 }
		//bz
		 if(!StringTools.isBlank(bz)) {
			 if(bz.length()>260) {
				 res="备注不能大于260个字符";
				 return res;
			 }
			 if(length(bz)>260) {
				 res="备注长度不能大于130个汉字或260个英文";
				 return res;
			 }
		 }
		//qybz
		 if(StringTools.isBlank(qybz)) {
			 res="企业标准不能为空";
			 return res;
		 }
		 if(qybz.length()>200) {
			 res="企业标准长度不能大于200个字符";
			 return res;
		 }
		//clscdwmc
		 if(StringTools.isBlank(clscdwmc)) {
			 res="车辆生产单位名称不能为空";
			 return res;
		 }
		 if(clscdwmc.length()>64) {
			 res="车辆生产单位名称长度不能大于64个字符";
			 return res;
		 }
		//scdz
		 if(StringTools.isBlank(scdz)) {
			 res="车辆生产单位地址不能为空";
			 return res;
		 }
		 if(scdz.length()>70) {
			 res="车辆生产单位地址长度不能大于70个字符";
			 return res;
		 }
		//qtxx
		 if(!StringTools.isBlank(qtxx)) {
			 if("VOYAH".equals(factorycode)) {
				 if(qtxx.length()>400) {
					 res="其他信息不能大于400个字符";
					 return res;
				 }
				 if(length(qtxx)>400) {
					 res="其他信息不能大于200个汉字或400个英文";
					 return res;
				 }
			 }else {
				 if(qtxx.length()>200) {
					 res="其他信息不能大于200个字符";
					 return res;
				 }
				 if(length(qtxx)>200) {
					 res="其他信息不能大于100个汉字或200个英文";
					 return res;
				 }
			 }
		 }
		//cpno
		 if(StringTools.isBlank(cpno)) {
			 res="产品公告号不能为空";
			 return res;
		 }
		 if(cpno.length()>50) {
			 res="产品公告号长度不能大于50个字符";
			 return res;
		 }
		//ph
		 if(StringTools.isBlank(ph)) {
			 res="公告批次不能为空";
			 return res;
		 }
		 if(ph.length()>50) {
			 res="公告批次长度不能大于50个字符";
			 return res;
		 }
		//sxrq
		 if(StringTools.isBlank(sxrq)) {
			 res="公告生效日期不能为空";
			 return res;
		 }
		 if(sxrq.trim().length()!=10) {
			 res="公告生效日期长度只能为10个字符，且格式应为yyyy-mm-dd";
			 return res;
		 }
		 if(!isDate(sxrq)) {
			 res="公告生效日期长度只能为10个字符，且格式应为yyyy-mm-dd";
			 return res;
		 }
		//pzxlh
		 if(!StringTools.isBlank(pzxlh)) {
			 if(pzxlh.length()>25) {
				 res="配置序列号不能大于25个字符";
				 return res;
			 }
		 }
		//jfpzid
		 if(!StringTools.isBlank(jfpzid)) {
			 if(jfpzid.length()>50) {
				 res="双积分配置ID不能大于50个字符";
				 return res;
			 }
		 }
		//mzxny
		 if(StringTools.isBlank(mzxny)) {
			 res="是否免征新能源不能为空";
			 return res;
		 }
		 if(mzxny.length()>1) {
			 res="是否免征新能源不能大于1个字符";
			 return res;
		 }
		 if("0,1,".indexOf(mzxny+",")==-1 ) {
			 res="是否免征新能源只能在[0,1]中";
			 return res;
		 }
		//jsgc
		 if(StringTools.isBlank(jsgc)) {
			 res="是否减税挂车不能为空";
			 return res;
		 }
		 if(jsgc.length()>1) {
			 res="是否减税挂车不能大于1个字符";
			 return res;
		 }
		 if("0,1,".indexOf(jsgc+",")==-1 ) {
			 res="是否减税挂车只能在[0,1]中";
			 return res;
		 }
		//iscxnf
		 if(StringTools.isBlank(iscxnf)) {
			 res="是否车型年份不能为空";
			 return res;
		 }
		 if(iscxnf.length()>1) {
			 res="是否车型年份不能大于1个字符";
			 return res;
		 }
		 if("0,1,".indexOf(iscxnf+",")==-1 ) {
			 res="是否车型年份只能在[0,1]中";
			 return res;
		 }
		//zyzycmsbs
		 if(StringTools.isBlank(zyzycmsbs)) {
			 res="是否专用作业车免税不能为空";
			 return res;
		 }
		 if(zyzycmsbs.length()>1) {
			 res="是否专用作业车免税不能大于1个字符";
			 return res;
		 }
		 if("0,1,".indexOf(zyzycmsbs+",")==-1 ) {
			 res="是否专用作业车免税只能在[0,1]中";
			 return res;
		 }
		 
		//xnyqcjmsbs
		 if(StringTools.isBlank(xnyqcjmsbs)) {
			 res="减免税标识不能为空";
			 return res;
		 }
		 if(xnyqcjmsbs.length()>1) {
			 res="减免税标识不能大于1个字符";
			 return res;
		 }
		 if("0,1,".indexOf(xnyqcjmsbs+",")==-1 ) {
			 res="减免税标识只能在[0,1]中";
			 return res;
		 }
		//hdmsbs
		 if(StringTools.isBlank(hdmsbs)) {
			 res="换电模式标识不能为空";
			 return res;
		 }
		 if(hdmsbs.length()>1) {
			 res="换电模式标识不能大于1个字符";
			 return res;
		 }
		 if("0,1,".indexOf(hdmsbs+",")==-1 ) {
			 res="换电模式标识只能在[0,1]中";
			 return res;
		 }
		//xnyqczl
		 if(StringTools.isBlank(xnyqczl)) {
			 res="新能源汽车种类标识不能为空";
			 return res;
		 }
		 if(xnyqczl.length()!=2) {
			 res="新能源汽车种类标识需为2个字符";
			 return res;
		 }
		 if("00,10,20,30,".indexOf(xnyqczl+",")==-1 ) {
			 res="新能源汽车种类标识只能在[00,10,20,30]中";
			 return res;
		 }
		 
		//zxzs
		 if(StringTools.isBlank(zxzs)) {
			 res="转向轴个数不能为空";
			 return res;
		 }
		 if(zxzs.length()>1) {
			 res="转向轴个数不能大于1个字符";
			 return res;
		 }
		//cddbj
		 if(StringTools.isBlank(cddbj)) {
			 res="纯电动标记不能为空";
			 return res;
		 }
		 if(cddbj.length()>1) {
			 res="纯电动标记不能大于1个字符";
			 return res;
		 }
		 if("1,2,".indexOf(cddbj+",")==-1 ) {
			 res="纯电动标记只能在[1,2]中";
			 return res;
		 }
		//dpcpno
		 if(!StringTools.isBlank(dpcpno)) {
			 if(dpcpno.length()>50) {
				 res="底盘产品公告号不能大于50个字符";
				 return res;
			 }
		 }
		//factorycode
		 if(StringTools.isBlank(factorycode)) {
			 res="工厂代码不能为空";
			 return res;
		 }
		 if(factorycode.trim().length()>20) {
			 res="工厂代码只能小于20个字符";
			 return res;
		 }
		 return res;

	}
	
	private String checkBaseHgzcs(BaseHgzcs info) {
		 String res="0";
		 
		 String scm=info.getScm();
		 String qymc=info.getQymc();
		 String pp=info.getPp();
		 String clmc=info.getClmc();
		 String mllb=info.getMllb();
		 String clxh=info.getClxh();
		 String vnm=info.getVnm();
		 String dpxh=info.getDpxh();
		 String dpid=info.getDpid();
		 String fdjxh=info.getFdjxh();
		 String ryzl=info.getRyzl();
		 String pl=info.getPl();
		 String gl=info.getGl();
		 String pfbz=info.getPfbz();
		 String yh=info.getYh();
		 String wkc=info.getWkc();
		 String wkk=info.getWkk();
		 String wkg=info.getWkg();
		 String hxc=info.getHxc();
		 String hxk=info.getHxk();
		 String hxg=info.getHxg();
		 String thps=info.getThps();
		 String lts=info.getLts();
		 String ltgg=info.getLtgg();
		 String qlj=info.getQlj();
		 String hlj=info.getHlj();
		 String zj=info.getZj();
		 String zh=info.getZh();
		 String zs=info.getZs();
		 String zxxs=info.getZxxs();
		 String zzl=info.getZzl();
		 String zbzl=info.getZbzl();
		 String edzl=info.getEdzl();
		 String zzlxs=info.getZzlxs();
		 String zqyzl=info.getZqyzl();
		 String bganzzdzl=info.getBganzzdzl();
		 String jsszcrs=info.getJsszcrs();
		 String edzk=info.getEdzk();
		 String zgcs=info.getZgcs();
		 String bz=info.getBz();
		 String qybz=info.getQybz();
		 String clscdwmc=info.getClscdwmc();
		 String scdz=info.getScdz();
		 String qtxx=info.getQtxx();
		 String cpno=info.getCpno();
		 String ph=info.getPh();
		 String sxrq=info.getSxrq();
		 String pzxlh=info.getPzxlh();
		 String jfpzid=info.getJfpzid();
		 String mzxny=info.getMzxny();
		 String jsgc=info.getJsgc();
		 String iscxnf=info.getIscxnf();
		 String zyzycmsbs=info.getZyzycmsbs();
		 
		 String zxzs=info.getZxzs();
		 String cddbj=info.getCddbj();
		 String dpcpno=info.getDpcpno();
		 String factorycode=info.getFactorycode();
		 
		 
		//scm
		 if(StringTools.isBlank(scm)) {
			 res="生产码不能为空";
			 return res;
		 }
		 if(scm.length()>100) {
			 res="生产码长度不能大于100个字符";
			 return res;
		 }
		//qymc
		 if(StringTools.isBlank(qymc)) {
			 res="企业名称不能为空";
			 return res;
		 }
		 if(qymc.length()>64) {
			 res="企业名称长度不能大于64个字符";
			 return res;
		 }
		//pp
		 if(StringTools.isBlank(pp)) {
			 res="车辆品牌不能为空";
			 return res;
		 }
		 if(pp.length()>30) {
			 res="车辆品牌长度不能大于30个字符";
			 return res;
		 }
		//clmc
		 if(StringTools.isBlank(clmc)) {
			 res="车辆名称不能为空";
			 return res;
		 }
		 if(clmc.length()>54) {
			 res="车辆名称长度不能大于54个字符";
			 return res;
		 }
		//mllb 0-11
		 if(StringTools.isBlank(mllb)) {
			 res="车辆分类不能为空";
			 return res;
		 }
		 if(mllb.length()>2) {
			 res="车辆分类长度不能大于2个字符";
			 return res;
		 }
		 if("0,1,2,3,4,5,6,7,8,9,10,11,".indexOf(mllb+",")==-1 ) {
			 res="车辆分类只能在[0,1,2,3,4,5,6,7,8,9,10,11]中";
			 return res;
		 }
		//clxh
		 if(StringTools.isBlank(clxh)) {
			 res="车辆型号不能为空";
			 return res;
		 }
		 if(clxh.length()>30) {
			 res="车辆型号长度不能大于30个字符";
			 return res;
		 }
		//vnm
		 if(StringTools.isBlank(vnm)) {
			 res="车辆识别代号前8位不能为空";
			 return res;
		 }
		 if(vnm.length()!=8) {
			 res="车辆识别代号前8位长度应为8个字符";
			 return res;
		 }
		//dpxh
		 if(!StringTools.isBlank(dpxh)) {
			 if(dpxh.length()>30) {
				 res="底盘型号长度不能大于30个字符";
				 return res;
			 }
		 }
		 
		//dpid
		 if(!StringTools.isBlank(dpid)) {
			 if(dpid.length()>7) {
				 res="底盘ID长度不能大于7个字符";
				 return res;
			 }
		 }
		//fdjxh
		 if(!StringTools.isBlank(fdjxh)) {
			 if(fdjxh.length()>20) {
				 res="发动机型号长度不能大于20个字符";
				 return res;
			 }
		 }
		//ryzl
		 if(StringTools.isBlank(ryzl)) {
			 res="燃料种类不能为空";
			 return res;
		 }
		 if(ryzl.length()>30) {
			 res="燃料种类长度不能大于30个字符";
			 return res;
		 }
		//pl
		 if(!StringTools.isBlank(pl)) {
			 if(pl.length()>5) {
				 res="排量长度不能大于5个字符";
				 return res;
			 }
		 }
		//gl
		 if(StringTools.isBlank(gl)) {
			 res="功率不能为空";
			 return res;
		 }
		 if(gl.length()>7) {
			 res="功率长度不能大于7个字符";
			 return res;
		 }
		//pfbz
		 if(StringTools.isBlank(pfbz)) {
			 res="排放标准不能为空";
			 return res;
		 }
		 if(pfbz.length()>60) {
			 res="排放标准长度不能大于60个字符";
			 return res;
		 }
		//yh
		 if(!StringTools.isBlank(yh)) {
			 if(yh.length()>30) {
				 res="油耗不能大于30个字符";
				 return res;
			 }
		 }
		//wkc
		 if(StringTools.isBlank(wkc)) {
			 res="外廓尺寸长不能为空";
			 return res;
		 }
		 if(wkc.length()>5) {
			 res="外廓尺寸长长度不能大于5个字符";
			 return res;
		 }
		//wkk
		 if(StringTools.isBlank(wkk)) {
			 res="外廓尺寸宽不能为空";
			 return res;
		 }
		 if(wkk.length()>4) {
			 res="外廓尺寸宽长度不能大于4个字符";
			 return res;
		 }
		//wkg
		 if(!StringTools.isBlank(wkg)) {
			 if(wkg.length()>4) {
				 res="外廓尺寸高长度不能大于4个字符";
				 return res;
			 }
		 }
		//hxc
		 if(!StringTools.isBlank(hxc)) {
			 if(hxc.length()>5) {
				 res="货厢内部尺寸长不能大于5个字符";
				 return res;
			 }
		 }
		//hxk
		 if(!StringTools.isBlank(hxk)) {
			 if(hxk.length()>4) {
				 res="货厢内部尺寸宽不能大于4个字符";
				 return res;
			 }
		 }
		//hxg
		 if(!StringTools.isBlank(hxg)) {
			 if(hxg.length()>4) {
				 res="货厢内部尺寸高不能大于4个字符";
				 return res;
			 }
		 }
		//thps
		 if(!StringTools.isBlank(thps)) {
			 if(thps.length()>30) {
				 res="钢板弹簧片数不能大于30个字符";
				 return res;
			 }
		 }
		//lts
		 if(StringTools.isBlank(lts)) {
			 res="轮胎数不能为空";
			 return res;
		 }
		 if(lts.length()>2) {
			 res="轮胎数长度不能大于2个字符";
			 return res;
		 }
		//ltgg
		 if(StringTools.isBlank(ltgg)) {
			 res="轮胎规格不能为空";
			 return res;
		 }
		 if(ltgg.length()>30) {
			 res="轮胎规格长度不能大于30个字符";
			 return res;
		 }
		//qlj
		 if(!StringTools.isBlank(qlj)) {
			 if(qlj.length()>9) {
				 res="轮距前不能大于9个字符";
				 return res;
			 }
		 }
		//hlj
		 if(!StringTools.isBlank(hlj)) {
			 if(hlj.length()>54) {
				 res="轮距后不能大于54个字符";
				 return res;
			 }
		 }
		//zj
		 if(StringTools.isBlank(zj)) {
			 res="轴距不能为空";
			 return res;
		 }
		 if(zj.length()>60) {
			 res="轴距长度不能大于60个字符";
			 return res;
		 }
		//zh
		 if(!StringTools.isBlank(zh)) {
			 if(zh.length()>30) {
				 res="轴荷不能大于30个字符";
				 return res;
			 }
		 }
		//zs
		 if(StringTools.isBlank(zs)) {
			 res="轴数不能为空";
			 return res;
		 }
		 if(zs.length()>1) {
			 res="轴数长度不能大于1个字符";
			 return res;
		 }
		//zxxs
		 if(!StringTools.isBlank(zxxs)) {
			 if(zxxs.length()>6) {
				 res="转向形式不能大于6个字符";
				 return res;
			 }
		 }
		//zzl
		 if(StringTools.isBlank(zzl)) {
			 res="总质量不能为空";
			 return res;
		 }
		 if(zzl.length()>8) {
			 res="总质量长度不能大于8个字符";
			 return res;
		 }
		//zbzl
		 if(StringTools.isBlank(zbzl)) {
			 res="整备质量不能为空";
			 return res;
		 }
		 if(zbzl.length()>8) {
			 res="整备质量长度不能大于8个字符";
			 return res;
		 }
		//edzl
		 if(!StringTools.isBlank(edzl)) {
			 if(edzl.length()>8) {
				 res="额定载质量不能大于8个字符";
				 return res;
			 }
		 }
		//zzlxs
		 if(!StringTools.isBlank(zzlxs)) {
			 if(zzlxs.length()>30) {
				 res="载质量利用系数不能大于30个字符";
				 return res;
			 }
		 }
		//zqyzl
		 if(!StringTools.isBlank(zqyzl)) {
			 if(zqyzl.length()>8) {
				 res="准牵引总质量不能大于8个字符";
				 return res;
			 }
		 }
		//bganzzdzl
		 if(!StringTools.isBlank(bganzzdzl)) {
			 if(bganzzdzl.length()>6) {
				 res="半挂车鞍座最大允许总质量不能大于8个字符";
				 return res;
			 }
		 }
		//jsszcrs
		 if(!StringTools.isBlank(jsszcrs)) {
			 if(jsszcrs.length()>3) {
				 res="驾驶室准乘人数不能大于3个字符";
				 return res;
			 }
		 }
		//edzk
		 if(!StringTools.isBlank(edzk)) {
			 if(edzk.length()>5) {
				 res="额定载客不能大于5个字符";
				 return res;
			 }
		 }
		//zgcs
		 if(!StringTools.isBlank(zgcs)) {
			 if(zgcs.length()>5) {
				 res="最高设计车速不能大于5个字符";
				 return res;
			 }
		 }
		//bz
		 if(!StringTools.isBlank(bz)) {
			 if(bz.length()>260) {
				 res="备注不能大于260个字符";
				 return res;
			 }
			 if(length(bz)>260) {
				 res="备注长度不能大于130个汉字或260个英文";
				 return res;
			 }
		 }
		//qybz
		 if(StringTools.isBlank(qybz)) {
			 res="企业标准不能为空";
			 return res;
		 }
		 if(qybz.length()>200) {
			 res="企业标准长度不能大于200个字符";
			 return res;
		 }
		//clscdwmc
		 if(StringTools.isBlank(clscdwmc)) {
			 res="车辆生产单位名称不能为空";
			 return res;
		 }
		 if(clscdwmc.length()>64) {
			 res="车辆生产单位名称长度不能大于64个字符";
			 return res;
		 }
		//scdz
		 if(StringTools.isBlank(scdz)) {
			 res="车辆生产单位地址不能为空";
			 return res;
		 }
		 if(scdz.length()>70) {
			 res="车辆生产单位地址长度不能大于70个字符";
			 return res;
		 }
		//qtxx
		 if(!StringTools.isBlank(qtxx)) {
			 if("VOYAH".equals(factorycode)) {
				 if(qtxx.length()>400) {
					 res="其他信息不能大于400个字符";
					 return res;
				 }
				 if(length(qtxx)>400) {
					 res="其他信息不能大于200个汉字或400个英文";
					 return res;
				 }
			 }else {
				 if(qtxx.length()>200) {
					 res="其他信息不能大于200个字符";
					 return res;
				 }
				 if(length(qtxx)>200) {
					 res="其他信息不能大于100个汉字或200个英文";
					 return res;
				 }
			 }
		 }
		//cpno
		 if(StringTools.isBlank(cpno)) {
			 res="产品公告号不能为空";
			 return res;
		 }
		 if(cpno.length()>50) {
			 res="产品公告号长度不能大于50个字符";
			 return res;
		 }
		//ph
		 if(StringTools.isBlank(ph)) {
			 res="公告批次不能为空";
			 return res;
		 }
		 if(ph.length()>50) {
			 res="公告批次长度不能大于50个字符";
			 return res;
		 }
		//sxrq
		 if(StringTools.isBlank(sxrq)) {
			 res="公告生效日期不能为空";
			 return res;
		 }
		 if(sxrq.trim().length()!=10) {
			 res="公告生效日期长度只能为10个字符，且格式应为yyyy-mm-dd";
			 return res;
		 }
		 if(!isDate(sxrq)) {
			 res="公告生效日期长度只能为10个字符，且格式应为yyyy-mm-dd";
			 return res;
		 }
		//pzxlh
		 if(!StringTools.isBlank(pzxlh)) {
			 if(pzxlh.length()>25) {
				 res="配置序列号不能大于25个字符";
				 return res;
			 }
		 }
		//jfpzid
		 if(!StringTools.isBlank(jfpzid)) {
			 if(jfpzid.length()>50) {
				 res="双积分配置ID不能大于50个字符";
				 return res;
			 }
		 }
		//mzxny
		 if(StringTools.isBlank(mzxny)) {
			 res="是否免征新能源不能为空";
			 return res;
		 }
		 if(mzxny.length()>1) {
			 res="是否免征新能源不能大于1个字符";
			 return res;
		 }
		 if("0,1,".indexOf(mzxny+",")==-1 ) {
			 res="是否免征新能源只能在[0,1]中";
			 return res;
		 }
		//jsgc
		 if(StringTools.isBlank(jsgc)) {
			 res="是否减税挂车不能为空";
			 return res;
		 }
		 if(jsgc.length()>1) {
			 res="是否减税挂车不能大于1个字符";
			 return res;
		 }
		 if("0,1,".indexOf(jsgc+",")==-1 ) {
			 res="是否减税挂车只能在[0,1]中";
			 return res;
		 }
		//iscxnf
		 if(StringTools.isBlank(iscxnf)) {
			 res="是否车型年份不能为空";
			 return res;
		 }
		 if(iscxnf.length()>1) {
			 res="是否车型年份不能大于1个字符";
			 return res;
		 }
		 if("0,1,".indexOf(iscxnf+",")==-1 ) {
			 res="是否车型年份只能在[0,1]中";
			 return res;
		 }
		//zyzycmsbs
		 if(StringTools.isBlank(zyzycmsbs)) {
			 res="是否专用作业车免税不能为空";
			 return res;
		 }
		 if(zyzycmsbs.length()>1) {
			 res="是否专用作业车免税不能大于1个字符";
			 return res;
		 }
		 if("0,1,".indexOf(zyzycmsbs+",")==-1 ) {
			 res="是否专用作业车免税只能在[0,1]中";
			 return res;
		 }
		 
		//zxzs
		 if(StringTools.isBlank(zxzs)) {
			 res="转向轴个数不能为空";
			 return res;
		 }
		 if(zxzs.length()>1) {
			 res="转向轴个数不能大于1个字符";
			 return res;
		 }
		//cddbj
		 if(StringTools.isBlank(cddbj)) {
			 res="纯电动标记不能为空";
			 return res;
		 }
		 if(cddbj.length()>1) {
			 res="纯电动标记不能大于1个字符";
			 return res;
		 }
		 if("1,2,".indexOf(cddbj+",")==-1 ) {
			 res="纯电动标记只能在[1,2]中";
			 return res;
		 }
		//dpcpno
		 if(!StringTools.isBlank(dpcpno)) {
			 if(dpcpno.length()>50) {
				 res="底盘产品公告号不能大于50个字符";
				 return res;
			 }
		 }
		//factorycode
		 if(StringTools.isBlank(factorycode)) {
			 res="工厂代码不能为空";
			 return res;
		 }
		 if(factorycode.trim().length()>20) {
			 res="工厂代码只能小于20个字符";
			 return res;
		 }
		 return res;

	}
	
	private String checkProductData(ProductData productData) {
		 String res="0";
		 String dph=productData.getDph();
		 String csys=productData.getCsys();
		 String fdjh=productData.getFdjh();
		 String scrq=productData.getScrq();
		 String bz=productData.getBz();
		 String qtxx=productData.getQtxx();
		 String vinbsyy=productData.getVinbsyy();
		 String hgzfl=productData.getHgzfl();
		 String scm=productData.getScm();
		 String isyclz=productData.getIsyclz();
		 String factorycode=productData.getFactorycode();
		 
		 if(StringTools.isBlank(dph )) {
			 res="底盘号不能为空";
			 return res;
		 }
		 if(dph.trim().length()!=8) {
			 res="底盘号长度只能为8个字符";
			 return res;
		 }
		 
		 if(StringTools.isBlank(csys )) {
			 res="车身颜色不能为空";
			 return res;
		 }
		 if(csys.trim().length()>70) {
			 res="车身颜色长度只能小于70个字符";
			 return res;
		 }
		 
		 
		 if(!StringTools.isBlank(fdjh)) {
			 if(fdjh.trim().length()>30) {
				 res="发动机号长度只能小于30个字符";
				 return res;
			 }
		 }
		 
		 
		 if(StringTools.isBlank(scrq)) {
			 res="生产日期不能为空";
			 return res;
		 }
		 if(scrq.trim().length()!=10) {
			 res="生产日期长度只能为10个字符，且格式应为yyyy-mm-dd";
			 return res;
		 }
		 if(!isDate(scrq)) {
			 res="生产日期长度只能为10个字符，且格式应为yyyy-mm-dd";
			 return res;
		 }
		 
		 
		 if(!StringTools.isBlank(bz)) {
			 if(bz.trim().length()>260) {
				 res="备注只能小于260个字符";
				 return res;
			 }
			 if(length(bz)>260) {
				 res="备注长度只能小于130个汉字或260个英文";
				 return res;
			 }
		 }
		 
		 if(!StringTools.isBlank(qtxx)) {
			 
			 if("VOYAH".equals(factorycode)) {
				 if(qtxx.length()>400) {
					 res="其他信息不能大于400个字符";
					 return res;
				 }
				 if(length(qtxx)>400) {
					 res="其他信息不能大于200个汉字或400个英文";
					 return res;
				 }
			 }else {
				 if(qtxx.trim().length()>100) {
					 res="其他信息只能小于100个字符";
					 return res;
				 }
			 }
			 
		 }
		 
		 if(StringTools.isBlank(vinbsyy)) {
			 res="VIN重新标示原因不能为空";
			 return res;
		 }
		 if(vinbsyy.trim().length()!=1) {
			 res="VIN重新标示原因只能为1个字符";
			 return res;
		 }
		 if(!"0".equals(vinbsyy.trim()) && !"1".equals(vinbsyy.trim()) 
				 &&!"2".equals(vinbsyy) &&!"3".equals(vinbsyy)  ) {
			 res="VIN重新标示原因只能在[0、1、2、3]中";
			 return res;
		 }
		 
		 if(StringTools.isBlank(hgzfl)) {
			 res="合格证分类不能为空";
			 return res;
		 }
		 if(hgzfl.length()!=1) {
			 res="合格证分类只能为1个字符";
			 return res;
		 }
		 if(!"0".equals(hgzfl) && !"1".equals(hgzfl)  ) {
			 res="合格证分类的值只能在[0、1]中";
			 return res;
		 }
		 
		 if(StringTools.isBlank(scm)) {
			 res="生产码不能为空";
			 return res;
		 }
		 if(scm.length()>100) {
			 res="生产码只能小于100个字符";
			 return res;
		 }
		 
		 if(StringTools.isBlank(isyclz)) {
			 res="是否一车两证不能为空";
			 return res;
		 }
		 if(isyclz.length()!=1) {
			 res="是否一车两证只能为1个字符";
			 return res;
		 }
		 if(!"0".equals(isyclz) && !"1".equals(isyclz)  ) {
			 res="是否一车两证的值只能在[0、1]中";
			 return res;
		 }
		 
		 if(StringTools.isBlank(factorycode)) {
			 res="工厂代码不能为空";
			 return res;
		 }
		 if(factorycode.length()>20) {
			 res="工厂代码只能小于20个字符";
			 return res;
		 }
		 
		 return res;

	}
	
	private String checkProductDataSwt(ProductDataSwt productData) {
		 String res="0";
		 String dph=productData.getDph();
		 String csys=productData.getCsys();
		 String fdjh=productData.getFdjh();
		 String scrq=productData.getScrq();
		 String bz=productData.getBz();
		 String qtxx=productData.getQtxx();
		 String vinbsyy=productData.getVinbsyy();
		 String hgzfl=productData.getHgzfl();
		 String scm=productData.getScm();
		 String isyclz=productData.getIsyclz();
		 String swtqymc=productData.getSwtqymc();
		 String swtqyscdz=productData.getSwtqyscdz();
		 String swtqytyshxydm=productData.getSwtqytyshxydm();
		 String factorycode=productData.getFactorycode();
		 
		 if(StringTools.isBlank(dph )) {
			 res="底盘号不能为空";
			 return res;
		 }
		 if(dph.trim().length()!=8) {
			 res="底盘号长度只能为8个字符";
			 return res;
		 }
		 
		 if(StringTools.isBlank(csys )) {
			 res="车身颜色不能为空";
			 return res;
		 }
		 if(csys.trim().length()>70) {
			 res="车身颜色长度只能小于70个字符";
			 return res;
		 }
		 
		 
		 if(!StringTools.isBlank(fdjh)) {
			 if(fdjh.trim().length()>30) {
				 res="发动机号长度只能小于30个字符";
				 return res;
			 }
		 }
		 
		 
		 if(StringTools.isBlank(scrq)) {
			 res="生产日期不能为空";
			 return res;
		 }
		 if(scrq.trim().length()!=10) {
			 res="生产日期长度只能为10个字符，且格式应为yyyy-mm-dd";
			 return res;
		 }
		 if(!isDate(scrq)) {
			 res="生产日期长度只能为10个字符，且格式应为yyyy-mm-dd";
			 return res;
		 }
		 
		 
		 if(!StringTools.isBlank(bz)) {
			 if(bz.trim().length()>260) {
				 res="备注只能小于260个字符";
				 return res;
			 }
			 if(length(bz)>260) {
				 res="备注长度只能小于130个汉字或260个英文";
				 return res;
			 }
		 }
		 
		 if(!StringTools.isBlank(qtxx)) {
			 
			 if("VOYAH".equals(factorycode)) {
				 if(qtxx.length()>400) {
					 res="其他信息不能大于400个字符";
					 return res;
				 }
				 if(length(qtxx)>400) {
					 res="其他信息不能大于200个汉字或400个英文";
					 return res;
				 }
			 }else {
				 if(qtxx.trim().length()>100) {
					 res="其他信息只能小于100个字符";
					 return res;
				 }
			 }
			 
		 }
		 
		 if(StringTools.isBlank(vinbsyy)) {
			 res="VIN重新标示原因不能为空";
			 return res;
		 }
		 if(vinbsyy.trim().length()!=1) {
			 res="VIN重新标示原因只能为1个字符";
			 return res;
		 }
		 if(!"0".equals(vinbsyy.trim()) && !"1".equals(vinbsyy.trim()) 
				 &&!"2".equals(vinbsyy) &&!"3".equals(vinbsyy)  ) {
			 res="VIN重新标示原因只能在[0、1、2、3]中";
			 return res;
		 }
		 
		 if(StringTools.isBlank(hgzfl)) {
			 res="合格证分类不能为空";
			 return res;
		 }
		 if(hgzfl.length()!=1) {
			 res="合格证分类只能为1个字符";
			 return res;
		 }
		 if(!"0".equals(hgzfl) && !"1".equals(hgzfl)  ) {
			 res="合格证分类的值只能在[0、1]中";
			 return res;
		 }
		 
		 if(StringTools.isBlank(scm)) {
			 res="生产码不能为空";
			 return res;
		 }
		 if(scm.length()>100) {
			 res="生产码只能小于100个字符";
			 return res;
		 }
		 
		 if(StringTools.isBlank(isyclz)) {
			 res="是否一车两证不能为空";
			 return res;
		 }
		 if(isyclz.length()!=1) {
			 res="是否一车两证只能为1个字符";
			 return res;
		 }
		 if(!"0".equals(isyclz) && !"1".equals(isyclz)  ) {
			 res="是否一车两证的值只能在[0、1]中";
			 return res;
		 }
		 
		 if(StringTools.isBlank(factorycode)) {
			 res="工厂代码不能为空";
			 return res;
		 }
		 if(factorycode.length()>20) {
			 res="工厂代码只能小于20个字符";
			 return res;
		 }
		 
		 if(!StringTools.isBlank(swtqymc)) {
			 if(swtqymc.trim().length()>100) {
				 res="受委托企业名称只能小于100个字符";
				 return res;
			 }
		 }
		 if(!StringTools.isBlank(swtqyscdz)) {
			 if(swtqyscdz.trim().length()>100) {
				 res="受委托企业生产地址只能小于100个字符";
				 return res;
			 }
		 }
		 if(!StringTools.isBlank(swtqytyshxydm)) {
			 if(swtqytyshxydm.trim().length()>100) {
				 res="受委托企业统一社会信用代码只能小于100个字符";
				 return res;
			 }
		 }

		 // 受委托企业名称、受委托企业生产地址、受委托企业统一社会信用代码需要同时为空或同时有值
		 if(
			  !(
			 	 (StringTools.isBlank(swtqymc)&StringTools.isBlank(swtqyscdz)&StringTools.isBlank(swtqytyshxydm))
				 ||
				(!StringTools.isBlank(swtqymc)&!StringTools.isBlank(swtqyscdz)&!StringTools.isBlank(swtqytyshxydm))
			   )
			){
			 res="受委托企业名称、受委托企业生产地址、受委托企业统一社会信用代码需要同时为空或同时有值";
			 return res;
		 }

		 
		 return res;

	}

	private String checkQueryHgzxx(QueryHgzxx info) {
		String res = "0";
		String dph = info.getDph();
		String factorycode = info.getFactorycode();

		if (StringTools.isBlank(dph)) {
			res = "底盘号不能为空";
			return res;
		}
		if (dph.trim().length() != 8) {
			res = "底盘号长度只能为8个字符";
			return res;
		}

		if (StringTools.isBlank(factorycode)) {
			res = "工厂代码不能为空";
			return res;
		}
		if (factorycode.length() > 20) {
			res = "工厂代码只能小于20个字符";
			return res;
		}

		return res;
	}
	
	private String checkQueryHgzWzxx(QueryHgzWzxx info) {
		String res = "0";
		String factorycode = info.getFactorycode();
		String hgzbh = info.getHgzbh();
		
		if (StringTools.isBlank(factorycode)) {
			res = "工厂代码不能为空";
			return res;
		}
		if (factorycode.length() > 20) {
			res = "工厂代码只能小于20个字符";
			return res;
		}
		
		if (StringTools.isBlank(hgzbh)) {
			res = "合格证编号不能为空";
			return res;
		}
		if (hgzbh.trim().length() != 15) {
			res = "合格证编号只能15个字符";
			return res;
		}
		
		return res;
	}
	
	
	
	public static boolean isValidDate(String str, String format) {
        if (format == null) {
            format = "yyyy-MM-dd HH:mm:ss";
        }
        boolean convertSuccess = true;
        // 指定日期格式
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        try {
            sdf.setLenient(false);
            sdf.parse(str);
            if (str.length() != format.length()) {
                convertSuccess = false;
            }
        } catch (ParseException e) {
            // 如果throw java.text.ParseException或者NullPointerException，就说明格式不对
            convertSuccess = false;
        }
        return convertSuccess;
    }
	
	public static boolean isDate(String str) {
		String path = "\\d{4}-\\d{2}-\\d{2}";// 定义匹配规则
		Pattern p = Pattern.compile(path);// 实例化Pattern
		Matcher m = p.matcher(str);// 验证字符串内容是否合法
		return m.matches();
	}
	
	public static boolean isDateYYYYMMDD(String str) {
		String path = "\\d{4}\\d{2}\\d{2}";// 定义匹配规则
		Pattern p = Pattern.compile(path);// 实例化Pattern
		Matcher m = p.matcher(str);// 验证字符串内容是否合法
		return m.matches();
	}
	
	/** 
     * 得到一个字符串的长度,显示的长度,一个汉字或日韩文长度为2,英文字符长度为1 
     * @param String s 需要得到长度的字符串 
     * @return int 得到的字符串长度 
     */ 
	public static int length(String s) {
        if (s == null)
            return 0;
        char[] c = s.toCharArray();
        int len = 0;
        for (int i = 0; i < c.length; i++) {
            len++;
            if (!isLetter(c[i])) {
                len++;
            }
        }
        return len;
    }
	
	public static boolean isLetter(char c) { 
        int k = 0x80; 
        return c / k == 0 ? true : false; 
    }



}
