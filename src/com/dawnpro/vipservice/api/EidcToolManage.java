package com.dawnpro.vipservice.api;

import java.util.HashMap;
import java.util.UUID;

import com.dawnpro.commons.SystemParam;
import com.dawnpro.core.log.Loggers;
import com.dawnpro.service.BaseServer;
import com.dawnpro.service.commons.HttpClientFactory;
import com.dawnpro.service.commons.HttpSSLUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import javax.crypto.Cipher;

import org.apache.http.client.HttpClient;

import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

/**
 * 装备中心VIP接口（车数保接口）
 * <AUTHOR>
 *
 */
public class  EidcToolManage  {
	

	/**
	 * 合格证交换状态查询接口
	 * @param hgzbh
	 * @return
	 */
	public EdicgetExchangeStatusRes getExchangeStatus(String hgzbh) {
		EdicgetExchangeStatusRes bResult = null;
		String vehicleCheckNum=hgzbh;
		String qUrl = SystemParam.getKey("eidc_getexchangestatus_url", "https://service.miit-eidc.org.cn/openapi/qualifiedCertificate/getExchangeStatus");
		String dwfw = SystemParam.getKey("eidc_dwfw", "dfmc");
		String account = SystemParam.getKey("eidc_account", "dongFengMotorCorporation");
		String privateKey = SystemParam.getKey("eidc_privatekey", "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAJfc9AYjWkm5EOZH6XJOiudQBI3Un78rWdRrd/WTTbo+w207AwBwrmVfoD4YXw1GcAjCwA0n5ST2+4F0Fa089MuS6AFyKloqIMyFoyJ63uwu4oYI4rsTERTVnp5Yg/dedqXW06v2zIA0JI1guZu4WfNiNPVjD9nUrNReywOeI/AhAgMBAAECgYAQCPybSVBXSHtIVX+I2lJAamqQr0rO3Lz3eQGJpKwoZkgsWHbQrRU9DwPUiq4+sJlT03yD5xqC9LFJ0gAX8r/GNXBIRHJD/8t14vwAXhDCk7LviUq1nKEXXWQjkCadJdS6aHFDXUd1aR2lbkAHEnMwhLN/+dsiGX49T70IvGFFgQJBAMfNr6TjMdIG5rWYDa7DIL4opezZGDUh2Bl6jWZ7ZEK0sDg2RlbC+q8/8C9MpVHGigxfZv7zsAzi6bevWoulsSUCQQDCk3MR2HRT+lEBZdCmxtUKtsvUCa0/8XUFtCkOEY2d6Gyympex/GZsNz0o79+FRXc/yPQAQjHlXx8cg9GQyohNAkEAqjD6PGmKNmzKeERc41Ayw8e8DlOd2yRI/ur1JyZT8L4YnMkegSj0f/LmlGOlLlL/pCpfgSvx+ggPLPehGPK6QQJAfkmFXgfHONo2yVlz20sh6xpJoQ3GzMHC2jjcjK0H5X19T07XNkZDk+kmYPOPd8hmZZfgBmrwR5c9chx8YCWT4QJAJ9th7MOTnVXG1va1+FTEPc+FoEljWMmSkcRjCGd667x4+wpxrHaL5jLGFXJp30jG4ox32PLdmFUUhy+b22DioQ==");
		
//		String qUrl = "https://service.miit-eidc.org.cn/openapi/qualifiedCertificate/getExchangeStatus";
//		qUrl="https://api.miit-eidc.com.cn/openapi/qualifiedCertificate/getExchangeStatus";
//		String dwfw =  "dfmc";
//		String account = "dongFengMotorCorporation";
//		String privateKey = "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAJfc9AYjWkm5EOZH6XJOiudQBI3Un78rWdRrd/WTTbo+w207AwBwrmVfoD4YXw1GcAjCwA0n5ST2+4F0Fa089MuS6AFyKloqIMyFoyJ63uwu4oYI4rsTERTVnp5Yg/dedqXW06v2zIA0JI1guZu4WfNiNPVjD9nUrNReywOeI/AhAgMBAAECgYAQCPybSVBXSHtIVX+I2lJAamqQr0rO3Lz3eQGJpKwoZkgsWHbQrRU9DwPUiq4+sJlT03yD5xqC9LFJ0gAX8r/GNXBIRHJD/8t14vwAXhDCk7LviUq1nKEXXWQjkCadJdS6aHFDXUd1aR2lbkAHEnMwhLN/+dsiGX49T70IvGFFgQJBAMfNr6TjMdIG5rWYDa7DIL4opezZGDUh2Bl6jWZ7ZEK0sDg2RlbC+q8/8C9MpVHGigxfZv7zsAzi6bevWoulsSUCQQDCk3MR2HRT+lEBZdCmxtUKtsvUCa0/8XUFtCkOEY2d6Gyympex/GZsNz0o79+FRXc/yPQAQjHlXx8cg9GQyohNAkEAqjD6PGmKNmzKeERc41Ayw8e8DlOd2yRI/ur1JyZT8L4YnMkegSj0f/LmlGOlLlL/pCpfgSvx+ggPLPehGPK6QQJAfkmFXgfHONo2yVlz20sh6xpJoQ3GzMHC2jjcjK0H5X19T07XNkZDk+kmYPOPd8hmZZfgBmrwR5c9chx8YCWT4QJAJ9th7MOTnVXG1va1+FTEPc+FoEljWMmSkcRjCGd667x4+wpxrHaL5jLGFXJp30jG4ox32PLdmFUUhy+b22DioQ==";
		
		
		String txnSn = UUID.randomUUID().toString().toLowerCase().replace("-", "");
		String sign = sign(account,txnSn,vehicleCheckNum,privateKey);
		
		HashMap headers = new HashMap();
		headers.put("dwfw", dwfw);
		headers.put("Content-Type", "application/json");
		
		EdicgetExchangeStatusQuery eQuery = new EdicgetExchangeStatusQuery();
		eQuery.setAccount(account);
		eQuery.setTxnSn(txnSn);
		eQuery.setSign(sign);
		eQuery.setVehicleCheckNum(vehicleCheckNum);
		
		ObjectMapper mapper = new ObjectMapper();
		String postData = null;
		try {
			postData = mapper.writeValueAsString(eQuery);
			Loggers.INTERFACE.debug("装备中心VIP-合格证交换状态查询接口，数据hgzbh["+vehicleCheckNum+"]，请求地址：["+qUrl+"]，发送报文："+postData);
			String result = HttpSSLUtil.doPostJson(qUrl, postData,headers,null);

			
//			String result = HttpClientFactory.doPostJson(qUrl, postData,headers,null);
			
			Loggers.INTERFACE.debug("装备中心VIP-合格证交换状态查询接口，数据hgzbh["+vehicleCheckNum+"]，接口反馈："+result);
			bResult=mapper.readValue(result,EdicgetExchangeStatusRes.class);
		} catch (JsonProcessingException e1) {
			Loggers.INTERFACE.debug("装备中心VIP-合格证交换状态查询接口：数据转换json报错");
			e1.printStackTrace();
			bResult.setCode("9998");
			bResult.setMsg("国家接口反馈数据转换json报错");
		}	catch (Exception e) {
			Loggers.INTERFACE.debug("装备中心VIP-合格证交换状态查询接口：报错："+e.toString());
			e.printStackTrace();
			bResult.setCode("9997");
			bResult.setMsg("国家接口请求报错");
		}
		
		return bResult;
	}
	
	/**
	 * 获取签名
	 * @param account
	 * @param txnSn
	 * @param vehicleCheckNum
	 * @param privateKey
	 * @return
	 */
	private String sign(String account, String txnSn, String vehicleCheckNum,String privateKey) {
		String message = account + txnSn + vehicleCheckNum;
		String sign = "";
		try {
			sign = privateKeyEncrypt(message, privateKey);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return sign;
	}

	
//	public static void main(String[] args) throws Exception {
//        // TODO 调用方加密
//        System.out.println("=======调用方签名加密=======》");
//        String account = "dongFengMotorCorporation";
//        String txnSn = "**************";  // 调用方自定义
//        String vin = "LVVDC21B6PD352027";
//        // 拼接 account + txnSn + vin，
//        // 然后对其进行 rsa 私钥签名，
//        // 最后 base64编码
//        String message = account + txnSn + vin;
//        String privateKey = "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAJfc9AYjWkm5EOZH6XJOiudQBI3Un78rWdRrd/WTTbo+w207AwBwrmVfoD4YXw1GcAjCwA0n5ST2+4F0Fa089MuS6AFyKloqIMyFoyJ63uwu4oYI4rsTERTVnp5Yg/dedqXW06v2zIA0JI1guZu4WfNiNPVjD9nUrNReywOeI/AhAgMBAAECgYAQCPybSVBXSHtIVX+I2lJAamqQr0rO3Lz3eQGJpKwoZkgsWHbQrRU9DwPUiq4+sJlT03yD5xqC9LFJ0gAX8r/GNXBIRHJD/8t14vwAXhDCk7LviUq1nKEXXWQjkCadJdS6aHFDXUd1aR2lbkAHEnMwhLN/+dsiGX49T70IvGFFgQJBAMfNr6TjMdIG5rWYDa7DIL4opezZGDUh2Bl6jWZ7ZEK0sDg2RlbC+q8/8C9MpVHGigxfZv7zsAzi6bevWoulsSUCQQDCk3MR2HRT+lEBZdCmxtUKtsvUCa0/8XUFtCkOEY2d6Gyympex/GZsNz0o79+FRXc/yPQAQjHlXx8cg9GQyohNAkEAqjD6PGmKNmzKeERc41Ayw8e8DlOd2yRI/ur1JyZT8L4YnMkegSj0f/LmlGOlLlL/pCpfgSvx+ggPLPehGPK6QQJAfkmFXgfHONo2yVlz20sh6xpJoQ3GzMHC2jjcjK0H5X19T07XNkZDk+kmYPOPd8hmZZfgBmrwR5c9chx8YCWT4QJAJ9th7MOTnVXG1va1+FTEPc+FoEljWMmSkcRjCGd667x4+wpxrHaL5jLGFXJp30jG4ox32PLdmFUUhy+b22DioQ==";
//        String sign = privateKeyEncrypt(message, privateKey);
//
//        // TODO 发送接口调用请求
//        System.out.println("========发送接口调用请求========》");
//        System.out.println(".............................");
//
//        // TODO 服务器方验签
//        System.out.println("========服务方验签========》");
//        String publicKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCX3PQGI1pJuRDmR+lyTornUASN1J+/K1nUa3f1k026PsNtOwMAcK5lX6A+GF8NRnAIwsANJ+Uk9vuBdBWtPPTLkugBcipaKiDMhaMiet7sLuKGCOK7ExEU1Z6eWIP3Xnal1tOr9syANCSNYLmbuFnzYjT1Yw/Z1KzUXssDniPwIQIDAQAB";
//        String signDe  = publicKeyDecrypt(sign, publicKey);
//        System.out.println("服务器方验签结果:"+message.equals(signDe));
//    }

    /**
     * RSA私钥加密
     */
    public static String privateKeyEncrypt(String str, String privateKey) throws Exception {
//        System.out.println("RSA私钥|privateKey:"+privateKey);
//        System.out.println("RSA私钥加密前的数据|str:"+str);
        //base64编码的公钥
        byte[] decoded = Base64.getDecoder().decode(privateKey);
        PrivateKey priKey = KeyFactory.getInstance("RSA").generatePrivate(new PKCS8EncodedKeySpec(decoded));
        //RSA加密
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.ENCRYPT_MODE, priKey);
        String outStr = Base64.getEncoder().encodeToString(cipher.doFinal(str.getBytes()));
//        System.out.println("RSA私钥加密后的数据|outStr:"+outStr);
        return outStr;
    }

    /**
     * RSA公钥解密
     */
    public static String publicKeyDecrypt(String str, String publicKey) throws Exception {
        System.out.println("RSA公钥|publicKey:"+publicKey);
        System.out.println("RSA公钥解密前的数据|str:"+str);
        //64位解码加密后的字符串
        byte[] inputByte = Base64.getDecoder().decode(str.getBytes("UTF-8"));
        //base64编码的私钥
        byte[] decoded = Base64.getDecoder().decode(publicKey);
        PublicKey pubKey =  KeyFactory.getInstance("RSA")
                .generatePublic(new X509EncodedKeySpec(decoded));
        //RSA解密
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.DECRYPT_MODE, pubKey);
        String outStr = new String(cipher.doFinal(inputByte));
        System.out.println("RSA公钥解密后的数据|outStr:"+outStr);
        return outStr;
    }
    
    public static void main(String[] args) {
		EidcToolManage eidcToolManage = new EidcToolManage();
		System.out.println(eidcToolManage.getExchangeStatus("WAC04ZYK0020264"));
	}
	
	
		
}
