package com.dawnpro.vipservice.api;

import com.fasterxml.jackson.annotation.JsonProperty;

public class EdicgetExchangeStatusRes {
	private String code;
	private	String msg;
	private String txnSn;
	private String vehicleCheckNum;
	
	@JsonProperty("data")
	private EdicgetExchangeStatusResData data;

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getMsg() {
		return msg;
	}

	public void setMsg(String msg) {
		this.msg = msg;
	}

	public String getTxnSn() {
		return txnSn;
	}

	public void setTxnSn(String txnSn) {
		this.txnSn = txnSn;
	}

	public String getVehicleCheckNum() {
		return vehicleCheckNum;
	}

	public void setVehicleCheckNum(String vehicleCheckNum) {
		this.vehicleCheckNum = vehicleCheckNum;
	}

	public EdicgetExchangeStatusResData getData() {
		return data;
	}

	public void setData(EdicgetExchangeStatusResData data) {
		this.data = data;
	}
	
	
	

}
