package com.dawnpro.vipservice;

import info.vidc.www.certificate.operation.CertificateInfo;
import info.vidc.www.certificate.operation.CertificateRequestVIPProxy;
import info.vidc.www.certificate.operation.NameValuePair;
import info.vidc.www.certificate.operation.OperateResult;

import java.rmi.RemoteException;
import java.sql.SQLException;
import java.sql.Types;
import java.text.*;
/*import java.text.ParseException;
import java.text.SimpleDateFormat;*/
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;

import com.dawnpro.commons.CommonDictionary;
import com.dawnpro.commons.SystemParam;
import com.dawnpro.commons.util.MapUtil;
import com.dawnpro.commons.util.StringTools;
import com.dawnpro.commons.util.db.DAOException;
import com.dawnpro.core.exception.AppException;
import com.dawnpro.core.log.Loggers;
import com.dawnpro.entity.HgzConfig;
import com.dawnpro.service.BaseServer;

/**
 * <p>Title:合格证上传处理类</p>
 * <p>Description: 合格证上传处理类</p>
 * <p>Copyright: Copyright dawnpro(c) 2010</p>
 * <p>Company: Dawnpro</p>
 * <AUTHOR>
 * @version 1.0
 * Created Date: 2010-6-15
 * Change History
 */
public class HgzVipUploadManage extends BaseServer {

	/**
	 * 上传合格证(自动上传)
	 */
	public void autoUploadHgz()
	{
		CertificateRequestVIPProxy service = new CertificateRequestVIPProxy();
		HgzConfig dflOnfig = getConfigData("东风汽车集团有限公司");

		Loggers.PERFORMANCE.info("东风汽车集团有限公司合格证上传开始:" + new GregorianCalendar().getTime());
		this.callService(service,dflOnfig,1,0);//新增上传
		this.callService(service,dflOnfig,4,0);//补传上传
		this.callService(service,dflOnfig,2,0);//修改上传
		this.callService(service,dflOnfig,5,0);//VIP修改上传
		this.callService(service,dflOnfig,3,0);//撤销上传
		Loggers.PERFORMANCE.info("东风汽车集团有限公司合格证上传结束:" + new GregorianCalendar().getTime());
	}

	/**
	 * 上传合格证(手动按上传类型)
	 * @param type 上传类别 1 新增/2 修改/3 撤销/4 补传/5 VIP修改
	 * @return
	 */
	public String uploadHgz(String type)
	{
		Loggers.PERFORMANCE.info("合格证上传开始:" + new GregorianCalendar().getTime());

		String result = "fail";
		CertificateRequestVIPProxy service = new CertificateRequestVIPProxy();
		HgzConfig dfl_onfig = getConfigData("东风汽车集团有限公司");
		this.callService(service,dfl_onfig,Integer.parseInt(type),0);
		result = "ok";
		Loggers.PERFORMANCE.info("合格证上传结束:" + new GregorianCalendar().getTime());
		return result;


	}

	/**
	 * 接口连接测试
	 * @return
	 */
	public String connHello() {
		Loggers.PERFORMANCE.info("connHello");
		String result = "fail";
		CertificateRequestVIPProxy service = new CertificateRequestVIPProxy();
		try {
			result = service.helloWorld();
		} catch (RemoteException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} 
		return result;
	}

	/**
	 * 调用上传接口服务
	 * @param service 接口服务
	 * @param hgzConfig 合格证配置信息
	 * @param type 上传类别 0 无/1 新增/2 修改/3 撤销/4 补传/5 VIP修改
	 */
	private void callService(CertificateRequestVIPProxy service,HgzConfig hgzConfig,int type,int qy) {
		CertificateInfo[] infos = new CertificateInfo[1];
		OperateResult result = null;
		Map map = null;
		String scyy = null;
		String hgzid = null;
		List dataList = getUploadData(type,qy);

		for (int i = 0; dataList != null && i < dataList.size(); i++) {
			map = (Map)dataList.get(i);
			infos[0] = (CertificateInfo)map.get("info");
			scyy = MapUtil.getMapValue(map, "scyy", "");
			hgzid = MapUtil.getMapValue(map, "hgz_id", "");
			try {
				switch (type) {
					case 1: //新增
						result = service.uploadInser_Ent(hgzConfig.getYcyh(), hgzConfig.getYcmm(), infos, hgzConfig.getUkey());
						break;
					case 2: //修改
						result = service.uploadUpdate_Ent(hgzConfig.getYcyh(), hgzConfig.getYcmm(), infos, scyy, hgzConfig.getUkey());
						break;
					case 3: //撤销
						result = service.uploadDelete_Ent(hgzConfig.getYcyh(), hgzConfig.getYcmm(), infos, scyy, hgzConfig.getUkey());
						break;
					case 4: //补传
						result = service.uploadOverTime_Ent(hgzConfig.getYcyh(), hgzConfig.getYcmm(), infos, scyy, hgzConfig.getUkey());
						break;
					case 5: //VIP修改
						result = service.uploadUpdate_EntEX(hgzConfig.getYcyh(), hgzConfig.getYcmm(), infos, scyy, hgzConfig.getUkey());
						break;
					default:
				}
				if (result == null) {
					Loggers.PERFORMANCE.info(hgzid + "," + hgzConfig.getYcyh() + "," + hgzConfig.getYcmm() + "," + hgzConfig.getUkey() + "-----null----");
				}
				else {
					//处理返回结果
					accessBackResult(hgzid, type, result);
				}
			} catch (RemoteException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
				Loggers.PERFORMANCE.info("合格证ID:"+hgzid+",上传异常："+e.getMessage());
			
			} 
		}

	}

	/**
	 * 获取上传用配置信息
	 * @return
	 */
	private HgzConfig getConfigData(String qymc) {
		HgzConfig hgzConfig = new HgzConfig();
		String ukey = null;
		String qydm = null;
		if ("东风汽车集团有限公司".equals(qymc)) {
			ukey = SystemParam.getKey("Upload_UKey_DFM", "");
			qydm = SystemParam.getKey("Upload_QYDM_DFM", "WAC0");
		}
		hgzConfig.setUkey(ukey);
		hgzConfig.setQydm(qydm);
		StringBuffer sqlBuffer = new StringBuffer("select ycyh,ycmm from tbl_qymc where qymc='").append(qymc).append("' and deleteflag='0' limit 1");
		try {
			List tempList = this.dao.queryForList(sqlBuffer.toString());
			if (tempList != null && tempList.size() > 0) {
				Map map = (Map)tempList.get(0);
				hgzConfig.setYcyh(MapUtil.getMapValue(map, "ycyh", ""));
				hgzConfig.setYcmm(MapUtil.getMapValue(map, "ycmm", ""));
			}
		} catch (DAOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return hgzConfig;
	}

	/**
	 * 获取待上传的数据
	 * @param type 上传类别 1 新增/2 修改/3 撤销/4 补传/5 VIP修改
	 * @return List 待上传的数据集
	 */
	public List getUploadData(int type, int qy) {
		List resList = new ArrayList();
		Map tempMap = null;
		Map map = null;
		StringBuffer sqlBuffer = new StringBuffer();
		/*VINBSYY：VIN重新标示或变更   2020年1月1日起必须赋值（“0”表示VIN未重新标示或变更，“1”表示VIN因打刻设备故障等原因重新标示，“2”表示VIN因校验位计算错误变更，“3”表示VIN因其他原因变更）徐姐要求默认为0
		* ISCXNF	是否使用车型年份	2020年1月1日起必须赋值（"是"或"否"） 徐姐要求默认为否*/
		try {
			List tempList = null;
			sqlBuffer.append("select t.JYW VERCODE,t.CDDBJ, t.MLLb CLLX,t.CLSCDWMC,(case when t1.hgzfl=0 then 'QX' else 'DP' end) CLZTXX, ")
					.append(" t.DYWYM,QYID,t.YH,(case when '1'= t.jsgc then '2' when '1'= t.mzxny then '1' else '' end ) ZCHGZBH,t.ZXZS,'' ZZBH,t.BGANZDZL BGCAZZDYXZZL,t.BZ,'' CJH,t.CLMC,t.PP CLPP, ")
					.append(" t.VNM CLSBDH,t.CLXH,t.QYMC CLZZQYMC,date_format(t.SCRQ,'%Y-%m-%d %H:%i:%s') CLZZRQ,t.CPNO CPH,t.SCDZ CPSCDZ,t.CSYS,t.DPHGZBH,t.DPID, ")
					.append(" t.DPXH,t.EDZK,t.EDZL EDZZL,t.FDJH,t.FDJXH,date_format(t.FZRQ,'%Y-%m-%d %H:%i:%s') FZRQ,t.THPS GBTHPS,date_format(t.SXRQ,'%Y-%m-%d %H:%i:%s') GGSXRQ,t.GL,t.HLJ,t.HXC HXNBC, ")
					.append(" t.HXG HXNBG,t.HXK HXNBK,t.HZDCZFS,t.HZDFS,t.JSSZCRS,t.LTGG,t.LTS,t.PH PC,t.PFBZ,t.PL,t.QLJ,t.QYBZ, ")
					.append(" t.QTXX QYQTXX,t.QZDCZFS,t.QZDFS,t.RYZL RLZL,t.WKC,t.WKG,t.WKK,t.ZBZL,t.ZGCS,t.ZH,t.ZJ,t.ZQYZL ZQYZZL, ")
					.append(" t.ZS,t.ZXXS,t.ZZL,t.ZZLXS ZZLLYXS,t.HGZBH WZHGZBH,t1.HGZ_ID HGZID,t.PZXLH,t.DPH,t.VINBSYY,t.ISCXNF,t.ZYZYCMSBS,t.JFPZID,");
				switch (type) {
				case 1:
					sqlBuffer.append("t1.MEMO from tbl_hgzpara t inner join ")
							.append(" (select a.HGZ_ID,b.MEMO,a.hgzfl from tbl_hgzmain a inner join tbl_businesstype b on b.deleteflag='0' ")
							.append(" and b.business_type1<>3 and a.business_type3=b.id where (a.cycle_status=20 or a.cycle_status=17) and a.upload_status = 10 ")
							.append(" and a.upload_type in (0,1)) t1 on t.hgz_id=t1.hgz_id where t.printversion='V3.0'");
					break;
				case 2:
					sqlBuffer.append("ifnull(t1.MEMO,'修改上传参数') memo from tbl_hgzpara t inner join ")
							.append(" (select a.HGZ_ID,b.MEMO,a.hgzfl from tbl_hgzmain a inner join tbl_businesstype b on b.deleteflag='0' ")
							.append(" and b.business_type1<>3 and a.business_type3=b.id where (a.cycle_status=20 or a.cycle_status=17) and a.upload_status=10 ")
							.append(" and a.upload_type=2) t1 on t.hgz_id=t1.hgz_id where t.printversion='V3.0'  ");
					break;
				case 3:
					sqlBuffer.append("ifnull(t1.MEMO,'更换新合格证') memo from tbl_hgzpara t inner join ")
							.append(" (select a.HGZ_ID,b.MEMO,a.hgzfl from tbl_hgzmain a inner join tbl_businesstype b on b.deleteflag='0' ")
							.append(" and b.business_type1<>3 and a.NULLIFYBUSINESS_TYPE=b.id where a.cycle_status=30 and a.upload_status=10 ")
							.append(" and a.upload_type=3) t1 on t.hgz_id=t1.hgz_id ");
					break;
				case 4:
					sqlBuffer.append("'超时补传' MEMO from tbl_hgzpara t inner join ")
							.append(" (select a.HGZ_ID,b.MEMO,a.hgzfl from tbl_hgzmain a inner join tbl_businesstype b on b.deleteflag='0' ")
							.append(" and b.business_type1<>3 and a.business_type3=b.id where (a.cycle_status=20 or a.cycle_status=17 ) and a.upload_status=10 ")
							.append(" and a.upload_type in (4)) t1 on t.hgz_id=t1.hgz_id where t.printversion='V3.0' ")
							.append("  and to_days(now())-to_days(t.fzrq)>2 ");
					break;
				case 5:
					sqlBuffer.append("ifnull(t1.MEMO,'修改上传参数') memo from tbl_hgzpara t inner join ")
							.append(" (select a.HGZ_ID,b.MEMO,a.hgzfl from tbl_hgzmain a inner join tbl_businesstype b on b.deleteflag='0' ")
							.append(" and b.business_type1<>3 and a.business_type3=b.id where (a.cycle_status=20  or a.cycle_status=17) and a.upload_status=10 ")
							.append(" and a.upload_type=5) t1 on t.hgz_id=t1.hgz_id where t.printversion='V3.0' ");
					break;
				default:
					break;
			}
			/**免征新能源机减税挂车不能同时为1**/
			sqlBuffer.append(" and not EXISTS (select 1 from tbl_hgzpara p where p.hgz_id=t.hgz_id and p.jsgc='1' and p.mzxny='1') ");
			tempList = this.dao.queryForList(sqlBuffer.toString());

			Map zc_map = CommonDictionary.getDictionary("HGZ_MLLB_ZC");
			Map dp_map = CommonDictionary.getDictionary("HGZ_MLLB_DP");

			for (int i = 0; tempList != null && i < tempList.size(); i++) {
				map = (Map)tempList.get(i);
				CertificateInfo info = new CertificateInfo();
				info.setVERCODE(MapUtil.getMapValue(map, "VERCODE", ""));
				info.setCDDBJ(MapUtil.getMapValue(map, "CDDBJ", ""));
				if (MapUtil.getMapValue(map, "CLZTXX", "") != null && "QX".equals(MapUtil.getMapValue(map, "CLZTXX", ""))) {
					info.setCLLX(MapUtil.getMapValue(zc_map, MapUtil.getMapValue(map, "CLLX", ""),""));
				}
				else if (MapUtil.getMapValue(map, "CLZTXX", "") != null && "DP".equals(MapUtil.getMapValue(map, "CLZTXX", ""))) {
					info.setCLLX(MapUtil.getMapValue(dp_map, MapUtil.getMapValue(map, "CLLX", ""),""));
				}
				info.setCLSCDWMC(MapUtil.getMapValue(map, "CLSCDWMC", ""));
				info.setCLZTXX(MapUtil.getMapValue(map, "CLZTXX", ""));
				info.setDYWYM(MapUtil.getMapValue(map, "DYWYM", ""));
				info.setQYID(MapUtil.getMapValue(map, "QYID", ""));
				info.setYH(MapUtil.getMapValue(map, "YH", ""));
				info.setZCHGZBH(MapUtil.getMapValue(map, "ZCHGZBH", ""));
				info.setZXZS(MapUtil.getMapValue(map, "ZXZS", ""));
				info.setZZBH(MapUtil.getMapValue(map, "ZZBH", ""));
				info.setBGCAZZDYXZZL(MapUtil.getMapValue(map, "BGCAZZDYXZZL", ""));
				info.setBZ(MapUtil.getMapValue(map, "BZ", ""));
				info.setCJH(MapUtil.getMapValue(map, "CJH", ""));
				info.setCLMC(MapUtil.getMapValue(map, "CLMC", ""));
				info.setCLPP(MapUtil.getMapValue(map, "CLPP", ""));
				info.setCLSBDH(MapUtil.getMapValue(map, "CLSBDH", ""));
				info.setCLXH(MapUtil.getMapValue(map, "CLXH", ""));
				info.setCLZZQYMC(MapUtil.getMapValue(map, "CLZZQYMC", ""));
				if(!StringTools.isBlank(MapUtil.getMapValue(map, "GGSXRQ", "")) && MapUtil.getMapValue(map, "GGSXRQ", "").length() >= 19) {
					SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
					format.parse(MapUtil.getMapValue(map, "GGSXRQ", "").substring(0, 10));
					Calendar calendar = format.getCalendar();
					//说明：时区需设置为GMT；时分秒必须进行设置，否则GregorianCalendar自动进行初始化会出现小时时区自动转换问题。
					//时分秒：可以使用SQL内容固定，或java赋值指定（本次使用java赋值）
					//注意，axis中的序列化时区已被设置为+08:00，以便和装备中心保持一致。
					GregorianCalendar nowGregorianCalendar =new GregorianCalendar(TimeZone.getTimeZone("GMT"));//设置时区
					nowGregorianCalendar.set(Calendar.YEAR, calendar.get(Calendar.YEAR));
					nowGregorianCalendar.set(Calendar.MONTH, calendar.get(Calendar.MONTH));
					nowGregorianCalendar.set(Calendar.DATE, calendar.get(Calendar.DATE));
					nowGregorianCalendar.set(Calendar.HOUR_OF_DAY,0);
					nowGregorianCalendar.set(Calendar.MINUTE,0);
					nowGregorianCalendar.set(Calendar.SECOND, 1);//设置时间为 0:0:1
					info.setGGSXRQ(nowGregorianCalendar);
				}
				else {
					if (type == 3) {
						info.setGGSXRQ(new GregorianCalendar());
					}
				}
				if(!StringTools.isBlank(MapUtil.getMapValue(map, "CLZZRQ", "")) && MapUtil.getMapValue(map, "CLZZRQ", "").length() >= 19) {
					SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
					format.parse(MapUtil.getMapValue(map, "CLZZRQ", "").substring(0, 10));
					Calendar calendar = format.getCalendar();
					//说明：时区需设置为GMT；时分秒必须进行设置，否则GregorianCalendar自动进行初始化会出现小时时区自动转换问题。
					//时分秒：可以使用SQL内容固定，或java赋值指定（本次使用java赋值）
					GregorianCalendar nowGregorianCalendar =new GregorianCalendar(TimeZone.getTimeZone("GMT"));//设置时区
					nowGregorianCalendar.set(Calendar.YEAR, calendar.get(Calendar.YEAR));
					nowGregorianCalendar.set(Calendar.MONTH, calendar.get(Calendar.MONTH));
					nowGregorianCalendar.set(Calendar.DATE, calendar.get(Calendar.DATE));
					nowGregorianCalendar.set(Calendar.HOUR_OF_DAY,0);
					nowGregorianCalendar.set(Calendar.MINUTE,0);
					nowGregorianCalendar.set(Calendar.SECOND, 1);//设置时间为 0:0:1
					info.setCLZZRQ(nowGregorianCalendar);
				}
				else {
					if (type == 3) {
						info.setCLZZRQ(new GregorianCalendar());
					}
				}
				if(!StringTools.isBlank(MapUtil.getMapValue(map, "FZRQ", "")) && MapUtil.getMapValue(map, "FZRQ", "").length() >= 19) {
					SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
					format.parse(MapUtil.getMapValue(map, "FZRQ", "").substring(0, 10));
					Calendar calendar = format.getCalendar();
					//说明：时区需设置为GMT；时分秒必须进行设置，否则GregorianCalendar自动进行初始化会出现小时时区自动转换问题。
					//时分秒：可以使用SQL内容固定，或java赋值指定（本次使用java赋值）
					GregorianCalendar nowGregorianCalendar =new GregorianCalendar(TimeZone.getTimeZone("GMT"));//设置时区
					nowGregorianCalendar.set(Calendar.YEAR, calendar.get(Calendar.YEAR));
					nowGregorianCalendar.set(Calendar.MONTH, calendar.get(Calendar.MONTH));
					nowGregorianCalendar.set(Calendar.DATE, calendar.get(Calendar.DATE));
					nowGregorianCalendar.set(Calendar.HOUR_OF_DAY,0);
					nowGregorianCalendar.set(Calendar.MINUTE,0);
					nowGregorianCalendar.set(Calendar.SECOND, 1);//设置时间为 0:0:1
					info.setFZRQ(nowGregorianCalendar);
				}
				else {
					if (type == 3) {
						info.setFZRQ(new GregorianCalendar());
					}
				}

				info.setCPH(MapUtil.getMapValue(map, "CPH", ""));
				info.setCPSCDZ(MapUtil.getMapValue(map, "CPSCDZ", ""));
				info.setCSYS(MapUtil.getMapValue(map, "CSYS", ""));
				info.setDPHGZBH(MapUtil.getMapValue(map, "DPHGZBH", ""));
				info.setDPID(MapUtil.getMapValue(map, "DPID", ""));
				info.setDPXH(MapUtil.getMapValue(map, "DPXH", ""));
				info.setEDZK(MapUtil.getMapValue(map, "EDZK", ""));
				info.setEDZZL(MapUtil.getMapValue(map, "EDZZL", ""));
				info.setFDJH(MapUtil.getMapValue(map, "FDJH", ""));
				info.setFDJXH(MapUtil.getMapValue(map, "FDJXH", ""));
				info.setGBTHPS(MapUtil.getMapValue(map, "GBTHPS", ""));
				info.setGL(MapUtil.getMapValue(map, "GL", ""));
				info.setHLJ(MapUtil.getMapValue(map, "HLJ", ""));
				info.setHXNBC(MapUtil.getMapValue(map, "HXNBC", ""));
				info.setHXNBG(MapUtil.getMapValue(map, "HXNBG", ""));
				info.setHXNBK(MapUtil.getMapValue(map, "HXNBK", ""));
				info.setHZDCZFS(MapUtil.getMapValue(map, "HZDCZFS", ""));
				info.setHZDFS(MapUtil.getMapValue(map, "HZDFS", ""));
				info.setJSSZCRS(MapUtil.getMapValue(map, "JSSZCRS", ""));
				info.setLTGG(MapUtil.getMapValue(map, "LTGG", ""));
				info.setLTS(MapUtil.getMapValue(map, "LTS", ""));
				info.setPC(MapUtil.getMapValue(map, "PC", ""));
				info.setPFBZ(MapUtil.getMapValue(map, "PFBZ", ""));
				info.setPL(MapUtil.getMapValue(map, "PL", ""));
				info.setQLJ(MapUtil.getMapValue(map, "QLJ", "", ""));
				info.setQYBZ(MapUtil.getMapValue(map, "QYBZ", ""));
				info.setQYQTXX(MapUtil.getMapValue(map, "QYQTXX", ""));
				info.setQZDCZFS(MapUtil.getMapValue(map, "QZDCZFS", ""));
				info.setQZDFS(MapUtil.getMapValue(map, "QZDFS", ""));
				info.setRLZL(MapUtil.getMapValue(map, "RLZL", ""));
				info.setWKC(MapUtil.getMapValue(map, "WKC", ""));
				info.setWKG(MapUtil.getMapValue(map, "WKG", ""));
				info.setWKK(MapUtil.getMapValue(map, "WKK", ""));
				info.setZBZL(MapUtil.getMapValue(map, "ZBZL", ""));
				info.setZGCS(MapUtil.getMapValue(map, "ZGCS", ""));
				info.setZH(MapUtil.getMapValue(map, "ZH", ""));
				info.setZJ(MapUtil.getMapValue(map, "ZJ", ""));
				info.setZQYZZL(MapUtil.getMapValue(map, "ZQYZZL", ""));
				info.setZS(MapUtil.getMapValue(map, "ZS", ""));
				info.setZXXS(MapUtil.getMapValue(map, "ZXXS", ""));
				info.setZZL(MapUtil.getMapValue(map, "ZZL", ""));
				info.setZZLLYXS(MapUtil.getMapValue(map, "ZZLLYXS", ""));
				info.setWZHGZBH(MapUtil.getMapValue(map, "WZHGZBH", ""));
				info.setVINBSYY(MapUtil.getMapValue(map, "VINBSYY", ""));
				info.setISCXNF(MapUtil.getMapValue(map, "ISCXNF", ""));
				info.setZYZYCMSBS(MapUtil.getMapValue(map, "ZYZYCMSBS", ""));
				info.setJFPZID(MapUtil.getMapValue(map, "JFPZID", ""));
				//CZRQ打印日期设置为比FZRQ大10秒，解决0-8点上传数据国家反馈【发证日期不能打印打印日期】的问题
				if(!StringTools.isBlank(MapUtil.getMapValue(map, "FZRQ", "")) && MapUtil.getMapValue(map, "FZRQ", "").length() >= 19) {
					SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
					format.parse(MapUtil.getMapValue(map, "FZRQ", "").substring(0, 10));
					Calendar calendar = format.getCalendar();
					GregorianCalendar nowGregorianCalendar =new GregorianCalendar(TimeZone.getTimeZone("GMT"));//设置时区
					nowGregorianCalendar.set(Calendar.YEAR, calendar.get(Calendar.YEAR));
					nowGregorianCalendar.set(Calendar.MONTH, calendar.get(Calendar.MONTH));
					nowGregorianCalendar.set(Calendar.DATE, calendar.get(Calendar.DATE));
					nowGregorianCalendar.set(Calendar.HOUR_OF_DAY,0);
					nowGregorianCalendar.set(Calendar.MINUTE,0);
					nowGregorianCalendar.set(Calendar.SECOND, 10);//设置时间为 0:0:10
					info.setCZRQ(nowGregorianCalendar);
				}
				//设置CREATETIME与CZRQ一致
				if(!StringTools.isBlank(MapUtil.getMapValue(map, "FZRQ", "")) && MapUtil.getMapValue(map, "FZRQ", "").length() >= 19) {
					SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
					format.parse(MapUtil.getMapValue(map, "FZRQ", "").substring(0, 10));
					Calendar calendar = format.getCalendar();
					GregorianCalendar nowGregorianCalendar =new GregorianCalendar(TimeZone.getTimeZone("GMT"));//设置时区
					nowGregorianCalendar.set(Calendar.YEAR, calendar.get(Calendar.YEAR));
					nowGregorianCalendar.set(Calendar.MONTH, calendar.get(Calendar.MONTH));
					nowGregorianCalendar.set(Calendar.DATE, calendar.get(Calendar.DATE));
					nowGregorianCalendar.set(Calendar.HOUR_OF_DAY,0);
					nowGregorianCalendar.set(Calendar.MINUTE,0);
					nowGregorianCalendar.set(Calendar.SECOND, 10);//设置时间为 0:0:2
					info.setCREATETIME(nowGregorianCalendar);
				}
				//设置UPDATETIME与CZRQ一致
				if(!StringTools.isBlank(MapUtil.getMapValue(map, "FZRQ", "")) && MapUtil.getMapValue(map, "FZRQ", "").length() >= 19) {
					SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
					format.parse(MapUtil.getMapValue(map, "FZRQ", "").substring(0, 10));
					Calendar calendar = format.getCalendar();
					GregorianCalendar nowGregorianCalendar =new GregorianCalendar(TimeZone.getTimeZone("GMT"));//设置时区
					nowGregorianCalendar.set(Calendar.YEAR, calendar.get(Calendar.YEAR));
					nowGregorianCalendar.set(Calendar.MONTH, calendar.get(Calendar.MONTH));
					nowGregorianCalendar.set(Calendar.DATE, calendar.get(Calendar.DATE));
					nowGregorianCalendar.set(Calendar.HOUR_OF_DAY,0);
					nowGregorianCalendar.set(Calendar.MINUTE,0);
					nowGregorianCalendar.set(Calendar.SECOND, 10);//设置时间为 0:0:2
					info.setUPDATETIME(nowGregorianCalendar);
				}
				//设置FEEDBACK_TIME与CZRQ一致
				if(!StringTools.isBlank(MapUtil.getMapValue(map, "FZRQ", "")) && MapUtil.getMapValue(map, "FZRQ", "").length() >= 19) {
					SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
					format.parse(MapUtil.getMapValue(map, "FZRQ", "").substring(0, 10));
					Calendar calendar = format.getCalendar();
					GregorianCalendar nowGregorianCalendar =new GregorianCalendar(TimeZone.getTimeZone("GMT"));//设置时区
					nowGregorianCalendar.set(Calendar.YEAR, calendar.get(Calendar.YEAR));
					nowGregorianCalendar.set(Calendar.MONTH, calendar.get(Calendar.MONTH));
					nowGregorianCalendar.set(Calendar.DATE, calendar.get(Calendar.DATE));
					nowGregorianCalendar.set(Calendar.HOUR_OF_DAY,0);
					nowGregorianCalendar.set(Calendar.MINUTE,0);
					nowGregorianCalendar.set(Calendar.SECOND, 10);//设置时间为 0:0:2
					info.setFEEDBACK_TIME(nowGregorianCalendar);
				}

				info.setPZXLH(MapUtil.getMapValue(map, "PZXLH", ""));

				tempMap = new HashMap();
				tempMap.put("info", info);
				tempMap.put("hgz_id", MapUtil.getMapValue(map, "HGZID", ""));
				tempMap.put("scyy", MapUtil.getMapValue(map, "MEMO", ""));

				if (MapUtil.getMapValue(map, "CLZTXX", "") == null || !"QX".equals(MapUtil.getMapValue(map, "CLZTXX", ""))
						|| !getWscDpHgz(MapUtil.getMapValue(map, "DPH", ""))) {
					resList.add(tempMap);
				}
			}

		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (ParseException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (DAOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return resList;
	}

	/**
	 * 整车上传时，检查是否存在对应底盘号的未上传底盘证
	 * @param dph
	 * @return
	 */
	public boolean getWscDpHgz(String dph) {
		boolean res = false;
		String sql = "select 1 from tbl_hgzmain m,tbl_hgzpara p where m.hgz_id=p.hgz_id and m.hgzfl=1 and m.cycle_status<30 and m.upload_status<=30 and p.dph='" + dph + "'";
		try {
			if (this.dao.executeToInt(sql) > 0) {
				res = true;
			}
		} catch (DAOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return res;
	}

	/**
	 * 根据上传返回结果处理合格证
	 * @param hgzid 合格证ID
	 * @param type 上传类别 1 新增/2 修改/3 撤销/4 补传/5 VIP修改
	 * @param result 上传返回结果
	 */
	private void accessBackResult(String hgzid,int type,OperateResult result) {
		NameValuePair valuePair = null;
		NameValuePair[] values = null;
		String upres = "1";
		String upval = null;
		if (result != null) {
			upres = String.valueOf(result.getResultCode());
			values = result.getResultDetail();
			//202011.11 zhangym  values如果为空则标识失败upres = "1"; values=‘’国家连接超时，上传失败，待系统重新上传‘
			if (StringTools.isBlank(upres)) {
				upres = "1";
			}
			if (values != null && values.length > 0) {
				valuePair = values[0];
				upval = valuePair.getValue();
			}
			else {
				upval = "0".equals(upres) ? "国家超时连接，上传失败，待重传" : "上传失败";
				upres = "1";
			}
			Loggers.PERFORMANCE.info("合格证ID：" + hgzid + "; 上传类型：" + type + "; 上传结果：" + upres + "-" + upval);

			try {
				List tempList = null;
				List paramlist = new ArrayList();
				int[] rt = {Types.VARCHAR};
				String[] paramtype = {"String","String","String","String"};
				paramlist.add(upres);
				paramlist.add(hgzid);
				paramlist.add(upval);
				paramlist.add(String.valueOf(type));
				tempList = this.dao.excuteprc_new("proc_uploadhgz_refer",paramtype, paramlist,rt);
				if (tempList == null || tempList.size() <= 0 || "0".equals(tempList.get(0))) {
					Loggers.PERFORMANCE.info("处理上传返回结果的存储过程执行失败:proc_uploadhgz_refer"+paramlist);
				}
			} catch (AppException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
	}
	
//	public static void main(String[] args) throws Exception {
//        SSLContext context = SSLContext.getInstance("TLS");
//        context.init(null, null, null);
//
//        SSLSocketFactory factory = (SSLSocketFactory) context.getSocketFactory();
//        SSLSocket socket = (SSLSocket) factory.createSocket();
//
//        String[] protocols = socket.getSupportedProtocols();
//
//        System.out.println("Supported Protocols: " + protocols.length);
//        for (int i = 0; i < protocols.length; i++) {
//            System.out.println(" " + protocols[i]);
//        }
//
//        protocols = socket.getEnabledProtocols();
//
//        System.out.println("Enabled Protocols: " + protocols.length);
//        for (int i = 0; i < protocols.length; i++) {
//            System.out.println(" " + protocols[i]);
//        }
//
//    }
}
