package com.dawnpro.vipservice;

import java.io.File;
import java.rmi.RemoteException;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import cn.org.vecc_mep.web1.WSXxgkVin.WSXxgkVinSoapProxy;

import net.sf.jasperreports.engine.JRException;
import net.sf.jasperreports.engine.JRRuntimeException;
import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.JasperReport;
import net.sf.jasperreports.engine.util.JRLoader;

import com.dawnpro.commons.SystemParam;
import com.dawnpro.commons.util.MapUtil;
import com.dawnpro.commons.util.StringTools;
import com.dawnpro.commons.util.db.DAOException;
import com.dawnpro.commons.util.db.TransactionException;
import com.dawnpro.core.log.Loggers;
import com.dawnpro.service.BaseServer;
import com.dawnpro.service.commons.FTPUtil;
import com.dawnpro.service.commons.PDFCreateMange;
import com.dawnpro.service.commons.QrCode14;

/**
 * 国家环保VIN码上传管理
 * <AUTHOR>
 * @version 1.0
 * 
 */
public class MepgkUploadManage extends BaseServer {
	
	
	private WSXxgkVinSoapProxy xxgkSoap = null;
	
	public void setXxgkSoap(WSXxgkVinSoapProxy xxgkSoap) {
		this.xxgkSoap = xxgkSoap;
	}
	
	public String forRes(String res) {
		String result = "fail";
		if (!StringTools.isBlank(res) && res.indexOf("<succeed>") >= 0 && res.indexOf("</succeed>") >= 9) { 
			res = res.substring(res.indexOf("<succeed>")+9,res.indexOf("</succeed>"));
			if (!StringTools.isBlank(res) && "true".equals(res.trim())) {
				result = "ok";
			}
		}
		return result;
	}

	/**
	 * 环保随车清单上传登录
	 * @param factory
	 * @return
	 */
	public String mepgkLogin(String factory) {
		String result = "fail";	
		try {
			List tempList = this.dao.queryForList("select qybh,password from tbl_up_account where deleteflag='0' and uptype='mepgkvin' and factorycode='" + factory + "'");
			if (tempList != null && tempList.size() > 0) {
				String qybh = MapUtil.getMapValue((Map)tempList.get(0), "qybh", "");
				String password = MapUtil.getMapValue((Map)tempList.get(0), "password", "");
				String res = xxgkSoap.login(qybh, password);
				if (!StringTools.isBlank(res) && "ok".equals(forRes(res))) {
					result = res.substring(res.indexOf("<data>") + 6,res.indexOf("</data>"));
				}
			}
		} catch (RemoteException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (DAOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} 
		return result;
	}
	
	/**
	 * 环保随车清单上传登出
	 * @param key
	 * @return
	 */
	public String mepgkLogout(String key) {
		String result = "fail";
		try {
			result = forRes(xxgkSoap.logout(key));
		} catch (RemoteException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return result;
	}
	
	/**
	 * 环保随车清单上传国家环保
	 * @param userid
	 * @param factory
	 * @return
	 */
	public String sendMepgkData(List resList,String factory,String userid) {
		String result = "fail";
		if (StringTools.isBlank(factory)) {
			factory = SystemParam.getKey("coc_bbbm");
		}
		if (StringTools.isBlank(userid)) {
			userid = "104";
		}
		
		List sqlList = new ArrayList();
		Loggers.PERFORMANCE.info("[环保随车清单数据上传] 开始上传");
		for (int i = 0; resList != null && i < resList.size(); i++) {
			String key = mepgkLogin(factory);
			if (!StringTools.isBlank(key) && !"fail".equals(key.trim())) {
				Map map = (Map)resList.get(i);
				StringBuffer data = new StringBuffer();
				data.append("<vindatas><vindata><vin>").append(MapUtil.getMapValue(map, "vin", "")).append("</vin>");
				data.append("<xxgkh>").append(MapUtil.getMapValue(map, "xxgkh", "")).append("</xxgkh>");
				data.append("<sb>").append(MapUtil.getMapValue(map, "sb", "")).append("</sb>");
				data.append("<sccdz>").append(MapUtil.getMapValue(map, "sccdz", "")).append("</sccdz>");
				data.append("<fdjh>").append(MapUtil.getMapValue(map, "fdjh", "")).append("</fdjh>");
				data.append("<scdate>").append(MapUtil.getMapValue(map, "scdate", "")).append("</scdate>");
				data.append("<ccdate>").append(MapUtil.getMapValue(map, "ccdate", "")).append("</ccdate>");
				data.append("<ccsy>").append(MapUtil.getMapValue(map, "ccsy", "")).append("</ccsy>");
				data.append("<ccjl>").append(MapUtil.getMapValue(map, "ccjl", "")).append("</ccjl>");
				data.append("<gkwww>").append(MapUtil.getMapValue(map, "gkwww", "")).append("</gkwww>");
				data.append("<fdjsb>").append(MapUtil.getMapValue(map, "fdjsb", "")).append("</fdjsb>");
				data.append("<fdjsccdz>").append(MapUtil.getMapValue(map, "fdjsccdz", "")).append("</fdjsccdz>");
				data.append("</vindata></vindatas>");
				String vin = MapUtil.getMapValue(map, "vin", "");
				String res;
				try {
					res = xxgkSoap.sendVinData(key, data.toString());
					if ("ok".equals(forRes(res))) {
						sqlList.add("update tbl_mepgk set vin_uptime=sysdate,vin_upstate=2,vin_remark='上传成功'  where vnm='" + vin + "'");
						Loggers.PERFORMANCE.info("[环保随车清单数据上传] 上传成功：" +vin);
					} else {
						sqlList.add("update tbl_mepgk set vin_remark='" + res.substring(res.indexOf("<data>") + 6,res.indexOf("</data>")).replaceAll("'", "-") + "'  where vnm='" + vin + "'");
						Loggers.PERFORMANCE.info("[环保随车清单数据上传] 上传失败：" +vin);
					}
				} catch (RemoteException e) {
					e.printStackTrace();
				}
				mepgkLogout(key);
			}
		}
		Loggers.PERFORMANCE.info("[环保随车清单数据上传] 结束上传");
		try {
			this.dao.excuteBatch(sqlList);
		} catch (DAOException e) {
			e.printStackTrace();
		} catch (TransactionException e) {
			e.printStackTrace();
		}
		result = "ok";
		return result;
	}
	
	/**
	 * 环保随车清单自动上传
	 */
	public void autoUploadMepgk() {
		StringBuffer sql = new StringBuffer(); 
		List resList = new ArrayList();	
		String auto = null;
		try {
			auto = SystemParam.getKey("AUTO_UPMEPGK" );
			if (!StringTools.isBlank(auto) && "1".equals(auto)) {
		         
		                List tempList = dao.queryForList("select ts_factory from tbl_up_account where deleteflag='0' and uptype='mepgkvin' ");
		                if(tempList != null && tempList.size() > 0)
		                {
		                    String ts_factory = "";
		                    for(int i = 0; i < tempList.size(); i++)
		                    {
		                        ts_factory = MapUtil.getMapValue((Map)tempList.get(i), "ts_factory", "");
		                        sql.delete(0, sql.length());
		                        sql.append(" SELECT m.id,m.hgz_id,vnm vin,xxgkbh xxgkh,sb,clsccdz sccdz,fdjbh fdjh,date_format( m.clscrq,'%Y-%m-%d') scdate, ")
		                        .append(" (SELECT date_format( fzrq,'%Y-%m-%d')  FROM tbl_hgzpara p WHERE p.hgz_id=m.hgz_id) ccdate, ")
		                        .append(" (case when model_cllx like '电%'  then SUBSTR(CCJYXMJL,0,INSTR(CCJYXMJL,'，')-1) else CCJYYJ end ) ccsy, ")
		                        .append(" (case when model_cllx like '电%'  then SUBSTR(CCJYXMJL,INSTR(CCJYXMJL,'，')+1) else CCJYYJJL end ) ccjl,  ")
		                        .append(" gsgfwz gkwww,fdjcp fdjsb,fdjsccdz ").append(" FROM tbl_mepgk m  ")
		                        .append( "WHERE  m.deleteflag = 0 and m.vin_upstate=1  and m.factorycode='")
		                        .append(ts_factory).append("'")
		                        .append(" AND EXISTS (SELECT 1 from tbl_hgzmain a, tbl_hgzpara p WHERE a.hgz_id=p.hgz_id AND p.vnm = m.vnm ")
		                        .append("  AND a.hgzfl=0 AND a.cycle_status=20 ")
		                        .append(" AND a.upload_STATUS in ('40','30')  ) ")
		                        .append(" ORDER BY m.createtime  limit 0,200 ");
				resList = this.dao.queryForList(sql.toString());
				if (resList != null && resList.size() > 0) {
					sendMepgkData(resList, ts_factory, "");
				}
		                    }
			}else{
				Loggers.PERFORMANCE.info("[环保随车清单数据上传] AUTO_UPMEPGK 值不为1，系统不进行上传！");
			}
			}
		} catch (DAOException e) {
			e.printStackTrace();
		} catch (SQLException e) {
			e.printStackTrace();
		} 
		
	}
	
	/**
	 * 环保PDF自动上传
	 */
	public void autoUploadMepPDF() {
		StringBuffer sql = new StringBuffer(); 
		List resList = new ArrayList();	
		String auto = null;
		try {
	           auto = SystemParam.getKey("AUTO_UPMEPGK");
	            if(!StringTools.isBlank(auto) && "1".equals(auto))
	            {
	                List tempList = dao.queryForList("select ts_factory from tbl_up_account where deleteflag='0' and uptype='mepgkpdf' ");
	                if(tempList != null && tempList.size() > 0)
	                {
	                    String ts_factory = "";
	                    for(int i = 0; i < tempList.size(); i++)
	                    {
	                        ts_factory = MapUtil.getMapValue((Map)tempList.get(i), "ts_factory", "");
	                        sql.delete(0, sql.length());
	                        sql.append("  select  m.id,m.hgz_id,vnm vin,date_format( m.createtime,'%Y-%m-%d') dat1,(FN_GET_MEPGKREPORTURL(m.hgz_id)) bt  from tbl_mepgk m ").append((new StringBuilder("where m.deleteflag=0 and pdf_upstate='1' and vin_upstate='2' and m.factorycode='")).append(ts_factory).append("' order by createtime  limit 0,60  ").toString());
	                        resList = dao.queryForList(sql.toString());
	                        if(resList != null && resList.size() > 0)
	                            sendMepPDF(resList, ts_factory, "");
	                        resList = null;
	                    }

	                }
	            } else{
				Loggers.PERFORMANCE.info("[环保随车清单PDF上传] AUTO_UPMEPGK 值不为1，系统不进行上传！");
			}
		} catch (DAOException e) {
			e.printStackTrace();
		} catch (SQLException e) {
			e.printStackTrace();
		} 		
	}
	
	/**
	 * 环保PDF上传国家环保
	 * @return
	 */
	public String sendMepPDF(List resList,String factory,String userid) {
		Loggers.PERFORMANCE.info("[环保随车清单PDF上传] 开始上传");
		String result = "fail";
		List sqlList = new ArrayList();
		
		if(StringTools.isBlank(userid))
			userid = "104";
        File localFile = null;
        boolean bool = false;
		//连接FTP开始
		List urlList = new ArrayList();
		List vinList = new ArrayList();
		String baseurl = SystemParam.getKey("mep_savepath","D:\\mep_pdf\\");
		for (int i = 0; resList != null && i < resList.size(); i++) {
	           Map map = (Map)resList.get(i);
	            String hgzid = MapUtil.getMapValue(map, "hgz_id", "");
	            String bt = MapUtil.getMapValue(map, "bt", "");
	            String vin = MapUtil.getMapValue(map, "vin", "");
	            String dat1 = MapUtil.getMapValue(map, "dat1", "");
	            String url = (new StringBuilder(String.valueOf(baseurl))).append(dat1).append("\\").append(vin).append(".pdf").toString();
	            vinList.add(vin);
	            urlList.add(url);
	            localFile = new File(url);
	            bool = false;
				if (localFile.exists()) {
					bool = true;
					Loggers.PERFORMANCE.info("[环保随车清单PDF上传] 生成待上传PDF文件：["+(i+1)+"/"+resList.size()+"]:PDF已存在");
				}
				
				//缺少PDF文件时，自动生成PDF文件
				if (!bool) {
					Loggers.PERFORMANCE.info("[环保随车清单PDF上传] 开始待上传PDF文件： ["+(i+1)+"/"+resList.size()+"]hgzid="+hgzid);
					 bool = createPDF(hgzid, vin, dat1, bt, factory);
				}
		}
		
		FTPUtil ftpUtil = null;
		String mep_pdfup_ip = SystemParam.getKey("mep_pdfup_ip","pdfs.vecc-mep.org.cn");
		String mep_pdfup_port = SystemParam.getKey("mep_pdfup_port","21");
		String up_username = null;
		String up_password = null;
		
		if (StringTools.isBlank(factory)) {
			factory = SystemParam.getKey("coc_bbbm");
		}
		//获取上传帐号密码
		List userList = new ArrayList();
		try {
			userList = this.dao.queryForList("select qybh,password from tbl_up_account where deleteflag='0' and uptype='mepgkpdf' and factorycode='"+factory+"'");
		} catch (DAOException e) {
			e.printStackTrace();
		} catch (SQLException e) {
			e.printStackTrace();
		}
		if (userList != null && userList.size() > 0) {
			up_username = MapUtil.getMapValue((Map)userList.get(0), "qybh", "");
			up_password = MapUtil.getMapValue((Map)userList.get(0), "password", "");
		}
		Loggers.PERFORMANCE.info("[环保随车清单PDF上传] 获取上传帐号密码成功");
		
		//连接FTP
		ftpUtil = new FTPUtil(mep_pdfup_ip, Integer.parseInt(mep_pdfup_port), up_username, up_password);
		boolean connResult = ftpUtil.connectServer();
		if (!connResult) {
			Loggers.PERFORMANCE.info("[环保随车清单PDF上传] FTP连接失败");
			return "环保FTP连接失败！";
		}
		Loggers.PERFORMANCE.info("[环保随车清单PDF上传] 连接FTP成功");
		
		//上传开始
			for(int j=0;  resList != null  && j < resList.size(); j++) {
					boolean uploadResult= ftpUtil.upload((String)urlList.get(j), "/" + (String)vinList.get(j) + ".pdf");
					if(uploadResult) {
						sqlList.add("update tbl_mepgk set pdf_uptime=sysdate,pdf_upstate=2,pdf_remark='上传成功' where vnm='" + (String)vinList.get(j)+ "'");
						Loggers.PERFORMANCE.info("[环保随车清单PDF上传] 环保PDF上传成功："+(String)vinList.get(j));
					} else {
						Loggers.PERFORMANCE.info("[环保随车清单PDF上传] 环保PDF上传失败："+(String)vinList.get(j));
					}
				
			}
		

		if (ftpUtil != null) {
			ftpUtil.closeConnect();
		}
		if (sqlList != null && sqlList.size()>0) {
			try {
				this.dao.excuteBatch(sqlList);
			} catch (DAOException e) {
				e.printStackTrace();
			} catch (TransactionException e) {
				e.printStackTrace();
			}
		}
		Loggers.PERFORMANCE.info("[环保随车清单PDF上传] 结束上传");
		return result;
	}
	
	
	/**
	 * 生成随车清单二维码并记录其路径
	 * @param hgzidList
	 */
	public void getQRimage(List hgzidList, List vinList, List qrpathList) {
		//获取二维码存放路径
		String qrpath = SystemParam.getKey("mepgk_QRPATH");
		File file=null ;
		if (!StringTools.isBlank(qrpath)) {
			 file = new File(qrpath);
			if (!file.isDirectory()) {
				file.mkdirs();
			}
		}
		
		//循环生成二维码
		if(hgzidList != null && qrpathList !=null && vinList != null && hgzidList.size()>0 && hgzidList.size() == vinList.size() && hgzidList.size() == qrpathList.size()) {
			List sqlList = new ArrayList();
			for(int i=0; i<hgzidList.size(); i++) {
				String hgzid = (String) hgzidList.get(i);
				String vin = (String) vinList.get(i);
				String realqrpath = (String) qrpathList.get(i);
				if(StringTools.isBlank(realqrpath)||!file.exists() || !file.isFile()){//如果为空，重新生成图片
						String qrcodeFilePath = qrpath+vin+".png";
						QrCode14 im=new QrCode14();
						im.encoderQRCode("http://xxgk.vecc.org.cn/vin/"+vin,qrcodeFilePath,"png",7);
						String updatesql = "update tbl_mepgk set  qrpath = '"+qrcodeFilePath+"' where hgz_id ="+hgzid;
						sqlList.add(updatesql);
				}
			}
			if (sqlList != null && sqlList.size()>0) {
				try {
					this.dao.excuteBatch(sqlList);
				} catch (DAOException e) {
					e.printStackTrace();
				} catch (TransactionException e) {
					e.printStackTrace();
				}
			}
		}
	}
	
	   public boolean createPDF(String hgzid, String vin, String dat1, String report, String factory){
       boolean bool;
       Connection conn;
       bool = false;
       conn = null;
       String baseurl = SystemParam.getKey("mep_savepath", "D:\\mep_pdf");
		try {
			conn = this.dao.getJdao().getDataSource().getConnection();
	        String save_path = "";
	        String sqls = "select m.*,M.VNM VIN, p.scrq from tbl_mepgk m, tbl_hgzpara p where m.hgz_id = p.hgz_id and m.hgz_id in ( "+hgzid+")";
					Map parameters = new HashMap(); 

					parameters.put("strsql", sqls);
					
				       if(report.indexOf("g6") > 0)
				        {
				            parameters.put("bgz_url", (new StringBuilder(String.valueOf(System.getProperty("user.dir")))).append("/webapps/schgz_dfm/service/report").append("/g6zm.jpg").toString());
				            parameters.put("bgf_url", (new StringBuilder(String.valueOf(System.getProperty("user.dir")))).append("/webapps/schgz_dfm/service/report").append("/g6fm.jpg").toString());
				            parameters.put("bg_url2", (new StringBuilder(String.valueOf(System.getProperty("user.dir")))).append("/webapps/schgz_dfm/service/report").append("/mep_").append(factory).append(".jpg").toString());
				        } else
				        {
				            parameters.put("bg_url", (new StringBuilder(String.valueOf(System.getProperty("user.dir")))).append("/webapps/schgz_dfm/service/report").append("/g5.jpg").toString());
				            parameters.put("bg_url2", (new StringBuilder(String.valueOf(System.getProperty("user.dir")))).append("/webapps/schgz_dfm/service/report").append("/mep_").append(factory).append(".jpg").toString());
				        }
				        report = (new StringBuilder(String.valueOf(report.substring(0, report.indexOf("."))))).append("_pdf").append(report.substring(report.indexOf("."))).toString();
				        save_path = (new StringBuilder(String.valueOf(System.getProperty("user.dir")))).append("/webapps/schgz_dfm/service/report/").append(report).toString();
				        Loggers.PERFORMANCE.info((new StringBuilder("")).append(save_path).toString());
				        File save_reportFile = new File(save_path);
					
					JasperPrint jaspersave = null; 
					JasperReport jasperReport = (JasperReport)JRLoader.loadObject(save_reportFile.getPath()); 
					jaspersave =  JasperFillManager.fillReport(jasperReport, parameters, conn); 
					if(report.indexOf("g6")>0){
					jaspersave.setPageHeight(842);
					}
					  bool = PDFCreateMange.exportMepPDF(jaspersave, vin, dat1);
					if(bool)
						 
					Loggers.PERFORMANCE.info("[环保随车清单PDF上传] 生成PDF结果："  + vin + "：ok"  );
				
			
		} catch (SQLException e) {
			e.printStackTrace();
		} catch (JRException e) {
			e.printStackTrace();
		} finally {
			try {
				if(!conn.isClosed()) {
					conn.close();
				}
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}
		return bool;
	}
	
}
