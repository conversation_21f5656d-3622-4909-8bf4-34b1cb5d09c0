package com.dawnpro.vipservice;

import java.rmi.RemoteException;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dawnpro.commons.SystemParam;
import com.dawnpro.commons.spring.ServiceFactory;
import com.dawnpro.commons.util.MapUtil;
import com.dawnpro.commons.util.StringTools;
import com.dawnpro.commons.util.db.DAOException;
import com.dawnpro.commons.util.db.TransactionException;
import com.dawnpro.service.BaseServer;
import com.jacob.activeX.ActiveXComponent;
import com.jacob.com.ComThread;
import com.jacob.com.Dispatch;
import com.jacob.com.Variant;
import com.sinosoft.webservice.VinUploadServiceProxy;

public class CocUpManage extends BaseServer {
	
	private String startuptime = "2015-01-01";


	/**
	 * COC自动上传
	 *
	 * @return
	 */
	public void autoUpload() {
		System.out.println("COC自动上传开始:" + new GregorianCalendar().getTime());
		String rand = getUpRand();
		String sign = doSign(rand);
		for (int i = 1; i < 2; i++) {
			doUploadCoc(getCocAutoUpdata(i), rand, sign);
		}
		System.out.println("COC自动上传结束:" + new GregorianCalendar().getTime());
	}

	/**
	 * COC手动上传
	 * 
	 * @param ids
	 * @param userid
	 * @return
	 */
	public String manuUpload(String ids, String userid, String rand, String sign) {
		String res = doUploadCoc(getCocManuUpdata(ids, userid), rand, sign);
		return res;
	}

	/**
	 * COC上传反馈结果集合
	 * @return
	 */
	public Map getUpBackMap() {
		Map resMap = new HashMap();
		resMap.put("100", "上传成功");
		resMap.put("101", "未收到随机串");
		resMap.put("102", "认证过期");
		resMap.put("107", "当前key与系统内用户未绑定");
		resMap.put("108", "证书无效");
		resMap.put("109", "签名验证失败");
		resMap.put("203", "VIN号码错误,不能为空");
		resMap.put("204", "VIN检验码不正确");
		resMap.put("205", "本次上传列表中存在VIN重复");
		resMap.put("206", "COC在当前厂商关系下不存在");
		resMap.put("207", "发动机号不能为空");
		resMap.put("208", "颜色不能为空");
		resMap.put("209", "日期填写有问题，请按照毫秒数填写");
		resMap.put("210", "日期填写有问题，请按照毫秒数填写");
		resMap.put("301", "VIN未查到");
		return resMap;
	}

	/**
	 * COC接口上传
	 * @param dataMap 待上传数据集
	 * @param rand 接口获取的随机数
	 * @param sign 数字签名
	 * @return
	 */
	public String doUploadCoc(Map dataMap, String rand, String sign) {
		String res = "没有待上传的数据！";
		String cocdata = null;
		String id = null;
		String vin = null;
		Map resMap = getUpBackMap();
		Map backMap = null;
		VinUploadServiceProxy proxy = null;
		String reljson = null;
		String[] manu_ids = null;
		String manuRelationId = null;

		if (dataMap != null && dataMap.size() > 0) {
			try {
				proxy = new VinUploadServiceProxy();
				sign = sign.replaceAll(" ", "+");

				// 认证
				reljson = proxy.auth(rand, sign);

				if (!StringTools.isBlank(reljson) && reljson.indexOf("\"manuRelationId\":") > 0) {
//					manu_ids = reljson.split("\"manuRelationId\":");
					
					//String to JsonArray 
					JSONArray jsonArray= JSONArray.parseArray(reljson);
					
					// 上传
					Iterator ite = dataMap.entrySet().iterator();
					while (ite.hasNext()) {
						Map.Entry entry = (Entry) ite.next();
						id = entry.getKey().toString().split(",")[0];
						vin = entry.getKey().toString().split(",")[1];
						cocdata = entry.getValue().toString();
						
//						for (int i = 1; i < manu_ids.length; i ++) {
//							manuRelationId = manu_ids[i].substring(0,manu_ids[i].indexOf(","));
//							res = proxy.upload(rand, manuRelationId, cocdata);
//							if (StringTools.isBlank(res)) {
//								res = "COC数据上传失败！";
//							}
//							else {
//								backMap = backCocUp(res);
//								if ("100".equals(backMap.get("status"))) {
//									break;
//								}
//							}
//						}	
						
						for (int i = 0; i < jsonArray.size(); i ++) {
			                JSONObject jsonObject = jsonArray.getJSONObject(i);			  			  		  			  				  			  	
			  			  	manuRelationId=jsonObject.getString("manuRelationId");
							res = proxy.upload(rand, manuRelationId, cocdata);
							if (StringTools.isBlank(res)) {
								res = "COC数据上传失败！";
							}
							else {
								backMap = backCocUp(res);
								if (!"100".equals(backMap.get("status"))) {
					  			  	JSONObject manuApply = jsonObject.getJSONObject("manuApply");
					  			  	manuRelationId=manuApply.getString("manufacturerId");
									res = proxy.upload(rand,manuRelationId, cocdata);
									backMap = backCocUp(res);				
								}
								if (!"100".equals(backMap.get("status"))) {
					  			  	JSONObject manuManu = jsonObject.getJSONObject("manuManu");
					  			  	manuRelationId=manuManu.getString("manufacturerId");
									res = proxy.upload(rand,manuRelationId, cocdata);
									backMap = backCocUp(res);				
								}
								if (!"100".equals(backMap.get("status"))) {
					  			  	JSONObject manuProdu = jsonObject.getJSONObject("manuProdu");
					  			  	manuRelationId=manuProdu.getString("manufacturerId");	
									res = proxy.upload(rand,manuRelationId, cocdata);
									backMap = backCocUp(res);				
								}
						/*		if (!"100".equals(backMap.get("status"))) {
									res = proxy.upload(rand,"12459", cocdata);
									backMap = backCocUp(res);
								}*/
								if ("100".equals(backMap.get("status"))) {
									break;
								}
							}
						}
						
						if ("100".equals(backMap.get("status"))) {
							cocUploadAfter(id, "1", "上传成功!");
						} else {
							String temp = backMap.get(vin).toString();
							if (temp != null && temp.length() == 3) {
								cocUploadAfter(id, "0", "上传失败：" + MapUtil.getMapValue(resMap, temp, temp));
							}
							else if (temp.length() > 3) {
								String temp1 = "";
								while (temp.length() >= 3) {
									temp1 = temp1 + MapUtil.getMapValue(resMap, temp.substring(0,3), temp.substring(0,3)) + "！";
									temp = temp.substring(3);
								}
								cocUploadAfter(id, "0", "上传失败：" + temp1);
							}
							else {
								cocUploadAfter(id, "0", "上传失败：" + MapUtil.getMapValue(resMap, res, res));
							}	
						}
					}
				} else {
					res = "上传认证失败：" + MapUtil.getMapValue(resMap, reljson, reljson);
				}

			} catch (Exception e) {
				e.printStackTrace();
				res = "上传连接失败";
			} 
		}
		return res;
	}

	/**
	 * 获取COC上传随机数
	 * 
	 * @return
	 */
	public String getUpRand() {
		String res = "fail";
		System.out.println("获取COC上传随机数开始:" );
		VinUploadServiceProxy proxy = new VinUploadServiceProxy();
		System.out.println("proxy:" +proxy);
				try {
			res = proxy.getRand();
			System.out.println("res:" +res);
		} catch (RemoteException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return res;

	}
	
	/**
	 * 数据签名
	 * @return
	 */
	public String doSign(String rand) {
		String res = null;
		ActiveXComponent component = null;
		Dispatch ds = null;
		Variant var = null;
		String cerid = null;
		try {
			System.out.println("获取COC数据签名开始:" );
			ComThread.InitSTA();
			System.out.println("获取component开始:" );
			component = new ActiveXComponent("CLSID:A252C809-ACF9-4391-96D7-2C2195FDA660");
			System.out.println("component:" +component);
			ds = (Dispatch)component.getObject();
			System.out.println("ds:" +ds);
			var = Dispatch.call(ds,"SOF_GetUserList");
			System.out.println("var:" +var);
			if (!StringTools.isBlank(var.toString()) && var.toString().indexOf("||") > 0) {
				System.out.println("获取cerid开始:");
				cerid = var.toString();
				cerid = cerid.substring(cerid.indexOf("||") + 2);
				if (cerid.indexOf("&&&") > 0) {
					cerid = cerid.substring(0, cerid.indexOf("&&&"));
				}
				System.out.println("cerid:" +cerid);
			}
			if (!StringTools.isBlank(cerid)) {
				//Ukey登录
				String cqc_password = SystemParam.getKey("cqc_password","cqcca");
				System.out.println("cqc_password:" +cqc_password);
				Dispatch.call(ds,"SOF_Login",cerid,cqc_password); 
				//数据签名
				var = Dispatch.call(ds,"SOF_SignDataByP7",cerid,rand);
				System.out.println("数据签名var:" +var);
				res = var.toString();
				System.out.println("res:" +res);
			}
		}
		catch (Exception e) {
			e.printStackTrace();
			System.out.println(e);
		}
		ComThread.Release();
		return res;
	}

	/**
	 * 获取COC自动上传数据
	 *
	 * @return
	 */
	public Map getCocAutoUpdata(int k) {
		Map resMap = new HashMap();
		StringBuffer jsonBuffer = new StringBuffer();
		StringBuffer sqlBuffer = new StringBuffer();
		List resList = new ArrayList();
		
		String orderby="";
		String condition= "";
		
		if(k==0) {//查询从未上传过的数据进行上传
			orderby=" id asc ";
			condition=" and a.upresult is null ";
		}else{//查询上传失败的数据进行重新上传
			orderby=" uptime asc ";
			condition=" and a.upresult is not null ";
		}
		
		sqlBuffer.append("select * from (select a.id,p.vnm as vinCode,p.FDJXH || ' ' || p.FDJH as engineNo ,row_number() over(order by ").append(orderby).append(" ) rm, ")
				.append(" (select coc_color from (select zmc1 hgz_color,zmc2 coc_color,dep_id companyid ")
				.append(" from tbl_zdxmzlb where xmdm='csys' and nvl(trim(zmc2),'0')>'0' ")
				.append(" union select hgz_color,coc_color,(select extend1 from ts_dept_info where deleteflag='0' and extend3='0' and rownum=1) ")
				.append(" from tbl_color where coc_color is not null) m,tbl_hgzpara n ")
				.append(" where m.hgz_color=n.csys and m.companyid=n.subcompany and n.hgz_id=a.id and rownum=1) as color, ")
				.append(" (p.scrq-to_date('1970-1-1','yyyy-mm-dd'))*24*60*60*1000 as productDate,a.coc_code as cocCode from TBL_COC_HGZ a,TBL_HGZPARA p,TBL_HGZMAIN m ")
				.append(" where a.ID=p.HGZ_ID and m.HGZ_ID=a.ID and m.CYCLE_STATUS<30 and m.business_type1<>3 ")
				.append(" and p.scrq>=to_date('").append(this.startuptime).append("','yyyy-mm-dd') and a.upstate=0 ").append(condition).append( ") ")
				.append(" where rm>0 and rm<=200 ");
		try {
			resList = this.dao.queryForList(sqlBuffer.toString());
			
			for (int i = 0; i < resList.size(); i++) {
				Map map = (Map) resList.get(i);
				jsonBuffer.delete(0, jsonBuffer.length());
				jsonBuffer.append("[{\"color\":\""
						+ MapUtil.getMapValue(map, "color", "") + "\",");
				jsonBuffer.append("\"cocCode\":\""
						+ MapUtil.getMapValue(map, "cocCode", "") + "\",");
				jsonBuffer.append("\"vinCode\":\""
						+ MapUtil.getMapValue(map, "vinCode", "") + "\",");
				jsonBuffer.append("\"engineNo\":\""
						+ MapUtil.getMapValue(map, "engineNo", "") + "\",");
				jsonBuffer.append("\"productDate\":"
						+ MapUtil.getMapValue(map, "productDate", "") + "}]");
				resMap.put(MapUtil.getMapValue(map, "id", "") + "," + MapUtil.getMapValue(map, "vinCode", ""), jsonBuffer.toString());	
			}
			
		} catch (DAOException e) {
			e.printStackTrace();
		} catch (SQLException e) {
			e.printStackTrace();
		}
		return resMap;
	}

	/**
	 * 获取COC手动上传数据
	 * 
	 * @param ids
	 * @return
	 */
	public Map getCocManuUpdata(String ids, String userid) {
		Map resMap = new HashMap();
		StringBuffer jsonBuffer = new StringBuffer();
		StringBuffer sqlBuffer = new StringBuffer();
		List resList = new ArrayList();
		sqlBuffer
				.append("select a.id,p.vnm as vinCode,p.FDJXH || ' ' || p.FDJH as engineNo , ")
				.append(" (select coc_color from (select zmc1 hgz_color,zmc2 coc_color,dep_id companyid ")
				.append(" from tbl_zdxmzlb where xmdm='csys' and nvl(trim(zmc2),'0')>'0' ")
				.append(" union select hgz_color,coc_color,(select extend1 from ts_dept_info where deleteflag='0' and extend3='0' and rownum=1) ")
				.append(" from tbl_color where coc_color is not null) m,tbl_hgzpara n ")
				.append(" where m.hgz_color=n.csys and m.companyid=n.subcompany and n.hgz_id=a.id and rownum=1) as color, ")
				.append(" (p.scrq-to_date('1970-1-1','yyyy-mm-dd'))*24*60*60*1000 as productDate,a.coc_code as cocCode from TBL_COC_HGZ a,TBL_HGZPARA p,TBL_HGZMAIN m ")
				.append(" where a.ID=p.HGZ_ID and m.HGZ_ID=a.ID and m.CYCLE_STATUS=20 and m.business_type1<>3 ")
				.append(" and p.scrq>=to_date('").append(this.startuptime)
				.append("','yyyy-mm-dd') and a.id in (").append(ids)
				.append(") and a.upstate=0 ");
		try {
			resList = this.dao.queryForList(sqlBuffer.toString());
			jsonBuffer.append("[");
			for (int i = 0; i < resList.size(); i++) {
				Map map = (Map) resList.get(i);
				jsonBuffer.delete(0, jsonBuffer.length());
				jsonBuffer.append("[{\"color\":\""
						+ MapUtil.getMapValue(map, "color", "") + "\",");
				jsonBuffer.append("\"cocCode\":\""
						+ MapUtil.getMapValue(map, "cocCode", "") + "\",");
				jsonBuffer.append("\"vinCode\":\""
						+ MapUtil.getMapValue(map, "vinCode", "") + "\",");
				jsonBuffer.append("\"engineNo\":\""
						+ MapUtil.getMapValue(map, "engineNo", "") + "\",");
				jsonBuffer.append("\"productDate\":"
						+ MapUtil.getMapValue(map, "productDate", "") + "}]");
				resMap.put(MapUtil.getMapValue(map, "id", "") + "," + MapUtil.getMapValue(map, "vinCode", ""), jsonBuffer.toString());
			}
			
		} catch (DAOException e) {
			e.printStackTrace();
		} catch (SQLException e) {
			e.printStackTrace();
		}
		return resMap;
	}

	/**
	 * COC上传后数据处理更新
	 * 
	 * @param ids
	 * @param ids
	 * @return
	 */
	public boolean cocUploadAfter(String ids, String upstate, String upresult) {
		boolean res = false;
		String sql = "update tbl_coc_hgz set uptime=sysdate,upstate=" + upstate
				+ ",upresult='" + upresult + "' where id in (" + ids + ")";
		try {
			if (this.dao.executeUpdate(sql) >= 0) {
				res = true;
			}
		} catch (DAOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (TransactionException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return res;
	}
	
	/**
	 * 处理COC上传返回信息
	 * @return
	 */
	public Map backCocUp(String value) {
		Map map = new HashMap();
		if (!StringTools.isBlank(value)) {
			if (value.startsWith("[")) {
				value = value.substring(1);
			}
			if (value.endsWith("]")) {
				value = value.substring(0,value.length()-1);
			}
			if (value.startsWith("{")) {
				value = value.substring(1);
			}
			if (value.endsWith("}")) {
				value = value.substring(0,value.length()-1);
			}
			String[] temps = value.split(",\"");
			for (int i = 0; i < temps.length; i++) {
				if (!StringTools.isBlank(temps[i]) && temps[i].indexOf(":") > 0) {
					String temp = temps[i].replaceAll("\"", "");
					map.put(temp.substring(0, temp.indexOf(":")), temp.substring(temp.indexOf(":")+1));
				}
			}
		}
		return map;
	}

	public static void main(String[] args) {
		CocUpManage manage = (CocUpManage)ServiceFactory.getBeanByName("CocUpManage");	
		manage.autoUpload();	
	}

}
