package com.dawnpro.vipservice.bsjx;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.dawnpro.commons.SystemParam;
import com.dawnpro.commons.util.MapUtil;
import com.dawnpro.commons.util.db.DAOException;
import com.dawnpro.commons.util.db.TransactionException;
import com.dawnpro.core.log.Loggers;
import com.dawnpro.entity.bsjx.BsjxHgzinfo;
import com.dawnpro.entity.bsjx.BsjxResult;
import com.dawnpro.service.BaseServer;
import com.dawnpro.service.commons.HttpUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * 标识解析系统
 * <AUTHOR>
 *
 */
public class BsjxManage extends BaseServer {
	
	/**
	 * 上传合格证信息到标识解析系统
	 */
	public void uploadHgzToBsjx()
	{
		Loggers.INTERFACE.info("标识解析-接口:自动上传接口执行开始！");
		Loggers.INTERFACE.info((new StringBuilder("标识解析-接口：新增上传开始:")).append((new GregorianCalendar()).getTime()).toString());
		this.callService("1");
		Loggers.INTERFACE.info((new StringBuilder("标识解析-接口：新增上传结束:")).append((new GregorianCalendar()).getTime()).toString());
		Loggers.INTERFACE.info((new StringBuilder("标识解析-接口：补充上传开始:")).append((new GregorianCalendar()).getTime()).toString());
		this.callService("2");
		Loggers.INTERFACE.info((new StringBuilder("标识解析-接口：补充上传结束:")).append((new GregorianCalendar()).getTime()).toString());
		
		Loggers.INTERFACE.info("标识解析-接口:自动上传接口执行结束！");
	}
	
	private void callService(String type) {
		List datalist = getUploadData(type);
		
		String loginUrl = SystemParam.getKey("bsjx_upload_url", "http://10.92.6.11:32203/v1/certificate/operation");
		
		Loggers.INTERFACE.info("标识解析-接口:待上传数据条数："+datalist.size());
		for (int i = 0; i < datalist.size(); i++)
		{
			ObjectMapper mapper = new ObjectMapper();
			String postData = null;
			BsjxHgzinfo bsjxHgzinfo = new BsjxHgzinfo();
			String id = "";
			Map hgzmap = new HashMap();
			hgzmap =(Map) datalist.get(i);
			
			id = MapUtil.getMapValue(hgzmap, "id", "");
			bsjxHgzinfo = (BsjxHgzinfo) hgzmap.get("hgz");
			
			try {
				postData = mapper.writeValueAsString(bsjxHgzinfo);
				
				String result = HttpUtil.doPostJson(loginUrl, postData);
				Loggers.INTERFACE.debug("标识解析-接口，数据id["+id+"]，接口反馈："+result);
				
				BsjxResult bResult=mapper.readValue(result,BsjxResult.class);
				
				updateBsjx(bResult,id);
				
			} catch (JsonProcessingException e1) {
				Loggers.INTERFACE.debug("标识解析-接口：数据转换json报错");
				e1.printStackTrace();
			}	catch (Exception e) {
				Loggers.INTERFACE.debug("标识解析-接口：报错："+e.toString());
				e.printStackTrace();
			}
		}
	}

	private void updateBsjx(BsjxResult bResult,String id) {
		// TODO Auto-generated method stub
		Loggers.INTERFACE.debug("标识解析-接口：回填结果数据开始");
		String sendflag="0";
		String sendmemo="error";
		if(!"".equals(id)) {
			if("0".equals(bResult.getCode())) {
				sendflag="1";
				sendmemo=bResult.getMessage();
			}else {
				sendflag="0";
				sendmemo=bResult.getMessage();
			}
			String sql = "update tif_to_bsjx_hgzinfo set sendflag='"+sendflag+"',sendtime=now(),sendmemo='"+sendmemo+"' where id="+id;
			try {
				this.dao.execute(sql);
			} catch (DAOException | TransactionException e) {
				e.printStackTrace();
				Loggers.INTERFACE.info("标识解析-接口：回填结果执行SQL报错"+e.toString());
			}
		}
		
		Loggers.INTERFACE.debug("标识解析-接口：回填结果数据结束");
	}

	private List getUploadData(String type) {

		StringBuffer sqlBuffer = new StringBuffer();
		sqlBuffer
				.append("select id,opertype,hgzxh,wzhgzbh,DATE_FORMAT(fzrq,'%Y-%m-%d') fzrq,")
				.append("clzzqymc,clpp,clmc,clxh,vin,dpid,dphgzbh,fdjxh,fdjh,cph,pc,")
				.append("DATE_FORMAT(ggsxrq,'%Y/%m/%d')  ggsxrq,cddbj ")
				.append("FROM tif_to_bsjx_hgzinfo ");
		if("1".equals(type)) {
			sqlBuffer.append(" where sendflag = 0 and sendtime is null order by id limit 0,100");
		}else {
			sqlBuffer.append(" where sendflag = 0 and sendtime is not null order by sendtime asc limit 0,100");
		}
		Map map = null;
		Map tempMap = null;
		List hgzlist = new ArrayList();
		List tempList = null;
		try {
			tempList = this.dao.queryForList(sqlBuffer.toString());
			for (int i = 0; tempList != null && i < tempList.size(); i++) {
				BsjxHgzinfo hgz = new BsjxHgzinfo();
				hgz.setOpertype(MapUtil.getMapValue((Map) tempList.get(i), "opertype", ""));
				hgz.setHgzxh(MapUtil.getMapValue((Map) tempList.get(i), "hgzxh", ""));
				hgz.setWzhgzbh(MapUtil.getMapValue((Map) tempList.get(i), "wzhgzbh", ""));
				hgz.setFzrq(MapUtil.getMapValue((Map) tempList.get(i), "fzrq", ""));
				hgz.setClzzqymc(MapUtil.getMapValue((Map) tempList.get(i), "clzzqymc", ""));
				hgz.setClpp(MapUtil.getMapValue((Map) tempList.get(i), "clpp", ""));
				hgz.setClmc(MapUtil.getMapValue((Map) tempList.get(i), "clmc", ""));
				hgz.setClxh(MapUtil.getMapValue((Map) tempList.get(i), "clxh", ""));
				hgz.setVin(MapUtil.getMapValue((Map) tempList.get(i), "vin", ""));
				hgz.setDpid(MapUtil.getMapValue((Map) tempList.get(i), "dpid", ""));
				hgz.setDphgzbh(MapUtil.getMapValue((Map) tempList.get(i), "dphgzbh", ""));
				hgz.setFdjxh(MapUtil.getMapValue((Map) tempList.get(i), "fdjxh", ""));
				hgz.setFdjh(MapUtil.getMapValue((Map) tempList.get(i), "fdjh", ""));
				hgz.setCph(MapUtil.getMapValue((Map) tempList.get(i), "cph", ""));
				hgz.setPc(MapUtil.getMapValue((Map) tempList.get(i), "pc", ""));
				hgz.setGgsxrq(MapUtil.getMapValue((Map) tempList.get(i), "ggsxrq", ""));
				hgz.setCddbj(MapUtil.getMapValue((Map) tempList.get(i), "cddbj", ""));
				
				tempMap = new HashMap();
				tempMap.put("hgz", hgz);
				tempMap.put("id", MapUtil.getMapValue((Map) tempList.get(i), "id", ""));
				hgzlist.add(tempMap);
			}
		} catch (DAOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return hgzlist;
	}
	
	public static void main(String[] args) {
		String result = "{\"code\":0,\"message\":\"添加成功\",\"data\":null}";
		Loggers.INTERFACE.debug("标识解析-接口 ,接口反馈："+result);
		
		ObjectMapper mapper = new ObjectMapper();
		String postData = null;
		try {
			BsjxResult bResult=mapper.readValue(result,BsjxResult.class);
			System.out.println(bResult.getMessage());
		} catch (JsonMappingException e) {
			e.printStackTrace();
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		
	}

	
	
		
}
