package com.dawnpro.vipservice;

import com.dawnpro.commons.CommonDictionary;
import com.dawnpro.commons.SystemParam;
import com.dawnpro.commons.util.MapUtil;
import com.dawnpro.commons.util.StringTools;
import com.dawnpro.commons.util.db.DAOException;
import com.dawnpro.commons.util.db.TransactionException;
import com.dawnpro.core.exception.AppException;
import com.dawnpro.core.log.Loggers;
import com.dawnpro.service.BaseServer;

import info.vidc.service.ugardianupdate.UGardianUpdateServiceProxy;

import java.rmi.RemoteException;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;

public class BzduidManage extends BaseServer {
  public List getObjectByid(Map paramMap) throws AppException {
    List resList = new ArrayList();
    try {
      resList = this.dao.queryForList(paramMap, "1", this.page);
    } catch (Exception e) {
      throw new AppException(1, "SYS-0027", null, e);
    } 
    return resList;
  }
  
  public List searchList(Map paramMap) throws AppException {
    String tempsql = MapUtil.getMapValue(paramMap, "tempsql", "");
    String factory = MapUtil.getMapValue(paramMap, "factory", "");
    String sql = "select * from (SELECT T.ID, D.DEPT_NAME  AS deptname, T.USER_ID, T.USER_NAME username, T.UDID, T.REFRSH,T.GETRQ, T.WRITERQ, T.ZSH, T.MAC, T.SFQY,t.ts_factory,U.user_id usercode "
    		+ "FROM TBL_BZDUID   T,TS_DEPT_INFO D, TS_USER_INFO U, TS_USER_POST UP "
    		+ "WHERE UP.USERID = U.ID AND UP.DEPTID = D.ID AND T.USER_ID = U.ID AND T.DELETEFLAG = '0' AND D.DELETEFLAG = '0' AND U.DELETEFLAG = '0' AND UP.DELETEFLAG = '0' and t.ts_factory='" + 
      
      factory + "') xx where 1=1 " + tempsql;
    List resList = new ArrayList();
    try {
      resList = this.dao.queryForList(sql, this.page);
    } catch (Exception e) {
      throw new AppException(1, "SYS-0027", null, e);
    } 
    return resList;
  }
  
  public String add(List vo) throws AppException {
    String returnstr = null;
    if (vo == null) {
		return null;
	} 
    returnstr = this.dao.insert(vo);
    return returnstr;
  }
  
  public String update(List vo) throws AppException {
    String returnstr = null;
    if (vo == null) {
		return null;
	} 
    boolean ret = this.dao.update(vo);
    if (ret) {
		returnstr = "ok";
	} 
    return returnstr;
  }
  
  public String cancell(Map paramMap) throws AppException {
    String id = MapUtil.getMapValue(paramMap, "id", "");
    String returnstr = null;
    String sql = "update tbl_bzduid set deleteflag='1',updatetime=now() where id = '" + 
      id + "'";
    try {
      this.dao.execute(sql);
    } catch (DAOException e) {
      e.printStackTrace();
      throw new AppException(1, "SYS-1003", id, e);
    } catch (TransactionException e) {
      e.printStackTrace();
      throw new AppException(1, "SYS-1003", id, e);
    } 
    return returnstr = "ok";
  }
  
  public List checkUserId(String userid) throws AppException {
    String sql = "SELECT COUNT(*) as num FROM TBL_BZDUID WHERE user_id='" + 
      userid + "' AND DELETEflag='0'";
    List resList = new ArrayList();
    try {
      resList = this.dao.queryForList(sql);
    } catch (Exception e) {
      throw new AppException(1, "SYS-0027", null, e);
    } 
    return resList;
  }
  
  
  public String changeSystemparamValue(Map paramMap) throws AppException {
	  try {
			String keyname = MapUtil.getRealValue(paramMap, "keyname", "");
			String keyValue = MapUtil.getRealValue(paramMap, "keyvalue", "");
			String sql = "update TS_SYSTEMPARAM set keyvalue='" + keyValue.trim()
					+ "' where deleteflag='0' and status='1' and  upper(keyname)=upper('"+keyname.trim()+"')";
			this.dao.executeUpdate(sql);
			SystemParam.reLoad();
			return "ok";
		} catch (DAOException var4) {
			var4.printStackTrace();
			throw new AppException(1, "SYS-0027", (String) null, var4);
		} catch (TransactionException var5) {
			var5.printStackTrace();
			throw new AppException(1, "SYS-0027", (String) null, var5);
		}
  }
  
  /**
   * 该用户是否已有部门和岗位
   * @param paramMap
   * @return
   * @throws AppException
   */
  public String checkUserhasDept(Map paramMap) throws AppException {
	    String id = MapUtil.getMapValue(paramMap, "id", "");
	    String returnstr = "fail";
	    String sql = "select 1 from  TS_DEPT_INFO D,TS_USER_INFO U,TS_USER_POST UP where D.DELETEFLAG = '0' AND UP.DELETEFLAG = '0' AND U.DELETEFLAG = '0' \r\n" + 
	    		"	AND UP.DEPTID = D.ID AND UP.USERID = U.ID AND U.ID = '" + 
	      id + "'";
	    int i = 0;
	    try {
	      i=this.dao.executeToInt(sql);
	    } catch (DAOException e) {
	      e.printStackTrace();
	      throw new AppException(1, "SYS-1003", id, e);
	    } 
	    if(i>0) {
	    	returnstr="ok";
	    }
	    
	    return returnstr;
	  }
	/**
	 * 通过定时任务更新U盾证书号
	 * @throws RemoteException 
	 * @throws info.vidc.service.ugardianupdate.Exception 
	 */
	public String callServiceUdidUpdate()throws info.vidc.service.ugardianupdate.Exception, RemoteException {
		Loggers.INTERFACE.info("00-U盾定时更新接口--开始");
		String xml="";
		UGardianUpdateServiceProxy service = new UGardianUpdateServiceProxy();
		
		String sql = "SELECT T.REFRSH_ACCOUNT USERNAME, Q.YCMM PASSWORD, T.UDID 	FROM TBL_BZDUID T, TBL_QYMC Q  WHERE T.REFRSH_ACCOUNT = Q.YCYH	AND T.DELETEFLAG = '0' AND t.REFRSH=DATE_FORMAT(NOW(), '%d')+0";
		String opKey = "0b3d852f-061f-4348-8844-763b26eac33e";
		
		int i = 1;
		try {
		    List<Map> tempList = this.dao.queryForList(sql);
		    if (tempList != null && tempList.size() > 0) {
		    	for (int j = 0; j < tempList.size(); j++) {
						if (i % 50 == 0) {
							Thread.sleep(10000);
						} 
			    		Map mapa = tempList.get(j);
			            Map<Object, Object> tempMap = new HashMap<Object, Object>();
						String username =MapUtil.getMapValue(mapa, "username", "");
						String password =MapUtil.getMapValue(mapa, "password", "");
						String uKey =MapUtil.getMapValue(mapa, "udid", "");
						Loggers.INTERFACE.info("00-U盾定时更新接口--"+"更新U盾["+uKey+"]");
						xml = service.UGardianUpdate(username, password, uKey, opKey);
						Loggers.INTERFACE.info("00-U盾定时更新接口--"+"更新U盾["+uKey+"]国家反馈：["+xml+"]");
						this.dao.execute("UPDATE TBL_BZDUID t SET t.ZSH='"
										+ xml
										+ "',t.GETRQ=now() WHERE t.REFRSH_ACCOUNT='"
										+ username
										+ "' AND t.UDID='"
										+ uKey
										+ "' AND t.REFRSH= DATE_FORMAT(NOW(), '%d') + 0 ");
					

					i++;
		    	}
		    }	
		} catch (DAOException e) {
			Loggers.RUNTIME.error(e.toString());
			e.printStackTrace();
		} catch (RemoteException e) {
			Loggers.RUNTIME.error(e.toString());
			e.printStackTrace();
		} catch (SQLException e) {
			Loggers.RUNTIME.error(e.toString());
			e.printStackTrace();
		}  catch (TransactionException e) {
			Loggers.RUNTIME.error(e.toString());
			e.printStackTrace();
		} catch (InterruptedException e) {
			Loggers.RUNTIME.error(e.toString());
			e.printStackTrace();
		}
		Loggers.INTERFACE.info("00-U盾定时更新接口--结束");
		return "OK";
	}
	/**
	 * 手动单个更新U盾证书号
	 * 
	 * @param username
	 *            远程登录名
	 * @param password
	 *            远程密码
	 * @param uKey
	 *            U盾KEY
	 * @return
	 */
  public String callServiceUdidUpdate(Map paramMap) {
    String username = MapUtil.getMapValue(paramMap, "username", "");
    String uKey = MapUtil.getMapValue(paramMap, "ukey", "");
    UGardianUpdateServiceProxy service = new UGardianUpdateServiceProxy();
    String opKey = "0b3d852f-061f-4348-8844-763b26eac33e";
    String xml = "";
    String password ="";
    List list;
	try {
		list = this.dao.queryForList("select ycmm password from tbl_qymc where ycyh='"+username+"' and deleteflag=0");
		
		if(list.size()>0) {
			HashMap map = (HashMap) list.get(0);
			password = MapUtil.getMapValue(map, "password", "");
	    }
	    try {
	    	Loggers.PERFORMANCE.info("更新U盾["+uKey+"]");
			xml = service.UGardianUpdate(
					username, password, uKey, opKey);
			Loggers.PERFORMANCE.info("更新U盾["+uKey+"]国家反馈：["+xml+"]");

	    } catch (RemoteException e) {
	      e.printStackTrace();
	    }  
	} catch (DAOException e1) {
		Loggers.RUNTIME.error(e1.toString());
		e1.printStackTrace();
	} catch (SQLException e1) {
		Loggers.RUNTIME.error(e1.toString());
		e1.printStackTrace();
	}
    
    return xml;
  }
  
  /**
	 * 手工批量更新U盾信息
	 * @throws RemoteException 
	 * @throws info.vidc.service.ugardianupdate.Exception 
	 */
	public String pycallServiceUdidUpdate(Map paramMap) throws info.vidc.service.ugardianupdate.Exception, RemoteException {
		String xml="";
		UGardianUpdateServiceProxy service = new UGardianUpdateServiceProxy();

		String sql = "SELECT T.REFRSH_ACCOUNT USERNAME, Q.YCMM PASSWORD, T.UDID 	"
				+ "FROM TBL_BZDUID T, TBL_QYMC Q  WHERE T.REFRSH_ACCOUNT = Q.YCYH	AND T.DELETEFLAG = '0' order by getrq ";
		String opKey = "0b3d852f-061f-4348-8844-763b26eac33e";
		
		int i = 1;
		try {
		    List<Map> tempList = this.dao.queryForList(sql);
		    if (tempList != null && tempList.size() > 0) {
		    	for (int j = 0; j < tempList.size(); j++) {
						if (i % 50 == 0) {
							Thread.sleep(10000);
						} 
			    		Map mapa = tempList.get(j);
			            Map<Object, Object> tempMap = new HashMap<Object, Object>();
						String username =MapUtil.getMapValue(mapa, "username", "");
						String password =MapUtil.getMapValue(mapa, "password", "");
						String uKey =MapUtil.getMapValue(mapa, "udid", "");
						Loggers.PERFORMANCE.info("更新U盾["+uKey+"]");
						xml = service.UGardianUpdate(username, password, uKey, opKey);
						Loggers.PERFORMANCE.info("更新U盾["+uKey+"]国家反馈：["+xml+"]");
						this.dao.execute("UPDATE TBL_BZDUID t SET t.ZSH='"
										+ xml
										+ "',t.GETRQ=now() WHERE t.REFRSH_ACCOUNT='"
										+ username
										+ "' AND t.UDID='"
										+ uKey
										+ "' ");
					

					i++;
		    	}
		    }	
		} catch (DAOException e) {
			Loggers.RUNTIME.error(e.toString());
			e.printStackTrace();
		} catch (RemoteException e) {
			Loggers.RUNTIME.error(e.toString());
			e.printStackTrace();
		} catch (SQLException e) {
			Loggers.RUNTIME.error(e.toString());
			e.printStackTrace();
		}  catch (TransactionException e) {
			Loggers.RUNTIME.error(e.toString());
			e.printStackTrace();
		} catch (InterruptedException e) {
			Loggers.RUNTIME.error(e.toString());
			e.printStackTrace();
		}
		return "ok";
	}
  
  public Map getUserItem() throws AppException, SQLException {
    Map<Object, Object> map = new HashMap<Object, Object>();
    String sql = "select id,user_name,deptpost from  (SELECT u.ID,u.USER_NAME||'('||u.user_id||')' as user_name,up.DEPTID||'_'||up.POSTID AS deptpost  FROM TS_USER_INFO u,TS_USER_POST up  WHERE u.ID=up.USERID AND u.USER_STATE=0 AND u.DELETEFLAG='0' AND up.DELETEFLAG='0') aa group by id";
    ResultSet rs = null;
    try {
      List<Map> tempList = this.dao.queryForList(sql);
      if (tempList != null && tempList.size() > 0) {
    	for (int j = 0; j < tempList.size(); j++) {
    		Map mapa = tempList.get(j);
            Map<Object, Object> tempMap = new HashMap<Object, Object>();
            tempMap.put("userid", MapUtil.getMapValue(mapa, "id", ""));
            tempMap.put("username", MapUtil.getMapValue(mapa, "user_name", ""));
            map.put(MapUtil.getMapValue(mapa, "deptpost", ""), tempMap);
		}
      } 
    } catch (DAOException e) {
      throw new AppException(1, "SYS-0027", "", e);
    } finally {
      try {
        if (rs != null) {
			rs.close();
		} 
      } catch (SQLException e) {
        e.printStackTrace();
      } 
    } 
    return map;
  }
  
  public Map getPostItem() throws AppException, SQLException {
    Map<Object, Object> map = new HashMap<Object, Object>();
    String sql = "select a.dept_id,a.post_id postid,b.post_name postname,b.post_state poststate from  ts_dept_post a,ts_post_info b where b.id=a.post_id and a.deleteflag='0' and b.deleteflag='0' group by a.dept_id";
    ResultSet rs = null;
    try {
    	List<Map> tempList = this.dao.queryForList(sql);
        if (tempList != null && tempList.size() > 0) {
      	for (int j = 0; j < tempList.size(); j++) {
      		Map mapa = tempList.get(j);
        Map<Object, Object> tempMap = new HashMap<Object, Object>();
        tempMap.put("postid", MapUtil.getMapValue(mapa, "postid", ""));
        tempMap.put("postname", MapUtil.getMapValue(mapa, "postname", ""));
        tempMap.put("poststate", MapUtil.getMapValue(mapa, "poststate", ""));
        map.put(MapUtil.getMapValue(mapa, "dept_id", ""), tempMap);
      	}
      } 
    } catch (DAOException e) {
      throw new AppException(1, "SYS-0027", "", e);
    } finally {
      try {
        if (rs != null) {
			rs.close();
		} 
      } catch (SQLException e) {
        e.printStackTrace();
      } 
    } 
    return map;
  }
  
  public String buildDeptPostTree(String role, String comid) throws AppException, SQLException {
    StringBuffer treeContent = new StringBuffer("");
    Map postMap = getPostItem();
    Map userMap = getUserItem();
    if (!StringTools.isBlank(role) && role.toLowerCase().trim().indexOf("administrators") >= 0) {
      Map mape = CommonDictionary.getDictionary("DeptType");
      Iterator ite = mape.keySet().iterator();
      while (ite != null && ite.hasNext()) {
        String nodeId = ite.next().toString();
        String nodeName = mape.get(nodeId).toString();
        treeContent.append("var nodeRoot").append(nodeId).append("=new WebFXTreeItem('").append(nodeName).append("',")
          .append("\"openURL('deptinfo_list.jsp?deptidup=").append(nodeId).append("&deptType=").append(nodeId).append("')\");\n");
        treeContent.append("deptRoot.add(nodeRoot").append(nodeId).append(");\n");
        treeContent.append("nodeRoot").append(nodeId).append(".setId('").append(nodeId).append("');\n");
        treeContent.append(buildPostTreeItem(postMap, nodeId, userMap, nodeName));
        treeContent.append(buildDeptTreeItem(nodeId, postMap, userMap));
      } 
    } else {
      String sql = "select id,dept_name from ts_dept_info where deleteflag='0' and extend1='" + comid + "'";
      try {
        List<Map> tempList = this.dao.queryForList(sql);
        if (tempList != null && tempList.size() > 0) {
          Map map = tempList.get(0);
          String nodeId = MapUtil.getMapValue(map, "id", "");
          String nodeName = MapUtil.getMapValue(map, "dept_name", "");
          treeContent.append("var nodeRoot").append(nodeId).append("=new WebFXTreeItem('").append(nodeName).append("',")
            .append("\"openURL('deptinfo_list.jsp?deptidup=").append(nodeId).append("&deptType=").append(nodeId).append("')\");\n");
          treeContent.append("deptRoot.add(nodeRoot").append(nodeId).append(");\n");
          treeContent.append("nodeRoot").append(nodeId).append(".setId('").append(nodeId).append("');\n");
          treeContent.append(buildPostTreeItem(postMap, nodeId, userMap, nodeName));
          treeContent.append(buildDeptTreeItem(nodeId, postMap, userMap));
        } 
      } catch (DAOException e) {
        e.printStackTrace();
      } 
    } 
    treeContent.append("document.write(deptRoot);\n");
    return treeContent.toString();
  }
  
  public String buildDeptTreeItem(String node, Map postMap, Map userMap) throws AppException, SQLException {
    StringBuffer treeContent = new StringBuffer("");
    String sql = "select dept_id_up,id,dept_name from (select * from ts_dept_info where deleteflag='0'  order by dept_id_up,nlssort(dept_name,'NLS_SORT=SCHINESE_PINYIN_M'))  connect by prior id=dept_id_up start with dept_id_up=" + 
      
      node;
    ResultSet rs = null;
    String up_id = null;
    String id = null;
    String name = null;
    try {
        
        List<Map> tempList = this.dao.queryForList(sql);
        if (tempList != null && tempList.size() > 0) {
      	for (int j = 0; j < tempList.size(); j++) {
      		Map mapa = tempList.get(j);
      		up_id = MapUtil.getMapValue(mapa, "dept_id_up", "");
            id = MapUtil.getMapValue(mapa, "id", "");
            name = MapUtil.getMapValue(mapa, "dept_name", "");
        treeContent.append("var nodeRoot").append(id).append("=new WebFXTreeItem('").append(name).append("',")
          .append("\"openURL('deptinfo_list.jsp?deptidup=").append(id)
          .append("&deptType=").append(node).append("&deptparent=").append(up_id).append("')\");\n");
        treeContent.append("nodeRoot").append(up_id).append(".add(nodeRoot").append(id).append(");\n");
        treeContent.append("nodeRoot").append(id).append(".setId('").append(id).append("');\n");
        treeContent.append(buildPostTreeItem(postMap, id, userMap, name));
      	}
      } 
    } catch (DAOException e) {
      throw new AppException(1, "SYS-0027", "", e);
    } finally {
      try {
        if (rs != null) {
			rs.close();
		} 
      } catch (SQLException e) {
        e.printStackTrace();
      } 
    } 
    return treeContent.toString();
  }
  
  public StringBuffer buildPostTreeItem(Map postMap, String upid, Map userMap, String deptname) throws AppException, SQLException {
    StringBuffer treeContent = new StringBuffer("");
    Map tempMap = null;
    String[] postid = (String[])null;
    String[] postname = (String[])null;
    String[] poststate = (String[])null;
    String pid = null;
    String pname = null;
    if (postMap.get(upid) != null) {
      tempMap = (Map)postMap.get(upid);
      postid = MapUtil.getMapValue(tempMap, "postid", "").split(",");
      postname = MapUtil.getMapValue(tempMap, "postname", "").split(",");
      poststate = MapUtil.getMapValue(tempMap, "poststate", "").split(",");
      for (int i = 0; i < postid.length && i < postname.length; i++) {
        pid = String.valueOf(upid) + "_" + postid[i];
        pname = postname[i];
        if (poststate.length > i && "1".equals(poststate[i].trim())) {
			pname = String.valueOf(pname) + "(冻结)";
		} 
        treeContent.append("var nodeRoot").append(pid).append("=new WebFXTreeItem('").append(pname).append("',")
          .append("\"openURL('post_user_list.jsp?postId=").append(postid[i])
          .append("&deptId=").append(upid).append("')\");\n");
        treeContent.append("nodeRoot").append(upid).append(".add(nodeRoot").append(pid).append(");\n");
        treeContent.append("nodeRoot").append(pid).append(".setId('").append(pid).append("');\n");
        treeContent.append(buildUserTreeItem(userMap, pid, deptname, pname));
      } 
    } 
    return treeContent;
  }
  
  public StringBuffer buildUserTreeItem(Map userMap, String upid, String deptname, String postname) throws AppException, SQLException {
    StringBuffer treeContent = new StringBuffer("");
    Map tempMap = null;
    String[] uid = (String[])null;
    String[] uname = (String[])null;
    String pid = null;
    String pname = null;
    if (userMap.get(upid) != null) {
      tempMap = (Map)userMap.get(upid);
      uid = MapUtil.getMapValue(tempMap, "userid", "").split(",");
      uname = MapUtil.getMapValue(tempMap, "username", "").split(",");
      for (int i = 0; i < uid.length && i < uname.length; i++) {
        treeContent.append("var nodeRoot").append(uid[0]).append("=new WebFXRadioTreeItem('").append(uname[0]).append("',")
          .append("\"" + deptname + ":" + postname + "," + uid[0] + "\");");
        treeContent.append("nodeRoot").append(upid).append(".add(nodeRoot").append(uid[0]).append(");\n");
        treeContent.append("nodeRoot").append(uid[0]).append(".setId('").append(uid[0]).append("');\n");
        treeContent.append("nodeRoot").append(uid[0]).append(".onchange = doClickNodeCheckBox;");
      } 
    } 
    return treeContent;
  }
  
  /**
   * 选择待绑定U盾的用户
   * @param paramMap
   * @return
   * @throws AppException
   */
  public List getUserSelection(Map paramMap) throws AppException {
	    String factory = MapUtil.getRealValue(paramMap, "factory", "");
	    String username = MapUtil.getRealValue(paramMap, "username", "");
	    String userid = MapUtil.getRealValue(paramMap, "userid", "");
	    String sql = "SELECT * FROM TS_USER_INFO u WHERE DELETEFLAG='0' ";
	    if (!StringTools.isBlank(factory)) {
			sql = String.valueOf(sql) + " AND u.TS_FACTORY = '" + factory + "' ";
		} 
	    if (!StringTools.isBlank(username)) {
			sql = String.valueOf(sql) + " AND upper(u.user_name) like  '%" + username.toUpperCase() + "%' ";
		} 
	    if (!StringTools.isBlank(userid)) {
			sql = String.valueOf(sql) + " AND upper(u.user_id) like '%" + userid.toUpperCase() + "%' ";
		} 
	    sql = String.valueOf(sql) + "AND ID not  IN (SELECT USER_ID FROM TBL_BZDUID WHERE DELETEFLAG='0' )  order by user_name";
	    List list = new ArrayList();
	    try {
	      list = this.dao.queryForList(sql);
	    } catch (Exception e) {
	      throw new AppException(1, "SYS-1027", null, e);
	    } 
	    return list;
	  }
}
