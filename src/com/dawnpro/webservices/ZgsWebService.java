package com.dawnpro.webservices;

import java.io.IOException;
import java.util.Map;

import com.dawnpro.commons.util.MapUtil;
import com.dawnpro.commons.util.StringTools;
import com.dawnpro.core.log.Loggers;
import com.dawnpro.entity.zgs.BaseHgzcs;
import com.dawnpro.entity.zgs.BaseXcxxjccs;
import com.dawnpro.entity.zgs.Hgzinfo;
import com.dawnpro.entity.zgs.ProductData;
import com.dawnpro.entity.zgs.QueryHgzxx;
import com.dawnpro.entity.zgs.ResData;
import com.dawnpro.entity.zgs.ResHgzData;
import com.dawnpro.vipservice.zgs.ZgsManage;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializerProvider;


public class ZgsWebService{

	public String helloWorld() {
		return "Hello World";
	}

	public String uploadProductData( String jsonstr) {
		Loggers.INTERFACE.info("uploadProductData接口接收到数据："+jsonstr);
		ResData resData = new ResData();
		String rdString = "hgz_error";
		if(!StringTools.isBlank(jsonstr)) {
			ProductData info = new ProductData();
			ObjectMapper mapper = new ObjectMapper();
			try {
				info=mapper.readValue(jsonstr,ProductData.class);
				ZgsManage zManage = new ZgsManage();
				String reString = zManage.uploadProductData(info);
				
//				String reString = zgsManage.uploadProductData(info);
				if(reString.equals("0")) {
					resData.setStatus("0");
					resData.setMessage("上传成功");
				}else {
					resData.setStatus("1");
					resData.setMessage(reString);
				}
			} catch (JsonMappingException e) {
				e.printStackTrace();
				resData.setStatus("1");
				resData.setMessage("上传json转换实体报错，请检查后重新上传。");
			} catch (JsonProcessingException e) {
				e.printStackTrace();
				resData.setStatus("1");
				resData.setMessage("上传json转换实体报错，请检查后重新上传。");
			}
		}else {
			Loggers.INTERFACE.info("uploadProductData接口接收到空串数据，反馈：1上传数据为空串，请检查后重新上传。");
			resData.setStatus("1");
			resData.setMessage("上传数据为空串，请检查后重新上传。");
		}
		
		ObjectMapper remapper = new ObjectMapper();
		try {
			rdString=remapper.writeValueAsString(resData);
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		Loggers.INTERFACE.info("uploadProductData接口反馈："+rdString);
		return rdString;
	}
	

	public String uploadBaseHgzcs(String jsonstr) {
		Loggers.INTERFACE.info("uploadBaseHgzcs接口接收到数据："+jsonstr);
		ResData resData = new ResData();
		String rdString = "hgz_error";
		if(!StringTools.isBlank(jsonstr)) {
			BaseHgzcs info = new BaseHgzcs();
//			ObjectMapper mapper = new ObjectMapper();

			ObjectMapper mapper = new ObjectMapper();
			// 这里我们重写了serialize方式把null替换为""
			mapper.getSerializerProvider().setNullValueSerializer(new JsonSerializer<Object>() {
				public void serialize(Object value, JsonGenerator gen, SerializerProvider serializers)
						throws IOException, JsonProcessingException {
					gen.writeString("");
				}
			});

			try {
				info=mapper.readValue(jsonstr,BaseHgzcs.class);
				ZgsManage zManage = new ZgsManage();
				String reString = zManage.uploadBaseHgzcs(info);
				if(reString.equals("0")) {
					resData.setStatus("0");
					resData.setMessage("上传成功");
				}else {
					resData.setStatus("1");
					resData.setMessage(reString);
				}
			} catch (JsonMappingException e) {
				e.printStackTrace();
				resData.setStatus("1");
				resData.setMessage("上传json转换实体报错，请检查后重新上传。");
			} catch (JsonProcessingException e) {
				e.printStackTrace();
				resData.setStatus("1");
				resData.setMessage("上传json转换实体报错，请检查后重新上传。");
			}
		}else {
			Loggers.INTERFACE.info("uploadBaseHgzcs接口接收到空串数据，反馈：1上传数据为空串，请检查后重新上传。");
			resData.setStatus("1");
			resData.setMessage("上传数据为空串，请检查后重新上传。");
		}
		
		ObjectMapper remapper = new ObjectMapper();
		try {
			rdString=remapper.writeValueAsString(resData);
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		Loggers.INTERFACE.info("uploadBaseHgzcs接口反馈："+rdString);
		return rdString;
	}
	

	public String getHgzinfo(String jsonstr) {
		Loggers.INTERFACE.info("getHgzinfo接口接收到数据："+jsonstr);
		ResHgzData rHgzData = new ResHgzData();
		String rdString = "hgz_error";
		if(!StringTools.isBlank(jsonstr)) {
			QueryHgzxx info = new QueryHgzxx();
			ObjectMapper mapper = new ObjectMapper();
			try {
				info=mapper.readValue(jsonstr,QueryHgzxx.class);
				ZgsManage zManage = new ZgsManage();
				Map resmap = zManage.queryHgzxx(info);
				String resflag = MapUtil.getMapValue(resmap, "flag", "1");
				String resmemo = MapUtil.getMapValue(resmap, "memo", "");
				Hgzinfo hgzinfo = new Hgzinfo();

				if(resflag.equals("0")) {
					rHgzData.setStatus("0");
					rHgzData.setMessage("获取成功");
					hgzinfo=(Hgzinfo)resmap.get("hgzinfo");
					rHgzData.setHgzinfo(hgzinfo);
				}else {
					rHgzData.setStatus("1");
					rHgzData.setMessage(resmemo);
				}
			} catch (JsonMappingException e) {
				e.printStackTrace();
				rHgzData.setStatus("1");
				rHgzData.setMessage("上传json转换实体报错，请检查后重新上传。");
			} catch (JsonProcessingException e) {
				e.printStackTrace();
				rHgzData.setStatus("1");
				rHgzData.setMessage("上传json转换实体报错，请检查后重新上传。");
			}
		}else {
			Loggers.INTERFACE.info("getHgzinfo接口接收到空串数据，反馈：上传数据为空串，请检查后重新上传。");
			rHgzData.setStatus("1");
			rHgzData.setMessage("上传数据为空串，请检查后重新上传。");
		}
		
		ObjectMapper remapper = new ObjectMapper();
		try {
			rdString=remapper.writeValueAsString(rHgzData);
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		
		return rdString;
	}
	

	public String uploadXcxxjc( String jsonstr) {
		Loggers.INTERFACE.info("uploadXcxxjc接口接收到数据："+jsonstr);
		ResData resData = new ResData();
		String rdString = "hgz_error";
		if(!StringTools.isBlank(jsonstr)) {
			BaseXcxxjccs info = new BaseXcxxjccs();
			ObjectMapper mapper = new ObjectMapper();
			try {
				info=mapper.readValue(jsonstr,BaseXcxxjccs.class);
				ZgsManage zManage = new ZgsManage();
				String reString = zManage.uploadBaseXcxxjccs(info);
				
				if(reString.equals("0")) {
					resData.setStatus("0");
					resData.setMessage("上传成功");
				}else {
					resData.setStatus("1");
					resData.setMessage(reString);
				}
				
			} catch (JsonMappingException e) {
				e.printStackTrace();
				resData.setStatus("1");
				resData.setMessage("上传json转换实体报错，请检查后重新上传。");
			} catch (JsonProcessingException e) {
				e.printStackTrace();
				resData.setStatus("1");
				resData.setMessage("上传json转换实体报错，请检查后重新上传。");
			}
		}else {
			Loggers.INTERFACE.info("uploadXcxxjc接口接收到空串数据，反馈：1上传数据为空串，请检查后重新上传。");
			resData.setStatus("1");
			resData.setMessage("上传数据为空串，请检查后重新上传。");
		}
		
		ObjectMapper remapper = new ObjectMapper();
		try {
			rdString=remapper.writeValueAsString(resData);
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		
		return rdString;
	}
	
}
