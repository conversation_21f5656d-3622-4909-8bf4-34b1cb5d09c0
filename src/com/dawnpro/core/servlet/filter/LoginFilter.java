package com.dawnpro.core.servlet.filter;

import com.dawnpro.commons.MD5;
import com.dawnpro.commons.SystemParam;
import com.dawnpro.commons.spring.ServiceFactory;
import com.dawnpro.commons.util.DateTools;
import com.dawnpro.commons.util.RequestUtil;
import com.dawnpro.commons.util.StringTools;
import com.dawnpro.core.exception.AppException;
import com.dawnpro.core.log.Loggers;
import com.dawnpro.service.commons.OnlineUsers;
import com.dawnpro.service.commons.User;
import com.dawnpro.service.sysadmin.UserInfoManage;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.Enumeration;
import java.util.List;
import java.util.Map;
import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpServletResponseWrapper;
import javax.servlet.http.HttpSession;

public class LoginFilter implements Filter {
  protected FilterConfig filterConfig;
  
  String logonStrings = "";
  
  String includeStrings = "";
  
  String redirectPath = "";
  
  String disabletestfilter = "";
  
  String encoding = "";
  
  public void init(FilterConfig config) throws ServletException {
    this.filterConfig = config;
    this.logonStrings = this.filterConfig.getInitParameter("logonStrings");
    this.encoding = this.filterConfig.getInitParameter("encoding");
    this.includeStrings = this.filterConfig.getInitParameter("includeStrings");
    this.redirectPath = this.filterConfig.getInitParameter("redirectPath");
    this.disabletestfilter = this.filterConfig.getInitParameter("disabletestfilter");
  }
  
  public void doFilter(ServletRequest req, ServletResponse res, FilterChain chain) throws IOException, ServletException {
    HttpServletRequest hreq = (HttpServletRequest)req;
    HttpServletResponse hres = (HttpServletResponse)res;
    
  //如果为WS服务，则直接访问 updated by chenrui 20210428
	String respath=hreq.getContextPath()+hreq.getServletPath();
	if(respath.indexOf("/services/ZgsWebService")>=0){
		chain.doFilter(hreq, hres);
		return;
	}else {
		return;
	}
	
   
  }
  
  public void destroy() {
    this.filterConfig = null;
  }
  
  public void setFilterConfig(FilterConfig filterConfig) {
    this.filterConfig = filterConfig;
  }
  
  private boolean isContainsSuffix(String container, String[] regx) {
    boolean result = false;
    if (StringTools.isBlank(container))
      container = "welcome.jsp"; 
    int nIndex = container.indexOf('.');
    if (nIndex == -1)
      return true; 
    container = container.replaceFirst("/", "");
    String suffix = "." + container.substring(nIndex);
    for (int i = 0; i < regx.length; i++) {
      if (suffix.toUpperCase().equals(regx[i].toUpperCase()))
        return true; 
    } 
    return result;
  }
  
  private boolean isContainsPages(String container, String[] regx) {
    boolean result = false;
    container = container.replaceFirst("/", "");
    for (int j = 0; j < regx.length; j++) {
      if (container.toUpperCase().equals(regx[j].toUpperCase()))
        return true; 
    } 
    return result;
  }
  
  private String getIpAddr(HttpServletRequest request) {
    String ip = request.getHeader("x-forwarded-for");
    if (ip == null || ip.length() == 0)
      ip = request.getRemoteAddr(); 
    return ip;
  }
}
