package com.dawnpro.entity.wxxxgk;

public class WxxxgkHgzinfo {
	private String productNm;
	private String factoryCode;
	private String brandNm;
	private String carModel;
	private String vinCode;
	private String firstPrintDate;
	private String carType;
	private String shapeLength;
	private String shapeWidth;
	private String shapeHigh;
	private String axleNum;
	private String axleSpread;
	private String frontTrack;
	private String rearTrack;
	private String totalQty;
	private String curbQty;
	private String ratedPassenger;
	private String fuelType;
	private String engineCode;
	private String engineDisplacement;
	private String frontTireModel;
	private String rearTireModel;
	private String tireNum;
	private String tireCode;
	public String getProductNm() {
		return productNm;
	}
	public void setProductNm(String productNm) {
		this.productNm = productNm;
	}
	public String getFactoryCode() {
		return factoryCode;
	}
	public void setFactoryCode(String factoryCode) {
		this.factoryCode = factoryCode;
	}
	
	public String getBrandNm() {
		return brandNm;
	}
	public void setBrandNm(String brandNm) {
		this.brandNm = brandNm;
	}
	public String getCarModel() {
		return carModel;
	}
	public void setCarModel(String carModel) {
		this.carModel = carModel;
	}
	public String getVinCode() {
		return vinCode;
	}
	public void setVinCode(String vinCode) {
		this.vinCode = vinCode;
	}
	public String getFirstPrintDate() {
		return firstPrintDate;
	}
	public void setFirstPrintDate(String firstPrintDate) {
		this.firstPrintDate = firstPrintDate;
	}
	public String getCarType() {
		return carType;
	}
	public void setCarType(String carType) {
		this.carType = carType;
	}
	public String getShapeLength() {
		return shapeLength;
	}
	public void setShapeLength(String shapeLength) {
		this.shapeLength = shapeLength;
	}
	public String getShapeWidth() {
		return shapeWidth;
	}
	public void setShapeWidth(String shapeWidth) {
		this.shapeWidth = shapeWidth;
	}
	public String getShapeHigh() {
		return shapeHigh;
	}
	public void setShapeHigh(String shapeHigh) {
		this.shapeHigh = shapeHigh;
	}
	public String getAxleNum() {
		return axleNum;
	}
	public void setAxleNum(String axleNum) {
		this.axleNum = axleNum;
	}
	public String getAxleSpread() {
		return axleSpread;
	}
	public void setAxleSpread(String axleSpread) {
		this.axleSpread = axleSpread;
	}
	public String getFrontTrack() {
		return frontTrack;
	}
	public void setFrontTrack(String frontTrack) {
		this.frontTrack = frontTrack;
	}
	public String getRearTrack() {
		return rearTrack;
	}
	public void setRearTrack(String rearTrack) {
		this.rearTrack = rearTrack;
	}
	public String getTotalQty() {
		return totalQty;
	}
	public void setTotalQty(String totalQty) {
		this.totalQty = totalQty;
	}
	public String getCurbQty() {
		return curbQty;
	}
	public void setCurbQty(String curbQty) {
		this.curbQty = curbQty;
	}
	public String getRatedPassenger() {
		return ratedPassenger;
	}
	public void setRatedPassenger(String ratedPassenger) {
		this.ratedPassenger = ratedPassenger;
	}
	public String getFuelType() {
		return fuelType;
	}
	public void setFuelType(String fuelType) {
		this.fuelType = fuelType;
	}
	public String getEngineCode() {
		return engineCode;
	}
	public void setEngineCode(String engineCode) {
		this.engineCode = engineCode;
	}
	public String getEngineDisplacement() {
		return engineDisplacement;
	}
	public void setEngineDisplacement(String engineDisplacement) {
		this.engineDisplacement = engineDisplacement;
	}
	public String getFrontTireModel() {
		return frontTireModel;
	}
	public void setFrontTireModel(String frontTireModel) {
		this.frontTireModel = frontTireModel;
	}
	public String getRearTireModel() {
		return rearTireModel;
	}
	public void setRearTireModel(String rearTireModel) {
		this.rearTireModel = rearTireModel;
	}
	public String getTireNum() {
		return tireNum;
	}
	public void setTireNum(String tireNum) {
		this.tireNum = tireNum;
	}
	public String getTireCode() {
		return tireCode;
	}
	public void setTireCode(String tireCode) {
		this.tireCode = tireCode;
	}
	
	
}
