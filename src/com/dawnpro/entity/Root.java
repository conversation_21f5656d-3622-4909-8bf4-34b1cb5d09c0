package com.dawnpro.entity;

import com.alibaba.fastjson.annotation.JSONField;

public class Root {
	@JSONField(ordinal=1)
	private VehicleData vehicleData;
	@JSONField(ordinal=2)
	private EnvData envData;
	@JSONField(ordinal=3)
	private TestData testData;
	@JSONField(ordinal=4)
	private ProcessData processData;
	@JSONField(ordinal=5)
	private ResultData resultData;
	@JSONField(ordinal=6)
	private EquipmentData equipmentData;
	@JSONField(ordinal=7)
	private String sign;
	@JSONField(ordinal=8)
	private String token;
	
	public Root() {
		// TODO Auto-generated constructor stub
	}	
	
	public Root(VehicleData vehicleData, EnvData envData, TestData testData, ProcessData processData,
			ResultData resultData, EquipmentData equipmentData, String token, String sign) {
		super();
		this.vehicleData = vehicleData;
		this.envData = envData;
		this.testData = testData;
		this.processData = processData;
		this.resultData = resultData;
		this.equipmentData = equipmentData;
		this.token = token;
		this.sign = sign;
	}

	public VehicleData getVehicleData() {
		return vehicleData;
	}

	public void setVehicleData(VehicleData vehicleData) {
		this.vehicleData = vehicleData;
	}

	public EnvData getEnvData() {
		return envData;
	}

	public void setEnvData(EnvData envData) {
		this.envData = envData;
	}

	public TestData getTestData() {
		return testData;
	}

	public void setTestData(TestData testData) {
		this.testData = testData;
	}

	public ProcessData getProcessData() {
		return processData;
	}

	public void setProcessData(ProcessData processData) {
		this.processData = processData;
	}

	public ResultData getResultData() {
		return resultData;
	}

	public void setResultData(ResultData resultData) {
		this.resultData = resultData;
	}

	public EquipmentData getEquipmentData() {
		return equipmentData;
	}

	public void setEquipmentData(EquipmentData equipmentData) {
		this.equipmentData = equipmentData;
	}

	public String getToken() {
		return token;
	}

	public void setToken(String token) {
		this.token = token;
	}

	public String getSign() {
		return sign;
	}

	public void setSign(String sign) {
		this.sign = sign;
	}

}
