package com.dawnpro.entity.bsjx;

public class BsjxHgzinfo {
	private String	opertype;
	private String	hgzxh;
	private String	wzhgzbh;
	private String	fzrq;
	private String	clzzqymc;
	private String	clpp;
	private String	clmc;
	private String	clxh;
	private String	vin;
	private String	dpid;
	private String	dphgzbh;
	private String	fdjxh;
	private String	fdjh;
	private String	cph;
	private String	pc;
	private String	ggsxrq;
	private String	cddbj;
	public String getOpertype() {
		return opertype;
	}
	public void setOpertype(String opertype) {
		this.opertype = opertype;
	}
	public String getHgzxh() {
		return hgzxh;
	}
	public void setHgzxh(String hgzxh) {
		this.hgzxh = hgzxh;
	}
	public String getWzhgzbh() {
		return wzhgzbh;
	}
	public void setWzhgzbh(String wzhgzbh) {
		this.wzhgzbh = wzhgzbh;
	}
	public String getFzrq() {
		return fzrq;
	}
	public void setFzrq(String fzrq) {
		this.fzrq = fzrq;
	}
	public String getClzzqymc() {
		return clzzqymc;
	}
	public void setClzzqymc(String clzzqymc) {
		this.clzzqymc = clzzqymc;
	}
	public String getClpp() {
		return clpp;
	}
	public void setClpp(String clpp) {
		this.clpp = clpp;
	}
	public String getClmc() {
		return clmc;
	}
	public void setClmc(String clmc) {
		this.clmc = clmc;
	}
	public String getClxh() {
		return clxh;
	}
	public void setClxh(String clxh) {
		this.clxh = clxh;
	}
	public String getVin() {
		return vin;
	}
	public void setVin(String vin) {
		this.vin = vin;
	}
	public String getDpid() {
		return dpid;
	}
	public void setDpid(String dpid) {
		this.dpid = dpid;
	}
	public String getDphgzbh() {
		return dphgzbh;
	}
	public void setDphgzbh(String dphgzbh) {
		this.dphgzbh = dphgzbh;
	}
	public String getFdjxh() {
		return fdjxh;
	}
	public void setFdjxh(String fdjxh) {
		this.fdjxh = fdjxh;
	}
	public String getFdjh() {
		return fdjh;
	}
	public void setFdjh(String fdjh) {
		this.fdjh = fdjh;
	}
	public String getCph() {
		return cph;
	}
	public void setCph(String cph) {
		this.cph = cph;
	}
	public String getPc() {
		return pc;
	}
	public void setPc(String pc) {
		this.pc = pc;
	}
	public String getGgsxrq() {
		return ggsxrq;
	}
	public void setGgsxrq(String ggsxrq) {
		this.ggsxrq = ggsxrq;
	}
	public String getCddbj() {
		return cddbj;
	}
	public void setCddbj(String cddbj) {
		this.cddbj = cddbj;
	}
	
}
