package com.dawnpro.entity;

import java.util.List;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * A.4 检测过程数据
 * <AUTHOR>
 *
 */
public class ProcessData {
	@JSONField(ordinal=1)
	private String obd;
	@J<PERSON><PERSON>ield(ordinal=2)
	private Double odo;
	@JSO<PERSON>ield(ordinal=3)
	private List<ModuleData> moduleData ;

	public ProcessData() {
		// TODO Auto-generated constructor stub
	}

	public ProcessData(String obd, Double odo, List<ModuleData> moduleData) {
		super();
		this.obd = obd;
		this.odo = odo;
		this.moduleData = moduleData;
	}

	public String getObd() {
		return obd;
	}

	public void setObd(String obd) {
		this.obd = obd;
	}

	public Double getOdo() {
		return odo;
	}

	public void setOdo(Double odo) {
		this.odo = odo;
	}

	public List<ModuleData> getModuleData() {
		return moduleData;
	}

	public void setModuleData(List<ModuleData> moduleData) {
		this.moduleData = moduleData;
	}

	

}
