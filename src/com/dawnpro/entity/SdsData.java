package com.dawnpro.entity;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * A.5.1双怠速法
 * <AUTHOR>
 *
 */
public class SdsData {
		@JSONField(ordinal=1)
		private double hlco;
		@JSONField(ordinal=2)
		private double lrco;
		@JSONField(ordinal=3)
		private double reac;
		@JSONField(ordinal=4)
		private int lrhc;
		@JSONField(ordinal=5)
		private int hrhc;
		@JSONField(ordinal=6)
		private double hrco;
		@JSONField(ordinal=7)
		private double leacmin;
		@JSONField(ordinal=8)
		private double leacmax;
		@JSONField(ordinal=9)
		private int llhc;
		@JSONField(ordinal=10)
		private double llco;
		@JSONField(ordinal=11)
		private int hlhc;
		
		public SdsData() {
			super();
		}


		public SdsData(double hlco, double lrco, double reac, int lrhc, int hrhc, double hrco, double leacmin,
				double leacmax, int llhc, double llco, int hlhc) {
			super();
			this.hlco = hlco;
			this.lrco = lrco;
			this.reac = reac;
			this.lrhc = lrhc;
			this.hrhc = hrhc;
			this.hrco = hrco;
			this.leacmin = leacmin;
			this.leacmax = leacmax;
			this.llhc = llhc;
			this.llco = llco;
			this.hlhc = hlhc;
		}

		
		public double getHlco() {
			return hlco;
		}

		public void setHlco(double hlco) {
			this.hlco = hlco;
		}

		public double getLrco() {
			return lrco;
		}

		public void setLrco(double lrco) {
			this.lrco = lrco;
		}

		public double getReac() {
			return reac;
		}

		public void setReac(double reac) {
			this.reac = reac;
		}

		public int getLrhc() {
			return lrhc;
		}

		public void setLrhc(int lrhc) {
			this.lrhc = lrhc;
		}

		public int getHrhc() {
			return hrhc;
		}

		public void setHrhc(int hrhc) {
			this.hrhc = hrhc;
		}

		public double getHrco() {
			return hrco;
		}

		public void setHrco(double hrco) {
			this.hrco = hrco;
		}

		public double getLeacmin() {
			return leacmin;
		}

		public void setLeacmin(double leacmin) {
			this.leacmin = leacmin;
		}

		public double getLeacmax() {
			return leacmax;
		}

		public void setLeacmax(double leacmax) {
			this.leacmax = leacmax;
		}

		public int getLlhc() {
			return llhc;
		}

		public void setLlhc(int llhc) {
			this.llhc = llhc;
		}

		public double getLlco() {
			return llco;
		}

		public void setLlco(double llco) {
			this.llco = llco;
		}

		public int getHlhc() {
			return hlhc;
		}

		public void setHlhc(int hlhc) {
			this.hlhc = hlhc;
		}
		
		

}