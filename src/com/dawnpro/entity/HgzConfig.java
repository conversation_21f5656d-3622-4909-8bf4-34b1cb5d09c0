/**
 * 
 */
package com.dawnpro.entity;

import java.io.Serializable;

import com.dawnpro.commons.Entity;

/**
 * <p>Title: 合格证配置信息Bean</p>
 * <p>Description: 合格证配置信息Bean</p>
 * <p>Copyright: Copyright dawnpro(c) 2010</p>
 * <p>Company: Dawnpro</p>
 * <AUTHOR>
 * @version 1.0
 * Created Date: 2010-6-11
 * Change History
 */
public class HgzConfig extends Entity implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = -251663344414840974L;

	private String qydm = "";		// 企业代码
	
	private String ycyh = null;		//上传用户名
	
	private String ycmm = null;		//上传用户密码
	
	private String ukey = null;		//上传U盾标识

	public HgzConfig() {
		super();
		// TODO Auto-generated constructor stub
	}

	public String getQydm() {
		return qydm;
	}

	public void setQydm(String qydm) {
		this.qydm = qydm;
	}

	public String getYcyh() {
		return ycyh;
	}

	public void setYcyh(String ycyh) {
		this.ycyh = ycyh;
	}

	public String getYcmm() {
		return ycmm;
	}

	public void setYcmm(String ycmm) {
		this.ycmm = ycmm;
	}

	public String getUkey() {
		return ukey;
	}

	public void setUkey(String ukey) {
		this.ukey = ukey;
	}

	
}
