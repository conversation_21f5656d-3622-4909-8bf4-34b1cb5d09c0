package com.dawnpro.entity;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * A.5.3简易瞬态工况法
 * <AUTHOR>
 *
 */
public class SvmassData {
	@JSONField(ordinal=1)
	private int vlnox;
	@JSONField(ordinal=2)
	private int vrhc;
	@JSONField(ordinal=3)
	private int vlco;
	@JSONField(ordinal=4)
	private int vlhc;
	@JSONField(ordinal=5)
	private int vrco;
	@JSONField(ordinal=6)
	private int vrnox;
	
	public SvmassData() {
		// TODO Auto-generated constructor stub
	}

	public int getVlnox() {
		return vlnox;
	}

	public void setVlnox(int vlnox) {
		this.vlnox = vlnox;
	}

	public int getVrhc() {
		return vrhc;
	}

	public void setVrhc(int vrhc) {
		this.vrhc = vrhc;
	}

	public int getVlco() {
		return vlco;
	}

	public void setVlco(int vlco) {
		this.vlco = vlco;
	}

	public int getVlhc() {
		return vlhc;
	}

	public void setVlhc(int vlhc) {
		this.vlhc = vlhc;
	}

	public int getVrco() {
		return vrco;
	}

	public void setVrco(int vrco) {
		this.vrco = vrco;
	}

	public int getVrnox() {
		return vrnox;
	}

	public void setVrnox(int vrnox) {
		this.vrnox = vrnox;
	}

	
}
