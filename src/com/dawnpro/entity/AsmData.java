package com.dawnpro.entity;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * A.5.2稳态工况法
 * 
 * <AUTHOR>
 *
 */
public class AsmData {
	@JSONField(ordinal = 1)
	private double alco2540;
	@JSONField(ordinal = 2)
	private double alco5025;
	@JSONField(ordinal = 3)
	private double arco5025;
	@JSONField(ordinal = 4)
	private double arco2540;
	@JSONField(ordinal = 5)
	private int alhc2540;
	@JSONField(ordinal = 6)
	private int arhc2540;
	@JSONField(ordinal = 7)
	private int alhc5025;
	@JSONField(ordinal = 8)
	private int alnox5025;
	@JSONField(ordinal = 9)
	private int arhc5025;
	@JSONField(ordinal = 10)
	private int arnox2540;
	@JSONField(ordinal = 11)
	private int arnox5025;
	@JSONField(ordinal = 12)
	private int alnox2540;

	public AsmData() {
		// TODO Auto-generated constructor stub
	}

	public AsmData(double alco2540, double alco5025, double arco5025, double arco2540, int alhc2540, int arhc2540,
			int alhc5025, int alnox5025, int arhc5025, int arnox2540, int arnox5025, int alnox2540) {
		super();
		this.alco2540 = alco2540;
		this.alco5025 = alco5025;
		this.arco5025 = arco5025;
		this.arco2540 = arco2540;
		this.alhc2540 = alhc2540;
		this.arhc2540 = arhc2540;
		this.alhc5025 = alhc5025;
		this.alnox5025 = alnox5025;
		this.arhc5025 = arhc5025;
		this.arnox2540 = arnox2540;
		this.arnox5025 = arnox5025;
		this.alnox2540 = alnox2540;
	}

	public double getAlco2540() {
		return alco2540;
	}

	public void setAlco2540(double alco2540) {
		this.alco2540 = alco2540;
	}

	public double getAlco5025() {
		return alco5025;
	}

	public void setAlco5025(double alco5025) {
		this.alco5025 = alco5025;
	}

	public double getArco5025() {
		return arco5025;
	}

	public void setArco5025(double arco5025) {
		this.arco5025 = arco5025;
	}

	public double getArco2540() {
		return arco2540;
	}

	public void setArco2540(double arco2540) {
		this.arco2540 = arco2540;
	}

	public int getAlhc2540() {
		return alhc2540;
	}

	public void setAlhc2540(int alhc2540) {
		this.alhc2540 = alhc2540;
	}

	public int getArhc2540() {
		return arhc2540;
	}

	public void setArhc2540(int arhc2540) {
		this.arhc2540 = arhc2540;
	}

	public int getAlhc5025() {
		return alhc5025;
	}

	public void setAlhc5025(int alhc5025) {
		this.alhc5025 = alhc5025;
	}

	public int getAlnox5025() {
		return alnox5025;
	}

	public void setAlnox5025(int alnox5025) {
		this.alnox5025 = alnox5025;
	}

	public int getArhc5025() {
		return arhc5025;
	}

	public void setArhc5025(int arhc5025) {
		this.arhc5025 = arhc5025;
	}

	public int getArnox2540() {
		return arnox2540;
	}

	public void setArnox2540(int arnox2540) {
		this.arnox2540 = arnox2540;
	}

	public int getArnox5025() {
		return arnox5025;
	}

	public void setArnox5025(int arnox5025) {
		this.arnox5025 = arnox5025;
	}

	public int getAlnox2540() {
		return alnox2540;
	}

	public void setAlnox2540(int alnox2540) {
		this.alnox2540 = alnox2540;
	}

}
