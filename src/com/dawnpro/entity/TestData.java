package com.dawnpro.entity;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * A.3 检测信息
 * <AUTHOR>
 *
 */
public class TestData {
	@JSONField(ordinal=1)
	private long opass;
	@JSONField(ordinal=2)
	private long result;
	@JSONField(ordinal=3)
	private String testNo;
	@JSONField(ordinal=4)
	private String ctestLocation;
	@JSONField(ordinal=5)
	private Long epass;
	@JSONField(ordinal=6)
	private String otestdate;
	@JSONField(ordinal=7)
	private Long apass;
	@JSONField(ordinal=8)
	private long testType;
	@JSONField(ordinal=9)
	private String ctest;
	@JSONField(ordinal=10)
	private String testDate;

	
	public TestData() {
		// TODO Auto-generated constructor stub
	}

	
	public TestData(long testType, String testNo, String testDate, long apass, long opass, String otestdate, long epass,
			long result,String ctest,String ctestLocation) {
		super();
		this.testType = testType;
		this.testNo = testNo;
		this.testDate = testDate;
		this.apass = apass;
		this.opass = opass;
		this.otestdate = otestdate;
		this.epass = epass;
		this.result = result;
		this.ctest=ctest;
		this.ctestLocation=ctestLocation;
	}
	/**
	 * 缺apass,epass
	 * @param testType
	 * @param testNo
	 * @param testDate
	 * @param opass
	 * @param otestdate
	 * @param epass
	 * @param result
	 * @param ctest
	 * @param ctestLocation
	 */
	public TestData(long testType, String testNo, String testDate, Long opass, String otestdate,
			long result,String ctest,String ctestLocation) {
		super();
		this.testType = testType;
		this.testNo = testNo;
		this.testDate = testDate;
		this.opass = opass;
		this.otestdate = otestdate;
		this.result = result;
		this.ctest=ctest;
		this.ctestLocation=ctestLocation;
	}

	public void setApass(Long apass) {
		this.apass = apass;
	}
	public long getTestType() {
		return testType;
	}

	public void setTestType(long testType) {
		this.testType = testType;
	}

	public String getTestNo() {
		return testNo;
	}

	public void setTestNo(String testNo) {
		this.testNo = testNo;
	}

	public String getTestDate() {
		return testDate;
	}

	public void setTestDate(String testDate) {
		this.testDate = testDate;
	}
	public long getOpass() {
		return opass;
	}

	public void setOpass(long opass) {
		this.opass = opass;
	}

	public String getOtestdate() {
		return otestdate;
	}

	public void setOtestdate(String otestdate) {
		this.otestdate = otestdate;
	}

	public Long getEpass() {
		return epass;
	}


	public void setEpass(Long epass) {
		this.epass = epass;
	}


	public long getResult() {
		return result;
	}

	public void setResult(long result) {
		this.result = result;
	}


	public String getCtestLocation() {
		return ctestLocation;
	}


	public void setCtestLocation(String ctestLocation) {
		this.ctestLocation = ctestLocation;
	}


	public String getCtest() {
		return ctest;
	}


	public void setCtest(String ctest) {
		this.ctest = ctest;
	}


	public Long getApass() {
		return apass;
	}

}
