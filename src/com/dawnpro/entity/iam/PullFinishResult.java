package com.dawnpro.entity.iam;

public class PullFinishResult {
	
//	属性名	中文名	值类型	值必须	描述
//	timestamp	时间戳	Long	Y	当前客户端系统时间截，集成客户端会自动产生
//	message	响应消息	String		只有 success=false 时，才有出错消息返回
//	success	是否成功	boolean	Y	成功--true，失败--false
//	exception	异常信息	String		只有 success=false 时，才可能有异常信息

	private boolean success;
	private String exception;
	private String message;
	private Long timestamp;
	private boolean interrupt;
	
	public boolean isSuccess() {
		return success;
	}
	public void setSuccess(boolean success) {
		this.success = success;
	}
	public String getException() {
		return exception;
	}
	public void setException(String exception) {
		this.exception = exception;
	}
	public String getMessage() {
		return message;
	}
	public void setMessage(String message) {
		this.message = message;
	}
	public Long getTimestamp() {
		return timestamp;
	}
	public void setTimestamp(Long timestamp) {
		this.timestamp = timestamp;
	}
	/**
	 * @return the interrupt
	 */
	public boolean isInterrupt() {
		return interrupt;
	}
	/**
	 * @param interrupt the interrupt to set
	 */
	public void setInterrupt(boolean interrupt) {
		this.interrupt = interrupt;
	}
	
}
