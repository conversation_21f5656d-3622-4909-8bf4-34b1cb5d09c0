package com.dawnpro.entity.iam;

public class PullFinishRequest {
	
//	属性名	中文名	值类型	值必须	描述
//	tokenId	令牌ID	String	Y	login接口生成
//	taskId	任务ID	String	Y	pullTask获取的下拉任务ID
//	success	是否成功	boolean	Y	成功--true，失败--false
//	guid	唯一标识	String		第三方系统中对象唯一标识Success=true时必须
//	message	错误消息	String		Success=false时必须
//	timestamp	时间戳	Long		当前客户端系统时间截，集成客户端会自动产生

	private String tokenId;
	private String taskId;
	private boolean success;
	private String guid;
	private String message;
	private Long timestamp;
	public String getTokenId() {
		return tokenId;
	}
	public void setTokenId(String tokenId) {
		this.tokenId = tokenId;
	}
	public String getTaskId() {
		return taskId;
	}
	public void setTaskId(String taskId) {
		this.taskId = taskId;
	}
	public boolean isSuccess() {
		return success;
	}
	public void setSuccess(boolean success) {
		this.success = success;
	}
	public String getGuid() {
		return guid;
	}
	public void setGuid(String guid) {
		this.guid = guid;
	}
	public String getMessage() {
		return message;
	}
	public void setMessage(String message) {
		this.message = message;
	}
	public Long getTimestamp() {
		return timestamp;
	}
	public void setTimestamp(Long timestamp) {
		this.timestamp = timestamp;
	}
	
}
