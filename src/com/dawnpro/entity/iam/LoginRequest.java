package com.dawnpro.entity.iam;

public class LoginRequest {
	
//	属性名	中文名	值类型	值必须	描述
//	systemCode	系统代码	String	Y	应用系统代码
//	integrationKey	集成秘钥	String	Y	应用集成密钥
//	force	强制登录	Boolean	Y	
//	timestamp	时间戳	long	Y	当前客户端系统时间截，集成客户端会自动产生

	
	private String systemCode;

	private String integrationKey;
	
	private String force;

	private String timestamp;

	public String getSystemCode() {
		return systemCode;
	}

	public void setSystemCode(String systemCode) {
		this.systemCode = systemCode;
	}

	public String getIntegrationKey() {
		return integrationKey;
	}

	public void setIntegrationKey(String integrationKey) {
		this.integrationKey = integrationKey;
	}

	public String getForce() {
		return force;
	}

	public void setForce(String force) {
		this.force = force;
	}

	public String getTimestamp() {
		return timestamp;
	}

	public void setTimestamp(String timestamp) {
		this.timestamp = timestamp;
	}
	
	
}
