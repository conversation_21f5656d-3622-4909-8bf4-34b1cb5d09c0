package com.dawnpro.entity.iam;

public class PullTaskNormalResult {
	
//	属性名	中文名	值类型	值必须	描述
//	id	对象Id	String		身份管理平台内部对象ID
//	taskId	任务Id	String		身份管理平台下拉任务ID
//	timestamp	时间戳	long		BIM当前时间错
//	effectOn	作用点	String		任务操作类型：
//	创建：CREATED
//	更新：UPDATED
//	禁用：DISABLED
//	启用：ENABLED
//	objectCode	对象代码	String		BIM中注册对象代码
//	objectType	对象类型	String		对象类型：
//	账号：TARGET_ACCOUNT
//	机构：TARGET_ORGANIZATION
//	资源：TARGET_RESOURCE
//	角色：TARGET_ROLE
//	success	是否成功	boolean	Y	成功--true，失败--false
//	data	对象数据			由objectType和objectCode决定data数据内容（账号、机构、岗位、岗位序列）。


	
	private String id;//ok
	private String taskId;//ok
	private long timestamp;//ok
	private String effectOn;//ok
	private String objectCode;//ok
	private String objectType;//ok
	private boolean success;//ok
	
	private String message;
	private String exception;
	private boolean interrupt;
	private boolean guid;
	
	public String getException() {
		return exception;
	}
	public void setException(String exception) {
		this.exception = exception;
	}
	public boolean isInterrupt() {
		return interrupt;
	}
	public void setInterrupt(boolean interrupt) {
		this.interrupt = interrupt;
	}
	public boolean isGuid() {
		return guid;
	}
	public void setGuid(boolean guid) {
		this.guid = guid;
	}
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getTaskId() {
		return taskId;
	}
	public void setTaskId(String taskId) {
		this.taskId = taskId;
	}
	public long getTimestamp() {
		return timestamp;
	}
	public void setTimestamp(long timestamp) {
		this.timestamp = timestamp;
	}
	public String getEffectOn() {
		return effectOn;
	}
	public void setEffectOn(String effectOn) {
		this.effectOn = effectOn;
	}
	public String getObjectCode() {
		return objectCode;
	}
	public void setObjectCode(String objectCode) {
		this.objectCode = objectCode;
	}
	public String getObjectType() {
		return objectType;
	}
	public void setObjectType(String objectType) {
		this.objectType = objectType;
	}
	public boolean isSuccess() {
		return success;
	}
	public void setSuccess(boolean success) {
		this.success = success;
	}
	/**
	 * @return the message
	 */
	public String getMessage() {
		return message;
	}
	/**
	 * @param message the message to set
	 */
	public void setMessage(String message) {
		this.message = message;
	}
	
	
	
}
