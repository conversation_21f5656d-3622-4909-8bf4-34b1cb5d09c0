package com.dawnpro.entity.iam;

import com.fasterxml.jackson.annotation.JsonProperty;

public class PullTaskUser {

//	"data" : {
//	"_user" : "test02",
//	"_organization" : "213",
//	"username" : "测试账户 02",
//	"password" : "password",
//	"fullname" : "Test02",
//	"isDisabled" : false,
//	"isLocked" : false,
//	"createAt" : "2016-03-16 16:38:55.000",
//	"updateAt" : "2016-03-24 20:07:22.000"
//	},
	
	@JsonProperty("hiType")
	private String hiType;
	
	@JsonProperty("iamEmpno")
	private String iamEmpno;
	
	@JsonProperty("orgCode")
	private String orgCode;
	
	@JsonProperty("mobile")
	private String mobile;
	
	@JsonProperty("email")
	private String email;
	
	@JsonProperty("idCard")
	private String idCard;
	
	@JsonProperty("startDate")
	private String startDate;
	
	@JsonProperty("endDate")
	private String endDate;
	
	@JsonProperty("isMaster")
	private boolean isMaster;
	
	@JsonProperty("isPublic")
	private boolean isPublic;
	
	@JsonProperty("isSystem")
	private boolean isSystem;
	
	@JsonProperty("_user")
	private String user;
	
	@JsonProperty("_organization")
	private String organization;
	
	private String username;
	
	private String password;
	
	private String fullname;
	
	@JsonProperty("isDisabled")
	private boolean disable;
	
	@JsonProperty("isLocked")
	private boolean isLocked;
	
	private String createAt;
	
	private String updateAt;

	public String getUser() {
		return user;
	}

	public void setUser(String user) {
		this.user = user;
	}

	public String getOrganization() {
		return organization;
	}

	public void setOrganization(String organization) {
		this.organization = organization;
	}

	public String getUsername() {
		return username;
	}

	public void setUsername(String username) {
		this.username = username;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public String getFullname() {
		return fullname;
	}

	public void setFullname(String fullname) {
		this.fullname = fullname;
	}

	public boolean isDisable() {
		return disable;
	}

	public void setDisable(boolean disable) {
		this.disable = disable;
	}

	public boolean isLocked() {
		return isLocked;
	}

	public void setLocked(boolean isLocked) {
		this.isLocked = isLocked;
	}
	

	public String getCreateAt() {
		return createAt;
	}

	public void setCreateAt(String createAt) {
		this.createAt = createAt;
	}

	public String getUpdateAt() {
		return updateAt;
	}

	public void setUpdateAt(String updateAt) {
		this.updateAt = updateAt;
	}
	
	
}
