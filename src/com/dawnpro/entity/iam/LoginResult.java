package com.dawnpro.entity.iam;

public class LoginResult {
	
//	属性名	中文名	值类型	值必须	描述
//	tokenId	令牌 ID	String	Y	必须附带 tokenId
//	systemId	系统 ID	String	Y	
//	systemCode	系统代码	String	Y	
//	systenName	系统名称	String	Y	
//	schemas	对象	Schema	Y	List<ApiObjectSchema>
//	enableSync	是否启用同步	boolean	Y	
//	enablePull	是否启用下拉	boolean	Y	
//	enablePush	是否启用上推	boolean	Y	
//	debug	调试开关	boolean	Y	
//	timestamp	时间戳	Long	Y	当前 BIM 系统时间截
//	success	是否成功	boolean	Y	
//	message	出错消息	String		只有 success=false 时，才有出错消息返回
//	exception	异常信息	Exception		只有 success=false 时，才可能有异常信息
	private String tokenId;
	private String systemId;
	private String systemCode;
	private String systenName;
	//private String schemas;
	private boolean enableSync;
	private boolean enablePull;
	private boolean enablePush;
	private boolean debug;
	private Long timestamp;
	private boolean success;
	private String message;
//	private String exception;
	public String getTokenId() {
		return tokenId;
	}
	public void setTokenId(String tokenId) {
		this.tokenId = tokenId;
	}
	public String getSystemId() {
		return systemId;
	}
	public void setSystemId(String systemId) {
		this.systemId = systemId;
	}
	public String getSystemCode() {
		return systemCode;
	}
	public void setSystemCode(String systemCode) {
		this.systemCode = systemCode;
	}
	public String getSystenName() {
		return systenName;
	}
	public void setSystenName(String systenName) {
		this.systenName = systenName;
	}
	public boolean isEnableSync() {
		return enableSync;
	}
	public void setEnableSync(boolean enableSync) {
		this.enableSync = enableSync;
	}
	public boolean isEnablePull() {
		return enablePull;
	}
	public void setEnablePull(boolean enablePull) {
		this.enablePull = enablePull;
	}
	public boolean isEnablePush() {
		return enablePush;
	}
	public void setEnablePush(boolean enablePush) {
		this.enablePush = enablePush;
	}
	public boolean isDebug() {
		return debug;
	}
	public void setDebug(boolean debug) {
		this.debug = debug;
	}
	public Long getTimestamp() {
		return timestamp;
	}
	public void setTimestamp(Long timestamp) {
		this.timestamp = timestamp;
	}
	public boolean isSuccess() {
		return success;
	}
	public void setSuccess(boolean success) {
		this.success = success;
	}
	public String getMessage() {
		return message;
	}
	public void setMessage(String message) {
		this.message = message;
	}
	
}
