package com.dawnpro.entity.iam;

public class LogoutResult {
	
//	属性名	中文名	值类型	值必须	描述
//	timestamp	时间戳	Long	Y	当前 BIM 系统时间截
//	success	是否成功	boolean	Y	成功--true，失败--false

	
	private Long timestamp;

	private boolean success;

	public Long getTimestamp() {
		return timestamp;
	}

	public void setTimestamp(Long timestamp) {
		this.timestamp = timestamp;
	}

	public boolean isSuccess() {
		return success;
	}

	public void setSuccess(boolean success) {
		this.success = success;
	}
	
		
}
