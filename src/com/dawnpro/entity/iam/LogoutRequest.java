package com.dawnpro.entity.iam;

public class LogoutRequest {
	
//	属性名	中文名	值类型	值必须	描述
//	timestamp	时间戳	Long	Y	当前客户端系统时间截，集成客户端会自动产生
//	tokenId	令牌ID	String	Y	login接口生成

	
	private String tokenId;

	private String timestamp;
	
	
	public String getTimestamp() {
		return timestamp;
	}

	public void setTimestamp(String timestamp) {
		this.timestamp = timestamp;
	}

	public String getTokenId() {
		return tokenId;
	}

	public void setTokenId(String tokenId) {
		this.tokenId = tokenId;
	}

	
	
	
}
