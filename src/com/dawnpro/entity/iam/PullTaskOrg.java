package com.dawnpro.entity.iam;

import com.fasterxml.jackson.annotation.JsonProperty;

public class PullTaskOrg {

//	{
//	    "_parent": "东风汽车集团有限公司",
//	    "_organization": null,
//	    "code": "2",
//	    "name": "东风资产管理有限公司",
//	    "fullname": "东风资产管理有限公司",
//	    "description": null,
//	    "sequence": 0,
//	    "isDisabled": false,
//	    "createAt": "2020-05-1215:20:14.020",
//	    "updateAt": "2020-05-1215:27:49.592",
//	    "parentcode": "1"
//	"mocsCode" : null,
//    "mocsParentCode" : null
//	}
	
	@JsonProperty("mocsCode")
	private String mocsCode;
	
	@JsonProperty("mocsParentCode")
	private String mocsParentCode;
	
	@JsonProperty("_parent")
	private String parent;
	
	@JsonProperty("_organization")
	private String organization;
	
	private String code;
	
	private String name;
	
	private String fullname;
	
	private String description;
	
	private long sequence;
	
	@JsonProperty("isDisabled")
	private boolean disable;
	
	private String createAt;
	
	private String updateAt;
	
	private String parentCode;

	public String getParent() {
		return parent;
	}

	public void setParent(String parent) {
		this.parent = parent;
	}

	public String getOrganization() {
		return organization;
	}

	public void setOrganization(String organization) {
		this.organization = organization;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getFullname() {
		return fullname;
	}

	public void setFullname(String fullname) {
		this.fullname = fullname;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public long getSequence() {
		return sequence;
	}

	public void setSequence(long sequence) {
		this.sequence = sequence;
	}

	public boolean isDisable() {
		return disable;
	}

	public void setDisable(boolean disable) {
		this.disable = disable;
	}
	
	public String getCreateAt() {
		return createAt;
	}

	public void setCreateAt(String createAt) {
		this.createAt = createAt;
	}

	public String getUpdateAt() {
		return updateAt;
	}

	public void setUpdateAt(String updateAt) {
		this.updateAt = updateAt;
	}

	public String getParentCode() {
		return parentCode;
	}

	public void setParentCode(String parentCode) {
		this.parentCode = parentCode;
	}

	public String getMocsCode() {
		return mocsCode;
	}

	public void setMocsCode(String mocsCode) {
		this.mocsCode = mocsCode;
	}

	public String getMocsParentCode() {
		return mocsParentCode;
	}

	public void setMocsParentCode(String mocsParentCode) {
		this.mocsParentCode = mocsParentCode;
	}
	
}
