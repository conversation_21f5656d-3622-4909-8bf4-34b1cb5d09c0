package com.dawnpro.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.dawnpro.entity.AsmData;

public class ResultData {
//  A.5.1双怠速法	
	@JSONField(ordinal=4)
	private SdsData sdsData;
//  A.5.2稳态工况法	
	@JSONField(ordinal=6)
	private AsmData asmData;
//  A.5.3简易瞬态工况法
	@JSONField(ordinal=2)
	private SvmassData svmassData;
//  A.5.4加载减速法	
	@JSONField(ordinal=3)
	private LugdownData lugdownData;
//  A.5.5自由加速法	
	@JSONField(ordinal=1)
	private FaData faData;
//  A.5.6瞬态工况法
	@JSONField(ordinal=5)
	private VmassData vmassData;
	
	public ResultData() {
		// TODO Auto-generated constructor stub
	}
	
	public ResultData(SdsData sdsData, AsmData asmData, SvmassData svmassData, LugdownData lugdownData, FaData faData,
			VmassData vmassData) {
		super();
		this.sdsData = sdsData;
		this.asmData = asmData;
		this.svmassData = svmassData;
		this.lugdownData = lugdownData;
		this.faData = faData;
		this.vmassData = vmassData;
	}

	public SdsData getSdsData() {
		return sdsData;
	}

	public void setSdsData(SdsData sdsData) {
		this.sdsData = sdsData;
	}

	public AsmData getAsmData() {
		return asmData;
	}

	public void setAsmData(AsmData asmData) {
		this.asmData = asmData;
	}

	public SvmassData getSvmassData() {
		return svmassData;
	}

	public void setSvmassData(SvmassData svmassData) {
		this.svmassData = svmassData;
	}

	public LugdownData getLugdownData() {
		return lugdownData;
	}

	public void setLugdownData(LugdownData lugdownData) {
		this.lugdownData = lugdownData;
	}

	public FaData getFaData() {
		return faData;
	}

	public void setFaData(FaData faData) {
		this.faData = faData;
	}

	public VmassData getVmassData() {
		return vmassData;
	}

	public void setVmassData(VmassData vmassData) {
		this.vmassData = vmassData;
	}

	
}
