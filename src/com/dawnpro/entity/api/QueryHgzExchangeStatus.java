package com.dawnpro.entity.api;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

public class QueryHgzExchangeStatus {
	private String factorycode;
	
	@JsonProperty("cars")
	private List<Car> carlist;

	public String getFactorycode() {
		return factorycode;
	}

	public void setFactorycode(String factorycode) {
		this.factorycode = factorycode;
	}

	public List<Car> getCarlist() {
		return carlist;
	}

	public void setCarlist(List<Car> carlist) {
		this.carlist = carlist;
	}
	
	
	
}
