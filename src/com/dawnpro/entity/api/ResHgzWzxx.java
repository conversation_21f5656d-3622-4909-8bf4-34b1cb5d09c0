package com.dawnpro.entity.api;

import com.fasterxml.jackson.annotation.JsonProperty;

import info.vidc.www.certificate.operation.CertificateInfo;

public class ResHgzWzxx {
	
	private String status;//status为反馈状态，0成功  1失败

	private String message;//status=1时，message保存失败原因
	
	@JsonProperty("certificateInfo")
	private CertificateInfo certificateInfo;//status=0时，反馈信息

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public CertificateInfo getCertificateInfo() {
		return certificateInfo;
	}

	public void setCertificateInfo(CertificateInfo certificateInfo) {
		this.certificateInfo = certificateInfo;
	}

}
