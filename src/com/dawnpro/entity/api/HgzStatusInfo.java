package com.dawnpro.entity.api;

public class HgzStatusInfo {
	
	private String status;//status为反馈状态，0成功  1失败

	private String message;//status=1时，message保存失败原因
	
	private String vin;//车辆识别代号
	private String hgzbh;//合格证编号
	private String statustime;//状态更新时间
	private String garesult;//公安反馈结果 0：未处理 1：驳回 2：通过
	private String gaaudittime;//公安反馈时间
	private String taxresult;//税务反馈结果 0：未处理 1：驳回 2：通过
	private String taxaudittime;//税务反馈时间
	
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public String getMessage() {
		return message;
	}
	public void setMessage(String message) {
		this.message = message;
	}
	public String getVin() {
		return vin;
	}
	public void setVin(String vin) {
		this.vin = vin;
	}
	public String getHgzbh() {
		return hgzbh;
	}
	public void setHgzbh(String hgzbh) {
		this.hgzbh = hgzbh;
	}
	public String getStatustime() {
		return statustime;
	}
	public void setStatustime(String statustime) {
		this.statustime = statustime;
	}
	public String getGaresult() {
		return garesult;
	}
	public void setGaresult(String garesult) {
		this.garesult = garesult;
	}
	public String getGaaudittime() {
		return gaaudittime;
	}
	public void setGaaudittime(String gaaudittime) {
		this.gaaudittime = gaaudittime;
	}
	public String getTaxresult() {
		return taxresult;
	}
	public void setTaxresult(String taxresult) {
		this.taxresult = taxresult;
	}
	public String getTaxaudittime() {
		return taxaudittime;
	}
	public void setTaxaudittime(String taxaudittime) {
		this.taxaudittime = taxaudittime;
	}
	
	
	
}
