package com.dawnpro.entity.api;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

public class ResHgzExchangeStatus {
	
	private String status;//status为反馈状态，0成功  1失败

	private String message;//status=1时，message保存失败原因
	
	@JsonProperty("hgzxx")
	private List<HgzStatusInfo> hgzstatusinfo;//status=0时，反馈信息

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public List<HgzStatusInfo> getHgzstatusinfo() {
		return hgzstatusinfo;
	}

	public void setHgzstatusinfo(List<HgzStatusInfo> hgzstatusinfo) {
		this.hgzstatusinfo = hgzstatusinfo;
	}

	

	

}
