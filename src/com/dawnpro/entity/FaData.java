package com.dawnpro.entity;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * A.5.5自由加速法
 * <AUTHOR>
 *
 */
public class FaData {
	@JSONField(ordinal=5)
	private int rateRev;
	@JSONField(ordinal=1)
	private int rev;
	@JSONField(ordinal=7)
	private double smokeK1;
	@JSONField(ordinal=6)
	private double smokeK2;
	@JSONField(ordinal=4)
	private double smokeK3;
	@JSONField(ordinal=3)
	private double smokeAvg;
	@JSONField(ordinal=2)
	private double smokeKLimit;

	public FaData() {
		// TODO Auto-generated constructor stub
	}

	public FaData(int rateRev, int rev, double smokeK1, double smokeK2, double smokeK3, double smokeAvg,
			double smokeKLimit) {
		super();
		this.rateRev = rateRev;
		this.rev = rev;
		this.smokeK1 = smokeK1;
		this.smokeK2 = smokeK2;
		this.smokeK3 = smokeK3;
		this.smokeAvg = smokeAvg;
		this.smokeKLimit = smokeKLimit;
	}



	public int getRateRev() {
		return rateRev;
	}

	public void setRateRev(int rateRev) {
		this.rateRev = rateRev;
	}

	public int getRev() {
		return rev;
	}

	public void setRev(int rev) {
		this.rev = rev;
	}

	public double getSmokeK1() {
		return smokeK1;
	}

	public void setSmokeK1(double smokeK1) {
		this.smokeK1 = smokeK1;
	}

	public double getSmokeK2() {
		return smokeK2;
	}

	public void setSmokeK2(double smokeK2) {
		this.smokeK2 = smokeK2;
	}

	public double getSmokeK3() {
		return smokeK3;
	}

	public void setSmokeK3(double smokeK3) {
		this.smokeK3 = smokeK3;
	}

	public double getSmokeAvg() {
		return smokeAvg;
	}

	public void setSmokeAvg(double smokeAvg) {
		this.smokeAvg = smokeAvg;
	}

	public double getSmokeKLimit() {
		return smokeKLimit;
	}

	public void setSmokeKLimit(double smokeKLimit) {
		this.smokeKLimit = smokeKLimit;
	}

	
}
