package com.dawnpro.entity;

import com.alibaba.fastjson.annotation.JSONField;

/**
 *  A.5.6瞬态工况法
 * <AUTHOR>
 *
 */
public class VmassData {
	@JSONField(ordinal=3)
	private int vrco;
	@JSONField(ordinal=2)
	private int vlco;
	@JSONField(ordinal=4)
	private int vrhcnox;
	@JSONField(ordinal=1)
	private int vlhcnox;

	public VmassData() {
		// TODO Auto-generated constructor stub
	}

	public int getVrco() {
		return vrco;
	}

	public void setVrco(int vrco) {
		this.vrco = vrco;
	}

	public int getVlco() {
		return vlco;
	}

	public void setVlco(int vlco) {
		this.vlco = vlco;
	}

	public int getVrhcnox() {
		return vrhcnox;
	}

	public void setVrhcnox(int vrhcnox) {
		this.vrhcnox = vrhcnox;
	}

	public int getVlhcnox() {
		return vlhcnox;
	}

	public void setVlhcnox(int vlhcnox) {
		this.vlhcnox = vlhcnox;
	}

	
}
