package com.dawnpro.entity;

import com.alibaba.fastjson.annotation.JSO<PERSON>ield;

public class ModuleData {
	@JSONField(ordinal=1)
	private String calID;
	@JSONField(ordinal=2)
	private String cvn;
	@JSONField(ordinal=3)
	private String moduleID;
	
	public ModuleData() {
		// TODO Auto-generated constructor stub
	}

	
	
	public ModuleData(String moduleID, String calID, String cvn) {
		super();
		this.moduleID = moduleID;
		this.calID = calID;
		this.cvn = cvn;
	}



	public String getModuleID() {
		return moduleID;
	}

	public void setModuleID(String moduleID) {
		this.moduleID = moduleID;
	}

	public String getCalID() {
		return calID;
	}

	public void setCalID(String calID) {
		this.calID = calID;
	}

	public String getCvn() {
		return cvn;
	}

	public void setCvn(String cvn) {
		this.cvn = cvn;
	}

}
