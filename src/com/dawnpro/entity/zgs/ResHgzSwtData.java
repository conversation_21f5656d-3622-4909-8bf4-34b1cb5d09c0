package com.dawnpro.entity.zgs;

import com.fasterxml.jackson.annotation.JsonProperty;

public class ResHgzSwtData {
	
	private String status;//status为反馈状态，0成功  1失败

	private String message;//status=1时，message保存失败原因
	
	@JsonProperty("hgzxx")
	private HgzSwtinfo hgzinfo;//status=0时，反馈信息

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

    public HgzSwtinfo getHgzinfo() {
        return hgzinfo;
    }

    public void setHgzinfo(HgzSwtinfo hgzinfo) {
        this.hgzinfo = hgzinfo;
    }
}
