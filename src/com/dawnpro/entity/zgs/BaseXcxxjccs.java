package com.dawnpro.entity.zgs;

import java.io.Serializable;

public class BaseXcxxjccs implements Serializable {

	private static final long serialVersionUID = 1L;

	private String dph;
	private String factorycode;
	private String rllx;
	private String testno;
	private String alltestdate;
	private String sb;
	private String sccdz;
	private String pfjd;
	private String rygjxt;
	private String edzs;
	private String bsq;
	private String chzhqxh;
	private String jzzl;
	private String qgs;
	private String rygjfs;
	private String ddjxh;
	private String cnzzxh;
	private String dcrl;
	private String obdjkwz;
	private String pqhcllx;
	private String pqhclxh;
	private String apass;
	private String obdtx;
	private String obd;
	private String odo;
	private String fdjmoduleid;
	private String fdjcalid;
	private String fdjcvn;
	private String hclmoduleid;
	private String hclcalid;
	private String hclcvn;
	private String othermoduleid;
	private String othercalid;
	private String othercvn;
	private String opass;
	private String otestdate;
	private String obdinspector;
	private String rh;
	private String et;
	private String ap;
	private String testtype;
	private String testdate;
	private String analymodel;
	private String analymanuf;
	private String analyname;
	private String analydate;
	private String dynomanuf;
	private String dynomodel;
	private String svrco;
	private String svlco;
	private String vrhcnox;
	private String vlhcnox;
	private String vrhc;
	private String vlhc;
	private String vrco;
	private String vlco;
	private String vrnox;
	private String vlnox;
	private String arhc;
	private String alhc;
	private String arco;
	private String alco;
	private String arnox;
	private String alnox;
	private String arhc5025;
	private String alhc5025;
	private String arco5025;
	private String alco5025;
	private String arnox5025;
	private String alnox5025;
	private String arhc2540;
	private String alhc2540;
	private String arco2540;
	private String alco2540;
	private String arnox2540;
	private String alnox2540;
	private String leacmax;
	private String leacmin;
	private String reac;
	private String lrco;
	private String llco;
	private String lrhc;
	private String llhc;
	private String hrco;
	private String hlco;
	private String hrhc;
	private String hlhc;
	private String raterev;
	private String rev;
	private String smokek1;
	private String smokek2;
	private String smokek3;
	private String smokeavg;
	private String smokeklimit;
	private String raterevup;
	private String raterevdown;
	private String rev100;
	private String maxpower;
	private String maxpowerlimit;
	private String smoke100;
	private String smoke80;
	private String smokelimit;
	private String nox;
	private String noxlimit;
	private String epass;
	private String testinspector;
	private String ctest;
	private String testlocation;
	private String result;
	private String fdjsccdz;

	public String getDph() {
		return dph;
	}

	public void setDph(String dph) {
		this.dph = dph;
	}

	public String getFactorycode() {
		return factorycode;
	}

	public void setFactorycode(String factorycode) {
		this.factorycode = factorycode;
	}

	public String getRllx() {
		return rllx;
	}

	public void setRllx(String rllx) {
		this.rllx = rllx;
	}

	public String getTestno() {
		return testno;
	}

	public void setTestno(String testno) {
		this.testno = testno;
	}

	public String getAlltestdate() {
		return alltestdate;
	}

	public void setAlltestdate(String alltestdate) {
		this.alltestdate = alltestdate;
	}

	public String getSb() {
		return sb;
	}

	public void setSb(String sb) {
		this.sb = sb;
	}

	public String getSccdz() {
		return sccdz;
	}

	public void setSccdz(String sccdz) {
		this.sccdz = sccdz;
	}

	public String getPfjd() {
		return pfjd;
	}

	public void setPfjd(String pfjd) {
		this.pfjd = pfjd;
	}

	public String getRygjxt() {
		return rygjxt;
	}

	public void setRygjxt(String rygjxt) {
		this.rygjxt = rygjxt;
	}

	public String getEdzs() {
		return edzs;
	}

	public void setEdzs(String edzs) {
		this.edzs = edzs;
	}

	public String getBsq() {
		return bsq;
	}

	public void setBsq(String bsq) {
		this.bsq = bsq;
	}

	public String getChzhqxh() {
		return chzhqxh;
	}

	public void setChzhqxh(String chzhqxh) {
		this.chzhqxh = chzhqxh;
	}

	public String getJzzl() {
		return jzzl;
	}

	public void setJzzl(String jzzl) {
		this.jzzl = jzzl;
	}

	public String getQgs() {
		return qgs;
	}

	public void setQgs(String qgs) {
		this.qgs = qgs;
	}

	public String getRygjfs() {
		return rygjfs;
	}

	public void setRygjfs(String rygjfs) {
		this.rygjfs = rygjfs;
	}

	public String getDdjxh() {
		return ddjxh;
	}

	public void setDdjxh(String ddjxh) {
		this.ddjxh = ddjxh;
	}

	public String getCnzzxh() {
		return cnzzxh;
	}

	public void setCnzzxh(String cnzzxh) {
		this.cnzzxh = cnzzxh;
	}

	public String getDcrl() {
		return dcrl;
	}

	public void setDcrl(String dcrl) {
		this.dcrl = dcrl;
	}

	public String getObdjkwz() {
		return obdjkwz;
	}

	public void setObdjkwz(String obdjkwz) {
		this.obdjkwz = obdjkwz;
	}

	public String getPqhcllx() {
		return pqhcllx;
	}

	public void setPqhcllx(String pqhcllx) {
		this.pqhcllx = pqhcllx;
	}

	public String getPqhclxh() {
		return pqhclxh;
	}

	public void setPqhclxh(String pqhclxh) {
		this.pqhclxh = pqhclxh;
	}

	public String getApass() {
		return apass;
	}

	public void setApass(String apass) {
		this.apass = apass;
	}

	public String getObdtx() {
		return obdtx;
	}

	public void setObdtx(String obdtx) {
		this.obdtx = obdtx;
	}

	public String getObd() {
		return obd;
	}

	public void setObd(String obd) {
		this.obd = obd;
	}
	
	public String getOdo() {
		return odo;
	}

	public void setOdo(String odo) {
		this.odo = odo;
	}

	public String getFdjmoduleid() {
		return fdjmoduleid;
	}

	public void setFdjmoduleid(String fdjmoduleid) {
		this.fdjmoduleid = fdjmoduleid;
	}

	public String getFdjcalid() {
		return fdjcalid;
	}

	public void setFdjcalid(String fdjcalid) {
		this.fdjcalid = fdjcalid;
	}

	public String getFdjcvn() {
		return fdjcvn;
	}

	public void setFdjcvn(String fdjcvn) {
		this.fdjcvn = fdjcvn;
	}

	public String getHclmoduleid() {
		return hclmoduleid;
	}

	public void setHclmoduleid(String hclmoduleid) {
		this.hclmoduleid = hclmoduleid;
	}

	public String getHclcalid() {
		return hclcalid;
	}

	public void setHclcalid(String hclcalid) {
		this.hclcalid = hclcalid;
	}

	public String getHclcvn() {
		return hclcvn;
	}

	public void setHclcvn(String hclcvn) {
		this.hclcvn = hclcvn;
	}

	public String getOthermoduleid() {
		return othermoduleid;
	}

	public void setOthermoduleid(String othermoduleid) {
		this.othermoduleid = othermoduleid;
	}

	public String getOthercalid() {
		return othercalid;
	}

	public void setOthercalid(String othercalid) {
		this.othercalid = othercalid;
	}

	public String getOthercvn() {
		return othercvn;
	}

	public void setOthercvn(String othercvn) {
		this.othercvn = othercvn;
	}

	public String getOpass() {
		return opass;
	}

	public void setOpass(String opass) {
		this.opass = opass;
	}

	public String getOtestdate() {
		return otestdate;
	}

	public void setOtestdate(String otestdate) {
		this.otestdate = otestdate;
	}

	public String getObdinspector() {
		return obdinspector;
	}

	public void setObdinspector(String obdinspector) {
		this.obdinspector = obdinspector;
	}
	
	public String getRh() {
		return rh;
	}

	public void setRh(String rh) {
		this.rh = rh;
	}

	public String getEt() {
		return et;
	}

	public void setEt(String et) {
		this.et = et;
	}

	public String getAp() {
		return ap;
	}

	public void setAp(String ap) {
		this.ap = ap;
	}

	public String getTesttype() {
		return testtype;
	}

	public void setTesttype(String testtype) {
		this.testtype = testtype;
	}

	public String getTestdate() {
		return testdate;
	}

	public void setTestdate(String testdate) {
		this.testdate = testdate;
	}

	public String getAnalymodel() {
		return analymodel;
	}

	public void setAnalymodel(String analymodel) {
		this.analymodel = analymodel;
	}

	public String getAnalymanuf() {
		return analymanuf;
	}

	public void setAnalymanuf(String analymanuf) {
		this.analymanuf = analymanuf;
	}

	public String getAnalyname() {
		return analyname;
	}

	public void setAnalyname(String analyname) {
		this.analyname = analyname;
	}

	public String getAnalydate() {
		return analydate;
	}

	public void setAnalydate(String analydate) {
		this.analydate = analydate;
	}

	public String getDynomanuf() {
		return dynomanuf;
	}

	public void setDynomanuf(String dynomanuf) {
		this.dynomanuf = dynomanuf;
	}

	public String getDynomodel() {
		return dynomodel;
	}

	public void setDynomodel(String dynomodel) {
		this.dynomodel = dynomodel;
	}


	public String getSvrco() {
		return svrco;
	}

	public void setSvrco(String svrco) {
		this.svrco = svrco;
	}

	public String getVrhcnox() {
		return vrhcnox;
	}

	public void setVrhcnox(String vrhcnox) {
		this.vrhcnox = vrhcnox;
	}

	public String getVlhcnox() {
		return vlhcnox;
	}

	public void setVlhcnox(String vlhcnox) {
		this.vlhcnox = vlhcnox;
	}

	public String getVrhc() {
		return vrhc;
	}

	public void setVrhc(String vrhc) {
		this.vrhc = vrhc;
	}

	public String getVlhc() {
		return vlhc;
	}

	public void setVlhc(String vlhc) {
		this.vlhc = vlhc;
	}

	public String getVrco() {
		return vrco;
	}

	public void setVrco(String vrco) {
		this.vrco = vrco;
	}

	public String getVlco() {
		return vlco;
	}

	public void setVlco(String vlco) {
		this.vlco = vlco;
	}

	public String getVrnox() {
		return vrnox;
	}

	public void setVrnox(String vrnox) {
		this.vrnox = vrnox;
	}

	public String getVlnox() {
		return vlnox;
	}

	public void setVlnox(String vlnox) {
		this.vlnox = vlnox;
	}

	public String getArhc() {
		return arhc;
	}

	public void setArhc(String arhc) {
		this.arhc = arhc;
	}

	public String getAlhc() {
		return alhc;
	}

	public void setAlhc(String alhc) {
		this.alhc = alhc;
	}

	public String getArco() {
		return arco;
	}

	public void setArco(String arco) {
		this.arco = arco;
	}

	public String getAlco() {
		return alco;
	}

	public void setAlco(String alco) {
		this.alco = alco;
	}

	public String getArnox() {
		return arnox;
	}

	public void setArnox(String arnox) {
		this.arnox = arnox;
	}

	public String getAlnox() {
		return alnox;
	}

	public void setAlnox(String alnox) {
		this.alnox = alnox;
	}

	public String getArhc5025() {
		return arhc5025;
	}

	public void setArhc5025(String arhc5025) {
		this.arhc5025 = arhc5025;
	}

	public String getAlhc5025() {
		return alhc5025;
	}

	public void setAlhc5025(String alhc5025) {
		this.alhc5025 = alhc5025;
	}

	public String getArco5025() {
		return arco5025;
	}

	public void setArco5025(String arco5025) {
		this.arco5025 = arco5025;
	}

	public String getAlco5025() {
		return alco5025;
	}

	public void setAlco5025(String alco5025) {
		this.alco5025 = alco5025;
	}

	public String getArnox5025() {
		return arnox5025;
	}

	public void setArnox5025(String arnox5025) {
		this.arnox5025 = arnox5025;
	}

	public String getAlnox5025() {
		return alnox5025;
	}

	public void setAlnox5025(String alnox5025) {
		this.alnox5025 = alnox5025;
	}

	public String getArhc2540() {
		return arhc2540;
	}

	public void setArhc2540(String arhc2540) {
		this.arhc2540 = arhc2540;
	}

	public String getAlhc2540() {
		return alhc2540;
	}

	public void setAlhc2540(String alhc2540) {
		this.alhc2540 = alhc2540;
	}

	public String getArco2540() {
		return arco2540;
	}

	public void setArco2540(String arco2540) {
		this.arco2540 = arco2540;
	}

	public String getAlco2540() {
		return alco2540;
	}

	public void setAlco2540(String alco2540) {
		this.alco2540 = alco2540;
	}

	public String getArnox2540() {
		return arnox2540;
	}

	public void setArnox2540(String arnox2540) {
		this.arnox2540 = arnox2540;
	}

	public String getAlnox2540() {
		return alnox2540;
	}

	public void setAlnox2540(String alnox2540) {
		this.alnox2540 = alnox2540;
	}

	public String getLeacmax() {
		return leacmax;
	}

	public void setLeacmax(String leacmax) {
		this.leacmax = leacmax;
	}

	public String getLeacmin() {
		return leacmin;
	}

	public void setLeacmin(String leacmin) {
		this.leacmin = leacmin;
	}

	public String getReac() {
		return reac;
	}

	public void setReac(String reac) {
		this.reac = reac;
	}

	public String getLrco() {
		return lrco;
	}

	public void setLrco(String lrco) {
		this.lrco = lrco;
	}

	public String getLlco() {
		return llco;
	}

	public void setLlco(String llco) {
		this.llco = llco;
	}

	public String getLrhc() {
		return lrhc;
	}

	public void setLrhc(String lrhc) {
		this.lrhc = lrhc;
	}

	public String getLlhc() {
		return llhc;
	}

	public void setLlhc(String llhc) {
		this.llhc = llhc;
	}

	public String getHrco() {
		return hrco;
	}

	public void setHrco(String hrco) {
		this.hrco = hrco;
	}

	public String getHlco() {
		return hlco;
	}

	public void setHlco(String hlco) {
		this.hlco = hlco;
	}

	public String getHrhc() {
		return hrhc;
	}

	public void setHrhc(String hrhc) {
		this.hrhc = hrhc;
	}

	public String getHlhc() {
		return hlhc;
	}

	public void setHlhc(String hlhc) {
		this.hlhc = hlhc;
	}

	public String getRaterev() {
		return raterev;
	}

	public void setRaterev(String raterev) {
		this.raterev = raterev;
	}

	public String getRev() {
		return rev;
	}

	public void setRev(String rev) {
		this.rev = rev;
	}

	public String getSmokek1() {
		return smokek1;
	}

	public void setSmokek1(String smokek1) {
		this.smokek1 = smokek1;
	}

	public String getSmokek2() {
		return smokek2;
	}

	public void setSmokek2(String smokek2) {
		this.smokek2 = smokek2;
	}

	public String getSmokek3() {
		return smokek3;
	}

	public void setSmokek3(String smokek3) {
		this.smokek3 = smokek3;
	}

	public String getSmokeavg() {
		return smokeavg;
	}

	public void setSmokeavg(String smokeavg) {
		this.smokeavg = smokeavg;
	}

	public String getSmokeklimit() {
		return smokeklimit;
	}

	public void setSmokeklimit(String smokeklimit) {
		this.smokeklimit = smokeklimit;
	}

	public String getRaterevup() {
		return raterevup;
	}

	public void setRaterevup(String raterevup) {
		this.raterevup = raterevup;
	}

	public String getRaterevdown() {
		return raterevdown;
	}

	public void setRaterevdown(String raterevdown) {
		this.raterevdown = raterevdown;
	}

	public String getRev100() {
		return rev100;
	}

	public void setRev100(String rev100) {
		this.rev100 = rev100;
	}

	public String getMaxpower() {
		return maxpower;
	}

	public void setMaxpower(String maxpower) {
		this.maxpower = maxpower;
	}

	public String getMaxpowerlimit() {
		return maxpowerlimit;
	}

	public void setMaxpowerlimit(String maxpowerlimit) {
		this.maxpowerlimit = maxpowerlimit;
	}

	public String getSmoke100() {
		return smoke100;
	}

	public void setSmoke100(String smoke100) {
		this.smoke100 = smoke100;
	}

	public String getSmoke80() {
		return smoke80;
	}

	public void setSmoke80(String smoke80) {
		this.smoke80 = smoke80;
	}

	public String getSmokelimit() {
		return smokelimit;
	}

	public void setSmokelimit(String smokelimit) {
		this.smokelimit = smokelimit;
	}

	public String getNox() {
		return nox;
	}

	public void setNox(String nox) {
		this.nox = nox;
	}

	public String getNoxlimit() {
		return noxlimit;
	}

	public void setNoxlimit(String noxlimit) {
		this.noxlimit = noxlimit;
	}

	public String getEpass() {
		return epass;
	}

	public void setEpass(String epass) {
		this.epass = epass;
	}

	public String getTestinspector() {
		return testinspector;
	}

	public void setTestinspector(String testinspector) {
		this.testinspector = testinspector;
	}

	public String getCtest() {
		return ctest;
	}

	public void setCtest(String ctest) {
		this.ctest = ctest;
	}

	public String getTestlocation() {
		return testlocation;
	}

	public void setTestlocation(String testlocation) {
		this.testlocation = testlocation;
	}

	public String getResult() {
		return result;
	}

	public void setResult(String result) {
		this.result = result;
	}

	public String getFdjsccdz() {
		return fdjsccdz;
	}

	public void setFdjsccdz(String fdjsccdz) {
		this.fdjsccdz = fdjsccdz;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}

	/**
	 * @return the svlco
	 */
	public String getSvlco() {
		return svlco;
	}

	/**
	 * @param svlco the svlco to set
	 */
	public void setSvlco(String svlco) {
		this.svlco = svlco;
	}

}
