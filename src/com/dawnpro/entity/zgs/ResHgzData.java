package com.dawnpro.entity.zgs;

import com.fasterxml.jackson.annotation.JsonProperty;

public class ResHgzData {
	
	private String status;//status为反馈状态，0成功  1失败

	private String message;//status=1时，message保存失败原因
	
	@JsonProperty("hgzxx")
	private Hgzinfo hgzinfo;//status=0时，反馈信息

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	/**
	 * @return the hgzinfo
	 */
	public Hgzinfo getHgzinfo() {
		return hgzinfo;
	}

	/**
	 * @param hgzinfo the hgzinfo to set
	 */
	public void setHgzinfo(Hgzinfo hgzinfo) {
		this.hgzinfo = hgzinfo;
	}

}
