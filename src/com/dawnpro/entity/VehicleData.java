package com.dawnpro.entity;

import com.alibaba.fastjson.annotation.JSONField;

public class VehicleData {
	
	@JSONField(ordinal=1)
	private String scdate;
	@JSONField(ordinal=2)
	private String fdjh;
	@JSONField(ordinal=3)
	private String fdjsccdz;
	@JSONField(ordinal=4)
	private String fdjsb;
	@JSONField(ordinal=5)
	private String vehicleModel;
	@JSONField(ordinal=6)
	private String sccdz;
	@JSONField(ordinal=7)
	private String sccmc;
	@JSONField(ordinal=8)
	private String vin;
	@JSONField(ordinal=9)
	private String xxgkbh;
	@JSONField(ordinal=10)
	private String sb;

	public VehicleData() {
		// TODO Auto-generated constructor stub
	}

	public VehicleData(String vehicleModel, String vin, String xxgkbh, String sb, String sccdz, String scdate,
			String fdjh, String fdjsb, String fdjsccdz, String sccmc) {
		super();
		this.vehicleModel = vehicleModel;
		this.vin = vin;
		this.xxgkbh = xxgkbh;
		this.sb = sb;
		this.sccdz = sccdz;
		this.scdate = scdate;
		this.fdjh = fdjh;
		this.fdjsb = fdjsb;
		this.fdjsccdz = fdjsccdz;
		this.sccmc = sccmc;
	}

	public String getVehicleModel() {
		return vehicleModel;
	}

	public void setVehicleModel(String vehicleModel) {
		this.vehicleModel = vehicleModel;
	}

	public String getVin() {
		return vin;
	}

	public void setVin(String vin) {
		this.vin = vin;
	}

	public String getXxgkbh() {
		return xxgkbh;
	}

	public void setXxgkbh(String xxgkbh) {
		this.xxgkbh = xxgkbh;
	}

	public String getSb() {
		return sb;
	}

	public void setSb(String sb) {
		this.sb = sb;
	}

	public String getSccdz() {
		return sccdz;
	}

	public void setSccdz(String sccdz) {
		this.sccdz = sccdz;
	}

	public String getScdate() {
		return scdate;
	}

	public void setScdate(String scdate) {
		this.scdate = scdate;
	}

	public String getFdjh() {
		return fdjh;
	}

	public void setFdjh(String fdjh) {
		this.fdjh = fdjh;
	}

	public String getFdjsb() {
		return fdjsb;
	}

	public void setFdjsb(String fdjsb) {
		this.fdjsb = fdjsb;
	}

	public String getFdjsccdz() {
		return fdjsccdz;
	}

	public void setFdjsccdz(String fdjsccdz) {
		this.fdjsccdz = fdjsccdz;
	}

	public String getSccmc() {
		return sccmc;
	}

	public void setSccmc(String sccmc) {
		this.sccmc = sccmc;
	}

}
