package com.dawnpro.entity;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * A.2 环境参数
 * 
 * <AUTHOR>
 *
 */
public class EnvData {
	@JSONField(ordinal = 1)
	private Double rh;
	@JSONField(ordinal = 2)
	private Double ap;
	@JSONField(ordinal = 3)
	private Double et;

	public EnvData() {
		// TODO Auto-generated constructor stub
	}

	public EnvData(Double rh, Double ap, Double et) {
		super();
		this.rh = rh;
		this.ap = ap;
		this.et = et;
	}

	public Double getRh() {
		return rh;
	}

	public void setRh(Double rh) {
		this.rh = rh;
	}

	public Double getAp() {
		return ap;
	}

	public void setAp(Double ap) {
		this.ap = ap;
	}

	public Double getEt() {
		return et;
	}

	public void setEt(Double et) {
		this.et = et;
	}

}
