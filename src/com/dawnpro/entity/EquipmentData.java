package com.dawnpro.entity;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * 检验设备信息
 * 
 * <AUTHOR>
 *
 */
public class EquipmentData {
	@JSONField(ordinal = 1)
	private String analyModel;
	@JSONField(ordinal = 2)
	private String dynoModel;
	@JSONField(ordinal = 3)
	private String analyManuf;
	@JSONField(ordinal = 4)
	private String analyName;
	@JSONField(ordinal = 5)
	private String dynoManuf;
	@JSONField(ordinal = 6)
	private String analyDate;

	public EquipmentData() {
		// TODO Auto-generated constructor stub
	}

	public EquipmentData(String analyManuf, String analyName, String analyModel, String analyDate, String dynoModel,
			String dynoManuf) {
		super();
		this.analyManuf = analyManuf;
		this.analyName = analyName;
		this.analyModel = analyModel;
		this.analyDate = analyDate;
		this.dynoModel = dynoModel;
		this.dynoManuf = dynoManuf;
	}

	public String getAnalyManuf() {
		return analyManuf;
	}

	public void setAnalyManuf(String analyManuf) {
		this.analyManuf = analyManuf;
	}

	public String getAnalyName() {
		return analyName;
	}

	public void setAnalyName(String analyName) {
		this.analyName = analyName;
	}

	public String getAnalyModel() {
		return analyModel;
	}

	public void setAnalyModel(String analyModel) {
		this.analyModel = analyModel;
	}

	public String getAnalyDate() {
		return analyDate;
	}

	public void setAnalyDate(String analyDate) {
		this.analyDate = analyDate;
	}

	public String getDynoModel() {
		return dynoModel;
	}

	public void setDynoModel(String dynoModel) {
		this.dynoModel = dynoModel;
	}

	public String getDynoManuf() {
		return dynoManuf;
	}

	public void setDynoManuf(String dynoManuf) {
		this.dynoManuf = dynoManuf;
	}

}
