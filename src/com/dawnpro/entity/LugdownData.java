package com.dawnpro.entity;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * A.5.4加载减速法
 * <AUTHOR>
 *
 */
public class LugdownData {
	@JSONField(ordinal=1)
	private int rev100;
	@JSONField(ordinal=2)
	private int rateRevUp;
	@JSONField(ordinal=3)
	private double smokeLimit;
	@JSONField(ordinal=4)
	private int nox;
	@JSONField(ordinal=5)
	private double smoke100;
	@JSONField(ordinal=6)
	private int rateRevDown;
	@JSONField(ordinal=7)
	private double maxPower;
	@JSONField(ordinal=8)
	private double maxPowerLimit;
	@JSONField(ordinal=9)
	private double smoke80;
	@JSONField(ordinal=10)
	private int noxLimit;
	
	public LugdownData() {
		// TODO Auto-generated constructor stub
	}	
	
	public LugdownData(int rev100, int rateRevUp, double smokeLimit, int nox, double smoke100, int rateRevDown,
			double maxPower, double maxPowerLimit, double smoke80, int noxLimit) {
		super();
		this.rev100 = rev100;
		this.rateRevUp = rateRevUp;
		this.smokeLimit = smokeLimit;
		this.nox = nox;
		this.smoke100 = smoke100;
		this.rateRevDown = rateRevDown;
		this.maxPower = maxPower;
		this.maxPowerLimit = maxPowerLimit;
		this.smoke80 = smoke80;
		this.noxLimit = noxLimit;
	}



	public int getRev100() {
		return rev100;
	}

	public void setRev100(int rev100) {
		this.rev100 = rev100;
	}

	public int getRateRevUp() {
		return rateRevUp;
	}

	public void setRateRevUp(int rateRevUp) {
		this.rateRevUp = rateRevUp;
	}

	public double getSmokeLimit() {
		return smokeLimit;
	}

	public void setSmokeLimit(double smokeLimit) {
		this.smokeLimit = smokeLimit;
	}

	public int getNox() {
		return nox;
	}

	public void setNox(int nox) {
		this.nox = nox;
	}

	public double getSmoke100() {
		return smoke100;
	}

	public void setSmoke100(double smoke100) {
		this.smoke100 = smoke100;
	}

	public int getRateRevDown() {
		return rateRevDown;
	}

	public void setRateRevDown(int rateRevDown) {
		this.rateRevDown = rateRevDown;
	}

	public double getMaxPower() {
		return maxPower;
	}

	public void setMaxPower(double maxPower) {
		this.maxPower = maxPower;
	}

	public double getMaxPowerLimit() {
		return maxPowerLimit;
	}

	public void setMaxPowerLimit(double maxPowerLimit) {
		this.maxPowerLimit = maxPowerLimit;
	}

	public double getSmoke80() {
		return smoke80;
	}

	public void setSmoke80(double smoke80) {
		this.smoke80 = smoke80;
	}

	public int getNoxLimit() {
		return noxLimit;
	}

	public void setNoxLimit(int noxLimit) {
		this.noxLimit = noxLimit;
	}
	
	

}
