package com.dawnpro.esb.ws;

public class ISendMessageServiceProxy implements com.dawnpro.esb.ws.ISendMessageService {
  private String _endpoint = null;
  private com.dawnpro.esb.ws.ISendMessageService iSendMessageService = null;
  
  public ISendMessageServiceProxy() {
    _initISendMessageServiceProxy();
  }
  
  public ISendMessageServiceProxy(String endpoint) {
    _endpoint = endpoint;
    _initISendMessageServiceProxy();
  }
  
  private void _initISendMessageServiceProxy() {
    try {
      iSendMessageService = (new com.dawnpro.esb.ws.ISendMessageServiceServiceLocator()).getISendMessageServicePort();
      if (iSendMessageService != null) {
        if (_endpoint != null)
          ((javax.xml.rpc.Stub)iSendMessageService)._setProperty("javax.xml.rpc.service.endpoint.address", _endpoint);
        else
          _endpoint = (String)((javax.xml.rpc.Stub)iSendMessageService)._getProperty("javax.xml.rpc.service.endpoint.address");
      }
      
    }
    catch (javax.xml.rpc.ServiceException serviceException) {}
  }
  
  public String getEndpoint() {
    return _endpoint;
  }
  
  public void setEndpoint(String endpoint) {
    _endpoint = endpoint;
    if (iSendMessageService != null)
      ((javax.xml.rpc.Stub)iSendMessageService)._setProperty("javax.xml.rpc.service.endpoint.address", _endpoint);
    
  }
  
  public com.dawnpro.esb.ws.ISendMessageService getISendMessageService() {
    if (iSendMessageService == null)
      _initISendMessageServiceProxy();
    return iSendMessageService;
  }
  
  public java.lang.String sendMessage(java.lang.String baseParams, java.lang.String bizParams) throws java.rmi.RemoteException{
    if (iSendMessageService == null)
      _initISendMessageServiceProxy();
    return iSendMessageService.sendMessage(baseParams, bizParams);
  }
  
  
}