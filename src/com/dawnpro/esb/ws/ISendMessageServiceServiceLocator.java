/**
 * ISendMessageServiceServiceLocator.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.dawnpro.esb.ws;

import com.dawnpro.commons.SystemParam;

public class ISendMessageServiceServiceLocator extends org.apache.axis.client.Service implements com.dawnpro.esb.ws.ISendMessageServiceService {

    public ISendMessageServiceServiceLocator() {
    }


    public ISendMessageServiceServiceLocator(org.apache.axis.EngineConfiguration config) {
        super(config);
    }

    public ISendMessageServiceServiceLocator(java.lang.String wsdlLoc, javax.xml.namespace.QName sName) throws javax.xml.rpc.ServiceException {
        super(wsdlLoc, sName);
    }

    // Use to get a proxy class for ISendMessageServicePort//
    // 测试地址：http://************:7080/ESB/sendMessageService
    // 正式地址：http://***********:7080/ESB/sendMessageService
    private java.lang.String ISendMessageServicePort_address = SystemParam.getKey("esbservice_address", "http://************:7080/ESB/sendMessageService");
//    private java.lang.String ISendMessageServicePort_address = "http://10.312.251.220:7080/ESB/sendMessageService";
    
    public java.lang.String getISendMessageServicePortAddress() {
        return ISendMessageServicePort_address;
    }

    // The WSDD service name defaults to the port name.
    private java.lang.String ISendMessageServicePortWSDDServiceName = "ISendMessageServicePort";

    public java.lang.String getISendMessageServicePortWSDDServiceName() {
        return ISendMessageServicePortWSDDServiceName;
    }

    public void setISendMessageServicePortWSDDServiceName(java.lang.String name) {
        ISendMessageServicePortWSDDServiceName = name;
    }

    public com.dawnpro.esb.ws.ISendMessageService getISendMessageServicePort() throws javax.xml.rpc.ServiceException {
       java.net.URL endpoint;
        try {
            endpoint = new java.net.URL(ISendMessageServicePort_address);
        }
        catch (java.net.MalformedURLException e) {
            throw new javax.xml.rpc.ServiceException(e);
        }
        return getISendMessageServicePort(endpoint);
    }

    public com.dawnpro.esb.ws.ISendMessageService getISendMessageServicePort(java.net.URL portAddress) throws javax.xml.rpc.ServiceException {
        try {
            com.dawnpro.esb.ws.ISendMessageServiceServiceSoapBindingStub _stub = new com.dawnpro.esb.ws.ISendMessageServiceServiceSoapBindingStub(portAddress, this);
            _stub.setPortName(getISendMessageServicePortWSDDServiceName());
            return _stub;
        }
        catch (org.apache.axis.AxisFault e) {
            return null;
        }
    }

    public void setISendMessageServicePortEndpointAddress(java.lang.String address) {
        ISendMessageServicePort_address = address;
    }

    /**
     * For the given interface, get the stub implementation.
     * If this service has no port for the given interface,
     * then ServiceException is thrown.
     */
    public java.rmi.Remote getPort(Class serviceEndpointInterface) throws javax.xml.rpc.ServiceException {
        try {
            if (com.dawnpro.esb.ws.ISendMessageService.class.isAssignableFrom(serviceEndpointInterface)) {
                com.dawnpro.esb.ws.ISendMessageServiceServiceSoapBindingStub _stub = new com.dawnpro.esb.ws.ISendMessageServiceServiceSoapBindingStub(new java.net.URL(ISendMessageServicePort_address), this);
                _stub.setPortName(getISendMessageServicePortWSDDServiceName());
                return _stub;
            }
        }
        catch (java.lang.Throwable t) {
            throw new javax.xml.rpc.ServiceException(t);
        }
        throw new javax.xml.rpc.ServiceException("There is no stub implementation for the interface:  " + (serviceEndpointInterface == null ? "null" : serviceEndpointInterface.getName()));
    }

    /**
     * For the given interface, get the stub implementation.
     * If this service has no port for the given interface,
     * then ServiceException is thrown.
     */
    public java.rmi.Remote getPort(javax.xml.namespace.QName portName, Class serviceEndpointInterface) throws javax.xml.rpc.ServiceException {
        if (portName == null) {
            return getPort(serviceEndpointInterface);
        }
        java.lang.String inputPortName = portName.getLocalPart();
        if ("ISendMessageServicePort".equals(inputPortName)) {
            return getISendMessageServicePort();
        }
        else  {
            java.rmi.Remote _stub = getPort(serviceEndpointInterface);
            ((org.apache.axis.client.Stub) _stub).setPortName(portName);
            return _stub;
        }
    }

    public javax.xml.namespace.QName getServiceName() {
        return new javax.xml.namespace.QName("http://ws.esb.dawnpro.com/", "ISendMessageServiceService");
    }

    private java.util.HashSet ports = null;

    public java.util.Iterator getPorts() {
        if (ports == null) {
            ports = new java.util.HashSet();
            ports.add(new javax.xml.namespace.QName("http://ws.esb.dawnpro.com/", "ISendMessageServicePort"));
        }
        return ports.iterator();
    }

    /**
    * Set the endpoint address for the specified port name.
    */
    public void setEndpointAddress(java.lang.String portName, java.lang.String address) throws javax.xml.rpc.ServiceException {
        
if ("ISendMessageServicePort".equals(portName)) {
            setISendMessageServicePortEndpointAddress(address);
        }
        else 
{ // Unknown Port Name
            throw new javax.xml.rpc.ServiceException(" Cannot set Endpoint Address for Unknown Port" + portName);
        }
    }

    /**
    * Set the endpoint address for the specified port name.
    */
    public void setEndpointAddress(javax.xml.namespace.QName portName, java.lang.String address) throws javax.xml.rpc.ServiceException {
        setEndpointAddress(portName.getLocalPart(), address);
    }

}
