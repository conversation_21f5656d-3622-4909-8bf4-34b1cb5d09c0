/**
 * CertificateInfo.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package info.vidc.www.certificate.operation;

public class CertificateInfo  implements java.io.Serializable {
    private java.lang.String h_ID;

    private java.lang.String CJH;

    private java.lang.String CLSBDH;

    private java.lang.String CLZZQYMC;

    private java.lang.String CLLX;

    private java.lang.String CLMC;

    private java.lang.String CLPP;

    private java.lang.String CLXH;

    private java.lang.String CSYS;

    private java.lang.String DPXH;

    private java.lang.String FDJH;

    private java.lang.String FDJXH;

    private java.lang.String RLZL;

    private java.lang.String PFBZ;

    private java.lang.String PL;

    private java.lang.String GL;

    private java.lang.String ZXXS;

    private java.lang.String QLJ;

    private java.lang.String HLJ;

    private java.lang.String LTS;

    private java.lang.String LTGG;

    private java.lang.String GBTHPS;

    private java.lang.String ZJ;

    private java.lang.String ZH;

    private java.lang.String ZS;

    private java.lang.String WKC;

    private java.lang.String WKK;

    private java.lang.String WKG;

    private java.lang.String HXNBC;

    private java.lang.String HXNBK;

    private java.lang.String HXNBG;

    private java.lang.String ZZL;

    private java.lang.String EDZZL;

    private java.lang.String ZBZL;

    private java.lang.String ZZLLYXS;

    private java.lang.String ZQYZZL;

    private java.lang.String EDZK;

    private java.lang.String BGCAZZDYXZZL;

    private java.lang.String JSSZCRS;

    private java.lang.String ZGCS;

    private java.util.Calendar CLZZRQ;

    private java.lang.String BZ;

    private java.lang.String CPSCDZ;

    private java.util.Calendar CZRQ;

    private java.util.Calendar FZRQ;

    private java.lang.String CLSCDWMC;

    private java.lang.String YH;

    private java.lang.String ZXZS;

    private java.lang.String CDDBJ;

    private java.lang.String PZXLH;

    private java.util.Calendar CREATETIME;

    private java.lang.String VEHICLE_STATUS;

    private java.lang.String RESPONSE_CODE;

    private java.lang.String CLIENT_HARDWARE_INFO;

    private java.util.Calendar FEEDBACK_TIME;

    private java.lang.String HD_HOST;

    private java.lang.String HD_USER;

    private java.lang.String UKEY;

    private java.util.Calendar UPDATETIME;

    private java.lang.String UPSEND_TAG;

    private java.lang.String VERCODE;

    private java.lang.String VERSION;

    private java.lang.String CLZTXX;

    private java.lang.String DYWYM;

    private java.lang.String QYID;

    private java.lang.String ZCHGZBH;

    private java.lang.String ZZBH;

    private java.lang.String CPH;

    private java.lang.String DPHGZBH;

    private java.lang.String DPID;

    private java.util.Calendar GGSXRQ;

    private java.lang.String HZDCZFS;

    private java.lang.String HZDFS;

    private java.lang.String PC;

    private java.lang.String QYBZ;

    private java.lang.String QYQTXX;

    private java.lang.String QZDCZFS;

    private java.lang.String QZDFS;

    private java.lang.String WZHGZBH;

    private java.lang.String JFPZID;

    private java.lang.String VINBSYY;

    private java.lang.String ISCXNF;

    private java.lang.String ZYZYCMSBS;

    private java.lang.String XNYQCJMSBS;

    private java.lang.String HDMSBS;

    private java.lang.String XNYQCZL;

    private java.lang.String SWTQYMC;

    private java.lang.String SWTQYSCDZ;

    private java.lang.String SWTQYTYSHXYDM;

    public CertificateInfo() {
    }

    public CertificateInfo(
           java.lang.String h_ID,
           java.lang.String CJH,
           java.lang.String CLSBDH,
           java.lang.String CLZZQYMC,
           java.lang.String CLLX,
           java.lang.String CLMC,
           java.lang.String CLPP,
           java.lang.String CLXH,
           java.lang.String CSYS,
           java.lang.String DPXH,
           java.lang.String FDJH,
           java.lang.String FDJXH,
           java.lang.String RLZL,
           java.lang.String PFBZ,
           java.lang.String PL,
           java.lang.String GL,
           java.lang.String ZXXS,
           java.lang.String QLJ,
           java.lang.String HLJ,
           java.lang.String LTS,
           java.lang.String LTGG,
           java.lang.String GBTHPS,
           java.lang.String ZJ,
           java.lang.String ZH,
           java.lang.String ZS,
           java.lang.String WKC,
           java.lang.String WKK,
           java.lang.String WKG,
           java.lang.String HXNBC,
           java.lang.String HXNBK,
           java.lang.String HXNBG,
           java.lang.String ZZL,
           java.lang.String EDZZL,
           java.lang.String ZBZL,
           java.lang.String ZZLLYXS,
           java.lang.String ZQYZZL,
           java.lang.String EDZK,
           java.lang.String BGCAZZDYXZZL,
           java.lang.String JSSZCRS,
           java.lang.String ZGCS,
           java.util.Calendar CLZZRQ,
           java.lang.String BZ,
           java.lang.String CPSCDZ,
           java.util.Calendar CZRQ,
           java.util.Calendar FZRQ,
           java.lang.String CLSCDWMC,
           java.lang.String YH,
           java.lang.String ZXZS,
           java.lang.String CDDBJ,
           java.lang.String PZXLH,
           java.util.Calendar CREATETIME,
           java.lang.String VEHICLE_STATUS,
           java.lang.String RESPONSE_CODE,
           java.lang.String CLIENT_HARDWARE_INFO,
           java.util.Calendar FEEDBACK_TIME,
           java.lang.String HD_HOST,
           java.lang.String HD_USER,
           java.lang.String UKEY,
           java.util.Calendar UPDATETIME,
           java.lang.String UPSEND_TAG,
           java.lang.String VERCODE,
           java.lang.String VERSION,
           java.lang.String CLZTXX,
           java.lang.String DYWYM,
           java.lang.String QYID,
           java.lang.String ZCHGZBH,
           java.lang.String ZZBH,
           java.lang.String CPH,
           java.lang.String DPHGZBH,
           java.lang.String DPID,
           java.util.Calendar GGSXRQ,
           java.lang.String HZDCZFS,
           java.lang.String HZDFS,
           java.lang.String PC,
           java.lang.String QYBZ,
           java.lang.String QYQTXX,
           java.lang.String QZDCZFS,
           java.lang.String QZDFS,
           java.lang.String WZHGZBH,
           java.lang.String JFPZID,
           java.lang.String VINBSYY,
           java.lang.String ISCXNF,
           java.lang.String ZYZYCMSBS,
           java.lang.String XNYQCJMSBS,
           java.lang.String HDMSBS,
           java.lang.String XNYQCZL,
           java.lang.String SWTQYMC,
           java.lang.String SWTQYSCDZ,
           java.lang.String SWTQYTYSHXYDM) {
           this.h_ID = h_ID;
           this.CJH = CJH;
           this.CLSBDH = CLSBDH;
           this.CLZZQYMC = CLZZQYMC;
           this.CLLX = CLLX;
           this.CLMC = CLMC;
           this.CLPP = CLPP;
           this.CLXH = CLXH;
           this.CSYS = CSYS;
           this.DPXH = DPXH;
           this.FDJH = FDJH;
           this.FDJXH = FDJXH;
           this.RLZL = RLZL;
           this.PFBZ = PFBZ;
           this.PL = PL;
           this.GL = GL;
           this.ZXXS = ZXXS;
           this.QLJ = QLJ;
           this.HLJ = HLJ;
           this.LTS = LTS;
           this.LTGG = LTGG;
           this.GBTHPS = GBTHPS;
           this.ZJ = ZJ;
           this.ZH = ZH;
           this.ZS = ZS;
           this.WKC = WKC;
           this.WKK = WKK;
           this.WKG = WKG;
           this.HXNBC = HXNBC;
           this.HXNBK = HXNBK;
           this.HXNBG = HXNBG;
           this.ZZL = ZZL;
           this.EDZZL = EDZZL;
           this.ZBZL = ZBZL;
           this.ZZLLYXS = ZZLLYXS;
           this.ZQYZZL = ZQYZZL;
           this.EDZK = EDZK;
           this.BGCAZZDYXZZL = BGCAZZDYXZZL;
           this.JSSZCRS = JSSZCRS;
           this.ZGCS = ZGCS;
           this.CLZZRQ = CLZZRQ;
           this.BZ = BZ;
           this.CPSCDZ = CPSCDZ;
           this.CZRQ = CZRQ;
           this.FZRQ = FZRQ;
           this.CLSCDWMC = CLSCDWMC;
           this.YH = YH;
           this.ZXZS = ZXZS;
           this.CDDBJ = CDDBJ;
           this.PZXLH = PZXLH;
           this.CREATETIME = CREATETIME;
           this.VEHICLE_STATUS = VEHICLE_STATUS;
           this.RESPONSE_CODE = RESPONSE_CODE;
           this.CLIENT_HARDWARE_INFO = CLIENT_HARDWARE_INFO;
           this.FEEDBACK_TIME = FEEDBACK_TIME;
           this.HD_HOST = HD_HOST;
           this.HD_USER = HD_USER;
           this.UKEY = UKEY;
           this.UPDATETIME = UPDATETIME;
           this.UPSEND_TAG = UPSEND_TAG;
           this.VERCODE = VERCODE;
           this.VERSION = VERSION;
           this.CLZTXX = CLZTXX;
           this.DYWYM = DYWYM;
           this.QYID = QYID;
           this.ZCHGZBH = ZCHGZBH;
           this.ZZBH = ZZBH;
           this.CPH = CPH;
           this.DPHGZBH = DPHGZBH;
           this.DPID = DPID;
           this.GGSXRQ = GGSXRQ;
           this.HZDCZFS = HZDCZFS;
           this.HZDFS = HZDFS;
           this.PC = PC;
           this.QYBZ = QYBZ;
           this.QYQTXX = QYQTXX;
           this.QZDCZFS = QZDCZFS;
           this.QZDFS = QZDFS;
           this.WZHGZBH = WZHGZBH;
           this.JFPZID = JFPZID;
           this.VINBSYY = VINBSYY;
           this.ISCXNF = ISCXNF;
           this.ZYZYCMSBS = ZYZYCMSBS;
           this.XNYQCJMSBS = XNYQCJMSBS;
           this.HDMSBS = HDMSBS;
           this.XNYQCZL = XNYQCZL;
           this.SWTQYMC = SWTQYMC;
           this.SWTQYSCDZ = SWTQYSCDZ;
           this.SWTQYTYSHXYDM = SWTQYTYSHXYDM;
    }


    /**
     * Gets the h_ID value for this CertificateInfo.
     * 
     * @return h_ID
     */
    public java.lang.String getH_ID() {
        return h_ID;
    }


    /**
     * Sets the h_ID value for this CertificateInfo.
     * 
     * @param h_ID
     */
    public void setH_ID(java.lang.String h_ID) {
        this.h_ID = h_ID;
    }


    /**
     * Gets the CJH value for this CertificateInfo.
     * 
     * @return CJH
     */
    public java.lang.String getCJH() {
        return CJH;
    }


    /**
     * Sets the CJH value for this CertificateInfo.
     * 
     * @param CJH
     */
    public void setCJH(java.lang.String CJH) {
        this.CJH = CJH;
    }


    /**
     * Gets the CLSBDH value for this CertificateInfo.
     * 
     * @return CLSBDH
     */
    public java.lang.String getCLSBDH() {
        return CLSBDH;
    }


    /**
     * Sets the CLSBDH value for this CertificateInfo.
     * 
     * @param CLSBDH
     */
    public void setCLSBDH(java.lang.String CLSBDH) {
        this.CLSBDH = CLSBDH;
    }


    /**
     * Gets the CLZZQYMC value for this CertificateInfo.
     * 
     * @return CLZZQYMC
     */
    public java.lang.String getCLZZQYMC() {
        return CLZZQYMC;
    }


    /**
     * Sets the CLZZQYMC value for this CertificateInfo.
     * 
     * @param CLZZQYMC
     */
    public void setCLZZQYMC(java.lang.String CLZZQYMC) {
        this.CLZZQYMC = CLZZQYMC;
    }


    /**
     * Gets the CLLX value for this CertificateInfo.
     * 
     * @return CLLX
     */
    public java.lang.String getCLLX() {
        return CLLX;
    }


    /**
     * Sets the CLLX value for this CertificateInfo.
     * 
     * @param CLLX
     */
    public void setCLLX(java.lang.String CLLX) {
        this.CLLX = CLLX;
    }


    /**
     * Gets the CLMC value for this CertificateInfo.
     * 
     * @return CLMC
     */
    public java.lang.String getCLMC() {
        return CLMC;
    }


    /**
     * Sets the CLMC value for this CertificateInfo.
     * 
     * @param CLMC
     */
    public void setCLMC(java.lang.String CLMC) {
        this.CLMC = CLMC;
    }


    /**
     * Gets the CLPP value for this CertificateInfo.
     * 
     * @return CLPP
     */
    public java.lang.String getCLPP() {
        return CLPP;
    }


    /**
     * Sets the CLPP value for this CertificateInfo.
     * 
     * @param CLPP
     */
    public void setCLPP(java.lang.String CLPP) {
        this.CLPP = CLPP;
    }


    /**
     * Gets the CLXH value for this CertificateInfo.
     * 
     * @return CLXH
     */
    public java.lang.String getCLXH() {
        return CLXH;
    }


    /**
     * Sets the CLXH value for this CertificateInfo.
     * 
     * @param CLXH
     */
    public void setCLXH(java.lang.String CLXH) {
        this.CLXH = CLXH;
    }


    /**
     * Gets the CSYS value for this CertificateInfo.
     * 
     * @return CSYS
     */
    public java.lang.String getCSYS() {
        return CSYS;
    }


    /**
     * Sets the CSYS value for this CertificateInfo.
     * 
     * @param CSYS
     */
    public void setCSYS(java.lang.String CSYS) {
        this.CSYS = CSYS;
    }


    /**
     * Gets the DPXH value for this CertificateInfo.
     * 
     * @return DPXH
     */
    public java.lang.String getDPXH() {
        return DPXH;
    }


    /**
     * Sets the DPXH value for this CertificateInfo.
     * 
     * @param DPXH
     */
    public void setDPXH(java.lang.String DPXH) {
        this.DPXH = DPXH;
    }


    /**
     * Gets the FDJH value for this CertificateInfo.
     * 
     * @return FDJH
     */
    public java.lang.String getFDJH() {
        return FDJH;
    }


    /**
     * Sets the FDJH value for this CertificateInfo.
     * 
     * @param FDJH
     */
    public void setFDJH(java.lang.String FDJH) {
        this.FDJH = FDJH;
    }


    /**
     * Gets the FDJXH value for this CertificateInfo.
     * 
     * @return FDJXH
     */
    public java.lang.String getFDJXH() {
        return FDJXH;
    }


    /**
     * Sets the FDJXH value for this CertificateInfo.
     * 
     * @param FDJXH
     */
    public void setFDJXH(java.lang.String FDJXH) {
        this.FDJXH = FDJXH;
    }


    /**
     * Gets the RLZL value for this CertificateInfo.
     * 
     * @return RLZL
     */
    public java.lang.String getRLZL() {
        return RLZL;
    }


    /**
     * Sets the RLZL value for this CertificateInfo.
     * 
     * @param RLZL
     */
    public void setRLZL(java.lang.String RLZL) {
        this.RLZL = RLZL;
    }


    /**
     * Gets the PFBZ value for this CertificateInfo.
     * 
     * @return PFBZ
     */
    public java.lang.String getPFBZ() {
        return PFBZ;
    }


    /**
     * Sets the PFBZ value for this CertificateInfo.
     * 
     * @param PFBZ
     */
    public void setPFBZ(java.lang.String PFBZ) {
        this.PFBZ = PFBZ;
    }


    /**
     * Gets the PL value for this CertificateInfo.
     * 
     * @return PL
     */
    public java.lang.String getPL() {
        return PL;
    }


    /**
     * Sets the PL value for this CertificateInfo.
     * 
     * @param PL
     */
    public void setPL(java.lang.String PL) {
        this.PL = PL;
    }


    /**
     * Gets the GL value for this CertificateInfo.
     * 
     * @return GL
     */
    public java.lang.String getGL() {
        return GL;
    }


    /**
     * Sets the GL value for this CertificateInfo.
     * 
     * @param GL
     */
    public void setGL(java.lang.String GL) {
        this.GL = GL;
    }


    /**
     * Gets the ZXXS value for this CertificateInfo.
     * 
     * @return ZXXS
     */
    public java.lang.String getZXXS() {
        return ZXXS;
    }


    /**
     * Sets the ZXXS value for this CertificateInfo.
     * 
     * @param ZXXS
     */
    public void setZXXS(java.lang.String ZXXS) {
        this.ZXXS = ZXXS;
    }


    /**
     * Gets the QLJ value for this CertificateInfo.
     * 
     * @return QLJ
     */
    public java.lang.String getQLJ() {
        return QLJ;
    }


    /**
     * Sets the QLJ value for this CertificateInfo.
     * 
     * @param QLJ
     */
    public void setQLJ(java.lang.String QLJ) {
        this.QLJ = QLJ;
    }


    /**
     * Gets the HLJ value for this CertificateInfo.
     * 
     * @return HLJ
     */
    public java.lang.String getHLJ() {
        return HLJ;
    }


    /**
     * Sets the HLJ value for this CertificateInfo.
     * 
     * @param HLJ
     */
    public void setHLJ(java.lang.String HLJ) {
        this.HLJ = HLJ;
    }


    /**
     * Gets the LTS value for this CertificateInfo.
     * 
     * @return LTS
     */
    public java.lang.String getLTS() {
        return LTS;
    }


    /**
     * Sets the LTS value for this CertificateInfo.
     * 
     * @param LTS
     */
    public void setLTS(java.lang.String LTS) {
        this.LTS = LTS;
    }


    /**
     * Gets the LTGG value for this CertificateInfo.
     * 
     * @return LTGG
     */
    public java.lang.String getLTGG() {
        return LTGG;
    }


    /**
     * Sets the LTGG value for this CertificateInfo.
     * 
     * @param LTGG
     */
    public void setLTGG(java.lang.String LTGG) {
        this.LTGG = LTGG;
    }


    /**
     * Gets the GBTHPS value for this CertificateInfo.
     * 
     * @return GBTHPS
     */
    public java.lang.String getGBTHPS() {
        return GBTHPS;
    }


    /**
     * Sets the GBTHPS value for this CertificateInfo.
     * 
     * @param GBTHPS
     */
    public void setGBTHPS(java.lang.String GBTHPS) {
        this.GBTHPS = GBTHPS;
    }


    /**
     * Gets the ZJ value for this CertificateInfo.
     * 
     * @return ZJ
     */
    public java.lang.String getZJ() {
        return ZJ;
    }


    /**
     * Sets the ZJ value for this CertificateInfo.
     * 
     * @param ZJ
     */
    public void setZJ(java.lang.String ZJ) {
        this.ZJ = ZJ;
    }


    /**
     * Gets the ZH value for this CertificateInfo.
     * 
     * @return ZH
     */
    public java.lang.String getZH() {
        return ZH;
    }


    /**
     * Sets the ZH value for this CertificateInfo.
     * 
     * @param ZH
     */
    public void setZH(java.lang.String ZH) {
        this.ZH = ZH;
    }


    /**
     * Gets the ZS value for this CertificateInfo.
     * 
     * @return ZS
     */
    public java.lang.String getZS() {
        return ZS;
    }


    /**
     * Sets the ZS value for this CertificateInfo.
     * 
     * @param ZS
     */
    public void setZS(java.lang.String ZS) {
        this.ZS = ZS;
    }


    /**
     * Gets the WKC value for this CertificateInfo.
     * 
     * @return WKC
     */
    public java.lang.String getWKC() {
        return WKC;
    }


    /**
     * Sets the WKC value for this CertificateInfo.
     * 
     * @param WKC
     */
    public void setWKC(java.lang.String WKC) {
        this.WKC = WKC;
    }


    /**
     * Gets the WKK value for this CertificateInfo.
     * 
     * @return WKK
     */
    public java.lang.String getWKK() {
        return WKK;
    }


    /**
     * Sets the WKK value for this CertificateInfo.
     * 
     * @param WKK
     */
    public void setWKK(java.lang.String WKK) {
        this.WKK = WKK;
    }


    /**
     * Gets the WKG value for this CertificateInfo.
     * 
     * @return WKG
     */
    public java.lang.String getWKG() {
        return WKG;
    }


    /**
     * Sets the WKG value for this CertificateInfo.
     * 
     * @param WKG
     */
    public void setWKG(java.lang.String WKG) {
        this.WKG = WKG;
    }


    /**
     * Gets the HXNBC value for this CertificateInfo.
     * 
     * @return HXNBC
     */
    public java.lang.String getHXNBC() {
        return HXNBC;
    }


    /**
     * Sets the HXNBC value for this CertificateInfo.
     * 
     * @param HXNBC
     */
    public void setHXNBC(java.lang.String HXNBC) {
        this.HXNBC = HXNBC;
    }


    /**
     * Gets the HXNBK value for this CertificateInfo.
     * 
     * @return HXNBK
     */
    public java.lang.String getHXNBK() {
        return HXNBK;
    }


    /**
     * Sets the HXNBK value for this CertificateInfo.
     * 
     * @param HXNBK
     */
    public void setHXNBK(java.lang.String HXNBK) {
        this.HXNBK = HXNBK;
    }


    /**
     * Gets the HXNBG value for this CertificateInfo.
     * 
     * @return HXNBG
     */
    public java.lang.String getHXNBG() {
        return HXNBG;
    }


    /**
     * Sets the HXNBG value for this CertificateInfo.
     * 
     * @param HXNBG
     */
    public void setHXNBG(java.lang.String HXNBG) {
        this.HXNBG = HXNBG;
    }


    /**
     * Gets the ZZL value for this CertificateInfo.
     * 
     * @return ZZL
     */
    public java.lang.String getZZL() {
        return ZZL;
    }


    /**
     * Sets the ZZL value for this CertificateInfo.
     * 
     * @param ZZL
     */
    public void setZZL(java.lang.String ZZL) {
        this.ZZL = ZZL;
    }


    /**
     * Gets the EDZZL value for this CertificateInfo.
     * 
     * @return EDZZL
     */
    public java.lang.String getEDZZL() {
        return EDZZL;
    }


    /**
     * Sets the EDZZL value for this CertificateInfo.
     * 
     * @param EDZZL
     */
    public void setEDZZL(java.lang.String EDZZL) {
        this.EDZZL = EDZZL;
    }


    /**
     * Gets the ZBZL value for this CertificateInfo.
     * 
     * @return ZBZL
     */
    public java.lang.String getZBZL() {
        return ZBZL;
    }


    /**
     * Sets the ZBZL value for this CertificateInfo.
     * 
     * @param ZBZL
     */
    public void setZBZL(java.lang.String ZBZL) {
        this.ZBZL = ZBZL;
    }


    /**
     * Gets the ZZLLYXS value for this CertificateInfo.
     * 
     * @return ZZLLYXS
     */
    public java.lang.String getZZLLYXS() {
        return ZZLLYXS;
    }


    /**
     * Sets the ZZLLYXS value for this CertificateInfo.
     * 
     * @param ZZLLYXS
     */
    public void setZZLLYXS(java.lang.String ZZLLYXS) {
        this.ZZLLYXS = ZZLLYXS;
    }


    /**
     * Gets the ZQYZZL value for this CertificateInfo.
     * 
     * @return ZQYZZL
     */
    public java.lang.String getZQYZZL() {
        return ZQYZZL;
    }


    /**
     * Sets the ZQYZZL value for this CertificateInfo.
     * 
     * @param ZQYZZL
     */
    public void setZQYZZL(java.lang.String ZQYZZL) {
        this.ZQYZZL = ZQYZZL;
    }


    /**
     * Gets the EDZK value for this CertificateInfo.
     * 
     * @return EDZK
     */
    public java.lang.String getEDZK() {
        return EDZK;
    }


    /**
     * Sets the EDZK value for this CertificateInfo.
     * 
     * @param EDZK
     */
    public void setEDZK(java.lang.String EDZK) {
        this.EDZK = EDZK;
    }


    /**
     * Gets the BGCAZZDYXZZL value for this CertificateInfo.
     * 
     * @return BGCAZZDYXZZL
     */
    public java.lang.String getBGCAZZDYXZZL() {
        return BGCAZZDYXZZL;
    }


    /**
     * Sets the BGCAZZDYXZZL value for this CertificateInfo.
     * 
     * @param BGCAZZDYXZZL
     */
    public void setBGCAZZDYXZZL(java.lang.String BGCAZZDYXZZL) {
        this.BGCAZZDYXZZL = BGCAZZDYXZZL;
    }


    /**
     * Gets the JSSZCRS value for this CertificateInfo.
     * 
     * @return JSSZCRS
     */
    public java.lang.String getJSSZCRS() {
        return JSSZCRS;
    }


    /**
     * Sets the JSSZCRS value for this CertificateInfo.
     * 
     * @param JSSZCRS
     */
    public void setJSSZCRS(java.lang.String JSSZCRS) {
        this.JSSZCRS = JSSZCRS;
    }


    /**
     * Gets the ZGCS value for this CertificateInfo.
     * 
     * @return ZGCS
     */
    public java.lang.String getZGCS() {
        return ZGCS;
    }


    /**
     * Sets the ZGCS value for this CertificateInfo.
     * 
     * @param ZGCS
     */
    public void setZGCS(java.lang.String ZGCS) {
        this.ZGCS = ZGCS;
    }


    /**
     * Gets the CLZZRQ value for this CertificateInfo.
     * 
     * @return CLZZRQ
     */
    public java.util.Calendar getCLZZRQ() {
        return CLZZRQ;
    }


    /**
     * Sets the CLZZRQ value for this CertificateInfo.
     * 
     * @param CLZZRQ
     */
    public void setCLZZRQ(java.util.Calendar CLZZRQ) {
        this.CLZZRQ = CLZZRQ;
    }


    /**
     * Gets the BZ value for this CertificateInfo.
     * 
     * @return BZ
     */
    public java.lang.String getBZ() {
        return BZ;
    }


    /**
     * Sets the BZ value for this CertificateInfo.
     * 
     * @param BZ
     */
    public void setBZ(java.lang.String BZ) {
        this.BZ = BZ;
    }


    /**
     * Gets the CPSCDZ value for this CertificateInfo.
     * 
     * @return CPSCDZ
     */
    public java.lang.String getCPSCDZ() {
        return CPSCDZ;
    }


    /**
     * Sets the CPSCDZ value for this CertificateInfo.
     * 
     * @param CPSCDZ
     */
    public void setCPSCDZ(java.lang.String CPSCDZ) {
        this.CPSCDZ = CPSCDZ;
    }


    /**
     * Gets the CZRQ value for this CertificateInfo.
     * 
     * @return CZRQ
     */
    public java.util.Calendar getCZRQ() {
        return CZRQ;
    }


    /**
     * Sets the CZRQ value for this CertificateInfo.
     * 
     * @param CZRQ
     */
    public void setCZRQ(java.util.Calendar CZRQ) {
        this.CZRQ = CZRQ;
    }


    /**
     * Gets the FZRQ value for this CertificateInfo.
     * 
     * @return FZRQ
     */
    public java.util.Calendar getFZRQ() {
        return FZRQ;
    }


    /**
     * Sets the FZRQ value for this CertificateInfo.
     * 
     * @param FZRQ
     */
    public void setFZRQ(java.util.Calendar FZRQ) {
        this.FZRQ = FZRQ;
    }


    /**
     * Gets the CLSCDWMC value for this CertificateInfo.
     * 
     * @return CLSCDWMC
     */
    public java.lang.String getCLSCDWMC() {
        return CLSCDWMC;
    }


    /**
     * Sets the CLSCDWMC value for this CertificateInfo.
     * 
     * @param CLSCDWMC
     */
    public void setCLSCDWMC(java.lang.String CLSCDWMC) {
        this.CLSCDWMC = CLSCDWMC;
    }


    /**
     * Gets the YH value for this CertificateInfo.
     * 
     * @return YH
     */
    public java.lang.String getYH() {
        return YH;
    }


    /**
     * Sets the YH value for this CertificateInfo.
     * 
     * @param YH
     */
    public void setYH(java.lang.String YH) {
        this.YH = YH;
    }


    /**
     * Gets the ZXZS value for this CertificateInfo.
     * 
     * @return ZXZS
     */
    public java.lang.String getZXZS() {
        return ZXZS;
    }


    /**
     * Sets the ZXZS value for this CertificateInfo.
     * 
     * @param ZXZS
     */
    public void setZXZS(java.lang.String ZXZS) {
        this.ZXZS = ZXZS;
    }


    /**
     * Gets the CDDBJ value for this CertificateInfo.
     * 
     * @return CDDBJ
     */
    public java.lang.String getCDDBJ() {
        return CDDBJ;
    }


    /**
     * Sets the CDDBJ value for this CertificateInfo.
     * 
     * @param CDDBJ
     */
    public void setCDDBJ(java.lang.String CDDBJ) {
        this.CDDBJ = CDDBJ;
    }


    /**
     * Gets the PZXLH value for this CertificateInfo.
     * 
     * @return PZXLH
     */
    public java.lang.String getPZXLH() {
        return PZXLH;
    }


    /**
     * Sets the PZXLH value for this CertificateInfo.
     * 
     * @param PZXLH
     */
    public void setPZXLH(java.lang.String PZXLH) {
        this.PZXLH = PZXLH;
    }


    /**
     * Gets the CREATETIME value for this CertificateInfo.
     * 
     * @return CREATETIME
     */
    public java.util.Calendar getCREATETIME() {
        return CREATETIME;
    }


    /**
     * Sets the CREATETIME value for this CertificateInfo.
     * 
     * @param CREATETIME
     */
    public void setCREATETIME(java.util.Calendar CREATETIME) {
        this.CREATETIME = CREATETIME;
    }


    /**
     * Gets the VEHICLE_STATUS value for this CertificateInfo.
     * 
     * @return VEHICLE_STATUS
     */
    public java.lang.String getVEHICLE_STATUS() {
        return VEHICLE_STATUS;
    }


    /**
     * Sets the VEHICLE_STATUS value for this CertificateInfo.
     * 
     * @param VEHICLE_STATUS
     */
    public void setVEHICLE_STATUS(java.lang.String VEHICLE_STATUS) {
        this.VEHICLE_STATUS = VEHICLE_STATUS;
    }


    /**
     * Gets the RESPONSE_CODE value for this CertificateInfo.
     * 
     * @return RESPONSE_CODE
     */
    public java.lang.String getRESPONSE_CODE() {
        return RESPONSE_CODE;
    }


    /**
     * Sets the RESPONSE_CODE value for this CertificateInfo.
     * 
     * @param RESPONSE_CODE
     */
    public void setRESPONSE_CODE(java.lang.String RESPONSE_CODE) {
        this.RESPONSE_CODE = RESPONSE_CODE;
    }


    /**
     * Gets the CLIENT_HARDWARE_INFO value for this CertificateInfo.
     * 
     * @return CLIENT_HARDWARE_INFO
     */
    public java.lang.String getCLIENT_HARDWARE_INFO() {
        return CLIENT_HARDWARE_INFO;
    }


    /**
     * Sets the CLIENT_HARDWARE_INFO value for this CertificateInfo.
     * 
     * @param CLIENT_HARDWARE_INFO
     */
    public void setCLIENT_HARDWARE_INFO(java.lang.String CLIENT_HARDWARE_INFO) {
        this.CLIENT_HARDWARE_INFO = CLIENT_HARDWARE_INFO;
    }


    /**
     * Gets the FEEDBACK_TIME value for this CertificateInfo.
     * 
     * @return FEEDBACK_TIME
     */
    public java.util.Calendar getFEEDBACK_TIME() {
        return FEEDBACK_TIME;
    }


    /**
     * Sets the FEEDBACK_TIME value for this CertificateInfo.
     * 
     * @param FEEDBACK_TIME
     */
    public void setFEEDBACK_TIME(java.util.Calendar FEEDBACK_TIME) {
        this.FEEDBACK_TIME = FEEDBACK_TIME;
    }


    /**
     * Gets the HD_HOST value for this CertificateInfo.
     * 
     * @return HD_HOST
     */
    public java.lang.String getHD_HOST() {
        return HD_HOST;
    }


    /**
     * Sets the HD_HOST value for this CertificateInfo.
     * 
     * @param HD_HOST
     */
    public void setHD_HOST(java.lang.String HD_HOST) {
        this.HD_HOST = HD_HOST;
    }


    /**
     * Gets the HD_USER value for this CertificateInfo.
     * 
     * @return HD_USER
     */
    public java.lang.String getHD_USER() {
        return HD_USER;
    }


    /**
     * Sets the HD_USER value for this CertificateInfo.
     * 
     * @param HD_USER
     */
    public void setHD_USER(java.lang.String HD_USER) {
        this.HD_USER = HD_USER;
    }


    /**
     * Gets the UKEY value for this CertificateInfo.
     * 
     * @return UKEY
     */
    public java.lang.String getUKEY() {
        return UKEY;
    }


    /**
     * Sets the UKEY value for this CertificateInfo.
     * 
     * @param UKEY
     */
    public void setUKEY(java.lang.String UKEY) {
        this.UKEY = UKEY;
    }


    /**
     * Gets the UPDATETIME value for this CertificateInfo.
     * 
     * @return UPDATETIME
     */
    public java.util.Calendar getUPDATETIME() {
        return UPDATETIME;
    }


    /**
     * Sets the UPDATETIME value for this CertificateInfo.
     * 
     * @param UPDATETIME
     */
    public void setUPDATETIME(java.util.Calendar UPDATETIME) {
        this.UPDATETIME = UPDATETIME;
    }


    /**
     * Gets the UPSEND_TAG value for this CertificateInfo.
     * 
     * @return UPSEND_TAG
     */
    public java.lang.String getUPSEND_TAG() {
        return UPSEND_TAG;
    }


    /**
     * Sets the UPSEND_TAG value for this CertificateInfo.
     * 
     * @param UPSEND_TAG
     */
    public void setUPSEND_TAG(java.lang.String UPSEND_TAG) {
        this.UPSEND_TAG = UPSEND_TAG;
    }


    /**
     * Gets the VERCODE value for this CertificateInfo.
     * 
     * @return VERCODE
     */
    public java.lang.String getVERCODE() {
        return VERCODE;
    }


    /**
     * Sets the VERCODE value for this CertificateInfo.
     * 
     * @param VERCODE
     */
    public void setVERCODE(java.lang.String VERCODE) {
        this.VERCODE = VERCODE;
    }


    /**
     * Gets the VERSION value for this CertificateInfo.
     * 
     * @return VERSION
     */
    public java.lang.String getVERSION() {
        return VERSION;
    }


    /**
     * Sets the VERSION value for this CertificateInfo.
     * 
     * @param VERSION
     */
    public void setVERSION(java.lang.String VERSION) {
        this.VERSION = VERSION;
    }


    /**
     * Gets the CLZTXX value for this CertificateInfo.
     * 
     * @return CLZTXX
     */
    public java.lang.String getCLZTXX() {
        return CLZTXX;
    }


    /**
     * Sets the CLZTXX value for this CertificateInfo.
     * 
     * @param CLZTXX
     */
    public void setCLZTXX(java.lang.String CLZTXX) {
        this.CLZTXX = CLZTXX;
    }


    /**
     * Gets the DYWYM value for this CertificateInfo.
     * 
     * @return DYWYM
     */
    public java.lang.String getDYWYM() {
        return DYWYM;
    }


    /**
     * Sets the DYWYM value for this CertificateInfo.
     * 
     * @param DYWYM
     */
    public void setDYWYM(java.lang.String DYWYM) {
        this.DYWYM = DYWYM;
    }


    /**
     * Gets the QYID value for this CertificateInfo.
     * 
     * @return QYID
     */
    public java.lang.String getQYID() {
        return QYID;
    }


    /**
     * Sets the QYID value for this CertificateInfo.
     * 
     * @param QYID
     */
    public void setQYID(java.lang.String QYID) {
        this.QYID = QYID;
    }


    /**
     * Gets the ZCHGZBH value for this CertificateInfo.
     * 
     * @return ZCHGZBH
     */
    public java.lang.String getZCHGZBH() {
        return ZCHGZBH;
    }


    /**
     * Sets the ZCHGZBH value for this CertificateInfo.
     * 
     * @param ZCHGZBH
     */
    public void setZCHGZBH(java.lang.String ZCHGZBH) {
        this.ZCHGZBH = ZCHGZBH;
    }


    /**
     * Gets the ZZBH value for this CertificateInfo.
     * 
     * @return ZZBH
     */
    public java.lang.String getZZBH() {
        return ZZBH;
    }


    /**
     * Sets the ZZBH value for this CertificateInfo.
     * 
     * @param ZZBH
     */
    public void setZZBH(java.lang.String ZZBH) {
        this.ZZBH = ZZBH;
    }


    /**
     * Gets the CPH value for this CertificateInfo.
     * 
     * @return CPH
     */
    public java.lang.String getCPH() {
        return CPH;
    }


    /**
     * Sets the CPH value for this CertificateInfo.
     * 
     * @param CPH
     */
    public void setCPH(java.lang.String CPH) {
        this.CPH = CPH;
    }


    /**
     * Gets the DPHGZBH value for this CertificateInfo.
     * 
     * @return DPHGZBH
     */
    public java.lang.String getDPHGZBH() {
        return DPHGZBH;
    }


    /**
     * Sets the DPHGZBH value for this CertificateInfo.
     * 
     * @param DPHGZBH
     */
    public void setDPHGZBH(java.lang.String DPHGZBH) {
        this.DPHGZBH = DPHGZBH;
    }


    /**
     * Gets the DPID value for this CertificateInfo.
     * 
     * @return DPID
     */
    public java.lang.String getDPID() {
        return DPID;
    }


    /**
     * Sets the DPID value for this CertificateInfo.
     * 
     * @param DPID
     */
    public void setDPID(java.lang.String DPID) {
        this.DPID = DPID;
    }


    /**
     * Gets the GGSXRQ value for this CertificateInfo.
     * 
     * @return GGSXRQ
     */
    public java.util.Calendar getGGSXRQ() {
        return GGSXRQ;
    }


    /**
     * Sets the GGSXRQ value for this CertificateInfo.
     * 
     * @param GGSXRQ
     */
    public void setGGSXRQ(java.util.Calendar GGSXRQ) {
        this.GGSXRQ = GGSXRQ;
    }


    /**
     * Gets the HZDCZFS value for this CertificateInfo.
     * 
     * @return HZDCZFS
     */
    public java.lang.String getHZDCZFS() {
        return HZDCZFS;
    }


    /**
     * Sets the HZDCZFS value for this CertificateInfo.
     * 
     * @param HZDCZFS
     */
    public void setHZDCZFS(java.lang.String HZDCZFS) {
        this.HZDCZFS = HZDCZFS;
    }


    /**
     * Gets the HZDFS value for this CertificateInfo.
     * 
     * @return HZDFS
     */
    public java.lang.String getHZDFS() {
        return HZDFS;
    }


    /**
     * Sets the HZDFS value for this CertificateInfo.
     * 
     * @param HZDFS
     */
    public void setHZDFS(java.lang.String HZDFS) {
        this.HZDFS = HZDFS;
    }


    /**
     * Gets the PC value for this CertificateInfo.
     * 
     * @return PC
     */
    public java.lang.String getPC() {
        return PC;
    }


    /**
     * Sets the PC value for this CertificateInfo.
     * 
     * @param PC
     */
    public void setPC(java.lang.String PC) {
        this.PC = PC;
    }


    /**
     * Gets the QYBZ value for this CertificateInfo.
     * 
     * @return QYBZ
     */
    public java.lang.String getQYBZ() {
        return QYBZ;
    }


    /**
     * Sets the QYBZ value for this CertificateInfo.
     * 
     * @param QYBZ
     */
    public void setQYBZ(java.lang.String QYBZ) {
        this.QYBZ = QYBZ;
    }


    /**
     * Gets the QYQTXX value for this CertificateInfo.
     * 
     * @return QYQTXX
     */
    public java.lang.String getQYQTXX() {
        return QYQTXX;
    }


    /**
     * Sets the QYQTXX value for this CertificateInfo.
     * 
     * @param QYQTXX
     */
    public void setQYQTXX(java.lang.String QYQTXX) {
        this.QYQTXX = QYQTXX;
    }


    /**
     * Gets the QZDCZFS value for this CertificateInfo.
     * 
     * @return QZDCZFS
     */
    public java.lang.String getQZDCZFS() {
        return QZDCZFS;
    }


    /**
     * Sets the QZDCZFS value for this CertificateInfo.
     * 
     * @param QZDCZFS
     */
    public void setQZDCZFS(java.lang.String QZDCZFS) {
        this.QZDCZFS = QZDCZFS;
    }


    /**
     * Gets the QZDFS value for this CertificateInfo.
     * 
     * @return QZDFS
     */
    public java.lang.String getQZDFS() {
        return QZDFS;
    }


    /**
     * Sets the QZDFS value for this CertificateInfo.
     * 
     * @param QZDFS
     */
    public void setQZDFS(java.lang.String QZDFS) {
        this.QZDFS = QZDFS;
    }


    /**
     * Gets the WZHGZBH value for this CertificateInfo.
     * 
     * @return WZHGZBH
     */
    public java.lang.String getWZHGZBH() {
        return WZHGZBH;
    }


    /**
     * Sets the WZHGZBH value for this CertificateInfo.
     * 
     * @param WZHGZBH
     */
    public void setWZHGZBH(java.lang.String WZHGZBH) {
        this.WZHGZBH = WZHGZBH;
    }


    /**
     * Gets the JFPZID value for this CertificateInfo.
     * 
     * @return JFPZID
     */
    public java.lang.String getJFPZID() {
        return JFPZID;
    }


    /**
     * Sets the JFPZID value for this CertificateInfo.
     * 
     * @param JFPZID
     */
    public void setJFPZID(java.lang.String JFPZID) {
        this.JFPZID = JFPZID;
    }


    /**
     * Gets the VINBSYY value for this CertificateInfo.
     * 
     * @return VINBSYY
     */
    public java.lang.String getVINBSYY() {
        return VINBSYY;
    }


    /**
     * Sets the VINBSYY value for this CertificateInfo.
     * 
     * @param VINBSYY
     */
    public void setVINBSYY(java.lang.String VINBSYY) {
        this.VINBSYY = VINBSYY;
    }


    /**
     * Gets the ISCXNF value for this CertificateInfo.
     * 
     * @return ISCXNF
     */
    public java.lang.String getISCXNF() {
        return ISCXNF;
    }


    /**
     * Sets the ISCXNF value for this CertificateInfo.
     * 
     * @param ISCXNF
     */
    public void setISCXNF(java.lang.String ISCXNF) {
        this.ISCXNF = ISCXNF;
    }


    /**
     * Gets the ZYZYCMSBS value for this CertificateInfo.
     * 
     * @return ZYZYCMSBS
     */
    public java.lang.String getZYZYCMSBS() {
        return ZYZYCMSBS;
    }


    /**
     * Sets the ZYZYCMSBS value for this CertificateInfo.
     * 
     * @param ZYZYCMSBS
     */
    public void setZYZYCMSBS(java.lang.String ZYZYCMSBS) {
        this.ZYZYCMSBS = ZYZYCMSBS;
    }


    /**
     * Gets the XNYQCJMSBS value for this CertificateInfo.
     * 
     * @return XNYQCJMSBS
     */
    public java.lang.String getXNYQCJMSBS() {
        return XNYQCJMSBS;
    }


    /**
     * Sets the XNYQCJMSBS value for this CertificateInfo.
     * 
     * @param XNYQCJMSBS
     */
    public void setXNYQCJMSBS(java.lang.String XNYQCJMSBS) {
        this.XNYQCJMSBS = XNYQCJMSBS;
    }


    /**
     * Gets the HDMSBS value for this CertificateInfo.
     * 
     * @return HDMSBS
     */
    public java.lang.String getHDMSBS() {
        return HDMSBS;
    }


    /**
     * Sets the HDMSBS value for this CertificateInfo.
     * 
     * @param HDMSBS
     */
    public void setHDMSBS(java.lang.String HDMSBS) {
        this.HDMSBS = HDMSBS;
    }


    /**
     * Gets the XNYQCZL value for this CertificateInfo.
     * 
     * @return XNYQCZL
     */
    public java.lang.String getXNYQCZL() {
        return XNYQCZL;
    }


    /**
     * Sets the XNYQCZL value for this CertificateInfo.
     * 
     * @param XNYQCZL
     */
    public void setXNYQCZL(java.lang.String XNYQCZL) {
        this.XNYQCZL = XNYQCZL;
    }


    /**
     * Gets the SWTQYMC value for this CertificateInfo.
     * 
     * @return SWTQYMC
     */
    public java.lang.String getSWTQYMC() {
        return SWTQYMC;
    }


    /**
     * Sets the SWTQYMC value for this CertificateInfo.
     * 
     * @param SWTQYMC
     */
    public void setSWTQYMC(java.lang.String SWTQYMC) {
        this.SWTQYMC = SWTQYMC;
    }


    /**
     * Gets the SWTQYSCDZ value for this CertificateInfo.
     * 
     * @return SWTQYSCDZ
     */
    public java.lang.String getSWTQYSCDZ() {
        return SWTQYSCDZ;
    }


    /**
     * Sets the SWTQYSCDZ value for this CertificateInfo.
     * 
     * @param SWTQYSCDZ
     */
    public void setSWTQYSCDZ(java.lang.String SWTQYSCDZ) {
        this.SWTQYSCDZ = SWTQYSCDZ;
    }


    /**
     * Gets the SWTQYTYSHXYDM value for this CertificateInfo.
     * 
     * @return SWTQYTYSHXYDM
     */
    public java.lang.String getSWTQYTYSHXYDM() {
        return SWTQYTYSHXYDM;
    }


    /**
     * Sets the SWTQYTYSHXYDM value for this CertificateInfo.
     * 
     * @param SWTQYTYSHXYDM
     */
    public void setSWTQYTYSHXYDM(java.lang.String SWTQYTYSHXYDM) {
        this.SWTQYTYSHXYDM = SWTQYTYSHXYDM;
    }

    private java.lang.Object __equalsCalc = null;
    public synchronized boolean equals(java.lang.Object obj) {
        if (!(obj instanceof CertificateInfo)) return false;
        CertificateInfo other = (CertificateInfo) obj;
        if (obj == null) return false;
        if (this == obj) return true;
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            ((this.h_ID==null && other.getH_ID()==null) || 
             (this.h_ID!=null &&
              this.h_ID.equals(other.getH_ID()))) &&
            ((this.CJH==null && other.getCJH()==null) || 
             (this.CJH!=null &&
              this.CJH.equals(other.getCJH()))) &&
            ((this.CLSBDH==null && other.getCLSBDH()==null) || 
             (this.CLSBDH!=null &&
              this.CLSBDH.equals(other.getCLSBDH()))) &&
            ((this.CLZZQYMC==null && other.getCLZZQYMC()==null) || 
             (this.CLZZQYMC!=null &&
              this.CLZZQYMC.equals(other.getCLZZQYMC()))) &&
            ((this.CLLX==null && other.getCLLX()==null) || 
             (this.CLLX!=null &&
              this.CLLX.equals(other.getCLLX()))) &&
            ((this.CLMC==null && other.getCLMC()==null) || 
             (this.CLMC!=null &&
              this.CLMC.equals(other.getCLMC()))) &&
            ((this.CLPP==null && other.getCLPP()==null) || 
             (this.CLPP!=null &&
              this.CLPP.equals(other.getCLPP()))) &&
            ((this.CLXH==null && other.getCLXH()==null) || 
             (this.CLXH!=null &&
              this.CLXH.equals(other.getCLXH()))) &&
            ((this.CSYS==null && other.getCSYS()==null) || 
             (this.CSYS!=null &&
              this.CSYS.equals(other.getCSYS()))) &&
            ((this.DPXH==null && other.getDPXH()==null) || 
             (this.DPXH!=null &&
              this.DPXH.equals(other.getDPXH()))) &&
            ((this.FDJH==null && other.getFDJH()==null) || 
             (this.FDJH!=null &&
              this.FDJH.equals(other.getFDJH()))) &&
            ((this.FDJXH==null && other.getFDJXH()==null) || 
             (this.FDJXH!=null &&
              this.FDJXH.equals(other.getFDJXH()))) &&
            ((this.RLZL==null && other.getRLZL()==null) || 
             (this.RLZL!=null &&
              this.RLZL.equals(other.getRLZL()))) &&
            ((this.PFBZ==null && other.getPFBZ()==null) || 
             (this.PFBZ!=null &&
              this.PFBZ.equals(other.getPFBZ()))) &&
            ((this.PL==null && other.getPL()==null) || 
             (this.PL!=null &&
              this.PL.equals(other.getPL()))) &&
            ((this.GL==null && other.getGL()==null) || 
             (this.GL!=null &&
              this.GL.equals(other.getGL()))) &&
            ((this.ZXXS==null && other.getZXXS()==null) || 
             (this.ZXXS!=null &&
              this.ZXXS.equals(other.getZXXS()))) &&
            ((this.QLJ==null && other.getQLJ()==null) || 
             (this.QLJ!=null &&
              this.QLJ.equals(other.getQLJ()))) &&
            ((this.HLJ==null && other.getHLJ()==null) || 
             (this.HLJ!=null &&
              this.HLJ.equals(other.getHLJ()))) &&
            ((this.LTS==null && other.getLTS()==null) || 
             (this.LTS!=null &&
              this.LTS.equals(other.getLTS()))) &&
            ((this.LTGG==null && other.getLTGG()==null) || 
             (this.LTGG!=null &&
              this.LTGG.equals(other.getLTGG()))) &&
            ((this.GBTHPS==null && other.getGBTHPS()==null) || 
             (this.GBTHPS!=null &&
              this.GBTHPS.equals(other.getGBTHPS()))) &&
            ((this.ZJ==null && other.getZJ()==null) || 
             (this.ZJ!=null &&
              this.ZJ.equals(other.getZJ()))) &&
            ((this.ZH==null && other.getZH()==null) || 
             (this.ZH!=null &&
              this.ZH.equals(other.getZH()))) &&
            ((this.ZS==null && other.getZS()==null) || 
             (this.ZS!=null &&
              this.ZS.equals(other.getZS()))) &&
            ((this.WKC==null && other.getWKC()==null) || 
             (this.WKC!=null &&
              this.WKC.equals(other.getWKC()))) &&
            ((this.WKK==null && other.getWKK()==null) || 
             (this.WKK!=null &&
              this.WKK.equals(other.getWKK()))) &&
            ((this.WKG==null && other.getWKG()==null) || 
             (this.WKG!=null &&
              this.WKG.equals(other.getWKG()))) &&
            ((this.HXNBC==null && other.getHXNBC()==null) || 
             (this.HXNBC!=null &&
              this.HXNBC.equals(other.getHXNBC()))) &&
            ((this.HXNBK==null && other.getHXNBK()==null) || 
             (this.HXNBK!=null &&
              this.HXNBK.equals(other.getHXNBK()))) &&
            ((this.HXNBG==null && other.getHXNBG()==null) || 
             (this.HXNBG!=null &&
              this.HXNBG.equals(other.getHXNBG()))) &&
            ((this.ZZL==null && other.getZZL()==null) || 
             (this.ZZL!=null &&
              this.ZZL.equals(other.getZZL()))) &&
            ((this.EDZZL==null && other.getEDZZL()==null) || 
             (this.EDZZL!=null &&
              this.EDZZL.equals(other.getEDZZL()))) &&
            ((this.ZBZL==null && other.getZBZL()==null) || 
             (this.ZBZL!=null &&
              this.ZBZL.equals(other.getZBZL()))) &&
            ((this.ZZLLYXS==null && other.getZZLLYXS()==null) || 
             (this.ZZLLYXS!=null &&
              this.ZZLLYXS.equals(other.getZZLLYXS()))) &&
            ((this.ZQYZZL==null && other.getZQYZZL()==null) || 
             (this.ZQYZZL!=null &&
              this.ZQYZZL.equals(other.getZQYZZL()))) &&
            ((this.EDZK==null && other.getEDZK()==null) || 
             (this.EDZK!=null &&
              this.EDZK.equals(other.getEDZK()))) &&
            ((this.BGCAZZDYXZZL==null && other.getBGCAZZDYXZZL()==null) || 
             (this.BGCAZZDYXZZL!=null &&
              this.BGCAZZDYXZZL.equals(other.getBGCAZZDYXZZL()))) &&
            ((this.JSSZCRS==null && other.getJSSZCRS()==null) || 
             (this.JSSZCRS!=null &&
              this.JSSZCRS.equals(other.getJSSZCRS()))) &&
            ((this.ZGCS==null && other.getZGCS()==null) || 
             (this.ZGCS!=null &&
              this.ZGCS.equals(other.getZGCS()))) &&
            ((this.CLZZRQ==null && other.getCLZZRQ()==null) || 
             (this.CLZZRQ!=null &&
              this.CLZZRQ.equals(other.getCLZZRQ()))) &&
            ((this.BZ==null && other.getBZ()==null) || 
             (this.BZ!=null &&
              this.BZ.equals(other.getBZ()))) &&
            ((this.CPSCDZ==null && other.getCPSCDZ()==null) || 
             (this.CPSCDZ!=null &&
              this.CPSCDZ.equals(other.getCPSCDZ()))) &&
            ((this.CZRQ==null && other.getCZRQ()==null) || 
             (this.CZRQ!=null &&
              this.CZRQ.equals(other.getCZRQ()))) &&
            ((this.FZRQ==null && other.getFZRQ()==null) || 
             (this.FZRQ!=null &&
              this.FZRQ.equals(other.getFZRQ()))) &&
            ((this.CLSCDWMC==null && other.getCLSCDWMC()==null) || 
             (this.CLSCDWMC!=null &&
              this.CLSCDWMC.equals(other.getCLSCDWMC()))) &&
            ((this.YH==null && other.getYH()==null) || 
             (this.YH!=null &&
              this.YH.equals(other.getYH()))) &&
            ((this.ZXZS==null && other.getZXZS()==null) || 
             (this.ZXZS!=null &&
              this.ZXZS.equals(other.getZXZS()))) &&
            ((this.CDDBJ==null && other.getCDDBJ()==null) || 
             (this.CDDBJ!=null &&
              this.CDDBJ.equals(other.getCDDBJ()))) &&
            ((this.PZXLH==null && other.getPZXLH()==null) || 
             (this.PZXLH!=null &&
              this.PZXLH.equals(other.getPZXLH()))) &&
            ((this.CREATETIME==null && other.getCREATETIME()==null) || 
             (this.CREATETIME!=null &&
              this.CREATETIME.equals(other.getCREATETIME()))) &&
            ((this.VEHICLE_STATUS==null && other.getVEHICLE_STATUS()==null) || 
             (this.VEHICLE_STATUS!=null &&
              this.VEHICLE_STATUS.equals(other.getVEHICLE_STATUS()))) &&
            ((this.RESPONSE_CODE==null && other.getRESPONSE_CODE()==null) || 
             (this.RESPONSE_CODE!=null &&
              this.RESPONSE_CODE.equals(other.getRESPONSE_CODE()))) &&
            ((this.CLIENT_HARDWARE_INFO==null && other.getCLIENT_HARDWARE_INFO()==null) || 
             (this.CLIENT_HARDWARE_INFO!=null &&
              this.CLIENT_HARDWARE_INFO.equals(other.getCLIENT_HARDWARE_INFO()))) &&
            ((this.FEEDBACK_TIME==null && other.getFEEDBACK_TIME()==null) || 
             (this.FEEDBACK_TIME!=null &&
              this.FEEDBACK_TIME.equals(other.getFEEDBACK_TIME()))) &&
            ((this.HD_HOST==null && other.getHD_HOST()==null) || 
             (this.HD_HOST!=null &&
              this.HD_HOST.equals(other.getHD_HOST()))) &&
            ((this.HD_USER==null && other.getHD_USER()==null) || 
             (this.HD_USER!=null &&
              this.HD_USER.equals(other.getHD_USER()))) &&
            ((this.UKEY==null && other.getUKEY()==null) || 
             (this.UKEY!=null &&
              this.UKEY.equals(other.getUKEY()))) &&
            ((this.UPDATETIME==null && other.getUPDATETIME()==null) || 
             (this.UPDATETIME!=null &&
              this.UPDATETIME.equals(other.getUPDATETIME()))) &&
            ((this.UPSEND_TAG==null && other.getUPSEND_TAG()==null) || 
             (this.UPSEND_TAG!=null &&
              this.UPSEND_TAG.equals(other.getUPSEND_TAG()))) &&
            ((this.VERCODE==null && other.getVERCODE()==null) || 
             (this.VERCODE!=null &&
              this.VERCODE.equals(other.getVERCODE()))) &&
            ((this.VERSION==null && other.getVERSION()==null) || 
             (this.VERSION!=null &&
              this.VERSION.equals(other.getVERSION()))) &&
            ((this.CLZTXX==null && other.getCLZTXX()==null) || 
             (this.CLZTXX!=null &&
              this.CLZTXX.equals(other.getCLZTXX()))) &&
            ((this.DYWYM==null && other.getDYWYM()==null) || 
             (this.DYWYM!=null &&
              this.DYWYM.equals(other.getDYWYM()))) &&
            ((this.QYID==null && other.getQYID()==null) || 
             (this.QYID!=null &&
              this.QYID.equals(other.getQYID()))) &&
            ((this.ZCHGZBH==null && other.getZCHGZBH()==null) || 
             (this.ZCHGZBH!=null &&
              this.ZCHGZBH.equals(other.getZCHGZBH()))) &&
            ((this.ZZBH==null && other.getZZBH()==null) || 
             (this.ZZBH!=null &&
              this.ZZBH.equals(other.getZZBH()))) &&
            ((this.CPH==null && other.getCPH()==null) || 
             (this.CPH!=null &&
              this.CPH.equals(other.getCPH()))) &&
            ((this.DPHGZBH==null && other.getDPHGZBH()==null) || 
             (this.DPHGZBH!=null &&
              this.DPHGZBH.equals(other.getDPHGZBH()))) &&
            ((this.DPID==null && other.getDPID()==null) || 
             (this.DPID!=null &&
              this.DPID.equals(other.getDPID()))) &&
            ((this.GGSXRQ==null && other.getGGSXRQ()==null) || 
             (this.GGSXRQ!=null &&
              this.GGSXRQ.equals(other.getGGSXRQ()))) &&
            ((this.HZDCZFS==null && other.getHZDCZFS()==null) || 
             (this.HZDCZFS!=null &&
              this.HZDCZFS.equals(other.getHZDCZFS()))) &&
            ((this.HZDFS==null && other.getHZDFS()==null) || 
             (this.HZDFS!=null &&
              this.HZDFS.equals(other.getHZDFS()))) &&
            ((this.PC==null && other.getPC()==null) || 
             (this.PC!=null &&
              this.PC.equals(other.getPC()))) &&
            ((this.QYBZ==null && other.getQYBZ()==null) || 
             (this.QYBZ!=null &&
              this.QYBZ.equals(other.getQYBZ()))) &&
            ((this.QYQTXX==null && other.getQYQTXX()==null) || 
             (this.QYQTXX!=null &&
              this.QYQTXX.equals(other.getQYQTXX()))) &&
            ((this.QZDCZFS==null && other.getQZDCZFS()==null) || 
             (this.QZDCZFS!=null &&
              this.QZDCZFS.equals(other.getQZDCZFS()))) &&
            ((this.QZDFS==null && other.getQZDFS()==null) || 
             (this.QZDFS!=null &&
              this.QZDFS.equals(other.getQZDFS()))) &&
            ((this.WZHGZBH==null && other.getWZHGZBH()==null) || 
             (this.WZHGZBH!=null &&
              this.WZHGZBH.equals(other.getWZHGZBH()))) &&
            ((this.JFPZID==null && other.getJFPZID()==null) || 
             (this.JFPZID!=null &&
              this.JFPZID.equals(other.getJFPZID()))) &&
            ((this.VINBSYY==null && other.getVINBSYY()==null) || 
             (this.VINBSYY!=null &&
              this.VINBSYY.equals(other.getVINBSYY()))) &&
            ((this.ISCXNF==null && other.getISCXNF()==null) || 
             (this.ISCXNF!=null &&
              this.ISCXNF.equals(other.getISCXNF()))) &&
            ((this.ZYZYCMSBS==null && other.getZYZYCMSBS()==null) || 
             (this.ZYZYCMSBS!=null &&
              this.ZYZYCMSBS.equals(other.getZYZYCMSBS()))) &&
            ((this.XNYQCJMSBS==null && other.getXNYQCJMSBS()==null) || 
             (this.XNYQCJMSBS!=null &&
              this.XNYQCJMSBS.equals(other.getXNYQCJMSBS()))) &&
            ((this.HDMSBS==null && other.getHDMSBS()==null) || 
             (this.HDMSBS!=null &&
              this.HDMSBS.equals(other.getHDMSBS()))) &&
            ((this.XNYQCZL==null && other.getXNYQCZL()==null) || 
             (this.XNYQCZL!=null &&
              this.XNYQCZL.equals(other.getXNYQCZL()))) &&
            ((this.SWTQYMC==null && other.getSWTQYMC()==null) || 
             (this.SWTQYMC!=null &&
              this.SWTQYMC.equals(other.getSWTQYMC()))) &&
            ((this.SWTQYSCDZ==null && other.getSWTQYSCDZ()==null) || 
             (this.SWTQYSCDZ!=null &&
              this.SWTQYSCDZ.equals(other.getSWTQYSCDZ()))) &&
            ((this.SWTQYTYSHXYDM==null && other.getSWTQYTYSHXYDM()==null) || 
             (this.SWTQYTYSHXYDM!=null &&
              this.SWTQYTYSHXYDM.equals(other.getSWTQYTYSHXYDM())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getH_ID() != null) {
            _hashCode += getH_ID().hashCode();
        }
        if (getCJH() != null) {
            _hashCode += getCJH().hashCode();
        }
        if (getCLSBDH() != null) {
            _hashCode += getCLSBDH().hashCode();
        }
        if (getCLZZQYMC() != null) {
            _hashCode += getCLZZQYMC().hashCode();
        }
        if (getCLLX() != null) {
            _hashCode += getCLLX().hashCode();
        }
        if (getCLMC() != null) {
            _hashCode += getCLMC().hashCode();
        }
        if (getCLPP() != null) {
            _hashCode += getCLPP().hashCode();
        }
        if (getCLXH() != null) {
            _hashCode += getCLXH().hashCode();
        }
        if (getCSYS() != null) {
            _hashCode += getCSYS().hashCode();
        }
        if (getDPXH() != null) {
            _hashCode += getDPXH().hashCode();
        }
        if (getFDJH() != null) {
            _hashCode += getFDJH().hashCode();
        }
        if (getFDJXH() != null) {
            _hashCode += getFDJXH().hashCode();
        }
        if (getRLZL() != null) {
            _hashCode += getRLZL().hashCode();
        }
        if (getPFBZ() != null) {
            _hashCode += getPFBZ().hashCode();
        }
        if (getPL() != null) {
            _hashCode += getPL().hashCode();
        }
        if (getGL() != null) {
            _hashCode += getGL().hashCode();
        }
        if (getZXXS() != null) {
            _hashCode += getZXXS().hashCode();
        }
        if (getQLJ() != null) {
            _hashCode += getQLJ().hashCode();
        }
        if (getHLJ() != null) {
            _hashCode += getHLJ().hashCode();
        }
        if (getLTS() != null) {
            _hashCode += getLTS().hashCode();
        }
        if (getLTGG() != null) {
            _hashCode += getLTGG().hashCode();
        }
        if (getGBTHPS() != null) {
            _hashCode += getGBTHPS().hashCode();
        }
        if (getZJ() != null) {
            _hashCode += getZJ().hashCode();
        }
        if (getZH() != null) {
            _hashCode += getZH().hashCode();
        }
        if (getZS() != null) {
            _hashCode += getZS().hashCode();
        }
        if (getWKC() != null) {
            _hashCode += getWKC().hashCode();
        }
        if (getWKK() != null) {
            _hashCode += getWKK().hashCode();
        }
        if (getWKG() != null) {
            _hashCode += getWKG().hashCode();
        }
        if (getHXNBC() != null) {
            _hashCode += getHXNBC().hashCode();
        }
        if (getHXNBK() != null) {
            _hashCode += getHXNBK().hashCode();
        }
        if (getHXNBG() != null) {
            _hashCode += getHXNBG().hashCode();
        }
        if (getZZL() != null) {
            _hashCode += getZZL().hashCode();
        }
        if (getEDZZL() != null) {
            _hashCode += getEDZZL().hashCode();
        }
        if (getZBZL() != null) {
            _hashCode += getZBZL().hashCode();
        }
        if (getZZLLYXS() != null) {
            _hashCode += getZZLLYXS().hashCode();
        }
        if (getZQYZZL() != null) {
            _hashCode += getZQYZZL().hashCode();
        }
        if (getEDZK() != null) {
            _hashCode += getEDZK().hashCode();
        }
        if (getBGCAZZDYXZZL() != null) {
            _hashCode += getBGCAZZDYXZZL().hashCode();
        }
        if (getJSSZCRS() != null) {
            _hashCode += getJSSZCRS().hashCode();
        }
        if (getZGCS() != null) {
            _hashCode += getZGCS().hashCode();
        }
        if (getCLZZRQ() != null) {
            _hashCode += getCLZZRQ().hashCode();
        }
        if (getBZ() != null) {
            _hashCode += getBZ().hashCode();
        }
        if (getCPSCDZ() != null) {
            _hashCode += getCPSCDZ().hashCode();
        }
        if (getCZRQ() != null) {
            _hashCode += getCZRQ().hashCode();
        }
        if (getFZRQ() != null) {
            _hashCode += getFZRQ().hashCode();
        }
        if (getCLSCDWMC() != null) {
            _hashCode += getCLSCDWMC().hashCode();
        }
        if (getYH() != null) {
            _hashCode += getYH().hashCode();
        }
        if (getZXZS() != null) {
            _hashCode += getZXZS().hashCode();
        }
        if (getCDDBJ() != null) {
            _hashCode += getCDDBJ().hashCode();
        }
        if (getPZXLH() != null) {
            _hashCode += getPZXLH().hashCode();
        }
        if (getCREATETIME() != null) {
            _hashCode += getCREATETIME().hashCode();
        }
        if (getVEHICLE_STATUS() != null) {
            _hashCode += getVEHICLE_STATUS().hashCode();
        }
        if (getRESPONSE_CODE() != null) {
            _hashCode += getRESPONSE_CODE().hashCode();
        }
        if (getCLIENT_HARDWARE_INFO() != null) {
            _hashCode += getCLIENT_HARDWARE_INFO().hashCode();
        }
        if (getFEEDBACK_TIME() != null) {
            _hashCode += getFEEDBACK_TIME().hashCode();
        }
        if (getHD_HOST() != null) {
            _hashCode += getHD_HOST().hashCode();
        }
        if (getHD_USER() != null) {
            _hashCode += getHD_USER().hashCode();
        }
        if (getUKEY() != null) {
            _hashCode += getUKEY().hashCode();
        }
        if (getUPDATETIME() != null) {
            _hashCode += getUPDATETIME().hashCode();
        }
        if (getUPSEND_TAG() != null) {
            _hashCode += getUPSEND_TAG().hashCode();
        }
        if (getVERCODE() != null) {
            _hashCode += getVERCODE().hashCode();
        }
        if (getVERSION() != null) {
            _hashCode += getVERSION().hashCode();
        }
        if (getCLZTXX() != null) {
            _hashCode += getCLZTXX().hashCode();
        }
        if (getDYWYM() != null) {
            _hashCode += getDYWYM().hashCode();
        }
        if (getQYID() != null) {
            _hashCode += getQYID().hashCode();
        }
        if (getZCHGZBH() != null) {
            _hashCode += getZCHGZBH().hashCode();
        }
        if (getZZBH() != null) {
            _hashCode += getZZBH().hashCode();
        }
        if (getCPH() != null) {
            _hashCode += getCPH().hashCode();
        }
        if (getDPHGZBH() != null) {
            _hashCode += getDPHGZBH().hashCode();
        }
        if (getDPID() != null) {
            _hashCode += getDPID().hashCode();
        }
        if (getGGSXRQ() != null) {
            _hashCode += getGGSXRQ().hashCode();
        }
        if (getHZDCZFS() != null) {
            _hashCode += getHZDCZFS().hashCode();
        }
        if (getHZDFS() != null) {
            _hashCode += getHZDFS().hashCode();
        }
        if (getPC() != null) {
            _hashCode += getPC().hashCode();
        }
        if (getQYBZ() != null) {
            _hashCode += getQYBZ().hashCode();
        }
        if (getQYQTXX() != null) {
            _hashCode += getQYQTXX().hashCode();
        }
        if (getQZDCZFS() != null) {
            _hashCode += getQZDCZFS().hashCode();
        }
        if (getQZDFS() != null) {
            _hashCode += getQZDFS().hashCode();
        }
        if (getWZHGZBH() != null) {
            _hashCode += getWZHGZBH().hashCode();
        }
        if (getJFPZID() != null) {
            _hashCode += getJFPZID().hashCode();
        }
        if (getVINBSYY() != null) {
            _hashCode += getVINBSYY().hashCode();
        }
        if (getISCXNF() != null) {
            _hashCode += getISCXNF().hashCode();
        }
        if (getZYZYCMSBS() != null) {
            _hashCode += getZYZYCMSBS().hashCode();
        }
        if (getXNYQCJMSBS() != null) {
            _hashCode += getXNYQCJMSBS().hashCode();
        }
        if (getHDMSBS() != null) {
            _hashCode += getHDMSBS().hashCode();
        }
        if (getXNYQCZL() != null) {
            _hashCode += getXNYQCZL().hashCode();
        }
        if (getSWTQYMC() != null) {
            _hashCode += getSWTQYMC().hashCode();
        }
        if (getSWTQYSCDZ() != null) {
            _hashCode += getSWTQYSCDZ().hashCode();
        }
        if (getSWTQYTYSHXYDM() != null) {
            _hashCode += getSWTQYTYSHXYDM().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(CertificateInfo.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://www.vidc.info/certificate/operation/", "CertificateInfo"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("h_ID");
        elemField.setXmlName(new javax.xml.namespace.QName("", "H_ID"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("CJH");
        elemField.setXmlName(new javax.xml.namespace.QName("", "CJH"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("CLSBDH");
        elemField.setXmlName(new javax.xml.namespace.QName("", "CLSBDH"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("CLZZQYMC");
        elemField.setXmlName(new javax.xml.namespace.QName("", "CLZZQYMC"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("CLLX");
        elemField.setXmlName(new javax.xml.namespace.QName("", "CLLX"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("CLMC");
        elemField.setXmlName(new javax.xml.namespace.QName("", "CLMC"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("CLPP");
        elemField.setXmlName(new javax.xml.namespace.QName("", "CLPP"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("CLXH");
        elemField.setXmlName(new javax.xml.namespace.QName("", "CLXH"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("CSYS");
        elemField.setXmlName(new javax.xml.namespace.QName("", "CSYS"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("DPXH");
        elemField.setXmlName(new javax.xml.namespace.QName("", "DPXH"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FDJH");
        elemField.setXmlName(new javax.xml.namespace.QName("", "FDJH"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FDJXH");
        elemField.setXmlName(new javax.xml.namespace.QName("", "FDJXH"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("RLZL");
        elemField.setXmlName(new javax.xml.namespace.QName("", "RLZL"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("PFBZ");
        elemField.setXmlName(new javax.xml.namespace.QName("", "PFBZ"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("PL");
        elemField.setXmlName(new javax.xml.namespace.QName("", "PL"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("GL");
        elemField.setXmlName(new javax.xml.namespace.QName("", "GL"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ZXXS");
        elemField.setXmlName(new javax.xml.namespace.QName("", "ZXXS"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("QLJ");
        elemField.setXmlName(new javax.xml.namespace.QName("", "QLJ"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("HLJ");
        elemField.setXmlName(new javax.xml.namespace.QName("", "HLJ"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("LTS");
        elemField.setXmlName(new javax.xml.namespace.QName("", "LTS"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("LTGG");
        elemField.setXmlName(new javax.xml.namespace.QName("", "LTGG"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("GBTHPS");
        elemField.setXmlName(new javax.xml.namespace.QName("", "GBTHPS"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ZJ");
        elemField.setXmlName(new javax.xml.namespace.QName("", "ZJ"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ZH");
        elemField.setXmlName(new javax.xml.namespace.QName("", "ZH"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ZS");
        elemField.setXmlName(new javax.xml.namespace.QName("", "ZS"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("WKC");
        elemField.setXmlName(new javax.xml.namespace.QName("", "WKC"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("WKK");
        elemField.setXmlName(new javax.xml.namespace.QName("", "WKK"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("WKG");
        elemField.setXmlName(new javax.xml.namespace.QName("", "WKG"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("HXNBC");
        elemField.setXmlName(new javax.xml.namespace.QName("", "HXNBC"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("HXNBK");
        elemField.setXmlName(new javax.xml.namespace.QName("", "HXNBK"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("HXNBG");
        elemField.setXmlName(new javax.xml.namespace.QName("", "HXNBG"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ZZL");
        elemField.setXmlName(new javax.xml.namespace.QName("", "ZZL"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("EDZZL");
        elemField.setXmlName(new javax.xml.namespace.QName("", "EDZZL"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ZBZL");
        elemField.setXmlName(new javax.xml.namespace.QName("", "ZBZL"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ZZLLYXS");
        elemField.setXmlName(new javax.xml.namespace.QName("", "ZZLLYXS"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ZQYZZL");
        elemField.setXmlName(new javax.xml.namespace.QName("", "ZQYZZL"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("EDZK");
        elemField.setXmlName(new javax.xml.namespace.QName("", "EDZK"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("BGCAZZDYXZZL");
        elemField.setXmlName(new javax.xml.namespace.QName("", "BGCAZZDYXZZL"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("JSSZCRS");
        elemField.setXmlName(new javax.xml.namespace.QName("", "JSSZCRS"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ZGCS");
        elemField.setXmlName(new javax.xml.namespace.QName("", "ZGCS"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("CLZZRQ");
        elemField.setXmlName(new javax.xml.namespace.QName("", "CLZZRQ"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "dateTime"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("BZ");
        elemField.setXmlName(new javax.xml.namespace.QName("", "BZ"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("CPSCDZ");
        elemField.setXmlName(new javax.xml.namespace.QName("", "CPSCDZ"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("CZRQ");
        elemField.setXmlName(new javax.xml.namespace.QName("", "CZRQ"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "dateTime"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FZRQ");
        elemField.setXmlName(new javax.xml.namespace.QName("", "FZRQ"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "dateTime"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("CLSCDWMC");
        elemField.setXmlName(new javax.xml.namespace.QName("", "CLSCDWMC"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("YH");
        elemField.setXmlName(new javax.xml.namespace.QName("", "YH"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ZXZS");
        elemField.setXmlName(new javax.xml.namespace.QName("", "ZXZS"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("CDDBJ");
        elemField.setXmlName(new javax.xml.namespace.QName("", "CDDBJ"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("PZXLH");
        elemField.setXmlName(new javax.xml.namespace.QName("", "PZXLH"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("CREATETIME");
        elemField.setXmlName(new javax.xml.namespace.QName("", "CREATETIME"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "dateTime"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("VEHICLE_STATUS");
        elemField.setXmlName(new javax.xml.namespace.QName("", "VEHICLE_STATUS"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("RESPONSE_CODE");
        elemField.setXmlName(new javax.xml.namespace.QName("", "RESPONSE_CODE"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("CLIENT_HARDWARE_INFO");
        elemField.setXmlName(new javax.xml.namespace.QName("", "CLIENT_HARDWARE_INFO"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FEEDBACK_TIME");
        elemField.setXmlName(new javax.xml.namespace.QName("", "FEEDBACK_TIME"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "dateTime"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("HD_HOST");
        elemField.setXmlName(new javax.xml.namespace.QName("", "HD_HOST"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("HD_USER");
        elemField.setXmlName(new javax.xml.namespace.QName("", "HD_USER"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("UKEY");
        elemField.setXmlName(new javax.xml.namespace.QName("", "UKEY"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("UPDATETIME");
        elemField.setXmlName(new javax.xml.namespace.QName("", "UPDATETIME"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "dateTime"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("UPSEND_TAG");
        elemField.setXmlName(new javax.xml.namespace.QName("", "UPSEND_TAG"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("VERCODE");
        elemField.setXmlName(new javax.xml.namespace.QName("", "VERCODE"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("VERSION");
        elemField.setXmlName(new javax.xml.namespace.QName("", "VERSION"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("CLZTXX");
        elemField.setXmlName(new javax.xml.namespace.QName("", "CLZTXX"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("DYWYM");
        elemField.setXmlName(new javax.xml.namespace.QName("", "DYWYM"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("QYID");
        elemField.setXmlName(new javax.xml.namespace.QName("", "QYID"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ZCHGZBH");
        elemField.setXmlName(new javax.xml.namespace.QName("", "ZCHGZBH"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ZZBH");
        elemField.setXmlName(new javax.xml.namespace.QName("", "ZZBH"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("CPH");
        elemField.setXmlName(new javax.xml.namespace.QName("", "CPH"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("DPHGZBH");
        elemField.setXmlName(new javax.xml.namespace.QName("", "DPHGZBH"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("DPID");
        elemField.setXmlName(new javax.xml.namespace.QName("", "DPID"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("GGSXRQ");
        elemField.setXmlName(new javax.xml.namespace.QName("", "GGSXRQ"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "dateTime"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("HZDCZFS");
        elemField.setXmlName(new javax.xml.namespace.QName("", "HZDCZFS"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("HZDFS");
        elemField.setXmlName(new javax.xml.namespace.QName("", "HZDFS"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("PC");
        elemField.setXmlName(new javax.xml.namespace.QName("", "PC"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("QYBZ");
        elemField.setXmlName(new javax.xml.namespace.QName("", "QYBZ"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("QYQTXX");
        elemField.setXmlName(new javax.xml.namespace.QName("", "QYQTXX"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("QZDCZFS");
        elemField.setXmlName(new javax.xml.namespace.QName("", "QZDCZFS"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("QZDFS");
        elemField.setXmlName(new javax.xml.namespace.QName("", "QZDFS"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("WZHGZBH");
        elemField.setXmlName(new javax.xml.namespace.QName("", "WZHGZBH"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("JFPZID");
        elemField.setXmlName(new javax.xml.namespace.QName("", "JFPZID"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("VINBSYY");
        elemField.setXmlName(new javax.xml.namespace.QName("", "VINBSYY"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ISCXNF");
        elemField.setXmlName(new javax.xml.namespace.QName("", "ISCXNF"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ZYZYCMSBS");
        elemField.setXmlName(new javax.xml.namespace.QName("", "ZYZYCMSBS"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("XNYQCJMSBS");
        elemField.setXmlName(new javax.xml.namespace.QName("", "XNYQCJMSBS"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("HDMSBS");
        elemField.setXmlName(new javax.xml.namespace.QName("", "HDMSBS"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("XNYQCZL");
        elemField.setXmlName(new javax.xml.namespace.QName("", "XNYQCZL"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("SWTQYMC");
        elemField.setXmlName(new javax.xml.namespace.QName("", "SWTQYMC"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("SWTQYSCDZ");
        elemField.setXmlName(new javax.xml.namespace.QName("", "SWTQYSCDZ"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("SWTQYTYSHXYDM");
        elemField.setXmlName(new javax.xml.namespace.QName("", "SWTQYTYSHXYDM"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           java.lang.String mechType, 
           java.lang.Class _javaType,  
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           java.lang.String mechType, 
           java.lang.Class _javaType,  
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
