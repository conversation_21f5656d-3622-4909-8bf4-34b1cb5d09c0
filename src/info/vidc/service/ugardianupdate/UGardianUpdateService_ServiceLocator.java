/**
 * UGardianUpdateService_ServiceLocator.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package info.vidc.service.ugardianupdate;

import com.dawnpro.commons.SystemParam;

public class UGardianUpdateService_ServiceLocator extends org.apache.axis.client.Service implements info.vidc.service.ugardianupdate.UGardianUpdateService_Service {

    public UGardianUpdateService_ServiceLocator() {
    }


    public UGardianUpdateService_ServiceLocator(org.apache.axis.EngineConfiguration config) {
        super(config);
    }

    public UGardianUpdateService_ServiceLocator(java.lang.String wsdlLoc, javax.xml.namespace.QName sName) throws javax.xml.rpc.ServiceException {
        super(wsdlLoc, sName);
    }

    // Use to get a proxy class for UGardianUpdateServiceImplPort
    private java.lang.String UGardianUpdateServiceImplPort_address = SystemParam.getKey("uidservice_address", "https://hgz.miit.gov.cn/enterprise/services/UGardianUpdateService");
    //private java.lang.String UGardianUpdateServiceImplPort_address = "http://hgz.miit.gov.cn/enterprise/services/UGardianUpdateService";

    public java.lang.String getUGardianUpdateServiceImplPortAddress() {
        return UGardianUpdateServiceImplPort_address;
    }

    // The WSDD service name defaults to the port name.
    private java.lang.String UGardianUpdateServiceImplPortWSDDServiceName = "UGardianUpdateServiceImplPort";

    public java.lang.String getUGardianUpdateServiceImplPortWSDDServiceName() {
        return UGardianUpdateServiceImplPortWSDDServiceName;
    }

    public void setUGardianUpdateServiceImplPortWSDDServiceName(java.lang.String name) {
        UGardianUpdateServiceImplPortWSDDServiceName = name;
    }

    public info.vidc.service.ugardianupdate.UGardianUpdateService_PortType getUGardianUpdateServiceImplPort() throws javax.xml.rpc.ServiceException {
       java.net.URL endpoint;
        try {
            endpoint = new java.net.URL(UGardianUpdateServiceImplPort_address);
        }
        catch (java.net.MalformedURLException e) {
            throw new javax.xml.rpc.ServiceException(e);
        }
        return getUGardianUpdateServiceImplPort(endpoint);
    }

    public info.vidc.service.ugardianupdate.UGardianUpdateService_PortType getUGardianUpdateServiceImplPort(java.net.URL portAddress) throws javax.xml.rpc.ServiceException {
        try {
            info.vidc.service.ugardianupdate.UGardianUpdateServiceSoapBindingStub _stub = new info.vidc.service.ugardianupdate.UGardianUpdateServiceSoapBindingStub(portAddress, this);
            _stub.setPortName(getUGardianUpdateServiceImplPortWSDDServiceName());
            return _stub;
        }
        catch (org.apache.axis.AxisFault e) {
            return null;
        }
    }

    public void setUGardianUpdateServiceImplPortEndpointAddress(java.lang.String address) {
        UGardianUpdateServiceImplPort_address = address;
    }

    /**
     * For the given interface, get the stub implementation.
     * If this service has no port for the given interface,
     * then ServiceException is thrown.
     */
    public java.rmi.Remote getPort(Class serviceEndpointInterface) throws javax.xml.rpc.ServiceException {
        try {
            if (info.vidc.service.ugardianupdate.UGardianUpdateService_PortType.class.isAssignableFrom(serviceEndpointInterface)) {
                info.vidc.service.ugardianupdate.UGardianUpdateServiceSoapBindingStub _stub = new info.vidc.service.ugardianupdate.UGardianUpdateServiceSoapBindingStub(new java.net.URL(UGardianUpdateServiceImplPort_address), this);
                _stub.setPortName(getUGardianUpdateServiceImplPortWSDDServiceName());
                return _stub;
            }
        }
        catch (java.lang.Throwable t) {
            throw new javax.xml.rpc.ServiceException(t);
        }
        throw new javax.xml.rpc.ServiceException("There is no stub implementation for the interface:  " + (serviceEndpointInterface == null ? "null" : serviceEndpointInterface.getName()));
    }

    /**
     * For the given interface, get the stub implementation.
     * If this service has no port for the given interface,
     * then ServiceException is thrown.
     */
    public java.rmi.Remote getPort(javax.xml.namespace.QName portName, Class serviceEndpointInterface) throws javax.xml.rpc.ServiceException {
        if (portName == null) {
            return getPort(serviceEndpointInterface);
        }
        java.lang.String inputPortName = portName.getLocalPart();
        if ("UGardianUpdateServiceImplPort".equals(inputPortName)) {
            return getUGardianUpdateServiceImplPort();
        }
        else  {
            java.rmi.Remote _stub = getPort(serviceEndpointInterface);
            ((org.apache.axis.client.Stub) _stub).setPortName(portName);
            return _stub;
        }
    }

    public javax.xml.namespace.QName getServiceName() {
        return new javax.xml.namespace.QName("http://service.vidc.info/ugardianupdate", "UGardianUpdateService");
    }

    private java.util.HashSet ports = null;

    public java.util.Iterator getPorts() {
        if (ports == null) {
            ports = new java.util.HashSet();
            ports.add(new javax.xml.namespace.QName("http://service.vidc.info/ugardianupdate", "UGardianUpdateServiceImplPort"));
        }
        return ports.iterator();
    }

    /**
    * Set the endpoint address for the specified port name.
    */
    public void setEndpointAddress(java.lang.String portName, java.lang.String address) throws javax.xml.rpc.ServiceException {
        
if ("UGardianUpdateServiceImplPort".equals(portName)) {
            setUGardianUpdateServiceImplPortEndpointAddress(address);
        }
        else 
{ // Unknown Port Name
            throw new javax.xml.rpc.ServiceException(" Cannot set Endpoint Address for Unknown Port" + portName);
        }
    }

    /**
    * Set the endpoint address for the specified port name.
    */
    public void setEndpointAddress(javax.xml.namespace.QName portName, java.lang.String address) throws javax.xml.rpc.ServiceException {
        setEndpointAddress(portName.getLocalPart(), address);
    }

}
