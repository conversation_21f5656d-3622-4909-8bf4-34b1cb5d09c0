package info.vidc.service.ugardianupdate;

public class UGardianUpdateServiceProxy implements info.vidc.service.ugardianupdate.UGardianUpdateService_PortType {
  private String _endpoint = null;
  private info.vidc.service.ugardianupdate.UGardianUpdateService_PortType uGardianUpdateService_PortType = null;
  
  public UGardianUpdateServiceProxy() {
    _initUGardianUpdateServiceProxy();
  }
  
  public UGardianUpdateServiceProxy(String endpoint) {
    _endpoint = endpoint;
    _initUGardianUpdateServiceProxy();
  }
  
  private void _initUGardianUpdateServiceProxy() {
    try {
      uGardianUpdateService_PortType = (new info.vidc.service.ugardianupdate.UGardianUpdateService_ServiceLocator()).getUGardianUpdateServiceImplPort();
      if (uGardianUpdateService_PortType != null) {
        if (_endpoint != null)
          ((javax.xml.rpc.Stub)uGardianUpdateService_PortType)._setProperty("javax.xml.rpc.service.endpoint.address", _endpoint);
        else
          _endpoint = (String)((javax.xml.rpc.Stub)uGardianUpdateService_PortType)._getProperty("javax.xml.rpc.service.endpoint.address");
      }
      
    }
    catch (javax.xml.rpc.ServiceException serviceException) {}
  }
  
  public String getEndpoint() {
    return _endpoint;
  }
  
  public void setEndpoint(String endpoint) {
    _endpoint = endpoint;
    if (uGardianUpdateService_PortType != null)
      ((javax.xml.rpc.Stub)uGardianUpdateService_PortType)._setProperty("javax.xml.rpc.service.endpoint.address", _endpoint);
    
  }
  
  public info.vidc.service.ugardianupdate.UGardianUpdateService_PortType getUGardianUpdateService_PortType() {
    if (uGardianUpdateService_PortType == null)
      _initUGardianUpdateServiceProxy();
    return uGardianUpdateService_PortType;
  }
  
  public java.lang.String UGardianUpdate(java.lang.String userName, java.lang.String password, java.lang.String uKey, java.lang.String opKey) throws java.rmi.RemoteException, info.vidc.service.ugardianupdate.Exception{
    if (uGardianUpdateService_PortType == null)
      _initUGardianUpdateServiceProxy();
    return uGardianUpdateService_PortType.UGardianUpdate(userName, password, uKey, opKey);
  }
  
  
}