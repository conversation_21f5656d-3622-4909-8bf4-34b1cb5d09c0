<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.dawnpro.service.commons.entity.ToDictionary" table="TO_DICTIONARY" >
        <id name="id" type="java.lang.Long">
            <column name="ID" precision="22" scale="0" />
            <generator class="com.dawnpro.commons.util.IncreateGenerator" >
             <!--<param name="sequence">SEQ_TO_DICTIONARY</param>-->
            </generator>
        </id>
        <property name="code" type="java.lang.String">
            <column name="CODE" length="40" not-null="true" />
        </property>
        <property name="name" type="java.lang.String">
            <column name="NAME" length="50" />
        </property>
        <property name="status" type="java.lang.String">
            <column name="STATUS" length="1" />
        </property>
        <property name="other" type="java.lang.String">
            <column name="OTHER" length="64" />
        </property>
        <property name="createtime" type="java.util.Date">
            <column name="CREATETIME" length="7" />
        </property>
        <property name="updatetime" type="java.util.Date">
            <column name="UPDATETIME" length="7" />
        </property>
        <property name="deleteflag" type="java.lang.String">
            <column name="DELETEFLAG" length="1" />
        </property>
        <property name="type" type="java.lang.String">
            <column name="TYPE" length="50" />
        </property>
        <property name="sort" type="java.lang.String">
            <column name="SORT" length="10" />
        </property>
        <property name="nameEn" type="java.lang.String">
            <column name="NAME_EN" length="100" />
        </property>
        <property name="uniqueKey" type="java.lang.String">
            <column name="UNIQUEKEY" length="100" />
        </property>
        <property name="tsFactory" type="java.lang.String">
        	<column name="TS_FACTORY" length="10"/>
        </property>
    </class>
</hibernate-mapping>
