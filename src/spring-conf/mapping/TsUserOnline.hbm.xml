<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse - Hibernate Tools
-->
<hibernate-mapping>
    <class name="com.dawnpro.service.commons.entity.TsUserOnline" table="TS_USER_ONLINE">
        <id name="id" type="java.lang.Long">
            <column name="ID" precision="22" scale="0" />
            <generator class="com.dawnpro.commons.util.IncreateGenerator" >
                <!--<param name="sequence">SEQ_TS_USER_ONLINE</param>-->
            </generator>
        </id>
        <property name="userid" type="java.lang.String">
            <column name="USER_ID" length="50" not-null="true" />
        </property>
        <property name="username" type="java.lang.String">
            <column name="USER_NAME" length="50" not-null="true" />
        </property>
        <property name="ipaddress" type="java.lang.String">
            <column name="IP_ADDRESS" length="50" />
        </property>
        <property name="login" type="java.util.Date">
            <column name="LOGIN" length="7" />
        </property>
        <property name="logout" type="java.util.Date">
            <column name="LOGOUT" length="7" />
        </property>
        <property name="createtime" type="java.util.Date">
            <column name="CREATETIME" length="7" />
        </property>
        <property name="updatetime" type="java.util.Date">
            <column name="UPDATETIME" length="7" />
        </property>
        <property name="deleteflag" type="java.lang.String">
            <column name="DELETEFLAG" length="2" />
        </property>
        <property name="extend1" type="java.lang.String">
            <column name="EXTEND1" length="20" />
        </property>
        <property name="extend2" type="java.lang.String">
            <column name="EXTEND2" length="20" />
        </property>
        <property name="extend3" type="java.lang.String">
            <column name="EXTEND3" length="20" />
        </property>
        <property name="extend4" type="java.lang.String">
            <column name="EXTEND4" length="20" />
        </property>
    </class>
</hibernate-mapping>
