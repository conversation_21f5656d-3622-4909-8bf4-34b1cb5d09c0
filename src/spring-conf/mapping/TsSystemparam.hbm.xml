<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse - Hibernate Tools
-->
<hibernate-mapping>
    <class name="com.dawnpro.service.commons.entity.TsSystemparam" table="TS_SYSTEMPARAM">
        <id name="id" type="java.lang.Long">
            <column name="ID" precision="22" scale="0" />
            <generator class="com.dawnpro.commons.util.IncreateGenerator" >
            <!--<param name="sequence">SEQ_TS_SYSTEMPARAM</param>-->
            </generator>
        </id>
        <property name="keyName" type="java.lang.String">
            <column name="KEYNAME" length="20" />
        </property>
        <property name="keyValue" type="java.lang.String">
            <column name="KEYVALUE"/>
        </property>
        <property name="remark" type="java.lang.String">
            <column name="REMARK" length="100" />
        </property>
        <property name="createtime" type="java.util.Date">
            <column name="CREATETIME" length="7" />
        </property>
        <property name="updatetime" type="java.util.Date">
            <column name="UPDATETIME" length="7" />
        </property>
        <property name="deleteflag" type="java.lang.String">
            <column name="DELETEFLAG" length="1" />
        </property>
        <property name="status" type="java.lang.String">
            <column name="STATUS" length="1" />
        </property>
        <property name="startdate" type="java.util.Date">
            <column name="STARTDATE" length="7" />
        </property>
        <property name="enddate" type="java.util.Date">
            <column name="ENDDATE" length="7" />
        </property>
    </class>
</hibernate-mapping>
