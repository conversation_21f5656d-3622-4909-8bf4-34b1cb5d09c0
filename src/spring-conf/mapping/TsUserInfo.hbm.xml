<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse - Hibernate Tools
-->
<hibernate-mapping>
    <class name="com.dawnpro.service.commons.entity.TsUserInfo" table="TS_USER_INFO">
        <id name="id" type="java.lang.Long">
            <column name="ID" precision="22" scale="0" />
            <generator class="com.dawnpro.commons.util.IncreateGenerator" >
            	<!--<param name="sequence">SEQ_TS_USER_INFO</param>-->
            </generator>
        </id>
        <property name="userName" type="java.lang.String">
            <column name="USER_NAME" length="10" not-null="true" />
        </property>
        <property name="userId" type="java.lang.String">
            <column name="USER_ID" length="100" not-null="true" />
        </property>
        <property name="depttype" type="java.lang.String">
            <column name="DEPT_ID"  />
        </property>
        <property name="userState" type="java.lang.String">
            <column name="USER_STATE" length="1" />
        </property>
        <property name="userSort" type="java.lang.String">
            <column name="USER_SORT" length="10" />
        </property>
        <property name="deptPhone" type="java.lang.String">
            <column name="DEPT_PHONE" length="100" />
        </property>
        <property name="userIdcard" type="java.lang.String">
            <column name="USER_IDCARD" length="18" />
        </property>
        <property name="EMail" type="java.lang.String">
            <column name="E_MAIL" length="100" />
        </property>
        <property name="msn" type="java.lang.String">
            <column name="MSN" length="30" />
        </property>
        <property name="userPwd" type="java.lang.String">
            <column name="USER_PWD" length="20" />
        </property>
        <property name="userLogintime" type="java.util.Date">
            <column name="USER_LOGINTIME" length="7" />
        </property>
        <property name="systemId" type="java.lang.String">
            <column name="SYSTEM_ID" length="10" />
        </property>
        <property name="createtime" type="java.util.Date">
            <column name="CREATETIME" length="7" />
        </property>
        <property name="updatetime" type="java.util.Date">
            <column name="UPDATETIME" length="7" />
        </property>
        <property name="deleteflag" type="java.lang.String">
            <column name="DELETEFLAG" length="2" />
        </property>
        <property name="extend1" type="java.lang.String">
            <column name="EXTEND1" length="20" />
        </property>
        <property name="extend2" type="java.lang.String">
            <column name="EXTEND2" length="20" />
        </property>
        <property name="extend3" type="java.lang.String">
            <column name="EXTEND3" length="20" />
        </property>
        <property name="extend4" type="java.lang.String">
            <column name="EXTEND4" length="20" />
        </property>
        <property name="tsFactory" type="java.lang.String">
            <column name="TS_FACTORY" length="10" />
        </property>
    </class>
</hibernate-mapping>
