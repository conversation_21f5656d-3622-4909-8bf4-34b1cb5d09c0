<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE beans PUBLIC "-//SPRING//DTD BEAN//EN" "http://www.springframework.org/dtd/spring-beans.dtd">

<beans>
	<bean id="HgzVipUploadManage" class="com.dawnpro.vipservice.HgzVipUploadManage">
		<property name="dao">
			<ref bean="DAO" />
		</property>
	</bean>	
	<bean id="HgzBackResultManage" class="com.dawnpro.vipservice.HgzBackResultManage">
		<property name="dao">
			<ref bean="DAO" />
		</property>
	</bean>
	
	<bean id="ZgsManage" class="com.dawnpro.vipservice.zgs.ZgsManage">
	</bean>
	
	<bean id="IamManage" class="com.dawnpro.vipservice.IamManage">
		<property name="dao">
			<ref bean="DAO" />
		</property>
	</bean>
	
	<bean id="HgzCafcnevUploadManage" class="com.dawnpro.vipservice.cafcnev.HgzCafcnevUploadManage">
		<property name="dao">
			<ref bean="DAO" />
		</property>
	</bean>
	
	<bean id="BsjxManage" class="com.dawnpro.vipservice.bsjx.BsjxManage">
		<property name="dao">
			<ref bean="DAO" />
		</property>
	</bean>
	
	<bean id="WxxxgkManage" class="com.dawnpro.vipservice.wxxxgk.WxxxgkManage">
		<property name="dao">
			<ref bean="DAO" />
		</property>
	</bean>
	
	
	<bean id="WSXxgkVinSoap" class="cn.org.vecc_mep.web1.WSXxgkVin.WSXxgkVinSoapProxy"/>
	
	<bean id="MepgkUploadManage" class="com.dawnpro.vipservice.MepgkUploadManage">  
		<property name="dao">  
			<ref bean="DAO" />  
		</property>
		<property name="xxgkSoap">  
			<ref bean="WSXxgkVinSoap" />  
		</property>
	</bean>
	
		<bean id="CocUpManage" class="com.dawnpro.vipservice.CocUpManage">
		<property name="dao">
			<ref bean="DAO" />
		</property>
	</bean>

	<!--新车下线检测报告上传服务类-->
	<bean id="OffLineTestUpload" class="com.dawnpro.vipservice.OffLineTestUpload">
		<property name="dao">  
			<ref bean="DAO" />
		</property>  
	</bean>
	
	<!--U盾管理-->
	<bean id="BzduidManage" class="com.dawnpro.vipservice.BzduidManage">  
		<property name="dao">  
			<ref bean="DAO" />  
		</property>  
	</bean>
	
	<!--邮件管理-->
	<bean id="HgzMailManage" class="com.dawnpro.vipservice.mail.HgzMailManage">  
		<property name="dao">  
			<ref bean="DAO" />  
		</property>  
	</bean>
	
	<!-- API接口管理-->
	<bean id="HgzApiToolManage" class="com.dawnpro.vipservice.api.HgzApiToolManage">
		<property name="dao">
			<ref bean="DAO" />
		</property>
	</bean>
	
</beans>

