<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE beans PUBLIC "-//SPRING//DTD BEAN//EN" "http://www.springframework.org/dtd/spring-beans.dtd">

<beans>
	
	<!-- 数据库连接池-->
	<bean id="dataSource" class="org.springframework.jndi.JndiObjectFactoryBean">
		<property name="jndiName">
			<value>java:comp/env/jdbc_hgzdb</value>
		</property>
	</bean>
	<bean id="searchDataSource"
		class="org.springframework.jndi.JndiObjectFactoryBean">
		<property name="jndiName">
			<value>java:comp/env/jdbc_hgzdb</value>
		</property>
	</bean>
	
	
	<bean id="transactionManager" class="org.springframework.orm.hibernate3.HibernateTransactionManager">
		<property name="sessionFactory">
			<ref bean="sessionFactory" />
		</property>
	</bean>
	<bean id="JDBCtransactionManager" class="org.springframework.jdbc.datasource.DataSourceTransactionManager" 
		singleton="true">
		<property name="dataSource">
			<ref bean="searchDataSource" />
		</property>
	</bean>
	
	<!--
	<bean id="dataSource"
		class="org.springframework.jndi.JndiObjectFactoryBean">
		<property name="jndiName">
			<value>jdbc/hgzdb</value>
		</property>
	</bean>
	<bean id="searchDataSource"
		class="org.springframework.jndi.JndiObjectFactoryBean">
		<property name="jndiName">
			<value>jdbc/hgzdb</value>
		</property>
	</bean>
	<bean id="wasTxMgr"
		class="org.springframework.transaction.jta.WebLogicServerTransactionManagerFactoryBean" />
	<bean id="transactionManager"
		class="org.springframework.transaction.jta.JtaTransactionManager">
		<property name="userTransactionName">
			<null />
		</property>
		<property name="transactionManager">
			<ref local="wasTxMgr" />
		</property>
	</bean>
	<bean id="JDBCtransactionManager" class="org.springframework.jdbc.datasource.DataSourceTransactionManager" 
		singleton="true">
		<property name="dataSource">
			<ref bean="searchDataSource" />
		</property>
	</bean>
	-->
	
</beans>