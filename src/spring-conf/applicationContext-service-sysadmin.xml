<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE beans PUBLIC "-//SPRING//DTD BEAN//EN" "http://www.springframework.org/dtd/spring-beans.dtd">

<beans>

	<bean id="EntityManage" class="com.dawnpro.service.sysadmin.EntityManage">
		<property name="dao">
			<ref bean="DAO" />
		</property>
	</bean>

	<bean id="ExcelFileImportManager" class="com.dawnpro.service.commons.excelImport.ExcelFileImportManager">
		<property name="dao">
			<ref bean="DAO" />
		</property>
	</bean>
	<bean id="EntityFieldManage" class="com.dawnpro.service.sysadmin.EntityFieldManage">
		<property name="dao">
			<ref bean="DAO" />
		</property>
	</bean>
	<bean id="PrototypeManage" class="com.dawnpro.service.sysadmin.PrototypeManage">
		<property name="dao">
			<ref bean="DAO" />
		</property>
	</bean>
	<bean id="BisinessObjectManage" class="com.dawnpro.service.sysadmin.BisinessObjectManage">
		<property name="dao">
			<ref bean="DAO" />
		</property>
	</bean>
	<bean id="BusinessErrorManage" class="com.dawnpro.service.sysadmin.BusinessErrorManage">
		<property name="dao">
			<ref bean="DAO" />
		</property>
	</bean>
	<bean id="LockManage" class="com.dawnpro.service.sysadmin.LockManage">
		<property name="dao">
			<ref bean="DAO" />
		</property>
	</bean>


	<bean id="DatabaseManager" class="com.dawnpro.service.sysadmin.DatabaseManager">
		<property name="dao">
			<ref bean="DAO" />
		</property>
	</bean>
	
	<!--公共服务管理-->
	<bean id="CommonsManage" class="com.dawnpro.service.commons.CommonsManage">  
		<property name="dao">  
			<ref bean="DAO" />  
		</property>  
	</bean>
	
	
	
	
	
	
	<bean id="ResInfoManager" class="com.dawnpro.service.commons.service.impl.ResInfoManage">
		<property name="dao">
			<ref bean="DAO" />
		</property>
	</bean>
	
	
	<bean id="BaseCRUD" class="com.dawnpro.service.commons.service.impl.BaseCRUD">
		<property name="dao">
			<ref bean="DAO" />
		</property>
	</bean>
	
	
	
	
	<!-- 框架启动默认插件9个
 1、ServiceFactory. getInstance（）;
 2、Error. getInstance（）;
 3、ReadBo. getInstance（）;
 4、SystemParam. getInstance（）;
 5、CommonDictionary. getInstance（）;
 6、SqlConf. getInstance（）;
 7、ReadImportExcelConfig. getInstance();
 8、CustomQueryConfig. getInstance（）;
 9、I18nProvider. loadProperties();
	
	-->
	<!-- 框架启动默认插件9个：第1个 
	 见init.conf中Springcon配置
	-->
	
	<!-- 框架启动默认插件9个：第2个 
	 见init.conf中errormessage配置
	-->
	
	<!-- 框架启动默认插件9个：第3个 -->
	<bean id="ReadBoImp" class="com.dawnpro.service.commons.service.impl.ReadBoImp">
		<property name="dao">
			<ref bean="DAO" />
		</property>
	</bean>
	
	<!-- 框架启动默认插件9个：第4个 -->
	<bean id="SystemParamManage" class="com.dawnpro.service.commons.service.impl.SystemParamManageImpl">
		<property name="dao">
			<ref bean="DAO" />
		</property>
	</bean>
	
	<!-- 框架启动默认插件9个：第5个 -->
	<bean id="Dictionary" class="com.dawnpro.service.commons.service.impl.DictionaryManage">
		<property name="dao">
			<ref bean="DAO" />
		</property>
	</bean>
	
	<!-- 框架启动默认插件9个：第6个 -->
	<!--SQL 语句-->
	<bean id="SqlConfManage" class="com.dawnpro.service.commons.service.impl.SqlConfManage">
		<property name="dao">
			<ref bean="DAO" />
		</property>
	</bean>
	
	<!-- 框架启动默认插件9个：第7个 
	 见init.conf中ImportExcel_Config配置
	-->
	
	<!-- 框架启动默认插件9个：第8个 -->
	<bean id="QueryManage" class="com.dawnpro.service.commons.service.impl.QueryManageImpl">
		<property name="dao">
			<ref bean="DAO" />
		</property>
	</bean>
	
	<!-- 框架启动默认插件9个：第9个 -->
	<bean id="I18nProvider" class="com.dawnpro.service.commons.service.impl.I18nProvider">
		<property name="dao">
			<ref bean="DAO" />
		</property>
	</bean>
	
	<!-- tools不需要dao  -->
	<bean id="Tools" class="com.dawnpro.commons.Tools"></bean>
</beans>

