<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE beans PUBLIC "-//SPRING//DTD BEAN//EN" "http://www.springframework.org/dtd/spring-beans.dtd">

<beans>
	
	<bean id="sessionFactory"
		class="org.springframework.orm.hibernate3.LocalSessionFactoryBean">
		<property name="dataSource">
			<ref bean="dataSource" />
		</property>
		<property name="hibernateProperties">
			<props>
				<prop key="hibernate.dialect">
					<!--org.hibernate.dialect.Oracle9Dialect-->
					<!--org.hibernate.dialect.SQLServerDialect-->
					org.hibernate.dialect.MySQL5Dialect
				</prop>
				<prop key="hibernate.connection.release_mode">auto</prop>
				<prop key="hibernate.connection.autocommit">true</prop>
				<prop key="hibernate.show_sql">true</prop>
			</props>
		</property>
		<property name="mappingDirectoryLocations">
			<list>
				<value>spring-conf/mapping</value>
			</list>
		</property>
	</bean>	

	
	<!-- 基本的操作数据库的JDBCDAO -->
	<bean id="JdbcDAO" class="com.dawnpro.commons.util.db.JdbcDAO" singleton="false">
		<property name="dataSource">
			<ref bean="searchDataSource" />
		</property>
	</bean>
	<!-- HIBERNATE方式操作数据库-->
	<bean name="hibernateTemplate"
		class="org.springframework.orm.hibernate3.HibernateTemplate">
		<property name="sessionFactory">
			<ref bean="sessionFactory" />
		</property>
	</bean>
	<!-- 领域对象操作DAO -->
	<bean name="VODAO"
		class="com.dawnpro.commons.util.entity.VODAO">
		<property name="sessionFactory">
			<ref bean="sessionFactory" />
		</property>
	</bean>
	<!-- DAO操作 -->
	<bean name="DAO"
		class="com.dawnpro.commons.util.db.DAO">
		<property name="jdao">
			<ref bean="JdbcDAO" />
		</property>
		<property name="vodao">
			<ref bean="VODAO" />
		</property>
	</bean>
	
</beans>