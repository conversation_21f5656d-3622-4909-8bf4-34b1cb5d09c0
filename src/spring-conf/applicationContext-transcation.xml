<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE beans PUBLIC "-//SPRING//DTD BEAN//EN" "http://www.springframework.org/dtd/spring-beans.dtd">

<beans>
	<!-- 代理工厂事务 -->
	<!-- -->
	<bean id="baseService"
		class="org.springframework.transaction.interceptor.TransactionProxyFactoryBean">
		<property name="transactionManager">
			<ref bean="transactionManager" />
		</property>
		<property name="transactionAttributes">
			<props>
				<prop key="query*">readOnly</prop>
				<prop key="search*">readOnly</prop>
				<prop key="add*">PROPAGATION_REQUIRED,-Exception</prop>
				<prop key="save*">PROPAGATION_REQUIRED,-Exception</prop>
				<prop key="update*">
					PROPAGATION_REQUIRED,-Exception
				</prop>
				<prop key="delete*">
					PROPAGATION_REQUIRED,-Exception
				</prop>
				<prop key="add*">PROPAGATION_REQUIRED,-Exception</prop>

			</props>
		</property>
		<property name="target">
			<list>
				<value>*</value>
			</list>
		</property>
		<property name="proxyTargetClass">
			<value>true</value>
		</property>
	</bean>
	<!-- jdbc baseserver -->
	<bean id="JDBCbaseService"
		class="org.springframework.transaction.interceptor.TransactionProxyFactoryBean">
		<property name="transactionManager">
			<ref bean="JDBCtransactionManager" />
		</property>
		<property name="transactionAttributes">
			<props>
				<prop key="executeBatch">PROPAGATION_REQUIRED,-Exception</prop>
			</props>
		</property>
		<property name="target">
			<list>
				<value>*</value>
			</list>
		</property>
		<property name="proxyTargetClass">
			<value>true</value>
		</property>
	</bean>

</beans>