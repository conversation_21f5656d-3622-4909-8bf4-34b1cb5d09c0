/**
 * WSXxgkVinLocator.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package cn.org.vecc_mep.web1.WSXxgkVin;

public class WSXxgkVinLocator extends org.apache.axis.client.Service implements cn.org.vecc_mep.web1.WSXxgkVin.WSXxgkVin {

/**
 * <strong>Vin报送服务<a href="http://www.vecc-mep.org.cn/download/WSXxgkVin.PDF"
 * target="_blank"><font size="3" color="#FF0000">(详细使用说明点此下载)</font></a><br><br></strong>数据来源于<strong>中国环境保护部机动车排污监控中心</strong>
 * <a href="http://www.vecc-mep.org.cn" target="_blank">http://www.vecc-mep.org.cn/</a>
 */

    public WSXxgkVinLocator() {
    }


    public WSXxgkVinLocator(org.apache.axis.EngineConfiguration config) {
        super(config);
    }

    public WSXxgkVinLocator(java.lang.String wsdlLoc, javax.xml.namespace.QName sName) throws javax.xml.rpc.ServiceException {
        super(wsdlLoc, sName);
    }

    // Use to get a proxy class for WSXxgkVinSoap
    private java.lang.String WSXxgkVinSoap_address = "http://web1.vecc.org.cn/WSXxgkVin/WSXxgkVin.asmx";

    public java.lang.String getWSXxgkVinSoapAddress() {
        return WSXxgkVinSoap_address;
    }

    // The WSDD service name defaults to the port name.
    private java.lang.String WSXxgkVinSoapWSDDServiceName = "WSXxgkVinSoap";

    public java.lang.String getWSXxgkVinSoapWSDDServiceName() {
        return WSXxgkVinSoapWSDDServiceName;
    }

    public void setWSXxgkVinSoapWSDDServiceName(java.lang.String name) {
        WSXxgkVinSoapWSDDServiceName = name;
    }

    public cn.org.vecc_mep.web1.WSXxgkVin.WSXxgkVinSoap getWSXxgkVinSoap() throws javax.xml.rpc.ServiceException {
       java.net.URL endpoint;
        try {
            endpoint = new java.net.URL(WSXxgkVinSoap_address);
        }
        catch (java.net.MalformedURLException e) {
            throw new javax.xml.rpc.ServiceException(e);
        }
        return getWSXxgkVinSoap(endpoint);
    }

    public cn.org.vecc_mep.web1.WSXxgkVin.WSXxgkVinSoap getWSXxgkVinSoap(java.net.URL portAddress) throws javax.xml.rpc.ServiceException {
        try {
            cn.org.vecc_mep.web1.WSXxgkVin.WSXxgkVinSoapStub _stub = new cn.org.vecc_mep.web1.WSXxgkVin.WSXxgkVinSoapStub(portAddress, this);
            _stub.setPortName(getWSXxgkVinSoapWSDDServiceName());
            return _stub;
        }
        catch (org.apache.axis.AxisFault e) {
            return null;
        }
    }

    public void setWSXxgkVinSoapEndpointAddress(java.lang.String address) {
        WSXxgkVinSoap_address = address;
    }

    /**
     * For the given interface, get the stub implementation.
     * If this service has no port for the given interface,
     * then ServiceException is thrown.
     */
    public java.rmi.Remote getPort(Class serviceEndpointInterface) throws javax.xml.rpc.ServiceException {
        try {
            if (cn.org.vecc_mep.web1.WSXxgkVin.WSXxgkVinSoap.class.isAssignableFrom(serviceEndpointInterface)) {
                cn.org.vecc_mep.web1.WSXxgkVin.WSXxgkVinSoapStub _stub = new cn.org.vecc_mep.web1.WSXxgkVin.WSXxgkVinSoapStub(new java.net.URL(WSXxgkVinSoap_address), this);
                _stub.setPortName(getWSXxgkVinSoapWSDDServiceName());
                return _stub;
            }
        }
        catch (java.lang.Throwable t) {
            throw new javax.xml.rpc.ServiceException(t);
        }
        throw new javax.xml.rpc.ServiceException("There is no stub implementation for the interface:  " + (serviceEndpointInterface == null ? "null" : serviceEndpointInterface.getName()));
    }

    /**
     * For the given interface, get the stub implementation.
     * If this service has no port for the given interface,
     * then ServiceException is thrown.
     */
    public java.rmi.Remote getPort(javax.xml.namespace.QName portName, Class serviceEndpointInterface) throws javax.xml.rpc.ServiceException {
        if (portName == null) {
            return getPort(serviceEndpointInterface);
        }
        java.lang.String inputPortName = portName.getLocalPart();
        if ("WSXxgkVinSoap".equals(inputPortName)) {
            return getWSXxgkVinSoap();
        }
        else  {
            java.rmi.Remote _stub = getPort(serviceEndpointInterface);
            ((org.apache.axis.client.Stub) _stub).setPortName(portName);
            return _stub;
        }
    }

    public javax.xml.namespace.QName getServiceName() {
        return new javax.xml.namespace.QName("http://web1.vecc-mep.org.cn/WSXxgkVin", "WSXxgkVin");
    }

    private java.util.HashSet ports = null;

    public java.util.Iterator getPorts() {
        if (ports == null) {
            ports = new java.util.HashSet();
            ports.add(new javax.xml.namespace.QName("http://web1.vecc-mep.org.cn/WSXxgkVin", "WSXxgkVinSoap"));
        }
        return ports.iterator();
    }

    /**
    * Set the endpoint address for the specified port name.
    */
    public void setEndpointAddress(java.lang.String portName, java.lang.String address) throws javax.xml.rpc.ServiceException {
        
if ("WSXxgkVinSoap".equals(portName)) {
            setWSXxgkVinSoapEndpointAddress(address);
        }
        else 
{ // Unknown Port Name
            throw new javax.xml.rpc.ServiceException(" Cannot set Endpoint Address for Unknown Port" + portName);
        }
    }

    /**
    * Set the endpoint address for the specified port name.
    */
    public void setEndpointAddress(javax.xml.namespace.QName portName, java.lang.String address) throws javax.xml.rpc.ServiceException {
        setEndpointAddress(portName.getLocalPart(), address);
    }

}
