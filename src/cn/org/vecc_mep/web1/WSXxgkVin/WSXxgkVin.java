/**
 * WSXxgkVin.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package cn.org.vecc_mep.web1.WSXxgkVin;

public interface WSXxgkVin extends javax.xml.rpc.Service {

/**
 * <strong>Vin报送服务<a href="http://www.vecc-mep.org.cn/download/WSXxgkVin.PDF"
 * target="_blank"><font size="3" color="#FF0000">(详细使用说明点此下载)</font></a><br><br></strong>数据来源于<strong>中国环境保护部机动车排污监控中心</strong>
 * <a href="http://www.vecc-mep.org.cn" target="_blank">http://www.vecc-mep.org.cn/</a>
 */
    public java.lang.String getWSXxgkVinSoapAddress();

    public cn.org.vecc_mep.web1.WSXxgkVin.WSXxgkVinSoap getWSXxgkVinSoap() throws javax.xml.rpc.ServiceException;

    public cn.org.vecc_mep.web1.WSXxgkVin.WSXxgkVinSoap getWSXxgkVinSoap(java.net.URL portAddress) throws javax.xml.rpc.ServiceException;
}
