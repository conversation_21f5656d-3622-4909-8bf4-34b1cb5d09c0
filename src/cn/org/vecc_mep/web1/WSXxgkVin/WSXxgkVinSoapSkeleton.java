/**
 * WSXxgkVinSoapSkeleton.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package cn.org.vecc_mep.web1.WSXxgkVin;

public class WSXxgkVinSoapSkeleton implements cn.org.vecc_mep.web1.WSXxgkVin.WSXxgkVinSoap, org.apache.axis.wsdl.Skeleton {
    private cn.org.vecc_mep.web1.WSXxgkVin.WSXxgkVinSoap impl;
    private static java.util.Map _myOperations = new java.util.Hashtable();
    private static java.util.Collection _myOperationsList = new java.util.ArrayList();

    /**
    * Returns List of OperationDesc objects with this name
    */
    public static java.util.List getOperationDescByName(java.lang.String methodName) {
        return (java.util.List)_myOperations.get(methodName);
    }

    /**
    * Returns Collection of OperationDescs
    */
    public static java.util.Collection getOperationDescs() {
        return _myOperationsList;
    }

    static {
        org.apache.axis.description.OperationDesc _oper;
        org.apache.axis.description.FaultDesc _fault;
        org.apache.axis.description.ParameterDesc [] _params;
        _params = new org.apache.axis.description.ParameterDesc [] {
            new org.apache.axis.description.ParameterDesc(new javax.xml.namespace.QName("http://web1.vecc-sepa.org.cn/WSXxgkVin", "manufid"), org.apache.axis.description.ParameterDesc.IN, new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"), java.lang.String.class, false, false), 
            new org.apache.axis.description.ParameterDesc(new javax.xml.namespace.QName("http://web1.vecc-sepa.org.cn/WSXxgkVin", "password"), org.apache.axis.description.ParameterDesc.IN, new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"), java.lang.String.class, false, false), 
        };
        _oper = new org.apache.axis.description.OperationDesc("login", _params, new javax.xml.namespace.QName("http://web1.vecc-sepa.org.cn/WSXxgkVin", "loginResult"));
        _oper.setReturnType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        _oper.setElementQName(new javax.xml.namespace.QName("http://web1.vecc-sepa.org.cn/WSXxgkVin", "login"));
        _oper.setSoapAction("http://web1.vecc-sepa.org.cn/WSXxgkVin/login");
        _myOperationsList.add(_oper);
        if (_myOperations.get("login") == null) {
            _myOperations.put("login", new java.util.ArrayList());
        }
        ((java.util.List)_myOperations.get("login")).add(_oper);
        _params = new org.apache.axis.description.ParameterDesc [] {
            new org.apache.axis.description.ParameterDesc(new javax.xml.namespace.QName("http://web1.vecc-sepa.org.cn/WSXxgkVin", "key"), org.apache.axis.description.ParameterDesc.IN, new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"), java.lang.String.class, false, false), 
            new org.apache.axis.description.ParameterDesc(new javax.xml.namespace.QName("http://web1.vecc-sepa.org.cn/WSXxgkVin", "xxgkh"), org.apache.axis.description.ParameterDesc.IN, new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"), java.lang.String.class, false, false), 
        };
        _oper = new org.apache.axis.description.OperationDesc("getVinCountByXxgkh", _params, new javax.xml.namespace.QName("http://web1.vecc-sepa.org.cn/WSXxgkVin", "getVinCountByXxgkhResult"));
        _oper.setReturnType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        _oper.setElementQName(new javax.xml.namespace.QName("http://web1.vecc-sepa.org.cn/WSXxgkVin", "getVinCountByXxgkh"));
        _oper.setSoapAction("http://web1.vecc-sepa.org.cn/WSXxgkVin/getVinCountByXshzh");
        _myOperationsList.add(_oper);
        if (_myOperations.get("getVinCountByXxgkh") == null) {
            _myOperations.put("getVinCountByXxgkh", new java.util.ArrayList());
        }
        ((java.util.List)_myOperations.get("getVinCountByXxgkh")).add(_oper);
        _params = new org.apache.axis.description.ParameterDesc [] {
            new org.apache.axis.description.ParameterDesc(new javax.xml.namespace.QName("http://web1.vecc-sepa.org.cn/WSXxgkVin", "key"), org.apache.axis.description.ParameterDesc.IN, new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"), java.lang.String.class, false, false), 
            new org.apache.axis.description.ParameterDesc(new javax.xml.namespace.QName("http://web1.vecc-sepa.org.cn/WSXxgkVin", "dtFrom"), org.apache.axis.description.ParameterDesc.IN, new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"), java.lang.String.class, false, false), 
            new org.apache.axis.description.ParameterDesc(new javax.xml.namespace.QName("http://web1.vecc-sepa.org.cn/WSXxgkVin", "dtTo"), org.apache.axis.description.ParameterDesc.IN, new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"), java.lang.String.class, false, false), 
        };
        _oper = new org.apache.axis.description.OperationDesc("getVinCountByDate", _params, new javax.xml.namespace.QName("http://web1.vecc-sepa.org.cn/WSXxgkVin", "getVinCountByDateResult"));
        _oper.setReturnType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        _oper.setElementQName(new javax.xml.namespace.QName("http://web1.vecc-sepa.org.cn/WSXxgkVin", "getVinCountByDate"));
        _oper.setSoapAction("http://web1.vecc-sepa.org.cn/WSXxgkVin/getVinCountByDate");
        _myOperationsList.add(_oper);
        if (_myOperations.get("getVinCountByDate") == null) {
            _myOperations.put("getVinCountByDate", new java.util.ArrayList());
        }
        ((java.util.List)_myOperations.get("getVinCountByDate")).add(_oper);
        _params = new org.apache.axis.description.ParameterDesc [] {
            new org.apache.axis.description.ParameterDesc(new javax.xml.namespace.QName("http://web1.vecc-sepa.org.cn/WSXxgkVin", "key"), org.apache.axis.description.ParameterDesc.IN, new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"), java.lang.String.class, false, false), 
            new org.apache.axis.description.ParameterDesc(new javax.xml.namespace.QName("http://web1.vecc-sepa.org.cn/WSXxgkVin", "vin"), org.apache.axis.description.ParameterDesc.IN, new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"), java.lang.String.class, false, false), 
        };
        _oper = new org.apache.axis.description.OperationDesc("getHbcodeByVin", _params, new javax.xml.namespace.QName("http://web1.vecc-sepa.org.cn/WSXxgkVin", "getHbcodeByVinResult"));
        _oper.setReturnType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        _oper.setElementQName(new javax.xml.namespace.QName("http://web1.vecc-sepa.org.cn/WSXxgkVin", "getHbcodeByVin"));
        _oper.setSoapAction("http://web1.vecc-sepa.org.cn/WSXxgkVin/getHbcodeByVin");
        _myOperationsList.add(_oper);
        if (_myOperations.get("getHbcodeByVin") == null) {
            _myOperations.put("getHbcodeByVin", new java.util.ArrayList());
        }
        ((java.util.List)_myOperations.get("getHbcodeByVin")).add(_oper);
        _params = new org.apache.axis.description.ParameterDesc [] {
            new org.apache.axis.description.ParameterDesc(new javax.xml.namespace.QName("http://web1.vecc-sepa.org.cn/WSXxgkVin", "key"), org.apache.axis.description.ParameterDesc.IN, new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"), java.lang.String.class, false, false), 
            new org.apache.axis.description.ParameterDesc(new javax.xml.namespace.QName("http://web1.vecc-sepa.org.cn/WSXxgkVin", "vin"), org.apache.axis.description.ParameterDesc.IN, new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"), java.lang.String.class, false, false), 
        };
        _oper = new org.apache.axis.description.OperationDesc("delData", _params, new javax.xml.namespace.QName("http://web1.vecc-sepa.org.cn/WSXxgkVin", "delDataResult"));
        _oper.setReturnType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        _oper.setElementQName(new javax.xml.namespace.QName("http://web1.vecc-sepa.org.cn/WSXxgkVin", "delData"));
        _oper.setSoapAction("http://web1.vecc-sepa.org.cn/WSXxgkVin/delData");
        _myOperationsList.add(_oper);
        if (_myOperations.get("delData") == null) {
            _myOperations.put("delData", new java.util.ArrayList());
        }
        ((java.util.List)_myOperations.get("delData")).add(_oper);
        _params = new org.apache.axis.description.ParameterDesc [] {
            new org.apache.axis.description.ParameterDesc(new javax.xml.namespace.QName("http://web1.vecc-sepa.org.cn/WSXxgkVin", "key"), org.apache.axis.description.ParameterDesc.IN, new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"), java.lang.String.class, false, false), 
        };
        _oper = new org.apache.axis.description.OperationDesc("logout", _params, new javax.xml.namespace.QName("http://web1.vecc-sepa.org.cn/WSXxgkVin", "logoutResult"));
        _oper.setReturnType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        _oper.setElementQName(new javax.xml.namespace.QName("http://web1.vecc-sepa.org.cn/WSXxgkVin", "logout"));
        _oper.setSoapAction("http://web1.vecc-sepa.org.cn/WSXxgkVin/logout");
        _myOperationsList.add(_oper);
        if (_myOperations.get("logout") == null) {
            _myOperations.put("logout", new java.util.ArrayList());
        }
        ((java.util.List)_myOperations.get("logout")).add(_oper);
        _params = new org.apache.axis.description.ParameterDesc [] {
            new org.apache.axis.description.ParameterDesc(new javax.xml.namespace.QName("http://web1.vecc-sepa.org.cn/WSXxgkVin", "key"), org.apache.axis.description.ParameterDesc.IN, new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"), java.lang.String.class, false, false), 
            new org.apache.axis.description.ParameterDesc(new javax.xml.namespace.QName("http://web1.vecc-sepa.org.cn/WSXxgkVin", "strVinData"), org.apache.axis.description.ParameterDesc.IN, new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"), java.lang.String.class, false, false), 
        };
        _oper = new org.apache.axis.description.OperationDesc("sendVinData", _params, new javax.xml.namespace.QName("http://web1.vecc-sepa.org.cn/WSXxgkVin", "sendVinDataResult"));
        _oper.setReturnType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        _oper.setElementQName(new javax.xml.namespace.QName("http://web1.vecc-sepa.org.cn/WSXxgkVin", "sendVinData"));
        _oper.setSoapAction("http://web1.vecc-sepa.org.cn/WSXxgkVin/sendVinData");
        _myOperationsList.add(_oper);
        if (_myOperations.get("sendVinData") == null) {
            _myOperations.put("sendVinData", new java.util.ArrayList());
        }
        ((java.util.List)_myOperations.get("sendVinData")).add(_oper);
    }

    public WSXxgkVinSoapSkeleton() {
        this.impl = new cn.org.vecc_mep.web1.WSXxgkVin.WSXxgkVinSoapImpl();
    }

    public WSXxgkVinSoapSkeleton(cn.org.vecc_mep.web1.WSXxgkVin.WSXxgkVinSoap impl) {
        this.impl = impl;
    }
    public java.lang.String login(java.lang.String manufid, java.lang.String password) throws java.rmi.RemoteException
    {
        java.lang.String ret = impl.login(manufid, password);
        return ret;
    }

    public java.lang.String getVinCountByXxgkh(java.lang.String key, java.lang.String xxgkh) throws java.rmi.RemoteException
    {
        java.lang.String ret = impl.getVinCountByXxgkh(key, xxgkh);
        return ret;
    }

    public java.lang.String getVinCountByDate(java.lang.String key, java.lang.String dtFrom, java.lang.String dtTo) throws java.rmi.RemoteException
    {
        java.lang.String ret = impl.getVinCountByDate(key, dtFrom, dtTo);
        return ret;
    }

    public java.lang.String getHbcodeByVin(java.lang.String key, java.lang.String vin) throws java.rmi.RemoteException
    {
        java.lang.String ret = impl.getHbcodeByVin(key, vin);
        return ret;
    }

    public java.lang.String delData(java.lang.String key, java.lang.String vin) throws java.rmi.RemoteException
    {
        java.lang.String ret = impl.delData(key, vin);
        return ret;
    }

    public java.lang.String logout(java.lang.String key) throws java.rmi.RemoteException
    {
        java.lang.String ret = impl.logout(key);
        return ret;
    }

    public java.lang.String sendVinData(java.lang.String key, java.lang.String strVinData) throws java.rmi.RemoteException
    {
        java.lang.String ret = impl.sendVinData(key, strVinData);
        return ret;
    }

}
