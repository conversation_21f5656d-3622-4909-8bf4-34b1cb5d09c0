package cn.org.vecc_mep.web1.WSXxgkVin;

public class WSXxgkVinSoapProxy implements cn.org.vecc_mep.web1.WSXxgkVin.WSXxgkVinSoap {
  private String _endpoint = null;
  private cn.org.vecc_mep.web1.WSXxgkVin.WSXxgkVinSoap wSXxgkVinSoap = null;
  
  public WSXxgkVinSoapProxy() {
    _initWSXxgkVinSoapProxy();
  }
  
  public WSXxgkVinSoapProxy(String endpoint) {
    _endpoint = endpoint;
    _initWSXxgkVinSoapProxy();
  }
  
  private void _initWSXxgkVinSoapProxy() {
    try {
      wSXxgkVinSoap = (new cn.org.vecc_mep.web1.WSXxgkVin.WSXxgkVinLocator()).getWSXxgkVinSoap();
      if (wSXxgkVinSoap != null) {
        if (_endpoint != null)
          ((javax.xml.rpc.Stub)wSXxgkVinSoap)._setProperty("javax.xml.rpc.service.endpoint.address", _endpoint);
        else
          _endpoint = (String)((javax.xml.rpc.Stub)wSXxgkVinSoap)._getProperty("javax.xml.rpc.service.endpoint.address");
      }
      
    }
    catch (javax.xml.rpc.ServiceException serviceException) {}
  }
  
  public String getEndpoint() {
    return _endpoint;
  }
  
  public void setEndpoint(String endpoint) {
    _endpoint = endpoint;
    if (wSXxgkVinSoap != null)
      ((javax.xml.rpc.Stub)wSXxgkVinSoap)._setProperty("javax.xml.rpc.service.endpoint.address", _endpoint);
    
  }
  
  public cn.org.vecc_mep.web1.WSXxgkVin.WSXxgkVinSoap getWSXxgkVinSoap() {
    if (wSXxgkVinSoap == null)
      _initWSXxgkVinSoapProxy();
    return wSXxgkVinSoap;
  }
  
  public java.lang.String login(java.lang.String manufid, java.lang.String password) throws java.rmi.RemoteException{
    if (wSXxgkVinSoap == null)
      _initWSXxgkVinSoapProxy();
    return wSXxgkVinSoap.login(manufid, password);
  }
  
  public java.lang.String getVinCountByXxgkh(java.lang.String key, java.lang.String xxgkh) throws java.rmi.RemoteException{
    if (wSXxgkVinSoap == null)
      _initWSXxgkVinSoapProxy();
    return wSXxgkVinSoap.getVinCountByXxgkh(key, xxgkh);
  }
  
  public java.lang.String getVinCountByDate(java.lang.String key, java.lang.String dtFrom, java.lang.String dtTo) throws java.rmi.RemoteException{
    if (wSXxgkVinSoap == null)
      _initWSXxgkVinSoapProxy();
    return wSXxgkVinSoap.getVinCountByDate(key, dtFrom, dtTo);
  }
  
  public java.lang.String getHbcodeByVin(java.lang.String key, java.lang.String vin) throws java.rmi.RemoteException{
    if (wSXxgkVinSoap == null)
      _initWSXxgkVinSoapProxy();
    return wSXxgkVinSoap.getHbcodeByVin(key, vin);
  }
  
  public java.lang.String delData(java.lang.String key, java.lang.String vin) throws java.rmi.RemoteException{
    if (wSXxgkVinSoap == null)
      _initWSXxgkVinSoapProxy();
    return wSXxgkVinSoap.delData(key, vin);
  }
  
  public java.lang.String logout(java.lang.String key) throws java.rmi.RemoteException{
    if (wSXxgkVinSoap == null)
      _initWSXxgkVinSoapProxy();
    return wSXxgkVinSoap.logout(key);
  }
  
  public java.lang.String sendVinData(java.lang.String key, java.lang.String strVinData) throws java.rmi.RemoteException{
    if (wSXxgkVinSoap == null)
      _initWSXxgkVinSoapProxy();
    return wSXxgkVinSoap.sendVinData(key, strVinData);
  }
  
  
}