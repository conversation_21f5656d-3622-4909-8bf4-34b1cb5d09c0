/**
 * WSXxgkVinSoapImpl.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package cn.org.vecc_mep.web1.WSXxgkVin;

public class WSXxgkVinSoapImpl implements cn.org.vecc_mep.web1.WSXxgkVin.WSXxgkVinSoap{
    public java.lang.String login(java.lang.String manufid, java.lang.String password) throws java.rmi.RemoteException {
        return null;
    }

    public java.lang.String getVinCountByXxgkh(java.lang.String key, java.lang.String xxgkh) throws java.rmi.RemoteException {
        return null;
    }

    public java.lang.String getVinCountByDate(java.lang.String key, java.lang.String dtFrom, java.lang.String dtTo) throws java.rmi.RemoteException {
        return null;
    }

    public java.lang.String getHbcodeByVin(java.lang.String key, java.lang.String vin) throws java.rmi.RemoteException {
        return null;
    }

    public java.lang.String delData(java.lang.String key, java.lang.String vin) throws java.rmi.RemoteException {
        return null;
    }

    public java.lang.String logout(java.lang.String key) throws java.rmi.RemoteException {
        return null;
    }

    public java.lang.String sendVinData(java.lang.String key, java.lang.String strVinData) throws java.rmi.RemoteException {
        return null;
    }

}
